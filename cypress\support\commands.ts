/// <reference types="cypress" />

const goToProjectTimeTab = () => {
  cy.visit(
    `${Cypress.env('BASE_URL')}projects/${Cypress.env(
      'projectHashId',
    )}/admin/time`,
  );
};

const loginWithoutSession = (phone: string, otc: string, baseURL: string) => {
  cy.visit(baseURL);
  cy.get('[data-testid="login-phone-input"]').click().type(phone);
  cy.get('[data-testid="login-enter-btn"]').click();
  cy.get('[data-testid="verify-phone-input"]').type(otc);
  cy.get('[data-testid="verify-phone-btn"]').click().should('not.exist');
};

const loginWithSession = (
  phone: string,
  otc: string,
  baseURL: string,
  userType: 'supervisor' | 'crew' | 'project-admin' | 'client-admin',
) => {
  cy.session(
    userType,
    () => {
      loginWithoutSession(phone, otc, baseURL);
      cy.get('[data-testid="card-Projects"]', { timeout: 15000 }).should(
        'be.visible',
      );
    },
    {
      cacheAcrossSpecs: true,
    },
  );
};

const pickDropdownOption = (
  dropdown: string,
  option: string,
  search = false,
) => {
  cy.get(dropdown).click();
  if (search) {
    cy.get('input[id=email-address]').should('be.visible').type(option);
  }
  cy.get('[role="menuitem"]').contains(option).click();
};

const pickSearchOption = (searchInput: string, option: string) => {
  cy.get(searchInput).click();
  cy.get(searchInput).type(option);
  cy.get('li').contains(option).click();
};

const selectDay = (datePicker: string, date: string) => {
  cy.log('Selecting date:', date);
  const dateLocator = `[datetime="${date}"]`;
  cy.get(datePicker).click();
  cy.get('body').then(($body) => {
    if ($body.find(dateLocator).is(':visible')) {
      cy.get(dateLocator).click();
    } else {
      cy.get('span').contains('Next month').parent().click();
      cy.get(dateLocator).click();
    }
  });
  cy.get('button[type="submit"]').contains('Select').click();
};

const selectTime = (timePicker: string, time: string) => {
  const hours = Number(time.split(':')[0]);
  const minutes = Number(time.split(':')[1].split(' ')[0]);
  const amPm = time.split(' ')[1];
  cy.get(timePicker).should('be.visible').click();
  cy.get('.w-8').contains(hours).should('be.visible').click();
  cy.get('.w-8').contains(minutes).should('be.visible').click();
  if (amPm === 'PM') {
    cy.get('.ml-1').contains('PM').should('be.visible').click();
  } else {
    cy.get('.mr-1').contains(amPm).should('be.visible').click();
  }
  cy.get('button[type="submit"]')
    .contains('Select')
    .should('be.visible')
    .click();
};

const signatureApproval = () => {
  cy.get('#signature-pad')
    .should('be.visible')
    .then(($canvas) => {
      const canvas = $canvas[0] as HTMLCanvasElement;
      const ctx = canvas.getContext('2d');

      if (!ctx) throw new Error('Canvas context is not available');

      ctx.strokeStyle = '#000000';
      ctx.lineWidth = 5;

      ctx.beginPath();
      ctx.moveTo(20, 50);
      ctx.lineTo(100, 80);
      ctx.lineTo(180, 30);
      ctx.stroke();

      ctx.beginPath();
      ctx.moveTo(60, 580);
      ctx.lineTo(150, 60);
      ctx.lineTo(150, 10);
      ctx.stroke();

      const imageData = ctx.getImageData(
        0,
        0,
        canvas.width,
        canvas.height,
      ).data;
      const hasDrawing = [...imageData].some((channel) => channel !== 0);
      if (!hasDrawing) {
        throw new Error('Canvas should have drawing');
      }
    });

  cy.get('#signature-pad').click({ force: true });

  cy.get('body').then(($body) => {
    if ($body.find('.modalFooter').is(':visible')) {
      cy.get('.modalFooter')
        .contains('button', 'Approve')
        .should('be.visible')
        .click();
    }
  });
};

Cypress.Commands.add('loginWithoutSession', loginWithoutSession);
Cypress.Commands.add('loginWithSession', loginWithSession);
Cypress.Commands.add('pickDropdownOption', pickDropdownOption);
Cypress.Commands.add('pickSearchOption', pickSearchOption);
Cypress.Commands.add('selectDay', selectDay);
Cypress.Commands.add('selectTime', selectTime);
Cypress.Commands.add('signatureApproval', signatureApproval);
Cypress.Commands.add('goToProjectTimeTab', goToProjectTimeTab);
