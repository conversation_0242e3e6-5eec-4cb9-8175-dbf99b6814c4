import { makeAutoObservable, runInAction } from 'mobx';
import { snackbarErr } from '@/reactComponents/library/Snackbar';
//i9
import { getCitizenshipStatuses } from '@/services/citzenship-statuses';

import type { CitizenStatus, OnboardingStep, I9Data } from './types';
import { StepIds } from './types';
import { sleep } from '@/reactComponents/utils/async';
import { listProofOfIdentityDocTypes } from '@/services/proof-of-identity';
import { listWorkAuthTypes } from '@/services/work-authorization';
import { updateI9, fetchI9 } from '@/services/onboarding';

import { prepI9Payload } from './utils';

class OnboardingStoreClass {
  private _onboardingSteps: OnboardingStep[] = [
    {
      id: StepIds.Personal,
      name: 'Personal',
      label: 'Step 1',
      indicator: 'user',
      isCompleted: true,
      isInProgress: false,
      isDisabled: false,
      isSelected: false,
    },
    {
      id: StepIds.Project,
      name: 'Project',
      label: 'Step 2',
      indicator: 'film',
      isCompleted: false,
      isInProgress: false,
      isDisabled: false,
      isSelected: false,
    },
    {
      id: StepIds.LoanOut,
      name: 'Tax Details',
      label: 'Step 3',
      indicator: 'dollar-sign',
      isCompleted: false,
      isInProgress: false,
      isDisabled: false,
      isSelected: false,
    },
    {
      id: StepIds.I9,
      name: 'I-9',
      label: 'Step 4',
      indicator: 'file-invoice',
      isCompleted: false,
      isInProgress: false,
      isDisabled: false,
      isSelected: false,
    },
    {
      id: StepIds.Review,
      name: 'Review',
      label: 'Step 5',
      indicator: 'pen-to-square',
      isCompleted: false,
      isInProgress: false,
      isDisabled: false,
      isSelected: false,
    },
  ];

  private _onboardingStep: OnboardingStep | null = null;
  private _onboardingStepIdx: number | null = null;

  private _initLoading: boolean = true;
  private _loading: boolean = true;
  private _submitting: boolean = false;

  private _citizenStatuses: CitizenStatus[] = [];
  private _i9Data: I9Data | null = null; // TODO: Define type for I9Data
  private _i9DocTypes: any[] = []; // TODO: Define type for I9DocTypes
  private _i9DocEmpAuthTypes: any[] = []; // TODO: Define type for I9DocWorkAuthTypes
  private _i9DocWorkAuthTypes: any[] = []; // TODO: Define type for I9DocWorkAuthTypes

  private _setSearchParams: any | null = null;
  private _showUnsavedModal: boolean = false;
  private _hasUnsavedChanges: boolean = false;
  private _confirmLeave: (() => void) | null = null;

  constructor() {
    makeAutoObservable(this, {}, { autoBind: true });
  }

  // Getters and Setters
  get onboardingStep() {
    return this._onboardingStep;
  }
  set onboardingStep(value: OnboardingStep | null) {
    this._onboardingStep = value;
  }
  get onboardingStepIdx() {
    return this._onboardingStepIdx;
  }
  set onboardingStepIdx(value: number | null) {
    this._onboardingStepIdx = value;
  }

  get onboardingSteps() {
    return this._onboardingSteps;
  }

  get initLoading() {
    return this._initLoading;
  }
  set initLoading(value: boolean) {
    this._initLoading = value;
  }
  get loading() {
    return this._loading;
  }
  set loading(value: boolean) {
    this._loading = value;
  }
  public get showUnsavedModal(): boolean {
    return this._showUnsavedModal;
  }
  public setShowUnsavedModal(value: boolean) {
    console.warn('setShowUnsavedModal: ', value);
    this._showUnsavedModal = value;
  }
  public get hasUnsavedChanges(): boolean {
    return this._hasUnsavedChanges;
  }
  public set hasUnsavedChanges(value: boolean) {
    this._hasUnsavedChanges = value;
  }
  public get confirmLeave(): (() => void) | null {
    return this._confirmLeave;
  }
  public set confirmLeave(value: (() => void) | null) {
    this._confirmLeave = () => {
      this.setShowUnsavedModal(false);
      if (value) value();
    };
  }
  public get submitting(): boolean {
    return this._submitting;
  }
  public set submitting(value: boolean) {
    this._submitting = value;
  }

  //React router search params

  //set from rootModal onload
  public set setSearchParams(value: any | null) {
    this._setSearchParams = value;
  }

  // i-9 specifics
  get i9Data() {
    return this._i9Data;
  }
  set i9Data(value: I9Data | null) {
    this._i9Data = value;
  }

  get citizenStatuses() {
    return this._citizenStatuses;
  }
  set citizenStatuses(value: CitizenStatus[]) {
    this._citizenStatuses = value;
  }

  get i9DocTypes() {
    return this._i9DocTypes;
  }
  set i9DocTypes(value: any[]) {
    this._i9DocTypes = value;
  }
  get i9DocEmpAuthTypes() {
    return this._i9DocEmpAuthTypes;
  }
  set i9DocEmpAuthTypes(value: any[]) {
    this._i9DocEmpAuthTypes = value;
  }

  get i9DocWorkAuthTypes() {
    return this._i9DocWorkAuthTypes;
  }
  set i9DocWorkAuthTypes(value: any[]) {
    this._i9DocWorkAuthTypes = value;
  }

  //Nav utils
  get prevBtnEnabled() {
    if (this.onboardingStepIdx === 0) {
      return false;
    }
    return true;
  }
  get nextBtnEnabled() {
    if (this.submitting) return false;

    return true;
  }

  setUnsaved(value: boolean) {
    this.hasUnsavedChanges = value;
  }

  reqCloseRootModal() {
    if (this.hasUnsavedChanges) {
      this.setShowUnsavedModal(true);
      this.confirmLeave = this.closeRootModal;
    } else {
      this.closeRootModal();
    }
  }

  closeRootModal() {
    this.onboardingStep = null;
    this.onboardingStepIdx = null;
    const searchParams = new URLSearchParams(window.location.search);
    searchParams.delete('onboarding');
    this._setSearchParams?.(searchParams, { replace: true });
  }

  handleClickNext() {
    const currentStepId = this.onboardingStep?.id;

    switch (currentStepId) {
      case StepIds.I9: {
        const form = document.getElementById(
          'i9OnboardingForm',
        ) as HTMLFormElement;
        form.requestSubmit();
        break;
      }
      case undefined:
        console.warn('Id undefined');
        break;
      default: {
        const nextStep =
          this.onboardingStepIdx || this.onboardingStepIdx === 0
            ? this.onboardingStepIdx + 1
            : 0;

        this.navToStep(nextStep, true);
        this.toggleComplete(currentStepId, true);
        break;
      }
    }

    // document.getElementById('i9OnboardingForm')?.onsubmit();
  }

  handleClickPrev() {
    if (this.hasUnsavedChanges) {
      this.setShowUnsavedModal(true);
      this.confirmLeave = this.confirmClickPrev;
      return;
    } else {
      this.confirmClickPrev();
    }
  }

  confirmClickPrev() {
    let index;
    if (this.onboardingStepIdx === null) {
      index = 0;
    } else {
      index = this.onboardingStepIdx - 1;
    }
    this.navToStep(index);
  }

  navToStep(index: number, setNextInProgress?: boolean | undefined) {
    const nextStep = this.onboardingSteps[index];

    if (nextStep) {
      // good to nav
      this.onboardingSteps.forEach((step) => {
        step.isSelected = false;
      });
      nextStep.isSelected = true;
      nextStep.isDisabled = false;
      if (setNextInProgress) nextStep.isInProgress = true;

      this.onboardingStep = nextStep;
      this.onboardingStepIdx = index;
    }
  }

  toggleComplete(id: StepIds, value: boolean | undefined) {
    const step = this.onboardingSteps.find((step) => step.id === id);
    if (step) {
      if (value !== undefined) {
        step.isCompleted = value;
      } else {
        step.isCompleted = !step.isCompleted;
      }
    }
  }

  toggleInProgress(id: StepIds, value: boolean | undefined) {
    const step = this.onboardingSteps.find((step) => step.id === id);
    if (step) {
      if (value !== undefined) {
        step.isInProgress = value;
      } else {
        step.isInProgress = !step.isInProgress;
      }
    }
  }

  toggleReadOnly(id: StepIds, value: boolean | undefined) {
    const step = this.onboardingSteps.find((step) => step.id === id);
    if (step) {
      if (value !== undefined) {
        step.isDisabled = value;
      } else {
        step.isDisabled = !step.isDisabled;
      }
    }
  }

  //fetch and set initial onboarding state
  async init(id: StepIds | undefined) {
    this.initLoading = true;
    await sleep(500); //TODO remove

    // const currentStep = await fetchOnboardingState() //fetch current state from api
    const currentStepId = id || StepIds.I9;

    runInAction(() => {
      const currentStepIdx = this.onboardingSteps.findIndex(
        (step) => step.id === currentStepId,
      );
      if (currentStepIdx === -1) {
        throw new Error('Current step not found');
      }
      this.onboardingStep = this.onboardingSteps[currentStepIdx];
      this.onboardingStepIdx = currentStepIdx;
      this.onboardingSteps.forEach((step, index) => {
        if (index < currentStepIdx) {
          step.isCompleted = true;
          step.isInProgress = false;
          step.isDisabled = false;
        } else if (index === currentStepIdx) {
          step.isInProgress = true;
          step.isCompleted = false;
          step.isDisabled = false;
          step.isSelected = true;
        } else {
          step.isInProgress = false;
          step.isCompleted = false;
          step.isDisabled = true;
        }
      });
      this.initLoading = false;
    });
  }

  async initI9() {
    this.loading = true;
    await Promise.all([
      this.fetchI9Data(),
      this.fetchCitizenStatuses(),
      this.fetchProofOfIdentityDocTypes(),
      //TODO fetch current state of i9
    ]).catch(() => {
      snackbarErr('Error loading templates');
      this.unmountI9();
    });
    this.loading = false;
  }
  async submitI9(i9Data: I9Data) {
    this.submitting = true;
    try {
      //TODO
      // console.info('Submitted i9Data: ', i9Data);
      // console.info(JSON.stringify(i9Data));

      const payload = prepI9Payload(i9Data);

      await updateI9(payload);
      this.hasUnsavedChanges = false;
      // await sleep(3000);

      this.toggleComplete(StepIds.I9, true);
      this.toggleInProgress(StepIds.I9, false);
    } catch (error) {
      snackbarErr('Error submitting i9 data');
      throw new Error('Error submitting i9 data');
    } finally {
      this.submitting = false;
    }
    //TODO submit i9 data
    const i9Step = this.onboardingSteps.findIndex((step) => step.id === 'i9');
    if (i9Step === -1) {
      throw new Error('I9 step not found');
    }
    this.navToStep(i9Step + 1, true);
  }

  unmountI9() {
    this.onboardingStep = null;
    this.citizenStatuses = [];
    this.i9DocTypes = [];
    this.i9DocEmpAuthTypes = [];
    this.i9DocWorkAuthTypes = [];
  }

  async fetchI9Data() {
    try {
      //TODO fetch i9 data
      const response = await fetchI9();
      const i9Data = response.data;
      runInAction(() => {
        this.i9Data = i9Data;
      });
      // console.info('Fetched I-9 data: ', i9Data);
    } catch (error) {
      snackbarErr('Error loading I-9 data');
      console.error(error);
    }
  }

  async fetchCitizenStatuses() {
    try {
      const response = await getCitizenshipStatuses();
      this.citizenStatuses = response.data;
    } catch (error) {
      snackbarErr('Error loading citizenship statuses');
    }
  }

  async fetchProofOfIdentityDocTypes() {
    try {
      const [docTypesRes, workAuthTypesRes] = await Promise.all([
        listProofOfIdentityDocTypes('AB'),
        listProofOfIdentityDocTypes('C'),
      ]);

      const docTypes = docTypesRes.data;
      // Move 'us-passport' and 'driver-license' to the front if they exist
      const usPassportIdx = docTypes.findIndex(
        (d: any) => d.key === 'us-passport',
      );

      const reordered: any[] = [];
      if (usPassportIdx !== -1) {
        reordered.push(docTypes[usPassportIdx]);
        docTypes.splice(usPassportIdx, 1);
      }
      // If driver-license was after us-passport, its index will have shifted by -1
      const newDriverLicenseIdx = docTypes.findIndex(
        (d: any) => d.key === 'driver-license',
      );
      if (newDriverLicenseIdx !== -1) {
        reordered.push(docTypes[newDriverLicenseIdx]);
        docTypes.splice(newDriverLicenseIdx, 1);
      }
      this.i9DocTypes = [...reordered, ...docTypes];

      const workAuths = workAuthTypesRes.data;
      // Move 'ssn-card' to the front if it exists
      const ssnCardIdx = workAuths.findIndex((d: any) => d.key === 'ssn-card');
      const reorderedWorkAuths: any[] = [];
      if (ssnCardIdx !== -1) {
        reorderedWorkAuths.push(workAuths[ssnCardIdx]);
        workAuths.splice(ssnCardIdx, 1);
      }
      this.i9DocEmpAuthTypes = [...reorderedWorkAuths, ...workAuths];
    } catch (error) {
      snackbarErr('Error loading proof of identity document types');
    }
  }

  async getWorkAuthTypes(citizenship: string) {
    try {
      const response = await listWorkAuthTypes(citizenship);
      this.i9DocWorkAuthTypes = response.data;
    } catch (error) {
      snackbarErr('Error loading work authorization document types');
    }
  }
}

const OnboardingStore = new OnboardingStoreClass();
export default OnboardingStore;
