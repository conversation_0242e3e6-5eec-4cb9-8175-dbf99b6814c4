import React from 'react';
import PropTypes from 'prop-types';

import {
  Box,
  Autocomplete,
  TextFieldRaw as TextFieldMui,
  FormControlLabel,
  FormControl,
  Radio,
  RadioGroup,
  DatePicker,
  Dialog,
  Modal,
  Loader,
} from '@/reactComponents/library';
import { Typography } from '@castandcrew/platform-ui';
import { styled } from '@mui/material';
import Section from '@/reactComponents/viewsV2/Onboarding/components/Section';
import { PageBackgroundBox, PageSectionsBox } from '../../styledComponents';
import PageTitle from '../PageTitle';
import { useDidMount } from '@/reactComponents/utils/customHooks';
import {
  getContractSettingsAndScheduleInfo,
  getCurrentMember,
  getTrackingDetails,
  listProjectHireLocations,
  listProjectOccupations,
  listProjectShootLocations,
  listProjectUnions,
  updateMember,
} from '@/services/project';
import { listRateTypes } from '@/services/rate-types';
import {
  getProjectMemberById,
  updateProjectMember, // for save
  requestLoanOutApproval,
} from '@/services/project-members';
// import { getStates } from '@/services/address';
import { ProjectMemberLoanOutStatusId } from '@/utils/enum';
import { TrackingKeysEnum } from '@/types/TimecardDay';
import { rateTypeFilter } from '@/utils/filterRateTypes';
import {
  snackbarSuccess,
  snackbarErr,
  snackbarInfo,
} from '@/reactComponents/library/Snackbar';
import { DateTime } from 'luxon';
import { Controller, useForm } from 'react-hook-form';
import axios from 'axios';
import ProjectStore from '@/reactComponents/stores/project';
import { observer } from 'mobx-react-lite';
import { useParams } from 'react-router';

const StyledBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: '6px',
  marginTop: '16px',
}));

const StyledTextField = styled(TextFieldMui)(({ theme }) => ({
  width: '100%',
  maxWidth: 'none',
  '& .MuiInputBase-root': {
    padding: '4px',
  },
}));

const LabelText = styled(Typography)(({ theme }) => ({
  fontSize: '0.875rem',
  fontWeight: '575',
  color: '#414755',
  lineHeight: '20px',
}));

const StyledRadio = styled(Radio)(({ theme }) => ({
  [theme.breakpoints.down('md')]: {
    padding: '2px',
    marginLeft: '7px',
  },
}));

const RadioGroupResponsive = styled(RadioGroup)(({ theme }) => ({
  display: 'flex',
  [theme.breakpoints.down('md')]: {
    flexDirection: 'column',
  },
  [theme.breakpoints.up('md')]: {
    flexDirection: 'row',
  },
}));

const Project = observer((props) => {
  const { supervisorView = false } = props;
  const project = ProjectStore.project;
  const params = useParams();

  const [lineNumberFieldsAllowed, setLineNumberFieldsAllowed] =
    React.useState(false);
  const [loadingProjectOnboarding, setLoadingProjectOnboarding] =
    React.useState(false);
  const [loadingOccupations, setLoadingOccupations] = React.useState(false);
  const [loadingRateTypes, setLoadingRateTypes] = React.useState(false);
  const [loadingUnions, setLoadingUnions] = React.useState(false);
  const [loadingHireLocation, setLoadingHireLocation] = React.useState(false);

  const [contractSettingsAndScheduleInfo, setContractSettingsAndScheduleInfo] =
    React.useState(null);

  const [confirmLoadOutModal, setConformLoadOutModal] = React.useState(false);
  const [requestingLoanOut, setRequestingLoanOut] = React.useState(false);
  const [pendingLoadOutModal, setPendingLoadOutModal] = React.useState(false);

  const [employmentType, setEmploymentType] = React.useState({
    label: 'W2',
    value: 'w2',
  });

  const {
    control,
    setValue,
    getValues,
    reset,
    handleSubmit,
    // register,
    // formState
  } = useForm({
    defaultValues: {
      rate: 0,
      rateType: undefined,
      occupation: undefined,
      startDate: DateTime.now().toUTC(),
      hireLocationCity: undefined,
      hireLocationState: undefined,
      workLocation: undefined,
      lineNumber: '',
      shootLineNumber: '',
      department: undefined,
      requiresHireLocation: undefined,
      unions: [],
    },
  });
  // console.log('formState', getValues());

  const [projectShootLocations, setProjectShootLocations] = React.useState([]);
  const [occupations, setOccupations] = React.useState([]);
  const [rateTypes, setRateTypes] = React.useState([]);
  const [hireLocations, setHireLocations] = React.useState([]);
  const [unions, setUnions] = React.useState([]);

  const [nonUnionOption, setNonUnionOption] = React.useState({}); // default non-union option value, could be just a ref

  const projectMemberId = React.useMemo(() => {
    if (!supervisorView) {
      return null;
    }
    const projectMemberId = params?.projectMemberId; // useParams in react router hook
    return projectMemberId ? parseInt(projectMemberId) : null;
  }, [supervisorView, params]);

  const [occupationSearch, setOccupationSearch] = React.useState('');
  const [occupationPagination, setOccupationPagination] = React.useState({
    page: 1,
    limit: 50,
    total: 0,
  });

  const [unionType, setUnionType] = React.useState(null); // union or non-union

  const handlUnionTypeChange = (value) => {
    setUnionType(value);
    if (value === 'Non-Union') {
      setValue('unions', [nonUnionOption]);
    }
    if (value === 'Union') {
      setValue('unions', []);
    }
    handleUnionChange();
  };

  const handleUnionChange = (newValue) => {
    // if (newValue) {

    setOccupations([]);
    setOccupationSearch('');
    setValue('occupation', null);

    setValue('hireLocation', null);
    setOccupationPagination({ page: 1, limit: 50, total: 0 });
    getOccupations();
    // }
  };

  useDidMount(() => {
    onInitMount();
  });

  const onInitMount = async () => {
    // await getTrackingHeaderDetails(); // needed in new onboarding
    // await getProjectMember(); // needed
    await Promise.all([getTrackingHeaderDetails(), getProjectMember()]);
    await populateOptions(); // needed
    // await loadShootLocations(); // needed, working location
    const projectOnboarding = getValues();
    await setDepartment(projectOnboarding?.department?.id || 0);
    getOccupations(); // needed
    handleOccupationChange(true); // initial mount occupation setup, needed
  };

  const [saving, setSaving] = React.useState(false);

  const save = async (projectOnboardingData) => {
    if (saving || loadingHireLocation) return;
    setSaving(true);
    const projectOnboarding = projectOnboardingData
      ? projectOnboardingData
      : getValues();
    if (!projectOnboarding.workLocation) {
      snackbarErr('Please select a work location.', 2500);
      setSaving(false);
      return;
    }

    if (
      projectOnboarding.requiresHireLocation &&
      !projectOnboarding.hireLocation
    ) {
      snackbarErr('Please select a hire location.', 2500);
      setSaving(false);
      return;
    }
    try {
      if (supervisorView && projectMemberId) {
        await updateProjectMember(projectMemberId, projectOnboarding);
        // for supervisor view in the future
        // props.refresh();
        // props.navigate(-1);
      } else {
        await updateMember(project.id, projectOnboarding);
        // navigate({
        //   pathname: `/projects/${project.hashId}`,
        // });
        if (employmentType.value === 'loan-out') {
          setConformLoadOutModal(true);
        }
        // TODO: navigate to next step
      }
      snackbarSuccess('Project information saved.', 2500);
    } catch (err) {
      if (axios.isAxiosError(err)) {
        const msg = err.response?.data?.['errors']?.[0]?.message;
        snackbarErr(msg || 'Failed to save', 2500);
      } else {
        snackbarErr(err.message || 'Failed to save', 2500);
      }
    } finally {
      setSaving(false);
    }
  };

  const loadContractSettingsAndScheduleInfo = async () => {
    try {
      const projectOnboarding = getValues();

      const { data } = await getContractSettingsAndScheduleInfo(
        project.id,
        projectOnboarding.workLocation?.payrollProjectLocationId || null,
        projectOnboarding.occupation?.payrollOccupationCode || null,
        projectOnboarding.startDate?.toISODate(),
        projectOnboarding.unions?.[0]?.name || '',
        projectOnboarding.unions?.[0]?.castAndCrewId ||
          projectOnboarding.unions?.[0]?.id ||
          null,
        projectOnboarding.hireLocation?.payrollHireLocationId || null,
      );
      const contractSettings = {
        ...data.item,
        isHireLocationRequired: data.isHireLocationRequired,
      };
      setContractSettingsAndScheduleInfo(contractSettings);
      setValue('requiresHireLocation', data.isHireLocationRequired);
      await getRateTypes(contractSettings);
    } catch (err) {
      snackbarErr(err.message || 'Failed to load contract settings', 2500);
      console.warn('Failed to load contract settings', err);
    }
  };

  const handleOccupationChange = async (onMountedCall = false) => {
    const projectOnboarding = getValues();
    const union = projectOnboarding.unions?.[0];
    if (!union) {
      return;
    }
    if (!onMountedCall) {
      setValue('rateType', null);
      setValue('hireLocation', null);
      setValue('hireLocationId', null);
    }
    await loadContractSettingsAndScheduleInfo();
    const projectOnboardingAfterContractReset = getValues();
    const requiresHireLocation =
      projectOnboardingAfterContractReset.requiresHireLocation;

    if (requiresHireLocation) {
      try {
        setLoadingHireLocation(true);
        const { data } = await listProjectHireLocations(
          project.id,
          projectOnboarding.workLocation?.payrollProjectLocationId || null,
          union.castAndCrewId || union.id || null,
          projectOnboarding.occupation?.payrollOccupationCode || null,
        );
        setValue('hireLocation', null);
        setValue('hireLocationId', null);
        setHireLocations(data.hireLocationList || []);
      } catch (err) {
        console.warn('Failed to list project hire locations', err);
        snackbarErr('Failed to load hire locations', 2500);
      } finally {
        setLoadingHireLocation(false);
      }
    } else {
      setValue('hireLocation', null);
      setValue('hireLocationId', null);
    }
  };

  const handleHireLocationChange = async () => {
    if (contractSettingsAndScheduleInfo?.isHireLocationRequired) {
      await loadContractSettingsAndScheduleInfo();
    }
  };

  const getRateTypes = async (contractSettingsAndScheduleInfo) => {
    try {
      setLoadingRateTypes(true);
      setRateTypes([]);
      const { data: rateTypesData } = await listRateTypes();

      if (
        contractSettingsAndScheduleInfo &&
        rateTypesData &&
        rateTypesData.length > 0
      ) {
        const filteredRateTypes = rateTypeFilter(
          contractSettingsAndScheduleInfo,
          rateTypesData,
        );
        setRateTypes(filteredRateTypes);
      }
    } catch (err) {
      console.warn('Failed to get rate types', err);
    } finally {
      setLoadingRateTypes(false);
    }
  };

  const NON_UNION_NAME = 'NON';
  const updateUnionList = async () => {
    try {
      setLoadingUnions(true);
      setUnions([]);
      const projectOnboarding = getValues();

      const { data: unionData } = await listProjectUnions(
        project.id,
        projectOnboarding.workLocation?.payrollProjectLocationId || null,
      );
      if (!projectOnboarding.unions || !unionData) return;
      const nonUnion = unionData.find(
        (union) => union.name?.toUpperCase() === NON_UNION_NAME,
      );
      setNonUnionOption(nonUnion);
      setUnions(
        unionData.filter(
          (union) => union.name?.toUpperCase() !== NON_UNION_NAME,
        ),
      );
    } catch (err) {
      snackbarErr('Failed to load unions.', 3500);
    } finally {
      setLoadingUnions(false);
    }
  };

  const getTrackingHeaderDetails = async () => {
    try {
      const { data: trackingKeys } = await getTrackingDetails(project.id);
      const trackingKeyHeaderDetails = trackingKeys.trackingKeyHeaderDetails;
      if (
        trackingKeyHeaderDetails !== null &&
        trackingKeyHeaderDetails.length > 0
      ) {
        trackingKeyHeaderDetails.forEach((eachTrackingKey) => {
          if (
            eachTrackingKey?.name === TrackingKeysEnum.KEY_TRACKKEY &&
            eachTrackingKey?.allowed
          ) {
            setLineNumberFieldsAllowed(true);
          }
        });
      }
    } catch (err) {
      console.warn('Failed to get tracking details', err);
    }
  };

  const getProjectMember = async () => {
    try {
      setLoadingProjectOnboarding(true);
      let projectOnboardingData;
      if (supervisorView && projectMemberId) {
        const { data } = await getProjectMemberById(projectMemberId);
        projectOnboardingData = data;
      } else {
        const { data } = await getCurrentMember(project.id);
        projectOnboardingData = data;
      }

      const requestingLoanOut =
        projectOnboardingData?.loanOutStatusId ===
        ProjectMemberLoanOutStatusId.Requested;

      if (requestingLoanOut) {
        setPendingLoadOutModal(true);
        setEmploymentType({ label: 'Loan out', value: 'loan-out' });
      }

      if (projectOnboardingData) {
        reset({
          ...projectOnboardingData,
        });
      }
      if (
        projectOnboardingData?.unions &&
        projectOnboardingData.unions.length > 0
      ) {
        if (
          projectOnboardingData.unions[0]?.name?.toUpperCase() ===
          NON_UNION_NAME
        ) {
          setUnionType('Non-Union');
        } else {
          setUnionType('Union');
        }
        // setUnion(nonUnionOption);
        // setUnion(projectOnboardingData.unions[0]);
        // setValue('unions', projectOnboardingData.unions);
      }
    } catch (err) {
      console.error('Error fetching project member:', err);
    } finally {
      setLoadingProjectOnboarding(false);
    }
  };

  const populateOptions = () => {
    return Promise.all([
      // getRateTypes(),
      // fetchStates(),
      updateUnionList(),
      loadShootLocations(),
    ]);
  };

  const loadShootLocations = async () => {
    try {
      const { data } = await listProjectShootLocations(project.id);
      setProjectShootLocations(data);
    } catch (err) {
      console.warn('Failed to list shoot locations', err);
    }
  };

  const getOccupations = async () => {
    try {
      setLoadingOccupations(true);
      const projectOnboarding = getValues();
      const { data: occupationsData } = await listProjectOccupations(
        project.id,
        projectOnboarding.workLocation?.payrollProjectLocationId || null,
        projectOnboarding.unions || null,
        occupationSearch,
        occupationPagination,
      );
      if (occupationPagination.page === 1) {
        setOccupations([]);
      }
      setOccupations(occupationsData.jobTitleList);
      setOccupationPagination((prev) => ({
        ...prev,
        total: occupationsData.totalCount,
      }));
    } catch (err) {
      console.warn('Failed to list occupations', err);
    } finally {
      setLoadingOccupations(false);
    }
  };

  const setDepartment = (departmentId) => {
    let department = null;
    if (departmentId) {
      department = project.departments.find((d) => d.id === departmentId);
    }
    if (!department) {
      department = project.departments.find((d) => d.type.key === 'general');
    }
    setValue('department', department || {});
  };

  // watch(occupationSearch, async (val) => {
  //   paginationOccupation.value = resetPagination(paginationOccupation.value);
  //   occupations.value = [];
  //   await getOccupations();
  // });

  // React.useEffect(() => {
  //   getOccupations();
  // }, [occupationSearch]);

  const union = getValues('unions')?.[0] || null;

  const isUnionWorker = unionType === 'Union';
  const hasUnionType = !!unionType;
  const shouldSelectOccupation =
    hasUnionType && ((isUnionWorker && !!union) || !isUnionWorker);

  const onSubmit = async (data) => {
    try {
      await save();
    } catch (e) {
      console.error('Error on submit:', e);
    }
  };

  const onRequestLoanOut = async () => {
    if (employmentType?.value !== 'loan-out') return;
    try {
      setRequestingLoanOut(true);
      const projectOnboarding = getValues();
      await requestLoanOutApproval(projectOnboarding?.id);
      snackbarInfo('Loan-out request submitted for approval.', 2500);
      getProjectMember();
    } catch (e) {
      snackbarErr('Loan-out request failed', 2500);
      console.error(e);
    } finally {
      setRequestingLoanOut(false);
      setConformLoadOutModal(false);
    }
  };

  return (
    <form
      style={{ width: '100%' }}
      id="projOnboardForm"
      onSubmit={handleSubmit(onSubmit)}
      onKeyDown={(e) => {
        if (e.ctrlKey && e.key === 'Enter') {
          const form = document.getElementById('projOnboardForm');
          form.requestSubmit();
        }
      }}
      tabIndex={-1}
    >
      {(loadingProjectOnboarding || saving) && (
        <Dialog open fullScreen sx={{ opacity: '0.6' }}>
          <Loader />
        </Dialog>
      )}
      <PageBackgroundBox>
        <PageTitle
          topText={'Project'}
          bottomText={"Let's collect information on this job."}
        />
        <PageSectionsBox
          sx={{ backgroundColor: 'background.default', width: '100%' }}
        >
          <Section title="Project Details" noCollapse>
            <StyledBox>
              <LabelText>Employment types *</LabelText>
              <Autocomplete
                sx={{ width: '100%', maxWidth: 'none' }}
                value={employmentType}
                getOptionLabel={(option) => option.label}
                options={[
                  { label: 'W2', value: 'w2' },
                  { label: ' Loan out', value: 'loan-out' },
                ]}
                onChange={(event, newValue) => {
                  setEmploymentType(newValue);
                }}
              />
            </StyledBox>
            <StyledBox>
              <LabelText>Work location *</LabelText>
              <Controller
                name="workLocation"
                control={control}
                render={({
                  field: { onChange, value, onBlur, ref },
                  ...others
                }) => {
                  return (
                    <Autocomplete
                      sx={{ width: '100%', maxWidth: 'none' }}
                      options={projectShootLocations}
                      onBlur={onBlur}
                      getOptionLabel={(option) =>
                        option?.shootLocation?.locationName || ''
                      }
                      getOptionKey={(option) => option.id}
                      onChange={(event, newValue) => {
                        onChange(newValue);
                        updateUnionList();
                        setValue('unions', []);
                        setValue('occupation', undefined);
                        setValue('hireLocation', null);
                      }}
                      disabled={loadingProjectOnboarding}
                      value={value || {}}
                      slotProps={{
                        input: {
                          inputRef: ref,
                        },
                      }}
                    />
                  );
                }}
                rules={{ required: 'Work location is required' }}
              />
            </StyledBox>
            <StyledBox>
              <FormControl
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '6px',
                }}
              >
                <LabelText>Unions *</LabelText>
                <RadioGroupResponsive
                  value={unionType}
                  onChange={(e) => {
                    handlUnionTypeChange(e.target.value);
                  }}
                >
                  <FormControlLabel
                    value="Union"
                    control={<StyledRadio variant="solid" color="black" />}
                    label="Union"
                    disabled={loadingUnions || unions.length === 0}
                  />
                  <FormControlLabel
                    value="Non-Union"
                    control={<StyledRadio variant="solid" color="black" />}
                    label="Non-Union"
                    disabled={
                      loadingUnions ||
                      nonUnionOption?.name?.toUpperCase() !== NON_UNION_NAME
                    }
                  />
                </RadioGroupResponsive>
              </FormControl>
            </StyledBox>
            {isUnionWorker && (
              <StyledBox>
                <LabelText>Union local number *</LabelText>
                <Controller
                  name="unions"
                  control={control}
                  render={({
                    field: { onChange, value, onBlur, ref },
                    ...others
                  }) => {
                    const val = value?.[0] || {};
                    return (
                      <Autocomplete
                        sx={{ width: '100%', maxWidth: 'none' }}
                        options={unions}
                        getOptionLabel={(option) => option.name || ''}
                        getOptionKey={(option) => option.id}
                        isOptionEqualToValue={(option, value) =>
                          option.id === value.id
                        }
                        onBlur={onBlur}
                        onChange={(event, newValue) => {
                          onChange(newValue ? [newValue] : []);
                          handleUnionChange(newValue);
                        }}
                        disabled={loadingUnions}
                        value={val || {}}
                        slotProps={{
                          input: {
                            inputRef: ref,
                          },
                        }}
                      />
                    );
                  }}
                />
              </StyledBox>
            )}
            {shouldSelectOccupation && (
              <StyledBox>
                <LabelText>Occupation *</LabelText>
                <Controller
                  name="occupation"
                  control={control}
                  render={({
                    field: { onChange, value, onBlur, ref },
                    ...others
                  }) => {
                    return (
                      <Autocomplete
                        sx={{ width: '100%', maxWidth: 'none' }}
                        options={occupations}
                        disabled={loadingOccupations}
                        getOptionLabel={(option) => option.name || ''}
                        getOptionKey={(option) => option.payrollOccupationCode}
                        onBlur={onBlur}
                        onChange={(event, newValue) => {
                          onChange(newValue);
                          handleOccupationChange();
                        }}
                        isOptionEqualToValue={(option, value) =>
                          option.payrollOccupationCode ===
                          value.payrollOccupationCode
                        }
                        value={value || {}}
                        slotProps={{
                          input: {
                            inputRef: ref,
                          },
                        }}
                      />
                    );
                  }}
                  rules={{ required: 'Occupation is required' }}
                />
              </StyledBox>
            )}
            {getValues('requiresHireLocation') &&
              getValues('occupation.name') && (
                <StyledBox>
                  <LabelText>Hire location *</LabelText>
                  <Autocomplete
                    sx={{ width: '100%', maxWidth: 'none' }}
                    options={hireLocations}
                    onChange={(event, newValue) => {
                      setValue('hireLocation', newValue);
                      setValue('hireLocationId', newValue?.id || null);
                      handleHireLocationChange();
                    }}
                  />
                </StyledBox>
              )}
            <Box sx={{ display: 'flex', gap: 2 }}>
              <StyledBox sx={{ width: '100%' }}>
                <LabelText>Rate *</LabelText>
                <Controller
                  name="rate"
                  control={control}
                  render={({ field: { onChange, value, onBlur, ref } }) => (
                    <StyledTextField
                      type="number"
                      value={value || ''}
                      onChange={(e) => {
                        const val = e.target.value;
                        if (val === '' || !isNaN(val)) {
                          onChange(val);
                        }
                      }}
                      onBlur={onBlur}
                      inputRef={ref}
                    />
                  )}
                  rules={{ required: 'Rate is required' }}
                />
              </StyledBox>
              <StyledBox sx={{ width: '100%' }}>
                <LabelText>Rate Type *</LabelText>
                <Controller
                  name="rateType"
                  control={control}
                  render={({ field: { onChange, value, onBlur, ref } }) => (
                    <Autocomplete
                      sx={{ width: '100%', maxWidth: 'none' }}
                      options={rateTypes}
                      getOptionLabel={(option) => option.name || ''}
                      getOptionKey={(option) => option.id}
                      isOptionEqualToValue={(option, value) =>
                        option.id === value.id
                      }
                      onBlur={onBlur}
                      onChange={(event, newValue) => {
                        onChange(newValue);
                      }}
                      disabled={loadingRateTypes}
                      value={value || {}}
                      slotProps={{
                        input: {
                          inputRef: ref,
                        },
                      }}
                    />
                  )}
                />
              </StyledBox>
            </Box>

            <StyledBox>
              <LabelText>Start date *</LabelText>
              <Controller
                name="startDate"
                control={control}
                render={({ field: { onChange, value, onBlur, ref } }) => (
                  <DatePicker
                    value={value}
                    onChange={(newValue) => {
                      if (newValue) {
                        onChange(newValue);
                      } else {
                        onChange(null);
                      }
                    }}
                    sx={{
                      '& .MuiInputBase-root': {
                        padding: '4px',
                        paddingRight: '14px',
                      },
                    }}
                    onBlur={onBlur}
                    ref={ref}
                  />
                )}
              />
            </StyledBox>
            {supervisorView && (
              <Box sx={{ display: 'flex', gap: 2 }}>
                <StyledBox sx={{ width: '100%' }}>
                  <LabelText>AICP/Line#</LabelText>
                  <Controller
                    name="lineNumber"
                    control={control}
                    render={({ field: { onChange, value, onBlur, ref } }) => (
                      <StyledTextField
                        type="text"
                        value={value || ''}
                        onChange={(e) => {
                          const val = e.target.value;
                          if (val === '' || /^[0-9]*$/.test(val)) {
                            onChange(val);
                          }
                        }}
                        onBlur={onBlur}
                        inputRef={ref}
                        disabled={!lineNumberFieldsAllowed}
                      />
                    )}
                  />
                </StyledBox>
                <StyledBox sx={{ width: '100%' }}>
                  <LabelText>AICP/Line# - SHOOT</LabelText>
                  <Controller
                    name="shootLineNumber"
                    control={control}
                    render={({ field: { onChange, value, onBlur, ref } }) => (
                      <StyledTextField
                        type="text"
                        value={value || ''}
                        onChange={(e) => {
                          const val = e.target.value;
                          if (val === '' || /^[0-9]*$/.test(val)) {
                            onChange(val);
                          }
                        }}
                        onBlur={onBlur}
                        inputRef={ref}
                        disabled={!lineNumberFieldsAllowed}
                      />
                    )}
                  />
                </StyledBox>
              </Box>
            )}
            <StyledBox>
              <LabelText>Department</LabelText>
              {/* TODO: in supervisor view, department should be a dropdown */}
              <Typography sx={{ fontWeight: '575' }}>
                {getValues('department.type.name') || 'None'}
              </Typography>
            </StyledBox>
            <button style={{ display: 'none' }} onClick={() => save()}>
              save
            </button>
          </Section>
        </PageSectionsBox>
      </PageBackgroundBox>
      <ConfirmLoanOutModal
        open={confirmLoadOutModal}
        setOpen={setConformLoadOutModal}
        onSubmit={onRequestLoanOut}
        loading={requestingLoanOut}
      />
      <PendingLoanOutModal
        open={pendingLoadOutModal}
        setOpen={setPendingLoadOutModal}
      />
    </form>
  );
});

const ConfirmLoanOutModal = (props) => {
  const { open, setOpen, onSubmit, loading } = props;
  return (
    <Modal
      open={open}
      setOpen={setOpen}
      loading={loading}
      title="Confirm loan-out selection"
      cancelText="No, go back"
      submitText="Request Approval"
      onSubmit={onSubmit}
      onCancel={() => setOpen(false)}
      sx={{
        '& .MuiDialog-paper': {
          maxWidth: '400px',
          width: '100%',
        },
      }}
    >
      <Box>
        <Box>
          <Typography>
            You selected to be paid through a loan-out, which needs supervisor
            approval.
          </Typography>
        </Box>
        <br />
        <Box>
          <Typography>
            You can’t complete onboarding until it’s approved.
          </Typography>
        </Box>
        <br />
        <Box>
          <Typography>
            You’ll get a notification once you’re cleared to continue.
          </Typography>
        </Box>
      </Box>
    </Modal>
  );
};

ConfirmLoanOutModal.propTypes = {
  open: PropTypes.bool.isRequired,
  setOpen: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  loading: PropTypes.bool,
};

const PendingLoanOutModal = (props) => {
  const { open, setOpen } = props;
  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Loan-out request is pending"
      sx={{
        '& .MuiDialog-paper': {
          backgroundColor: '#FFF8E5',
          maxWidth: '400px',
          width: '100%',
        },
      }}
    >
      <Box>
        <Box>
          <Typography>
            Your request to be paid through a loan-out is still awaiting
            supervisor approval.
          </Typography>
        </Box>
        <br />
        <Box>
          <Typography>
            You won’t be able to complete onboarding until it's approved.
          </Typography>
        </Box>
        <br />
        <Box>
          <Typography>
            We’ll notify you once you're cleared to continue.
          </Typography>
        </Box>
      </Box>
    </Modal>
  );
};

PendingLoanOutModal.propTypes = {
  open: PropTypes.bool.isRequired,
  setOpen: PropTypes.func.isRequired,
};

Project.propTypes = {
  supervisorView: PropTypes.bool,
  // refresh: PropTypes.func,
  // navigate: PropTypes.func,
};

export default Project;
