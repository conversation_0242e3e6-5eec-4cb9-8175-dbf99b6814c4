<template>
  <div>
    <div class="m-auto max-w-sm text-center">
      <div class="m-4">
        <h1 class="text-xl font-bold mb-2">Starting Paperwork</h1>
        <p>We are now going to generate your starting paperwork.</p>
        <div
          v-if="displayLoanOutAlert() && !isLoanOutApprovalRequested"
          class="py-3"
        >
          <span class="text-red-400"> Attention</span>: you have a loan out
          uploaded to the system, however the production supervisor has not
          approved loan out usage for you. Please message them requesting
          approval if you wish to generate start paperwork with your loan out
          information or send a reminder here.
          <Button
            size="sm"
            class="mx-auto mt-2 mb-2"
            :disabled="isLoanOutApprovalRequested"
            @click="isConfirmRequestLoanOutOpen = true"
          >
            {{
              isLoanOutApprovalRequested
                ? 'Loan Out Approval Requested'
                : 'Request Loan Out Approval'
            }}
          </Button>
        </div>
        <Modal v-model="isConfirmRequestLoanOutOpen">
          <p>
            Please confirm that you have received prior approval from the
            production supervisor to be paid as a loan out.
          </p>
          <div class="flex justify-center space-x-2 mt-6">
            <Button color="gray" @click="isConfirmRequestLoanOutOpen = false">
              Cancel
            </Button>
            <Button color="primary" @click="requestLoanOutApproval">
              Confirm
            </Button>
          </div>
        </Modal>
        <div v-if="isLoanOutApprovalRequested" class="py-3">
          <span class="text-yellow-400">Message: </span>
          Your loan out request is pending. You will not be able to complete
          onboarding until a supervisor approves your request. You will receive
          a notification if it is approved, at which point you can complete your
          onboarding.
        </div>
        <div v-if="displayLoanOutOption()" class="py-3">
          <span class="text-green-400">Loan out has been approved. </span> Would
          you like to use your loan out information for this project?
          <div class="flex justify-center pt-3">
            <Toggle v-model="useLoanOut"> Use Loan Out Information </Toggle>
          </div>
        </div>
        <div class="flex justify-center">
          <Button
            :disabled="isLoanOutApprovalRequested"
            @click="handleNavigate()"
            class="mt-3"
            color="primary"
          >
            Generate Paperwork
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Modal from '@/components/library/Modal.vue';
import Toggle from '@/components/library/Toggle.vue';
import { getCurrentMember } from '@/services/project';
import { requestLoanOutApproval } from '@/services/project-members';
import type Project from '@/types/Project';
import type ProjectMember from '@/types/ProjectMember';
import { ProjectMemberLoanOutStatusId } from '@/utils/enum';
import { PencilSquareIcon } from '@heroicons/vue/24/outline';
import { defineComponent, type PropType } from 'vue';
import Button from '../../components/library/Button.vue';
import Dropdown from '../../components/library/Dropdown.vue';
import TextInput from '../../components/library/TextInput.vue';

export default defineComponent({
  props: {
    project: {
      type: Object as PropType<Project>,
      required: true,
    },
    navigate: {
      type: Function,
      required: true,
    },
  },
  components: {
    Button,
    TextInput,
    Dropdown,
    PencilSquareIcon,
    Modal,
    Toggle,
  },
  data() {
    return {
      projectMember: null as ProjectMember | null,
      useLoanOut: false,
      isConfirmRequestLoanOutOpen: false,
    };
  },
  computed: {
    isLoanOutApprovalRequested(): boolean {
      return (
        this.projectMember?.loanOutStatusId ===
        ProjectMemberLoanOutStatusId.Requested
      );
    },
  },
  methods: {
    handleNavigate() {
      const path = `/projects/${this.project.hashId}/start/approve`;
      const searchParams = new URLSearchParams();
      searchParams.set('useLoanOut', this.useLoanOut?.toString() || '');
      searchParams.set(
        'projectMemberId',
        this.projectMember?.id.toString() || '',
      );

      this.navigate({
        pathname: path,
        search: searchParams.toString(),
      });
    },
    displayLoanOutAlert() {
      return (
        this.projectMember?.user?.userCrew?.loanOut &&
        ![
          ProjectMemberLoanOutStatusId.Approved,
          ProjectMemberLoanOutStatusId.Used,
        ].includes(this.projectMember?.loanOutStatusId)
      );
    },
    displayLoanOutOption() {
      return (
        this.projectMember?.user?.userCrew?.loanOut &&
        [
          ProjectMemberLoanOutStatusId.Approved,
          ProjectMemberLoanOutStatusId.Used,
        ].includes(this.projectMember?.loanOutStatusId)
      );
    },
    async requestLoanOutApproval(): Promise<void> {
      await requestLoanOutApproval(this.projectMember?.id!);
      this.isConfirmRequestLoanOutOpen = false;
      this.getProjectMember();
    },
    async getProjectMember(): Promise<void> {
      const { data } = await getCurrentMember(this.project.id!);
      this.projectMember = data;
    },
  },
  async mounted() {
    await this.getProjectMember();
    this.useLoanOut = this.displayLoanOutOption() || false;
  },
});
</script>
