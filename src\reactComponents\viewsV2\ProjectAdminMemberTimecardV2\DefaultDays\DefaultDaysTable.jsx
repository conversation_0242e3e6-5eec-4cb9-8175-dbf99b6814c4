import React from 'react';
import PropTypes from 'prop-types';
import _cloneDeep from 'lodash/cloneDeep';
import { Box, styled, Text } from '@/reactComponents/library';

import { StyledWorkDaysGrid } from '@/reactComponents/library/DataGrid/StyledDataGrid';
import AdditionalFields from '@/reactComponents/viewsV2/ProjectAdminMemberTimecardV2/AdditionalFields';
import { useGridApiRef } from '@mui/x-data-grid-pro';
import { observer } from 'mobx-react-lite';
import DefaultDaysStore from './store';
import ActionsCell from './ActionsCell';

import { INITIAL_TC_ERRORS } from '@/reactComponents/viewsV2/ProjectAdminMemberTimecardV2/timecardUtils';

import {
  defaultDaysColumns,
  ADDITIONAL_FIELDS,
  TIMECARD_TABLE_FIELDS,
} from '@/reactComponents/viewsV2/ProjectAdminMemberTimecardV2/timecardTableUtils';

const EmptyTableOverlay = styled('div')(({ theme }) => ({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  height: '100%',
}));

defaultDaysColumns.push({
  field: 'actions',
  headerName: '',
  width: 120,
  renderCell: (params) => <ActionsCell {...params} />,
});

const DefaultDaysTable = (props) => {
  const { showErrors } = props;
  const [additionalFields, setAdditionalFields] = React.useState(
    _cloneDeep(ADDITIONAL_FIELDS),
  );

  const {
    validate: tcErrors,
    loading,
    timecard,
    updateTimecard,
    activeDays,
  } = DefaultDaysStore;

  const displayErrors = showErrors ? tcErrors : INITIAL_TC_ERRORS;

  const apiRef = useGridApiRef();

  React.useEffect(() => {
    if (apiRef.current.state) {
      apiRef.current.state.displayErrors = displayErrors;
    }
  }, [apiRef, displayErrors]);

  const hiddenColumns = React.useMemo(() => {
    const hiddenColumns = additionalFields?.reduce?.((hiddenCols, option) => {
      if (option.selected === false) {
        option.tableFields.forEach((field) => {
          hiddenCols[field] = false;
        });
      } else if (option.selected === true) {
        if (option.label === 'Grace') {
          const meal2Visible = additionalFields.find(
            (f) => f.label === 'Meal 2',
          )?.selected;
          if (!meal2Visible) {
            hiddenCols[TIMECARD_TABLE_FIELDS.meal2Grace] = false;
          }
        }
      }
      return hiddenCols;
    }, {});

    if (!timecard?.holidayDates || timecard?.holidayDates?.length === 0) {
      hiddenColumns['hasHoliday'] = false;
    }

    return hiddenColumns;
  }, [additionalFields, timecard]);

  return (
    <Box>
      <AdditionalFields
        additionalFields={additionalFields}
        setAdditionalFields={setAdditionalFields}
        timecard={timecard}
        updateTimecard={updateTimecard}
      />
      <StyledWorkDaysGrid
        apiRef={apiRef}
        rows={activeDays || []}
        columns={defaultDaysColumns}
        pinnedColumns={{ left: ['date'], right: ['actions'] }}
        columnVisibilityModel={hiddenColumns}
        editMode="row"
        loading={loading}
        slots={{
          noRowsOverlay: () => (
            <EmptyTableOverlay>
              <Text variant="baseSemi">There are no day defaults</Text>
            </EmptyTableOverlay>
          ),
        }}
        disableColumnMenu
        disableSelectionOnClick
        disableColumnSorting
        disableColumnReorder
        hideFooter
        hideFooterSelectedRowCount
        onCellClick={(params, e, actions) => {
          if (params.cellMode === 'view' && params.isEditable) {
            actions.api.startRowEditMode({ id: params.id });
          }
        }}
      />
    </Box>
  );
};

DefaultDaysTable.propTypes = {
  showErrors: PropTypes.bool,
};

export default observer(DefaultDaysTable);
