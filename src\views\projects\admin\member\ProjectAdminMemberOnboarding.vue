<template>
  <section>
    <div class="shadow sm:rounded-lg bg-white dark:bg-gray-900">
      <div
        class="flex justify-between px-4 py-5 sm:px-6 bg-gray-50 dark:bg-gray-700 sm:rounded-t-lg"
      >
        <h2
          id="applicant-information-title"
          class="text-lg font-medium mb-0 pb-0"
        >
          Onboarding
        </h2>
        <Button size="sm" @click="goToEditOnboarding"> Edit </Button>
      </div>
      <div
        class="border-t border-gray-200 dark:border-gray-600 px-4 py-5 sm:px-6"
      >
        <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
          <div
            class="sm:col-span-1"
            v-for="(item, itemIndex) in projectMemberDetails"
            :key="`${item.title}-${itemIndex}`"
          >
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
              {{ item.title }}
            </dt>
            <dd class="mt-1 text-sm">
              {{ item.value }}
            </dd>
          </div>
        </dl>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import Button from '@/components/library/Button.vue';
import type Project from '@/types/Project';
import type ProjectMember from '@/types/ProjectMember';
import type { StartPaperwork } from '@/types/StartPaperwork';
import { defineComponent, type PropType } from 'vue';

export default defineComponent({
  components: { Button },
  props: {
    projectMember: { type: Object as PropType<ProjectMember>, required: true },
    startPaperwork: {
      type: Array as PropType<StartPaperwork[]>,
      required: true,
    },
    project: { type: Object as PropType<Project>, required: true },
    navigate: { type: Function, required: false },
  },
  computed: {
    hasSupervisorSignedStartPaperwork(): boolean {
      return !!this.startPaperwork?.[0]?.supervisorSignedById;
    },
    projectMemberDetails() {
      return [
        { title: 'Rate', value: this.projectMember?.rate },
        { title: 'Rate Type', value: this.projectMember?.rateType?.name },
        { title: 'AICP/Line#', value: this.projectMember?.lineNumber },
        {
          title: 'AICP/Line# - SHOOT',
          value: this.projectMember?.shootLineNumber,
        },
        { title: 'Occupation', value: this.projectMember?.occupation?.name },
        {
          title: 'Department',
          value: this.projectMember?.department?.type?.name || 'None',
        },
        {
          title: 'Start Date',
          value: this.projectMember?.startDate?.toFormat('MM/dd/yyyy'),
        },
      ];
    },
  },
  methods: {
    goToEditOnboarding(): void {
      if (this.navigate) {
        this.navigate({
          pathname: `/projects/${this.project.hashId}/admin/members/${this.projectMember.id}/member/onboarding/edit`,
        });
      }
    },
  },
});
</script>
