<template>
  <span
    class="m-1 inline-flex items-center rounded-full border border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-700 py-1 px-2.5 text-xs font-medium text-gray-900 dark:text-gray-400"
  >
    <span>{{ label }}</span>
    <span>: {{ filterFormattedValue(filter) }}</span>

    <button
      type="button"
      class="ml-1 inline-flex h-4 w-4 flex-shrink-0 rounded-full p-1 text-gray-400 hover:bg-gray-200 hover:text-gray-500"
      @click="emit('remove')"
    >
      <span class="sr-only">Remove filter for {{ label }}</span>
      <svg class="h-2 w-2" stroke="currentColor" fill="none" viewBox="0 0 8 8">
        <path stroke-linecap="round" stroke-width="1.5" d="M1 1l6 6m0-6L1 7" />
      </svg>
    </button>
  </span>
</template>

<script setup lang="ts">
import { FilterUIType, type Filter } from '@/types/Filter';
import { computed, type PropType } from 'vue';

const { filter } = defineProps({
  filter: {
    type: Object as PropType<Filter>,
    required: true,
  },
});

const emit = defineEmits(['remove']);

const filterFormattedValue = (filter: Filter): string => {
  if (filter.type === FilterUIType.MultiSelect) {
    return (
      filter.options
        ?.filter((option) => option.active)
        .map((option) => option.label)
        .join(', ') || ''
    );
  }
  return filter.value?.toString() || '';
};

const label = computed(() => filter.label);
</script>
