import { create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Router<PERSON>rovider, Outlet } from 'react-router-dom';
import { SnackbarProvider } from 'notistack';
import PropTypes from 'prop-types';

import CompanyFormView from '@/reactComponents/views/companies/CompanyFormView';

// ----- Selectors -----
const nameInput = '[data-testid="Name-input"]';
const phoneInput = '[data-testid="Phone-input"]';
const castCrewIdInput = '[data-testid="Cast and Crew Id-input"]';
const generateStartToggle =
  '[data-testid="company-generate-start-toggle"] [role="switch"]';
const generateWtpaToggle =
  '[data-testid="company-generate-wtpa-toggle"] [role="switch"]';
const streetAddressInput = '[data-testid="Street Address-input"]';
const streetAddress2Input = '[data-testid="Street Address Line 2-input"]';
const cityInput = '[data-testid="City-input"]';
const stateDropdown = '[data-testid="company-state-dropdown"]';
const stateDropdownBtn = '[data-testid="company-state-dropdown"] button';
const zipInput = '[data-testid="ZIP / Postal code-input"]';
const taxClassificationDropdown =
  '[data-testid="company-taxClassification-dropdown"]';
const payFrequencyDropdown = '[data-testid="company-payFrequency-dropdown"]';
const payDaysDropdown = '[data-testid="company-payDays-dropdown"]';
const createUpdateBtn = '[data-testid="company-create-update-btn"]';
const cancelBtn = '[data-testid="company-cancel-button"]';
const addAddressIcon = '[data-testid="add-address-icon"]';
const removeAddressIcon = '[data-testid="remove-address-icon"]';
const customPaydayText = '[data-testid="company-custom-payday-text"]';
const dashboardCompanies = '[data-testid="dashboard-companies"]';

const mockCompany = {
  id: 123,
  name: 'Test Company',
};

const LayoutWithOutletContext = ({ context }) => (
  <SnackbarProvider>
    <Outlet context={context} />
  </SnackbarProvider>
);

LayoutWithOutletContext.propTypes = {
  context: PropTypes.object.isRequired,
};

// TestWrapper to simulate React Router context
const TestWrapper = () => {
  const router = createMemoryRouter(
    [
      {
        path: '/',
        handle: { editMode: false },
        element: (
          <LayoutWithOutletContext
            context={{ editMode: false, company: mockCompany }}
          />
        ),
        children: [
          {
            path: '/',
            element: <CompanyFormView />,
            handle: { editMode: false },
          },
        ],
      },
      {
        path: '/companies',
        element: (
          <div data-testid="dashboard-companies">dashboard-companies</div>
        ),
      },
    ],
    { initialEntries: ['/'] },
  );

  return <RouterProvider router={router} />;
};

TestWrapper.propTypes = {
  company: PropTypes.object.isRequired,
};

describe('<CompanyFormView component testing/>', () => {
  beforeEach(() => {
    cy.fixture('/componentPayloads/taxClassification').then(
      (taxClassification) => {
        cy.intercept('GET', '**/v2/api/core/tax-classifications/', {
          statusCode: 200,
          body: taxClassification,
        }).as('taxClassification');
      },
    );

    cy.fixture('/componentPayloads/payDay').then((payDay) => {
      cy.intercept('GET', '**/v2/api/core/payday', {
        statusCode: 200,
        body: payDay,
      }).as('payDay');
    });

    cy.fixture('/componentPayloads/payDayFrequency').then((payDayFrequency) => {
      cy.intercept('GET', '**/v2/api/core/payday/frequencies', {
        statusCode: 200,
        body: payDayFrequency,
      }).as('payDayFrequency');
    });

    cy.fixture('/componentPayloads/addressesStates').then((addressesStates) => {
      cy.intercept('GET', '**/v2/api/core/addresses/states', {
        statusCode: 200,
        body: addressesStates,
      }).as('addressesStates');
    });
    cy.mount(<TestWrapper />);
  });

  it('should present the Company Form component with correct initial state', () => {
    // ----------- Main Labels and Fields -----------
    cy.contains('Company').should('be.visible');
    cy.contains('This form is used to create a new production company.').should(
      'be.visible',
    );
    cy.contains('Name').should('be.visible');
    cy.get(nameInput).should('be.visible').and('be.enabled');
    cy.contains('Phone').should('be.visible');
    cy.get(phoneInput).should('be.visible').and('be.enabled');
    cy.contains('Cast and Crew Id').should('be.visible');
    cy.get(castCrewIdInput).should('be.visible').and('be.enabled');

    // ----------- Toggles -----------
    cy.contains('Generate Start Form').should('be.visible');
    cy.contains('Generate WTPA').should('be.visible');
    cy.get(generateStartToggle)
      .should('exist')
      .and('have.attr', 'aria-checked', 'true');
    cy.get(generateWtpaToggle)
      .should('exist')
      .and('have.attr', 'aria-checked', 'true');

    // ----------- Addresses Section -----------
    cy.contains('Addresses').should('be.visible');
    cy.contains('Street Address').should('be.visible');
    cy.get(streetAddressInput).should('be.visible').and('be.enabled');
    cy.contains('Street Address Line 2').should('be.visible');
    cy.get(streetAddress2Input).should('be.visible').and('be.enabled');
    cy.contains('City').should('be.visible');
    cy.get(cityInput).should('be.visible').and('be.enabled');
    cy.contains('State').should('be.visible');
    cy.get(stateDropdown).should('exist');
    cy.get(stateDropdownBtn)
      .should('exist')
      .and('be.enabled')
      .and('have.attr', 'aria-haspopup', 'menu');
    cy.contains('ZIP / Postal code').should('be.visible');
    cy.get(zipInput).should('be.visible').and('be.enabled');
    cy.get(addAddressIcon).should('be.visible').click();

    // ----------- Tax Classification /Payments Section -----------
    cy.contains('Tax Classification').should('be.visible');
    cy.get(taxClassificationDropdown).should('be.visible').click();
    cy.get(taxClassificationDropdown)
      .contains('[role="menuitem"]', 'C Corporation')
      .should('exist');
    cy.get(taxClassificationDropdown)
      .contains('[role="menuitem"]', 'S Corporation')
      .should('exist')
      .click();

    // ----------- Pay Frequency Dropdown -----------
    cy.contains('Pay Day Frequency').should('be.visible');
    cy.get(payFrequencyDropdown).should('be.visible').click();
    cy.get(payFrequencyDropdown)
      .contains('[role="menuitem"]', 'Other')
      .should('exist');
    cy.get(payFrequencyDropdown)
      .contains('[role="menuitem"]', 'Weekly')
      .should('exist');
    cy.get(payFrequencyDropdown)
      .contains('[role="menuitem"]', 'Biweekly')
      .should('exist')
      .click();

    // ----------- Pay Day Dropdown -----------
    cy.contains('Pay Day').should('be.visible');
    cy.get(payDaysDropdown).should('be.visible').click();
    cy.get(payDaysDropdown)
      .contains('[role="menuitem"]', 'Monday')
      .should('exist');
    cy.get(payDaysDropdown)
      .contains('[role="menuitem"]', 'Tuesday')
      .should('exist');
    cy.get(payDaysDropdown)
      .contains('[role="menuitem"]', 'Wednesday')
      .should('exist');
    cy.get(payDaysDropdown)
      .contains('[role="menuitem"]', 'Thursday')
      .should('exist');
    cy.get(payDaysDropdown)
      .contains('[role="menuitem"]', 'Friday')
      .should('exist');
    cy.get(payDaysDropdown)
      .contains('[role="menuitem"]', 'Saturday')
      .should('exist');
    cy.get(payDaysDropdown)
      .contains('[role="menuitem"]', 'Sunday')
      .should('exist');

    // ----------- Action Buttons -----------
    cy.get(createUpdateBtn).should('be.visible').and('not.be.disabled');
    cy.get(cancelBtn).should('be.visible').and('not.be.disabled');
  });

  it('should toggle the Generate Start Form and Generate WTPA switches', () => {
    cy.get(generateStartToggle).click();
    cy.get(generateStartToggle).should('have.attr', 'aria-checked', 'false');
    cy.get(generateWtpaToggle).click();
    cy.get(generateWtpaToggle).should('have.attr', 'aria-checked', 'false');
    cy.get(generateStartToggle).click();
    cy.get(generateStartToggle).should('have.attr', 'aria-checked', 'true');
    cy.get(generateWtpaToggle).click();
    cy.get(generateWtpaToggle).should('have.attr', 'aria-checked', 'true');
  });

  it('should allow to add and delete a Street Address Line 2', () => {
    cy.get(addAddressIcon).should('be.visible').click();
    cy.get(streetAddressInput)
      .should('be.visible')
      .eq(1)
      .type('second address');
    cy.get(streetAddress2Input)
      .should('be.visible')
      .eq(1)
      .type('second address line 2');
    cy.get(cityInput).should('be.visible').eq(1).type('second city');
    cy.get(stateDropdownBtn).should('exist').eq(1).click();
    cy.get('[role="menuitem"]').first().should('be.visible').click();
    cy.get(zipInput).should('be.visible').eq(1).type('12345');
    cy.get(removeAddressIcon).should('be.visible').click();
    cy.get(removeAddressIcon).should('not.exist');
  });

  it('should display custom pay Day Option after selecting pay Day frequency other', () => {
    cy.get(payFrequencyDropdown).should('be.visible').click();
    cy.get(payFrequencyDropdown)
      .contains('[role="menuitem"]', 'Other')
      .should('exist')
      .click();
    cy.get(customPaydayText).should('be.visible').type('Every 2 weeks');
    cy.get(createUpdateBtn).should('be.visible').click();
  });

  it('should display custom pay Day Option after selecting pay Day frequency other', () => {
    cy.get(payFrequencyDropdown).should('be.visible').click();
    cy.get(payFrequencyDropdown)
      .contains('[role="menuitem"]', 'Other')
      .should('exist')
      .click();
    cy.get(customPaydayText).should('be.visible').type('Every 2 weeks');
    cy.get(createUpdateBtn).should('be.visible').click();
  });

  it('should select the cancel option', () => {
    cy.get(cancelBtn).should('be.visible').click();
    cy.get(dashboardCompanies).should('exist');
  });
});
