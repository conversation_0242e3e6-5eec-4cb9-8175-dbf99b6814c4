import axios, { type AxiosResponse } from 'axios';
import { coreBaseUrl } from '../utils/constants';

export const downloadBatchPdfs = async (
  batchId: string | number,
  scope: string,
  includeKitRental: boolean,
  includeMileageForm: boolean,
  includeReimbursementForms: boolean,
  exportOrder: string,
): Promise<AxiosResponse> => {
  const url = `${coreBaseUrl()}/batches/${batchId}/pdf?scope=${scope}&format=pdf&includeKitRental=${includeKitRental}&includeMileageForm=${includeMileageForm}&includeReimbursementForms=${includeReimbursementForms}&exportOrder=${exportOrder}`;
  return await axios({
    url,
    method: 'GET',
    responseType: 'json',
  });
};

export const downloadBatchZip = async (
  batchId: string | number,
  scope: string,
  includeKitRental: boolean,
  includeMileageForm: boolean,
  includeReimbursementForms: boolean,
): Promise<AxiosResponse> => {
  const url = `${coreBaseUrl()}/batches/${batchId}/pdf?scope=${scope}&format=zip&includeKitRental=${includeKitRental}&includeMileageForm=${includeMileageForm}&includeReimbursementForms=${includeReimbursementForms}`;
  return await axios({
    url,
    method: 'GET',
    responseType: 'json',
  });
};

export const updateBatch = async (
  batchId: string | number,
  data: any,
): Promise<AxiosResponse> => {
  return await axios({
    url: `${coreBaseUrl()}/batches/${batchId}`,
    method: 'PATCH',
    data,
  });
};

export const deleteBatch = async (
  batchId: string | number,
): Promise<AxiosResponse> => {
  return await axios({
    url: `${coreBaseUrl()}/batches/${batchId}`,
    method: 'DELETE',
  });
};

export const submitBatch = async (
  batchId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/batches/${batchId}/submit`;
  const response = await axios.patch(url, {}, { withCredentials: true });
  return response;
};

export const openBatch = async (
  batchId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/batches/${batchId}/open`;
  const response = await axios.patch(url, {}, { withCredentials: true });
  return response;
};

export const getBatchDetails = async (
  batchId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/batches/${batchId}/batch-details`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const timecardReport = async (
  batchId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/batches/${batchId}/timecard-report`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const pointZeroReport = async (
  batchId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/batches/${batchId}/point-zero-report`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const blobDownload = async (url: string): Promise<Blob> => {
  const response = await axios.get(url, {
    timeout: 600000, // 10 minutes
    responseType: 'blob',
  });

  const blob = new Blob([response.data], {
    type: response.headers['content-type'],
  });
  return blob;
};
