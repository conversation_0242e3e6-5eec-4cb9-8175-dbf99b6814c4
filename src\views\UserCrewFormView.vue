<template>
  <div class="relative flex min-h-screen flex-col overflow-hidden mx-5 pt-16">
    <div class="mx-auto mb-5 pt-6">
      <h2 class="text-center text-3xl font-extrabold leading-9">
        Personal Information
      </h2>
      <div>
        <DatePicker
          class="md:basis-2/5 md:mr-2"
          label="Date of Birth"
          v-model="userCrew.dateOfBirth"
          type="birthday"
          initial-view="year"
          :min="'1900-01-01'"
          :max="'2100-01-01'"
        />
        <Dropdown
          class="grow"
          label="Ethnicity"
          v-model="userCrew.ethnicity"
          :menu-items="ethnicities"
          display-name="name"
        />
        <Dropdown
          class="grow"
          label="Gender"
          v-model="userCrew.gender"
          :menu-items="genders"
          display-name="name"
        />
        <TextInput
          label="Social Security Number"
          :model-value="userCrew.socialSecurityNumber"
          @update:rawValue="userCrew.socialSecurityNumber = $event"
          type="password"
          maxlength="9"
        />
        <TextInput
          label="Social Security Number (Confirm)"
          :model-value="userCrew.confirmSocialSecurityNumber"
          @update:rawValue="userCrew.confirmSocialSecurityNumber = $event"
          type="password"
          maxlength="9"
        />
        <h3 class="text-xl font-bold">Address</h3>
        <TextInput
          label="Street 1"
          v-model="userCrew.address.street"
          type="street"
        />
        <TextInput
          label="Street 2"
          v-model="userCrew.address.street2"
          type="street2"
        />
        <div class="flex items-center">
          <TextInput
            label="City"
            class="basis-1/2"
            v-model="userCrew.address.city"
            type="city"
          />
          <Dropdown
            label="State"
            class="mx-2 basis-1/4"
            v-model="userCrew.address.state"
            :menu-items="states"
            type="state"
            display-name="name"
          />
          <TextInput
            label="Zip Code"
            class="basis-1/4"
            v-model="userCrew.address.zip"
            type="postal"
          />
        </div>
        <h2 class="text-lg font-extrabold leading-9">W-4 Info</h2>
        <Dropdown
          v-model="userCrew.maritalStatus"
          :menu-items="maritalStatuses"
          label="Marital Status"
          display-name="name"
        />
        <div class="sm:flex sm:space-x-2 mb-3">
          <Checkbox
            label="Multiple jobs or spouse works"
            v-model="userCrew.multipleJobsOrSpouseWorks"
          >
          </Checkbox>
        </div>
        <div class="sm:flex sm:space-x-2">
          <!-- <TextInput
            v-if="!userCrew.exemptFromWithholdingsBasedOnDependents"
            label="Claim Dependents or Other Credits"
            v-model="userCrew.withholdingsBasedOnDependents"
            type="number"
          />
          <TextInput
            v-if="!userCrew.exemptFromWithholdingsBasedOnDependents"
            label="Adjustments - Other income"
            v-model="userCrew.withholdingsFromOtherIncome"
            type="number"
          />
          <TextInput
            v-if="!userCrew.exemptFromWithholdingsBasedOnDependents"
            label="Adjustments - Deductions"
            v-model="userCrew.withholdingsFromDeductions"
            type="number"
          />
          <TextInput
            v-if="!userCrew.exemptFromWithholdingsBasedOnDependents"
            label="Adjustments - Extra withholding"
            v-model="userCrew.extraWithholdings"
            type="number"
          /> -->
          <TextInput
            v-if="!userCrew.exemptFromWithholdingsBasedOnDependents"
            label="Claim Dependents or Other Credits"
            v-model="formattedWithholdingsBasedOnDependents"
            type="text"
          />

          <TextInput
            v-if="!userCrew.exemptFromWithholdingsBasedOnDependents"
            label="Adjustments - Other income"
            v-model="formattedWithholdingsFromOtherIncome"
            type="text"
          />

          <TextInput
            v-if="!userCrew.exemptFromWithholdingsBasedOnDependents"
            label="Adjustments - Deductions"
            v-model="formattedWithholdingsFromDeductions"
            type="text"
          />

          <TextInput
            v-if="!userCrew.exemptFromWithholdingsBasedOnDependents"
            label="Adjustments - Extra withholding"
            v-model="formattedExtraWithholdings"
            type="text"
          />

          <Toggle
            class="ml-2"
            v-model="userCrew.exemptFromWithholdingsBasedOnDependents"
            @update:modelValue="updateW4Fields($event)"
          >
            Exempt from Withholdings
          </Toggle>
        </div>
      </div>
      <div>
        <h2 class="text-lg font-extrabold leading-9">I-9 Info</h2>
        <Dropdown
          class="grow"
          label="Citizenship Status"
          v-model="userCrew.citizenshipStatus"
          display-name="name"
          :menu-items="citizenshipStatuses"
        />
        <div
          v-if="
            ['permanent_resident', 'alien_authorized_to_work'].includes(
              userCrew.citizenshipStatus.key,
            )
          "
        >
          <Dropdown
            v-model="userCrew.workAuthorizationType"
            :menu-items="
              userCrew.citizenshipStatus.key === 'permanent_resident'
                ? permanentResidentWorkAuthorizationOptions
                : alienWorkAuthorizationOptions
            "
            label="Work Authorization Type"
            display-name="name"
          />
        </div>
        <TextInput
          v-if="userCrew.workAuthorizationType"
          v-model="userCrew.workAuthorizationNumber"
          :label="userCrew.workAuthorizationType?.name"
        />
        <div
          v-if="workAuthorizationMethod?.value === 'foreign_passport_number'"
        >
          <TextInput
            label="Foreign Passport Issuing Country"
            v-model="userCrew.foreignPassportIssuingCountry"
          />
        </div>
        <Toggle
          class="m-2"
          v-if="userCrew.citizenshipStatus.key === 'alien_authorized_to_work'"
          v-model="hasWorkAuthorizationExpiration"
        >
          Does your work authorization expire?
        </Toggle>
        <DatePicker
          v-if="
            hasWorkAuthorizationExpiration &&
            userCrew?.workAuthorizationExpirationDate
          "
          label="Work Authorization Expiration Date"
          v-model="userCrew.workAuthorizationExpirationDate"
        />
        <div>
          <h3 class="text-xl font-bold">Proof of Identity</h3>
          <p class="mb-2 text-gray-700 dark:text-gray-400">
            This section is for getting document information that proves your
            identity. It will be primarily used to fill out I-9's. Fill this out
            as you would the document section of an I-9.
          </p>
          <div class="flex justify-start my-1 space-x-2">
            <Button size="sm" color="secondary" @click="passportPrefill()">
              <div class="font-semibold">Passport Prefill</div>
            </Button>
            <Button
              size="sm"
              color="secondary"
              @click="driversLicensePrefill()"
            >
              <div class="font-semibold">Drivers License Prefill</div>
            </Button>
          </div>
          <Dropdown
            label="Proof of Identity Type"
            class="basis-1/4"
            v-model="userCrew.proofOfIdentity.type"
            :menu-items="proofOfIdentityTypes"
            type="state"
            display-name="name"
          />
          <TextInput
            label="Document Title"
            class="basis-1/2"
            v-model="userCrew.proofOfIdentity.documentTitle"
            type="city"
          />
          <TextInput
            label="Document Number"
            class="basis-1/2"
            v-model="userCrew.proofOfIdentity.documentNumber"
            type="city"
          />
          <TextInput
            label="Issuing Authority"
            class="basis-1/2"
            v-model="userCrew.proofOfIdentity.issuingAuthority"
            type="city"
          />
          <DatePicker
            label="Expires On"
            v-model="userCrew.proofOfIdentity.expiresAt"
            initial-view="year"
          />
        </div>
        <!-- <div class="pt-8">
          <div>
            <h3
              class="text-lg font-medium leading-6 text-gray-900 dark:text-gray-400"
            >
              Unions
            </h3>
            <p>
              Add all unions you are a part of.
            </p>
          </div>
          <div class="flex flex-wrap items-center space-x-2 py-3">
            <div
              v-for="(union, unionIndex) in userCrew.unions"
              :key="`union-${unionIndex}`"
              @click="removeUnion(union)"
              class="rounded-full bg-green-100 hover:bg-green-200 px-2 text-xs font-semibold leading-5 text-green-800 cursor-pointer"
            >
              <div class="flex">
                {{ union.name }}
                <XMarkIcon
                  class="w-3 ml-1 cursor-pointer hover:dark:text-gray-100"
                />
              </div>
            </div>
          </div>

          <div class="flex justify-center">
            <Button @click="addUnionModal = true" color="gray" size="sm">
              <template #icon>
                <PlusIcon
                  class="w-5 mr-1 cursor-pointer hover:dark:text-gray-100"
                />
              </template>
              Add Union
            </Button>
            <Modal v-model="addUnionModal">
              <div class="mx-4 mb-32">
                <div class="flex justify-between">
                  <h2>Add Union</h2>
                  <XMarkIcon @click="addUnionModal = false" class="w-6" />
                </div>
                <Dropdown
                  label="Union"
                  v-model="selectedUnion"
                  :menu-items="unions"
                  display-name="name"
                />
                <div class="flex justify-center h-32">
                  <Button @click="addUnion"> Submit </Button>
                </div>
              </div>
            </Modal>
          </div>
        </div> -->
        <div class="flex justify-center">
          <Button class="mt-4" @click="save" color="primary" :loading="loading">
            Save
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Button from '@/components/library/Button.vue';
import Checkbox from '@/components/library/Checkbox.vue';
import DatePicker from '@/components/library/DatePicker.vue';
import Dropdown from '@/components/library/Dropdown.vue';
import TextInput from '@/components/library/TextInput.vue';
import Toggle from '@/components/library/Toggle.vue';
import { getStates } from '@/services/address';
import { getCitizenshipStatuses } from '@/services/citzenship-statuses';
import { getEthnicities } from '@/services/ethnicities';
import { listGenders } from '@/services/gender';
import { getMaritalStatuses } from '@/services/marital-statuses';
import { getProofOfIdentityTypes } from '@/services/proof-of-identity-types';
import { getUnions } from '@/services/unions';
import {
  createUserCrew,
  getUserCrew,
  updateUserCrew,
} from '@/services/user-crew';
import SnackbarStore from '@/reactComponents/stores/snackbar';

import type CreateUserCrew from '@/types/CreateUserCrew';
import type { Pagination } from '@/types/Pagination';
import type { SnackType } from '@/types/Snackbar';
import type State from '@/types/State';
import axios from 'axios';
import { DateTime } from 'luxon';
import { defineComponent } from 'vue';

export default defineComponent({
  setup() {
    return { SnackbarStore };
  },
  props: {
    navigate: {
      type: Function,
      default: null,
    },
    editMode: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    Button,
    TextInput,
    Checkbox,
    Dropdown,
    DatePicker,
    Toggle,
  },
  data() {
    return {
      addUnionModal: false,
      citizenshipStatuses: [],
      maritalStatuses: [] as any[],
      ethnicities: [],
      genders: [] as any[],
      proofOfIdentityTypes: [],
      state: {} as State,
      states: [] as State[],
      unions: [] as any[],
      selectedUnion: {} as any,
      workAuthorizationMethod: null as any,
      hasWorkAuthorizationExpiration: false,
      permanentResidentWorkAuthorizationOptions: [
        {
          id: 1,
          name: 'Alien Registration Number',
          key: 'alien_registration_number',
        },
        {
          id: 2,
          name: 'USCIS Number',
          key: 'uscis_number',
        },
      ],
      alienWorkAuthorizationOptions: [
        {
          id: 1,
          name: 'Alien Registration Number',
          key: 'alien_registration_number',
        },
        {
          id: 2,
          name: 'USCIS Number',
          key: 'uscis_number',
        },
        {
          id: 3,
          name: 'I-94 Number',
          key: 'i94_number',
        },
        {
          id: 4,
          name: 'Foreign Passport Number',
          key: 'foreign_passport_number',
        },
      ],
      proofOfIdentityPrefill: {
        name: 'Other',
        key: 'other',
      },
      proofOfIdentityPrefills: [
        {
          name: 'Passport',
          key: 'passport',
        },
        {
          name: 'Drivers License',
          key: 'drivers_license',
        },
        {
          name: 'Other',
          key: 'other',
        },
      ],
      userCrew: {
        address: {
          street: '',
          street2: '',
          city: '',
          state: {},
          zip: '',
        },
        socialSecurityNumber: '',
        confirmSocialSecurityNumber: '',
        dateOfBirth: DateTime.now(),
        maritalStatus: {},
        citizenshipStatus: {},
        proofOfIdentity: {
          type: null,
          documentTitle: '',
          documentNumber: '',
          expiresAt: DateTime.now(),
        },
        ethnicity: {},
        gender: {},
        unions: [] as any[],
        workAuthorizationExpirationDate: DateTime.now(),
      } as CreateUserCrew,
      loading: false,
      unionSearch: '' as string,
      paginationUnion: {
        page: 1,
        limit: 100,
        total: 0,
      } as Pagination,
    };
  },
  computed: {
    formattedWithholdingsBasedOnDependents: {
      get() {
        return this.formatCurrency(this.userCrew.withholdingsBasedOnDependents);
      },
      set(value: any) {
        this.userCrew.withholdingsBasedOnDependents =
          this.validateW4Fields(value);
      },
    },
    formattedWithholdingsFromOtherIncome: {
      get() {
        return this.formatCurrency(this.userCrew.withholdingsFromOtherIncome);
      },
      set(value: any) {
        this.userCrew.withholdingsFromOtherIncome =
          this.validateW4Fields(value);
      },
    },
    formattedWithholdingsFromDeductions: {
      get() {
        return this.formatCurrency(this.userCrew.withholdingsFromDeductions);
      },
      set(value: any) {
        this.userCrew.withholdingsFromDeductions = this.validateW4Fields(value);
      },
    },
    formattedExtraWithholdings: {
      get() {
        return this.formatCurrency(this.userCrew.extraWithholdings);
      },
      set(value: any) {
        this.userCrew.extraWithholdings = this.validateW4Fields(value);
      },
    },
  },
  methods: {
    triggerSnackbar(message: string, duration: number, type: SnackType) {
      this.SnackbarStore.triggerSnackbar(message, duration, type);
    },
    async save() {
      if (this.loading) return;
      this.loading = true;
      if (this.editMode) {
        try {
          const payload: CreateUserCrew = JSON.parse(
            JSON.stringify(this.userCrew),
          ) as CreateUserCrew;
          if (payload.withholdingsBasedOnDependents) {
            payload.withholdingsBasedOnDependents = parseInt(
              (payload.withholdingsBasedOnDependents * 100).toFixed(0),
            );
          }
          if (payload.withholdingsFromOtherIncome) {
            payload.withholdingsFromOtherIncome = parseInt(
              (payload.withholdingsFromOtherIncome * 100).toFixed(0),
            );
          }
          if (payload.withholdingsFromDeductions) {
            payload.withholdingsFromDeductions = parseInt(
              (payload.withholdingsFromDeductions * 100).toFixed(0),
            );
          }
          if (payload.extraWithholdings) {
            payload.extraWithholdings = parseInt(
              (payload.extraWithholdings * 100).toFixed(0),
            );
          }
          payload.workAuthorizationExpirationDate = this
            .hasWorkAuthorizationExpiration
            ? payload.workAuthorizationExpirationDate
            : undefined;
          payload.withholdingsBasedOnDependents =
            !payload.exemptFromWithholdingsBasedOnDependents
              ? payload.withholdingsBasedOnDependents
              : undefined;
          await updateUserCrew(payload);
          this.triggerSnackbar('Profile Updated', 2500, 'success');
          this.navigate('/profile');
        } catch (err) {
          if (axios.isAxiosError(err)) {
            const msg = err.response?.data?.['errors']?.[0]?.message;
            this.triggerSnackbar(msg, 2500, 'error');
          } else {
            this.triggerSnackbar(err as string, 2500, 'error');
          }
        }
      } else {
        try {
          const payload: CreateUserCrew = JSON.parse(
            JSON.stringify(this.userCrew),
          ) as CreateUserCrew;
          if (payload.withholdingsBasedOnDependents) {
            payload.withholdingsBasedOnDependents = parseInt(
              (payload.withholdingsBasedOnDependents * 100).toFixed(0),
            );
          }
          if (payload.withholdingsFromOtherIncome) {
            payload.withholdingsFromOtherIncome = parseInt(
              (payload.withholdingsFromOtherIncome * 100).toFixed(0),
            );
          }
          if (payload.withholdingsFromDeductions) {
            payload.withholdingsFromDeductions = parseInt(
              (payload.withholdingsFromDeductions * 100).toFixed(0),
            );
          }
          if (payload.extraWithholdings) {
            payload.extraWithholdings = parseInt(
              (payload.extraWithholdings * 100).toFixed(0),
            );
          }
          payload.workAuthorizationExpirationDate = this
            .hasWorkAuthorizationExpiration
            ? payload.workAuthorizationExpirationDate
            : undefined;
          payload.withholdingsBasedOnDependents =
            !payload.exemptFromWithholdingsBasedOnDependents
              ? payload.withholdingsBasedOnDependents
              : undefined;
          await createUserCrew(payload);
          this.triggerSnackbar('User Crew Created', 2500, 'success');
          this.navigate('/profile');
        } catch (err) {
          if (axios.isAxiosError(err)) {
            const msg = err.response?.data?.['errors']?.[0]?.message;
            this.triggerSnackbar(msg, 2500, 'error');
          } else {
            this.triggerSnackbar(err as string, 2500, 'error');
          }
        }
      }
      this.loading = false;
    },
    async addUnion() {
      if (!this.selectedUnion.id) {
        return;
      }
      this.userCrew.unions.push(this.selectedUnion);
      this.addUnionModal = false;
      this.selectedUnion = {};
      await this.updateUnionList();
    },
    async removeUnion(union: any) {
      this.userCrew.unions.splice(this.userCrew.unions.indexOf(union), 1);
      await this.updateUnionList();
    },
    async updateUnionList() {
      const {
        data: { data: unions },
      } = await getUnions(this.unionSearch, this.paginationUnion);
      this.unions = unions.filter((union: any) => {
        return !this.userCrew.unions.find(
          (userUnion: any) => userUnion.id === union.id,
        );
      });
    },
    passportPrefill() {
      this.userCrew.proofOfIdentity.type = this.proofOfIdentityTypes.find(
        (type: any) => type.key === 'identity_and_employment_authorization',
      );
      this.userCrew.proofOfIdentity.documentTitle = 'Passport';
      this.userCrew.proofOfIdentity.issuingAuthority = 'U.S. Dept. of State';
      this.userCrew.proofOfIdentity.documentNumber = null;
      this.userCrew.proofOfIdentity.issuingAuthority = null;
      this.userCrew.proofOfIdentity.expiresAt = DateTime.now();
    },
    driversLicensePrefill() {
      this.userCrew.proofOfIdentity.type = this.proofOfIdentityTypes.find(
        (type: any) => type.key === 'identity',
      );
      this.userCrew.proofOfIdentity.documentTitle = 'Drivers License';
      this.userCrew.proofOfIdentity.documentNumber = null;
      this.userCrew.proofOfIdentity.issuingAuthority = null;
      this.userCrew.proofOfIdentity.expiresAt = DateTime.now();
    },
    formatCurrency(value: any) {
      return value !== null && value !== undefined ? `$${value}` : '';
    },
    validateW4Fields(value: any) {
      const numericValue = value.replace(/[^0-9.]/g, '');
      return numericValue ? Number(numericValue) : null;
    },
    updateW4Fields(value: boolean) {
      if (value) {
        this.userCrew.withholdingsBasedOnDependents = null;
        this.userCrew.withholdingsFromOtherIncome = null;
        this.userCrew.withholdingsFromDeductions = null;
        this.userCrew.extraWithholdings = null;
      }
    },
  },
  async mounted() {
    if (this.editMode) {
      const { data: userCrew } = await getUserCrew();
      this.userCrew = userCrew;
      if (this.userCrew.withholdingsBasedOnDependents)
        this.userCrew.withholdingsBasedOnDependents /= 100;
      if (this.userCrew.withholdingsFromOtherIncome)
        this.userCrew.withholdingsFromOtherIncome /= 100;
      if (this.userCrew.extraWithholdings)
        this.userCrew.extraWithholdings /= 100;
      if (this.userCrew.withholdingsFromDeductions)
        this.userCrew.withholdingsFromDeductions /= 100;
      this.userCrew.confirmSocialSecurityNumber =
        this.userCrew.socialSecurityNumber;
      this.hasWorkAuthorizationExpiration =
        !!this.userCrew.workAuthorizationExpirationDate;
      this.userCrew.workAuthorizationExpirationDate =
        this.userCrew.workAuthorizationExpirationDate || DateTime.now();
    }
    const { data: ethnicitiesResponse } = await getEthnicities();
    const { data: citizenshipStatusesResponse } =
      await getCitizenshipStatuses();
    const {
      data: { data: proofOfIdentityTypes },
    } = await getProofOfIdentityTypes();
    const { data: maritalStatuses } = await getMaritalStatuses();
    this.maritalStatuses = maritalStatuses;
    this.ethnicities = ethnicitiesResponse;
    this.citizenshipStatuses = citizenshipStatusesResponse;
    this.proofOfIdentityTypes = proofOfIdentityTypes;
    const {
      data: { data: states },
    } = await getStates();
    this.states = states;
    const {
      data: { data: genders },
    } = await listGenders();
    this.genders = genders;
    await this.updateUnionList();
  },
});
</script>

<style></style>
