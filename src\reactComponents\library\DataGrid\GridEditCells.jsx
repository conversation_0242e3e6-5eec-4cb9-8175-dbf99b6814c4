import React, { useCallback, useEffect, useRef, useMemo } from 'react';
import { Checkbox } from '@mui/material';
import { TextFieldRaw as TextField } from '../../library';
import { fixDecimalTo } from '@/reactComponents/utils/stringNum';
import KeyboardControlBox from './CellKeyboardControl';

const CheckboxCell = (params) => {
  const field = params.colDef.field;
  const isHidden = params.colDef?.isHidden;
  const isEditable = params.isEditable;
  const api = params.api;
  const update = params.update;
  const rowId = params.row.id;
  const elmRef = useRef();

  useEffect(() => {
    if (params.hasFocus) {
      elmRef?.current?.focus();
    }
  }, [params.hasFocus]);

  const hidden = useMemo(() => {
    if (isHidden) {
      return isHidden(params.row);
    }
    return !isEditable;
  }, [params.row, isHidden, isEditable]);

  return (
    <KeyboardControlBox
      sx={{ marginLeft: 'auto', marginRight: 'auto' }}
      api={api}
      rowId={rowId}
      colDef={params.colDef}
      ref={hidden ? elmRef : null}
    >
      {!hidden && (
        <Checkbox
          inputRef={elmRef}
          tabIndex={0}
          checked={!!params.value}
          onChange={(e) => {
            e.stopPropagation();
            const isWorkDaysGrid = !!api;
            update({
              field: field,
              value: e.target.checked,
              rowId: rowId,
              isWorkDaysGrid: isWorkDaysGrid,
              api: api,
            });
            api.setEditCellValue({
              id: rowId,
              field,
              value: e.target.checked,
            });
          }}
        />
      )}
    </KeyboardControlBox>
  );
};
export const CheckboxEditCell = (params) => <CheckboxCell {...params} />;

const InputCell = (params) => {
  const { update, api, colDef, row, value } = params;
  const elmRef = useRef();

  const { field, cellType = 'text', decimals, isNum, isHidden } = colDef;

  const [localValue, setLocalValue] = React.useState(value);
  React.useEffect(() => {
    setLocalValue(value);
  }, [value]);

  useEffect(() => {
    if (params.hasFocus) {
      elmRef?.current?.focus();
      elmRef?.current?.select();
    }
  }, [params.hasFocus]);

  const hidden = useMemo(() => {
    if (isHidden) {
      return isHidden(row);
    }
    return false;
  }, [row, isHidden]);

  const onChange = (e) => {
    let val = e.target.value;
    if (cellType === 'number' && decimals) {
      val = fixDecimalTo(val, decimals);
    }
    setLocalValue(val);
  };

  const handleUpdate = useCallback(
    (newVal) => {
      const isWorkDaysGrid = !!api;
      let newValue = newVal;
      update({
        field: field,
        value: newValue,
        rowId: row.id,
        parentRowId: row.parentRowId,
        isWorkDaysGrid: isWorkDaysGrid,
        api: api,
      });
      const isReRateCell = 'parentRowId' in row;
      if (!isReRateCell) {
        api.setEditCellValue({
          id: row.id,
          field,
          value: newValue,
        });
      }
    },
    [field, row, api, update],
  );

  const onBlur = useCallback(
    (e) => {
      let newVal = localValue;
      if (isNum) {
        //TODO how does DataGrid handle number input?
        newVal = Number(newVal) || 0;
      }

      if (newVal === value) {
        //nothing to update, value is the same
        return;
      }
      handleUpdate(newVal);
    },
    [localValue, value, isNum, handleUpdate],
  );

  return (
    <KeyboardControlBox
      sx={{ display: 'flex', padding: '0px 4px' }}
      api={api}
      rowId={row.id}
      colDef={colDef}
    >
      {!hidden && (
        <TextField
          inputRef={elmRef}
          type={cellType}
          inputProps={{
            style: {
              padding: '4px',
              fontSize: '14px',
            },
            autoComplete: 'off',
          }}
          sx={{ justifyContent: 'center' }}
          value={localValue ?? ''}
          onChange={onChange}
          onBlur={onBlur}
        />
      )}
    </KeyboardControlBox>
  );
};
export const InputEditCell = (params) => {
  return <InputCell {...params} />;
};
