export default class TimecardListPageObject {
  addTimecard: string = '[data-testid="timecard-add-btn"]';
  projectHome: string = '[data-testid="timecard-projectHome-btn"]';
  addMoreTimeButton: string = '[data-testid="timecard-addMoreTime-btn-{{id}}"]';
  timecardDate: string = '[data-testid="timecard-date-{{id}}"]';
  timecardStatus: string = '[data-testid="timecard-status-{{id}}"]';
  timecardStartedOn: string = '[data-testid="timecard-startedOn-{{id}}"]';
  addMoreTimeCreteButton: string = '[data-testid="addMoreTime-create-btn"]';

  goToAddTimecard() {
    cy.get(this.addTimecard).click();
  }

  goToTimecard(id: string) {
    cy.get(this.timecardDate.replace('{{id}}', id)).click();
  }

  goToDuplicateTimecard(id: string) {
    cy.get(this.addMoreTimeButton.replace('{{id}}', id))
      .should('be.visible')
      .click();
    cy.get(this.addMoreTimeCreteButton).should('be.visible').click();
  }
}
