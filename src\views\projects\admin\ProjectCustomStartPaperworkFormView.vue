<template>
  <div class="flex justify-center mx-auto w-full py-2 px-5">
    <div class="max-w-7xl">
      <div>
        <h2 class="text-xl font-bold mb-3">Create Custom Start Paperwork</h2>
        <TextInput
          :disabled="isDefaultDoc"
          v-model="customPaperwork.documentTemplate.name"
          class="mt-2"
          label="Name"
          data-testid="custom-paperwork-name-input"
        />
        <Dropdown
          v-model="customPaperwork.locationSetting"
          :menu-items="projectDocumentTemlateWrorkLocationSettings"
          display-name="name"
          label="Work Location Setting"
          data-testid="work-location-setting-dropdown"
        />
        <div
          v-if="
            customPaperwork?.locationSetting?.key === 'specific_work_locations'
          "
          class="mb-3 pb-3"
        >
          <Dropdown
            v-model="customPaperwork.locationInclusionStatus"
            :menu-items="inclusionStatuses"
            display-name="name"
            @change="selectLocationSetting"
          />
          <MultiSelectSearchDropdown
            v-model="customPaperwork.locations"
            class="mb-2"
            label="Work location"
            value-name="locationName"
            display-name="locationName"
            :menu-items="workLocationData.workLocations"
            @load-more="fetchMoreLocations"
            @update:searchQuery="handleLocationSearchQuery"
            :infiniteScroll="true"
            :loading="loadingLocations"
          />
          <!-- <div class="pt-8">
            <div>
              <h3
                class="text-lg font-medium leading-6 text-gray-900 dark:text-gray-400"
              >
                Work Locations
              </h3>
            </div>
            <div class="flex flex-wrap items-center space-x-2 py-3">
              <div
                v-for="(
                  workLocation, workLocationIndex
                ) in customPaperwork.locations"
                :key="`union-${workLocationIndex}`"
                @click="removeWorkLocation(workLocation)"
                class="rounded-full bg-green-100 hover:bg-green-200 px-2 text-xs font-semibold leading-5 text-green-800 cursor-pointer"
              >
                <div class="flex">
                  {{ workLocation.locationName }}
                  <XMarkIcon
                    class="w-3 ml-1 cursor-pointer hover:dark:text-gray-100"
                  />
                </div>
              </div>
            </div>

            <div class="flex justify-center">
              <Button
                @click="workLocationData.modal = true"
                color="gray"
                size="sm"
              >
                <template #icon>
                  <PlusIcon
                    class="w-5 mr-1 cursor-pointer hover:dark:text-gray-100"
                  />
                </template>
                Add Work Location
              </Button>
              <Modal v-model="workLocationData.modal">
                <div class="mx-4 mb-32">
                  <div class="flex justify-between">
                    <h2>Add Work Location</h2>
                    <XMarkIcon
                      @click="workLocationData.modal = false"
                      class="w-6"
                    />
                  </div>
                  <Dropdown
                    label="Work Location"
                    v-model="workLocationData.selectedWorkLocation"
                    :menu-items="workLocationData.workLocations"
                    display-name="locationName"
                  />
                  <div class="flex justify-center h-32">
                    <Button @click="addWorkLocation"> Submit </Button>
                  </div>
                </div>
              </Modal>
            </div>
          </div> -->
        </div>
        <Dropdown
          v-model="customPaperwork.type"
          :menu-items="projectDocumentTemplateTypes"
          display-name="name"
          label="Union Setting"
          data-testid="union-setting-dropdown"
        />
        <!-- <div
          v-if="customPaperwork?.type?.key === 'specific_unions'"
          class="mb-3 pb-3 border-b-2"
        >
          <div class="pt-8">
            <div>
              <h3
                class="text-lg font-medium leading-6 text-gray-900 dark:text-gray-400"
              >
                Unions
              </h3>
            </div>
            <div class="flex flex-wrap items-center space-x-2 py-3">
              <div
                v-for="(union, unionIndex) in customPaperwork.unions"
                :key="`union-${unionIndex}`"
                @click="removeUnion(union)"
                class="rounded-full bg-green-100 hover:bg-green-200 px-2 text-xs font-semibold leading-5 text-green-800 cursor-pointer"
              >
                <div class="flex">
                  {{ union.name }}
                  <XMarkIcon
                    class="w-3 ml-1 cursor-pointer hover:dark:text-gray-100"
                  />
                </div>
              </div>
            </div>

            <div class="flex justify-center">
              <Button @click="unionData.modal = true" color="gray" size="sm">
                <template #icon>
                  <PlusIcon
                    class="w-5 mr-1 cursor-pointer hover:dark:text-gray-100"
                  />
                </template>
                Add Union
              </Button>
              <Modal v-model="unionData.modal">
                <div class="mx-4 mb-32">
                  <div class="flex justify-between">
                    <h2>Add Union</h2>
                    <XMarkIcon @click="unionData.modal = false" class="w-6" />
                  </div>
                  <Dropdown
                    label="Union"
                    v-model="unionData.selectedUnion"
                    :menu-items="unionData.unions"
                    display-name="name"
                  />
                  <div class="flex justify-center h-32">
                    <Button @click="addUnion"> Submit </Button>
                  </div>
                </div>
              </Modal>
            </div>
          </div>
        </div> -->
        <Dropdown
          v-if="customPaperwork?.type?.key === 'specific_unions'"
          v-model="customPaperwork.unionInclusionStatus"
          :menu-items="inclusionStatuses"
          display-name="name"
        />
        <MultiSelectSearchDropdown
          v-if="customPaperwork?.type?.key === 'specific_unions'"
          v-model="selectedUnionData"
          class="mb-2"
          label="Unions"
          value-name="name"
          display-name="name"
          :menu-items="unionData.unions"
          @load-more="fetchMoreUnions"
          @update:searchQuery="handleSearchQuery"
          :infiniteScroll="true"
          :loading="loadingUnions"
        />
        <Dropdown
          v-model="customPaperwork.loanOutSetting"
          :menu-items="projectDocumentTemplateLoanOutSettings"
          display-name="name"
          label="Loan Out Setting"
          data-testid="loan-out-setting-dropdown"
        />
        <CodeArea
          v-if="!isDefaultDoc"
          v-model="customPaperwork.documentTemplate.schema"
          class="mt-2 mb-5"
          label="Schema"
          data-testid="custom-paperwork-schema-input"
        />
        <CodeArea
          v-if="!isDefaultDoc"
          v-model="customPaperwork.documentTemplate.countersignSchema"
          class="mt-2 mb-5"
          label="Countersign Schema"
          data-testid="custom-paperwork-countersign-schema-input"
        />
        <Toggle
          v-if="!isDefaultDoc"
          v-model="customPaperwork.isOptional"
          class="mb-2"
          label="Default"
        >
          Optional document
        </Toggle>
        <FileUpload
          v-if="!isDefaultDoc"
          v-model="customPaperwork.documentTemplate.documentId"
          class="my-1"
        />
        <div class="flex justify-center space-x-1 pt-3">
          <Button color="gray" @click="goToDocumentTemplateTable">
            Back
          </Button>
          <Button @click="save"> Save </Button>
        </div>
      </div>
    </div>
    <div v-if="!isDefaultDoc" class="max-w-2xl min-h-2/3 h-2/3 max-h-2/3 ml-4">
      <PDFDragAndDrop
        v-if="customPaperwork.documentTemplate.documentId"
        :documentId="customPaperwork.documentTemplate.documentId"
        :schemas="processedSchemas"
        :fieldOptions="pdfFieldOptions"
        @update:schemas="handleSchemaUpdate($event)"
      />
    </div>
  </div>
</template>

<script lang="ts">
import Button from '@/components/library/Button.vue';
import CodeArea from '@/components/library/CodeArea.vue';
import Dropdown from '@/components/library/Dropdown.vue';
import FileUpload from '@/components/library/FileUpload.vue';
import Modal from '@/components/library/Modal.vue';
import TextInput from '@/components/library/TextInput.vue';
import Toggle from '@/components/library/Toggle.vue';
import PDFDragAndDrop from '@/components/PDFDragAndDrop.vue';
import { listInclusionStatuses } from '@/services/inclusion-statuses';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';
import {
  createProjectDocumentTemplate,
  getProjectDocumentTemplate,
  updateProjectDocumentTemplate,
} from '@/services/project';
import { listProjectDocumentTemplateLoanOutSettings } from '@/services/project-document-template-loan-out-settings';
import { listProjectDocumentTemplateTypes } from '@/services/project-document-template-types';
import { listProjectDocumentTemplateWorkLocationSettings } from '@/services/project-document-template-work-location-settings';
import { getUnions } from '@/services/unions';
import { getAllWorkLocations } from '@/services/app';
import type { Pagination } from '@/types/Pagination';
import type { PDFFieldOption } from '@/types/PDFFieldOption';
import type Project from '@/types/Project';
import { ProjectDocumentTemplateType } from '@/types/ProjectDocumentTemplateType';
import type { SchemaItem } from '@/types/Schema';
import type { Union } from '@/types/Union';
import type { WorkLocation } from '@/types/WorkLocation';
import { PDFField } from '@/utils/enum';
import {
  convertSchemasArrayToSchemasString,
  convertSchemasStringToSchemasArray,
} from '@/utils/schema';
import type { Schema } from '@pdfme/common';
import axios from 'axios';
import SnackbarStore from '@/reactComponents/stores/snackbar';

import type { SnackType } from '@/types/Snackbar';

import { defineComponent, type PropType } from 'vue';
import MultiSelectSearchDropdown from '@/components/library/MultiSelectSearchDropdown.vue';
import { isJSON } from '@/utils/json';

export default defineComponent({
  props: {
    project: {
      type: Object as PropType<Project>,
      required: true,
    },
    editMode: {
      type: Boolean,
      required: true,
    },
    navigate: {
      type: Function as PropType<Function>,
      required: true,
    },
    route: {
      type: Object as PropType<ParsedRoute>,
      required: true,
    },
  },
  components: {
    TextInput,
    Dropdown,
    FileUpload,
    CodeArea,
    Modal,
    Toggle,
    PDFDragAndDrop,
    Button,
    MultiSelectSearchDropdown,
  },
  data() {
    return {
      customPaperwork: {
        documentTemplate: {
          schema: '',
          name: '' as string,
          documentId: undefined as string | undefined,
          isCustom: true as boolean,
          sendToPayroll: false as boolean,
          countersignSchema: '',
        },
        type: null as any,
        locationSetting: null as any,
        locationInclusionStatus: null as any,
        unionInclusionStatus: null as any,
        locations: [] as WorkLocation[],
        loanOutSetting: null as any,
        unions: [] as any,
        isOptional: false as boolean,
        isEnabled: true as boolean,
        isCompanyDoc: false as boolean,
      },
      unionData: {
        modal: false,
        unions: [] as Union[],
        selectedUnion: null as Union | null,
      },
      workLocationSearch: '' as string,
      paginationWorkLocation: {
        page: 1,
        limit: 50,
        total: 0,
      } as Pagination,
      projectDocumentTemlateWrorkLocationSettings: [] as any,
      projectDocumentTemplateTypes: [] as any,
      projectDocumentTemplateLoanOutSettings: [] as any,
      inclusionStatuses: [] as any,
      workLocationData: {
        modal: false,
        workLocations: [] as WorkLocation[],
        selectedWorkLocation: null as WorkLocation | null,
      },
      unionSearch: '' as string,
      paginationUnion: {
        page: 1,
        limit: 50,
        total: 0,
      } as Pagination,
      loadingUnions: false as boolean,
      selectedUnionData: [] as Union[],
      isDefaultDoc: false as boolean,
      loadingLocations: false as boolean,
    };
  },
  async mounted() {
    this.isDefaultDoc = this.route.params.isDefaultDoc === '1';
    await this.getProjectDocumentTemplateWorkLocationSettings();
    await this.getProjectDocumentTemplateTypes();
    await this.projectDocumentTemplateLoanOutSettingsLoader();
    await this.getInclusionStatuses();
    await this.updateWorkLocationList();
    if (this.editMode) {
      await this.fetchProjectDocumentTemplate();
    }
    await this.updateUnionList();
  },
  computed: {
    pdfFieldOptions(): PDFFieldOption[] {
      return [
        PDFField.FullName.value,
        PDFField.TodaysDate.value,
        PDFField.Signature.value,
        PDFField.ProjectName.value,
      ];
    },
    processedSchemas(): SchemaItem[] {
      return convertSchemasStringToSchemasArray(
        this.customPaperwork.documentTemplate.schema,
      );
    },
  },
  methods: {
    triggerSnackbar(message: string, timeout: number, type: SnackType) {
      SnackbarStore.triggerSnackbar(message, timeout, type);
    },
    async getProjectDocumentTemplateWorkLocationSettings() {
      const { data } = await listProjectDocumentTemplateWorkLocationSettings();
      this.projectDocumentTemlateWrorkLocationSettings = data;
    },
    async getProjectDocumentTemplateTypes() {
      const {
        data: { data: projectDocumentTemplateTypes },
      } = await listProjectDocumentTemplateTypes();
      this.projectDocumentTemplateTypes = projectDocumentTemplateTypes;
    },
    async projectDocumentTemplateLoanOutSettingsLoader() {
      const { data } = await listProjectDocumentTemplateLoanOutSettings();
      this.projectDocumentTemplateLoanOutSettings = data;
    },
    async fetchProjectDocumentTemplate(): Promise<void> {
      const { data: documentTemplate } = await getProjectDocumentTemplate(
        this.project.id!,
        this.route.params.projectDocumentTemplateId as string,
      );
      documentTemplate.documentTemplate.schema = JSON.stringify(
        documentTemplate.documentTemplate.schema,
        null,
        2,
      );
      documentTemplate.documentTemplate.countersignSchema = JSON.stringify(
        documentTemplate.documentTemplate.countersignSchema,
        null,
        2,
      );
      documentTemplate.unions?.forEach((union: Union) => {
        union.unionLocalId = union.castAndCrewId;
      });
      this.customPaperwork = documentTemplate;
      this.selectedUnionData = documentTemplate.unions;
    },
    async getInclusionStatuses() {
      const { data } = await listInclusionStatuses();
      this.inclusionStatuses = data;
      this.customPaperwork.locationInclusionStatus = this.inclusionStatuses[0];
      this.customPaperwork.unionInclusionStatus = this.inclusionStatuses[0];
    },
    async save() {
      if (!this.customPaperwork.documentTemplate.documentId) {
        this.triggerSnackbar(
          ProjectCustomStartPaperworkFormViewErrors.PDF_REQUIRED as string,
          2500,
          'error',
        );
        return;
      }
      if (!this.customPaperwork.documentTemplate.name) {
        this.triggerSnackbar(
          ProjectCustomStartPaperworkFormViewErrors.NAME_REQUIRED as string,
          2500,
          'error',
        );
        return;
      }
      if (!this.customPaperwork.documentTemplate.schema) {
        this.triggerSnackbar(
          ProjectCustomStartPaperworkFormViewErrors.SCHEMA_REQUIRED as string,
          2500,
          'error',
        );
        return;
      }
      if (!isJSON(this.customPaperwork.documentTemplate.schema)) {
        this.triggerSnackbar(
          ProjectCustomStartPaperworkFormViewErrors.SCHEMA_INVALIDJSON as string,
          2500,
          'error',
        );
        return;
      }
      if (!this.customPaperwork.documentTemplate.countersignSchema) {
        this.triggerSnackbar(
          ProjectCustomStartPaperworkFormViewErrors.COUNTERSIGN_REQUIRED as string,
          2500,
          'error',
        );
        return;
      }
      if (!isJSON(this.customPaperwork.documentTemplate.countersignSchema)) {
        this.triggerSnackbar(
          ProjectCustomStartPaperworkFormViewErrors.COUNTERSIGN_INVALIDJSON as string,
          2500,
          'error',
        );
        return;
      }

      this.customPaperwork.unions = this.selectedUnionData;
      if (
        this.customPaperwork?.type?.key !=
        ProjectDocumentTemplateType.KEY_SPECIFIC_UNIONS
      ) {
        this.customPaperwork.unions = [];
        this.customPaperwork.unionInclusionStatus = this.inclusionStatuses[0];
      } else if (
        this.customPaperwork?.type?.key ===
          ProjectDocumentTemplateType.KEY_SPECIFIC_UNIONS &&
        this.customPaperwork.unions.length === 0
      ) {
        this.triggerSnackbar('Unions is required' as string, 2500, 'error');
        return;
      }
      if (
        this.customPaperwork?.locationSetting?.key !=
        ProjectDocumentTemplateType.KEY_SPECIFIC_LOCATIONS
      ) {
        this.customPaperwork.locations = [];
        this.customPaperwork.locationInclusionStatus =
          this.inclusionStatuses[0];
      } else if (
        this.customPaperwork?.locationSetting?.key ===
          ProjectDocumentTemplateType.KEY_SPECIFIC_LOCATIONS &&
        this.customPaperwork.locations.length === 0
      ) {
        this.triggerSnackbar('Locations is required' as string, 2500, 'error');
        return;
      }
      try {
        const customPaperwork = {
          ...this.customPaperwork,
          documentTemplate: {
            ...this.customPaperwork.documentTemplate,
            schema: JSON.parse(this.customPaperwork.documentTemplate.schema),
            countersignSchema: JSON.parse(
              this.customPaperwork.documentTemplate.countersignSchema,
            ),
          },
        };
        if (this.editMode) {
          await updateProjectDocumentTemplate(
            this.project.id!,
            customPaperwork,
          );
        } else {
          await createProjectDocumentTemplate(
            this.project.id!,
            customPaperwork,
          );
        }
      } catch (err: any) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          this.triggerSnackbar(msg, 2500, 'error');
        } else {
          this.triggerSnackbar(err as string, 2500, 'error');
        }
        return;
      }
      this.goToDocumentTemplateTable();
    },
    goToDocumentTemplateTable() {
      this.navigate({
        pathname: `/projects/${this.project.hashId!}/admin/document-templates`,
      });
    },
    handleSchemaUpdate(schemas: Schema) {
      this.customPaperwork.documentTemplate.schema =
        convertSchemasArrayToSchemasString(schemas);
    },
    async updateWorkLocationList() {
      this.loadingLocations = true;
      try {
        const { data: workLocationListData } = await getAllWorkLocations(
          this.workLocationSearch,
          this.paginationWorkLocation,
        );
        this.paginationWorkLocation.total = workLocationListData.totalCount;
        if (this.paginationWorkLocation.page === 1) {
          this.workLocationData.workLocations = [];
        }
        if (!this.customPaperwork?.locations) return;
        const updateWorkLocations =
          workLocationListData.workLocationList.filter((workLocation: any) => {
            return !this.customPaperwork.locations!.find(
              (userWorkLocation: any) =>
                userWorkLocation.locationId === workLocation.locationId,
            );
          });
        this.workLocationData.workLocations = [
          ...this.workLocationData.workLocations,
          ...updateWorkLocations,
        ];
        this.loadingLocations = false;
      } catch (err) {
        this.workLocationData.workLocations = [];
        this.loadingLocations = false;
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.message;
          this.triggerSnackbar(msg, 2500, 'error');
        } else {
          this.triggerSnackbar(err as string, 2500, 'error');
        }
      }
    },
    async updateUnionList() {
      this.loadingUnions = true;
      try {
        const { data } = await getUnions(
          this.unionSearch,
          this.paginationUnion,
        );
        const unionListData = data.unionList;
        this.paginationUnion.total = data.totalCount;
        if (this.paginationUnion.page === 1) {
          this.unionData.unions = [];
        }
        if (!this.customPaperwork?.unions) return;
        const filteredUnions = unionListData.filter((union: any) => {
          return !this.customPaperwork.unions!.some(
            (userUnion: any) => userUnion.id === union.id,
          );
        });
        this.unionData.unions = [...this.unionData.unions, ...filteredUnions];
        this.loadingUnions = false;
      } catch (err) {
        this.unionData.unions = [];
        this.loadingUnions = false;
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.message;
          this.triggerSnackbar(msg, 2500, 'error');
        } else {
          this.triggerSnackbar(err as string, 2500, 'error');
        }
      }
    },
    async addWorkLocation() {
      if (!this.workLocationData.selectedWorkLocation?.locationId) return;
      this.customPaperwork.locations.push(
        this.workLocationData.selectedWorkLocation,
      );
      this.workLocationData.modal = false;
      this.workLocationData.selectedWorkLocation = null;
      await this.updateWorkLocationList();
    },
    async removeWorkLocation(workLocation: WorkLocation) {
      if (!workLocation?.locationId) return;
      this.customPaperwork.locations = this.customPaperwork.locations.filter(
        (u: WorkLocation) => u.locationId !== workLocation?.locationId,
      );
      await this.updateWorkLocationList();
    },
    fetchMoreUnions() {
      if (
        this.paginationUnion.page == 1 ||
        this.paginationUnion.total -
          this.paginationUnion.page * this.paginationUnion.limit >
          50
      ) {
        this.paginationUnion.page = this.paginationUnion.page + 1;
        this.updateUnionList();
      }
    },
    handleSearchQuery(searchQuery: string) {
      this.unionSearch = searchQuery;
      if (this.unionSearch) {
        this.unionData.unions = [];
        this.paginationUnion.page = 1;
      }
      this.updateUnionList();
    },
    async addUnion() {
      if (!this.unionData.selectedUnion?.id) return;
      this.customPaperwork.unions.push(this.unionData.selectedUnion);
      this.unionData.modal = false;
      this.unionData.selectedUnion = null;
      await this.updateUnionList();
    },
    async removeUnion(union: Union) {
      if (!union?.id) return;
      this.customPaperwork.unions = this.customPaperwork.unions.filter(
        (u: Union) => u.id !== union?.id,
      );
      await this.updateUnionList();
    },
    fetchMoreLocations() {
      if (
        this.paginationWorkLocation.page == 1 ||
        this.paginationWorkLocation.total -
          this.paginationWorkLocation.page * this.paginationWorkLocation.limit >
          50
      ) {
        this.paginationWorkLocation.page = this.paginationWorkLocation.page + 1;
        this.updateWorkLocationList();
      }
    },
    handleLocationSearchQuery(searchQuery: string) {
      this.workLocationSearch = searchQuery;
      if (this.workLocationSearch) {
        this.workLocationData.workLocations = [];
        this.paginationWorkLocation.page = 1;
      }
      this.updateWorkLocationList();
    },
    selectLocationSetting() {
      if (this.customPaperwork?.locationSetting?.key === 'all') {
        this.customPaperwork.locations = [];
      }
    },
  },
});
const ProjectCustomStartPaperworkFormViewErrors = {
  PDF_REQUIRED: 'PDF upload is required',
  NAME_REQUIRED: 'Name is required',
  SCHEMA_REQUIRED: 'Schema is required',
  SCHEMA_INVALIDJSON: 'Schema is not valid JSON',
  COUNTERSIGN_REQUIRED: 'Countersign Schema is required',
  COUNTERSIGN_INVALIDJSON: 'Countersign Schema is not valid JSON',
  UNIONS_REQUIRED: 'Unions is required',
  LOCATIONS_REQUIRED: 'Locations is required',
};
</script>
