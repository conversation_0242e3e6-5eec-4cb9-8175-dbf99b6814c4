import React from 'react';
import PropTypes from 'prop-types';

import { Box, IconButton, Tooltip } from '@/reactComponents/library';
import SigPad from 'signature_pad';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';

const styles = {
  sigPadBox: { display: 'flex', justifyContent: 'flex-end' },
  canvasBox: {
    border: '1px solid black',
    width: '100%',
    height: '100%',
  },
};

const SignaturePad = (props) => {
  const { sigPad, setSigPad, height = 200, width = 400 } = props;

  const sigPadRef = React.useRef(null);

  React.useEffect(() => {
    const pad = new SigPad(sigPadRef.current);
    setSigPad(pad);

    sigPadRef.current.height = height;
    sigPadRef.current.width = width;
  }, [height, setSigPad, width]);

  return (
    <Box sx={styles.sigPadBox}>
      <Box sx={styles.canvasBox}>
        <canvas id="signature-pad" ref={sigPadRef} />
      </Box>
      <IconButton
        sx={{ position: 'absolute' }}
        onClick={() => sigPad.clear()}
        aria-label="clear signature"
      >
        <Tooltip placement="top" title="Clear Signature">
          <ClearOutlinedIcon />
        </Tooltip>
      </IconButton>
    </Box>
  );
};

SignaturePad.propTypes = {
  sigPad: PropTypes.object,
  setSigPad: PropTypes.func,
  height: PropTypes.number,
  width: PropTypes.number,
};

export default SignaturePad;
