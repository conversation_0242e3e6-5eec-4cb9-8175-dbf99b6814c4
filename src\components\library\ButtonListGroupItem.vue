<template>
  <div :class="computedClasses">
    <slot />
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  props: {
    href: {
      type: String,
      default: '#',
    },
    isActive: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: 'md',
    },
  },
  computed: {
    computedClasses() {
      const baseSize = {
        md: 'text-md py-2 h-10',
        sm: 'text-sm py-1 h-8',
        xs: 'text-xs py-1 h-6',
      }[this.size as string];
      const baseClasses = `${baseSize} block w-full px-4 border-b border-gray-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:border-gray-600`;
      const activeClasses = this.isActive ? 'bg-gray-100 dark:bg-gray-600' : '';
      const hoverClasses = 'hover:bg-gray-100 dark:hover:bg-gray-600';

      return `${baseClasses} ${activeClasses} ${hoverClasses}`;
    },
  },
});
</script>

<style scoped>
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  div:first-of-type {
    @apply rounded-t-lg;
  }

  div:last-of-type {
    @apply rounded-b-lg border-b-0;
  }
}
</style>
