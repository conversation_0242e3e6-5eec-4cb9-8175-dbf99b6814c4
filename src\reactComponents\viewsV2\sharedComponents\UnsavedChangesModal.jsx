import React from 'react';
import PropTypes from 'prop-types';
import { useBlocker } from 'react-router';
import { Modal, Box } from '@/reactComponents/library';

const UnsavedChangesModal = (props) => {
  const { hasUnsavedChanges } = props;

  const [open, setOpen] = React.useState(true);

  const { state: blockerState, proceed, reset } = useBlocker(hasUnsavedChanges);

  React.useEffect(() => {
    if (blockerState !== 'unblocked') {
      setOpen(true);
    } else {
      setOpen(false);
    }
  }, [blockerState]);

  if (blockerState === 'unblocked') {
    return null;
  }

  return (
    <Modal
      title="Unsaved Changes"
      open={open}
      setOpen={setOpen}
      onCancel={() => {
        reset();

        setOpen(false);
      }}
      onSubmit={() => proceed()}
      submitText="Continue, discard changes"
      cancelText="Stay"
    >
      <Box
        sx={{
          display: 'flex',
          height: '75px',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        Are you sure you want to leave this page?
      </Box>
    </Modal>
  );
};

UnsavedChangesModal.propTypes = {
  hasUnsavedChanges: PropTypes.bool,
};

export default UnsavedChangesModal;
