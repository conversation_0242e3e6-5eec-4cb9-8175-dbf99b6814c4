<template>
  <header class="bg-gray-50 dark:bg-gray-700 pt-6 fixed z-10 w-full shadow">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between">
        <div class="min-w-0 flex-1">
          <nav class="flex" aria-label="Breadcrumb">
            <ol role="list" class="flex items-center space-x-4">
              <li>
                <div>
                  <a
                    class="text-sm cursor-pointer font-medium text-gray-500 hover:text-gray-700 dark:hover:text-gray-400"
                    @click="navigate?.({ pathname: '/companies' })"
                    >Companies</a
                  >
                </div>
              </li>
              <li>
                <div class="flex items-center">
                  <ChevronRightIcon
                    class="h-5 w-5 flex-shrink-0 text-gray-400"
                    aria-hidden="true"
                  />
                  <a
                    class="ml-4 cursor-pointer text-sm font-medium text-gray-500 hover:text-gray-700 dark:hover:text-gray-400"
                    >{{ company.name }}
                  </a>
                </div>
              </li>
            </ol>
          </nav>
          <h1
            class="mt-2 text-2xl font-bold leading-7 sm:truncate sm:text-3xl sm:tracking-tight"
          >
            {{ company?.name }}
          </h1>
        </div>

        <DropdownMenu class="sm:hidden" :item-groups="moreItems">
          <template #buttonContent>
            <MenuButton
              class="inline-flex items-center gap-x-1.5 rounded-md bg-white dark:bg-gray-800 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-200 shadow-sm ring-1 ring-inset ring-gray-300 hover:ring-gray-400"
            >
              More
              <ChevronDownIcon
                class="-mr-1 h-5 w-5 text-gray-400"
                aria-hidden="true"
              />
            </MenuButton>
          </template>
        </DropdownMenu>

        <button
          type="button"
          @click="goTo('/edit')"
          class="hidden sm:inline-flex items-center gap-x-1.5 rounded-md bg-white dark:bg-gray-800 px-3 py-2 text-sm font-semibold shadow-sm ring-1 ring-inset ring-gray-300 hover:ring-gray-400 dark:hover:ring-gray-300 hover:bg-gray-50 hover:dark:bg-gray-700"
          data-testid="edit-company-btn"
        >
          <PencilIcon
            class="-ml-0.5 h-5 w-5 text-gray-400"
            aria-hidden="true"
          />
          Edit
        </button>
      </div>
      <CompanyAdminTabs class="mt-2" :tabs="tabs" />
    </div>
  </header>
</template>

<script lang="ts">
import CompanyAdminTabs from '@/components/CompanyAdminTabs.vue';
import DropdownMenu from '@/components/library/DropdownMenu.vue';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';
import type Company from '@/types/Company';
import type { MenuItemGroup } from '@/types/Menu';
import { MenuButton } from '@headlessui/vue';
import { ChevronDownIcon, ChevronRightIcon } from '@heroicons/vue/20/solid';
import { PencilIcon } from '@heroicons/vue/24/outline';
import { defineComponent, type PropType, provide, ref, watchEffect } from 'vue';

export default defineComponent({
  components: {
    DropdownMenu,
    MenuButton,
    ChevronDownIcon,
    ChevronRightIcon,
    PencilIcon,
    CompanyAdminTabs,
  },
  props: {
    company: {
      type: Object as () => Company,
      required: true,
    },
    navigate: {
      type: Function,
      required: false,
    },
    route: {
      type: Object as PropType<ParsedRoute>,
      required: false,
    },
  },
  setup(props, ctx) {
    const route = ref(props.route);
    watchEffect(() => {
      route.value = props.route;
    });
    provide('navigate', props.navigate);
    provide('route', route);
  },
  computed: {
    tabs() {
      // add pathname for redirect
      return [
        {
          label: 'Dashboard',
          key: 'dashboard',
          routeName: 'company-dashboard',
          routePath: '',
        },
        {
          label: 'Members',
          key: 'members',
          routeName: 'company-members',
          routePath: 'members',
        },
        {
          label: 'Document Templates',
          key: 'company-document-templates',
          routeName: 'company-document-templates',
          routePath: 'document-templates',
        },
      ];
    },
    moreItems(): MenuItemGroup[] {
      return [
        this.tabs.map((tab) => {
          return {
            label: tab.label,
            action: () => this.goTo(tab.routePath),
          };
        }),
        [
          {
            label: 'Edit Company',
            action: () => this.goTo('edit'),
          },
        ],
      ];
    },
  },
  methods: {
    goTo(routeName: string) {
      const path = `/companies/${this.company.id}/${routeName || ''}`;
      this.navigate?.({ pathname: path });
    },
  },
});
</script>
