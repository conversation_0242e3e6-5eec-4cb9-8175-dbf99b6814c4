import React from 'react';
import PropTypes from 'prop-types';
import { FiltersBox } from './AdminTimeFilters.jsx';
import { Typography, PillBadge } from '@/reactComponents/library';
import XCloseIcon from '../../assets/icons/XCloseIcon.jsx';

const DEFAULT_HEIGHT = 58;

const AdminTimeFiltersApplied = (props) => {
  const { appliedFilters, setFilters, setBatchFilters } = props;

  const [expanded, setExpanded] = React.useState(false);
  const [overflowing, setOverflowing] = React.useState(false);
  const totalCount = React.useRef(0);
  const appliedFiltersRef = React.useRef(null);
  let count = 0;

  // change to useState for dynamic threshold in the future
  const renderedFiltersThreshold = 3;

  React.useEffect(() => {
    const target = document.getElementById('applied-filters');
    const clientHeight = target?.clientHeight;
    const scrollHeight = target?.scrollHeight;
    if (appliedFilters.length === 0) {
      setOverflowing(false);
    }
    if (
      scrollHeight === clientHeight &&
      totalCount.current <= renderedFiltersThreshold
    ) {
      setOverflowing(false);
    }
  }, [appliedFilters]);

  React.useEffect(() => {
    const target =
      appliedFiltersRef.current || document.getElementById('applied-filters');
    const observer = new MutationObserver((mutations, observer) => {
      const record = mutations[0];
      const target = record?.target;
      const clientHeight = target?.clientHeight;
      const scrollHeight = target?.scrollHeight;
      const offsetHeight = target?.offsetHeight;
      if (scrollHeight > clientHeight || scrollHeight > DEFAULT_HEIGHT) {
        setOverflowing(true);
      } else if (
        offsetHeight === DEFAULT_HEIGHT &&
        scrollHeight <= clientHeight &&
        totalCount.current <= renderedFiltersThreshold
      ) {
        setOverflowing(false);
      }
    });

    if (target) {
      let config = { attributes: true, childList: true, subtree: true };
      observer.observe(target, config);

      const scrollHeight = target?.scrollHeight;
      if (scrollHeight > DEFAULT_HEIGHT) {
        setOverflowing(true);
      } else {
        setOverflowing(false);
      }
    }
    return () => {
      observer.disconnect();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [appliedFiltersRef.current]);

  return (
    <>
      {appliedFilters.length > 0 && (
        <FiltersBox
          sx={{
            borderTop: '1px solid',
            borderTopColor: 'grey.300',
            flexWrap: 'wrap',
            padding: '10px 16px',
            height: expanded ? 'inherit' : DEFAULT_HEIGHT,
          }}
          id="applied-filters"
          ref={appliedFiltersRef}
        >
          <Typography sx={{ mr: '16px', fontSize: '14px', fontWeight: 600 }}>
            Applied Filters:
          </Typography>
          {appliedFilters.map((filter, firstIndex) => {
            const label = filter.label;
            return filter?.options?.map?.((option, optionIndex) => {
              if (count === 0) totalCount.current = 0;
              count++;
              totalCount.current++;
              if (!expanded && overflowing && count > renderedFiltersThreshold)
                return null;
              return (
                <PillBadge
                  variant="lightGray"
                  sx={{
                    margin: '6px 4px',
                  }}
                  key={`${filter.field}-${option.id}`}
                  onClick={() => {
                    let onSetFilters = () => {
                      console.warn('no setFilters');
                    };
                    switch (filter.subject) {
                      case 'timecard':
                        onSetFilters = setFilters;
                        break;
                      case 'batch':
                        onSetFilters = setBatchFilters;
                        break;
                      default:
                        console.warn('no filter subject');
                        onSetFilters = setFilters;
                        break;
                    }
                    onSetFilters((prevFilters) => {
                      const newFilters = prevFilters.map((f) => f);
                      const match = newFilters.find(
                        (f) => f.field === filter.field,
                      );
                      if (!match) console.error('filter not found');
                      else {
                        match.options = match.options.filter(
                          (o) => o.id !== option.id,
                        );
                      }
                      return newFilters;
                    });
                  }}
                >
                  {`${label}: ${option?.name}`}
                  <XCloseIcon sx={{ fontSize: '12px', ml: '6px' }} />
                </PillBadge>
              );
            });
          })}
          {overflowing && (
            <PillBadge
              variant="infoBtn"
              onClick={() => {
                setExpanded((prev) => !prev);
              }}
            >
              View {`${expanded ? 'less' : 'more'}`}
            </PillBadge>
          )}
        </FiltersBox>
      )}
    </>
  );
};

AdminTimeFiltersApplied.propTypes = {
  appliedFilters: PropTypes.array.isRequired,
  setFilters: PropTypes.func.isRequired,
  setBatchFilters: PropTypes.func.isRequired,
};

export default AdminTimeFiltersApplied;
