import React from 'react';
// import type Project from '@/types/Project';
import PrimaryTab from '@/reactComponents/library/PrimaryTab';
import { ProjectHeaderContext } from './ProjectHeader';
import PropTypes from 'prop-types';

const tabs = [
  {
    label: 'Members',
    key: 'members',
    isMatch: (route) => {
      return !route?.params?.timecardId;
    },
    routeName: 'project-admin-members-list',
    routePath: '/projects/:hashId/admin/members',
  },
  // {
  //   label: 'Timecards',
  //   key: 'timecards',
  //   routeName: 'project-admin-timecards',
  //   routePath: '/projects/:hashId/admin/timecards',
  // },
  // {
  //   label: 'Batches',
  //   key: 'batches',
  //   routeName: 'project-admin-batches',
  //   routePath: '/projects/:hashId/admin/batches',
  // },
  {
    label: 'Time',
    key: '/time',
    routeName: 'project-admin-time',
    routePath: '/projects/:hashId/admin/time',
  },
];

const ProjectAdminTabs = (props) => {
  const { fullWidthHeader } = props;
  const { route, navigate } = React.useContext(ProjectHeaderContext);

  const currentTab = React.useMemo(() => {
    return tabs.find((tab) =>
      tab?.isMatch
        ? tab?.isMatch?.(route) && route?.location?.pathname.includes(tab.key)
        : route?.location?.pathname.includes(tab.key),
    );
  }, [route]);
  const goTo = (routeTab) => {
    if (navigate) {
      const hashId = route.params.hashId;
      const pathname = routeTab.routePath.replace(':hashId', hashId);
      navigate({
        pathname: pathname,
        // params: {
        //   hashId: project.value.hashId,
        // },
      });
    }
  };

  return (
    <div className="bg-gray-50 dark:bg-gray-700 w-full hidden sm:block">
      <div
        className={`px-4 sm:px-6 lg:px-8 flex items-center justify-start ${
          !fullWidthHeader && 'mx-auto max-w-7xl'
        }`}
      >
        {tabs.map((tab) => {
          return (
            <PrimaryTab
              key={tab.key}
              tab={tab}
              currentTab={currentTab}
              onClick={goTo}
            />
          );
        })}
      </div>
    </div>
  );
};

ProjectAdminTabs.propTypes = {
  fullWidthHeader: PropTypes.bool,
};

export default ProjectAdminTabs;
