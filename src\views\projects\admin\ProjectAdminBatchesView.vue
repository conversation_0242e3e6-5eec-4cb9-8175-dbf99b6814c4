<template>
  <div class="min-h-full">
    <main class="pt-8 pb-16">
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div class="px-4 sm:px-0 pb-5">
          <section class="pt-5">
            <ul
              role="list"
              class="mt-5 divide-y divide-gray-200 dark:divide-gray-500 border-t border-gray-200 dark:border-gray-500 sm:mt-0 sm:border-t-0"
            >
              <li
                v-for="(batch, batchIndex) in batches"
                :key="`${batch.id}-${batchIndex}`"
              >
                <div class="group block">
                  <div class="flex items-center py-3 px-4 sm:py-4 sm:px-0">
                    <div class="flex min-w-0 flex-1 items-center space-x-6">
                      <div class="flex md:block cursor-pointer">
                        <div @click="goToBatchTimecards(batch)">
                          <span
                            class="text-xs text-gray-700 dark:text-gray-400"
                          >
                            Batch #
                          </span>
                          <div
                            class="flex items-center space-x-2 justify-starttext-lg text-gray-900 hover:text-gray-700 dark:text-gray-300 dark:hover:text-gray-100"
                          >
                            <div>{{ batch.capsPayId }}</div>
                          </div>
                        </div>
                      </div>
                      <div class="flex md:block cursor-pointer">
                        <div @click="goToBatchTimecards(batch)">
                          <span
                            class="text-xs text-gray-700 dark:text-gray-400"
                          >
                            Name
                          </span>
                          <div
                            class="flex items-center space-x-2 justify-starttext-lg text-gray-900 hover:text-gray-700 dark:text-gray-300 dark:hover:text-gray-100"
                          >
                            <div>
                              <span class="text-gray-400 hover:text-gray-500">{{
                                batch.name
                              }}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="flex items-center space-x-2 pt-4"></div>
                      <div class="flex items-center space-x-2 pt-4">
                        <Button
                          size="xs"
                          color="secondaryOutlined"
                          @click="openEditBatchModal(batch)"
                        >
                          <div class="flex items-center space-x-1">
                            <Icon
                              name="pencil"
                              class="h-4 w-4 stroke-black dark:stroke-white"
                            />
                            <div class="font-semibold">Edit</div>
                          </div>
                        </Button>
                        <Button
                          size="xs"
                          color="secondaryOutlined"
                          @click="openDeleteBatchModal(batch)"
                        >
                          <div class="flex items-center space-x-1">
                            <Icon
                              name="trash"
                              class="h-4 w-4 stroke-black dark:stroke-white"
                            />
                            <div class="font-semibold">Delete</div>
                          </div>
                        </Button>
                      </div>
                    </div>
                    <div class="flex items-center space-x-2">
                      <Button @click="openDownloadModal(batch)" size="sm">
                        <div class="flex space-x-2 items-center">
                          <Icon name="download" />
                          <div>Export PDFs</div>
                        </div>
                      </Button>
                      <Button
                        size="sm"
                        @click="timecardReportForBatch(batch)"
                        :loading="loadingTcReport[batch.id] || false"
                        :disabled="loadingTcReport[batch.id] || false"
                      >
                        <div class="flex items-center space-x-2">
                          <Icon name="link-external" />
                          <div>TC Report</div>
                        </div>
                      </Button>
                      <Button
                        size="sm"
                        @click="pointZeroForBatch(batch)"
                        :loading="loadingPointZeroReport[batch.id] || false"
                        :disabled="loadingPointZeroReport[batch.id] || false"
                      >
                        <div class="flex items-center space-x-2">
                          <Icon name="download" />
                          <div>PointZero</div>
                        </div>
                      </Button>
                    </div>
                    <div>
                      <ChevronRightIcon
                        @click="goToBatchTimecards(batch)"
                        class="h-5 w-5 dark:text-gray-400 dark:hover:text-gray-200 text-gray-600 hover:text-gray-800 cursor-pointer"
                        aria-hidden="true"
                      />
                    </div>
                  </div>
                </div>
              </li>
            </ul>

            <Modal v-model="batchModalIsOpen">
              <div class="flex flex-col space-y-4">
                <div class="flex flex-col space-y-2">
                  <div class="text-lg font-semibold">Edit Batch</div>
                </div>
                <div>
                  <TextInput v-model="batchModalName" label="Name" />
                </div>
                <div class="flex justify-end space-x-2">
                  <Button
                    size="sm"
                    @click="batchModalIsOpen = false"
                    color="gray"
                  >
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    @click="updateCurrentBatch"
                    :loading="updatingBatch"
                  >
                    Save
                  </Button>
                </div>
              </div>
            </Modal>

            <Modal v-model="deleteBatchModalIsOpen">
              <div class="flex flex-col space-y-4">
                <div class="flex flex-col space-y-2">
                  <div class="text-lg font-semibold">Delete Batch</div>
                  <div>Are you sure you want to delete this batch?</div>
                </div>
                <div class="flex justify-end space-x-2">
                  <Button
                    size="sm"
                    @click="deleteBatchModalIsOpen = false"
                    color="gray"
                  >
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    @click="deleteCurrentBatch"
                    :loading="updatingBatch"
                    color="error"
                  >
                    Delete
                  </Button>
                </div>
              </div>
            </Modal>

            <Modal v-model="downloadModal">
              <div class="flex flex-col space-y-4 mb-12" @click.stop="() => {}">
                <div class="flex flex-col space-y-2">
                  <div class="text-lg font-semibold">Export PDFs</div>
                </div>
                <Dropdown
                  v-model="format"
                  label="One PDF vs Multiple PDFs"
                  :menu-items="exportFormats"
                  @click.stop="() => {}"
                />
                <Dropdown
                  v-if="format.value === 'pdf'"
                  v-model="exportOrder"
                  label="Export Order"
                  :menu-items="exportOrderOptions"
                />
                <Dropdown
                  v-model="exportStartPaperworkScope"
                  label="Paperwork Scope"
                  :menu-items="exportStartPaperworkScopes"
                  @change="paperworkscopeOnChange"
                />
                <Toggle
                  v-model="includeKitRental"
                  :menu-items="exportStartPaperworkScopes"
                >
                  Include Kit Rental Forms
                </Toggle>
                <Toggle
                  v-model="includeMileageForm"
                  label="Include Kit Rental Forms"
                  :menu-items="exportStartPaperworkScopes"
                >
                  Include Mileage Forms
                </Toggle>
                <Toggle
                  v-model="includeReimbursementForms"
                  label="Include Reimbursement Forms"
                  :menu-items="exportStartPaperworkScopes"
                >
                  Include Reimbursement Forms
                </Toggle>
                <div class="flex justify-end space-x-2 pt-10">
                  <Button size="sm" @click="downloadModal = false" color="gray">
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    @click="downloadPdfs()"
                    :loading="loadingExport"
                    :disabled="loadingExport"
                  >
                    Export
                  </Button>
                </div>
              </div>
            </Modal>

            <Pagination
              v-if="pagination.total > pagination.limit"
              v-model="pagination"
            />
          </section>
        </div>
      </div>
    </main>
  </div>
</template>

<script lang="ts" setup>
import Icon from '@/components/Icon.vue';
import Button from '@/components/library/Button.vue';
import Dropdown from '@/components/library/Dropdown.vue';
import Modal from '@/components/library/Modal.vue';
import Pagination from '@/components/library/Pagination.vue';
import TextInput from '@/components/library/TextInput.vue';
import Toggle from '@/components/library/Toggle.vue';
import {
  deleteBatch,
  downloadBatchPdfs,
  downloadBatchZip,
  submitBatch,
  updateBatch,
  timecardReport,
  pointZeroReport,
  blobDownload,
} from '@/services/batch';
import { getBatches } from '@/services/project';
import {
  manualDownloadFileByUrl,
  extractCSVFileNameFromUrl,
} from '@/utils/download';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import type Batch from '@/types/Batch';
import { FilterOperator, FilterUIType, type Filter } from '@/types/Filter';
import type { Pagination as PaginationType } from '@/types/Pagination';
import type Project from '@/types/Project';
import { convertFiltersToURLQuery } from '@/utils/filter';
import { ChevronRightIcon } from '@heroicons/vue/20/solid';
import axios from 'axios';
import { onMounted, ref, watch, type Ref } from 'vue';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';

const props = defineProps<{
  project: Project;
  isAdmin: boolean;
  route: ParsedRoute;
  navigate?: Function;
}>();

const pagination = ref<PaginationType>({
  page: 1,
  limit: 10,
  total: 0,
} as PaginationType);

const includeKitRental: Ref<boolean> = ref(true);
const includeMileageForm: Ref<boolean> = ref(true);
const includeReimbursementForms: Ref<boolean> = ref(true);

const exportStartPaperworkScopes = [
  { label: 'All Documentation', value: 'all' },
  { label: 'Timecards and Documents flagged for Payroll', value: 'payroll' },
  { label: 'Timecards Only', value: 'none' },
];

const exportStartPaperworkScope: Ref<any> = ref(exportStartPaperworkScopes[0]);

const exportFormats = [
  { label: 'One PDF for all Members', value: 'pdf' },
  { label: 'Individual PDFs for each Member', value: 'zip' },
];

const exportOrderOptions: any = [
  { label: 'Start Paperwork, then Timecards', value: 'startPaperworkFirst' },
  { label: 'Timecards, then Start Paperwork', value: 'timecardFirst' },
];

const exportOrder: Ref<any> = ref(exportOrderOptions[0]);

const format: Ref<any> = ref(exportFormats[0]);

const batches: Ref<Batch[]> = ref([]);

const exportingToCaps: Ref<boolean> = ref(false);

const loadingTcReport = ref<{ [key: number]: boolean }>({});

const loadingPointZeroReport = ref<{ [key: number]: boolean }>({});

const exportToCaps = async (batch: any) => {
  exportingToCaps.value = true;
  try {
    await submitBatch(batch.id);
    await load();
    SnackbarStore.triggerSnackbar(
      'Batch exported to CAPS ONE successfully.',
      2500,
      'success',
    );
  } catch (err) {
    if (axios.isAxiosError(err)) {
      SnackbarStore.triggerSnackbar(
        err.response?.data.toString(),
        2500,
        'error',
      );
    } else {
      SnackbarStore.triggerSnackbar(
        'Error exporting batch to CAPS ONE.',
        2500,
        'error',
      );
    }
  }
  exportingToCaps.value = false;
};

const downloadModal: Ref<boolean> = ref(false);
const currentBatch: Ref<Batch | null> = ref(null);

const openDownloadModal = async (batch: Batch) => {
  downloadModal.value = true;
  currentBatch.value = batch;
};

const loadingExport: Ref<boolean> = ref(false);

const downloadPdfs = async () => {
  loadingExport.value = true;
  try {
    const scope = exportStartPaperworkScope.value.value;
    const formatValue = format.value.value;

    if (formatValue === 'zip') {
      const response = await downloadBatchZip(
        currentBatch?.value?.id!,
        scope,
        includeKitRental.value,
        includeMileageForm.value,
        includeReimbursementForms.value,
      );
      const href = response.data.filePath;
      const link = document.createElement('a');

      link.href = href;
      link.setAttribute('download', response.data.fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
    } else {
      const response = await downloadBatchPdfs(
        currentBatch?.value?.id!,
        scope,
        includeKitRental.value,
        includeMileageForm.value,
        includeReimbursementForms.value,
        exportOrder.value.value,
      );
      const href = response.data.filePath;
      const link = document.createElement('a');

      link.href = href;
      link.setAttribute('download', response.data.fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
    }
    SnackbarStore.triggerSnackbar('Batch exported successfully.');
  } catch (err: any) {
    const msg = err?.response?.data?.message;
    SnackbarStore.triggerSnackbar(
      msg ? msg : 'Error exporting batch.',
      2500,
      'error',
    );
  }

  downloadModal.value = false;
  loadingExport.value = false;
};

const batchModalIsOpen: Ref<boolean> = ref(false);
const batchModalName: Ref<string> = ref('');
const updatingBatch: Ref<boolean> = ref(false);

const openEditBatchModal = (batch: Batch) => {
  batchModalIsOpen.value = true;
  batchModalName.value = batch.name;
  currentBatch.value = batch;
};

const deleteBatchModalIsOpen: Ref<boolean> = ref(false);

const openDeleteBatchModal = (batch: Batch) => {
  deleteBatchModalIsOpen.value = true;
  currentBatch.value = batch;
};

const deleteCurrentBatch = async () => {
  updatingBatch.value = true;
  try {
    await deleteBatch(currentBatch.value!.id);
    SnackbarStore.triggerSnackbar('Batch deleted successfully.');
    await load();
  } catch (err) {
    if (axios.isAxiosError(err)) {
      const msg = err.response?.data?.['errors']?.[0]?.message;
      SnackbarStore.triggerSnackbar(msg, 2500, 'error');
    } else {
      SnackbarStore.triggerSnackbar('Error updating batch.', 2500, 'error');
    }
  }
  deleteBatchModalIsOpen.value = false;
  updatingBatch.value = false;
};

const updateCurrentBatch = async () => {
  updatingBatch.value = true;
  try {
    await updateBatch(currentBatch.value!.id, { name: batchModalName.value });
    SnackbarStore.triggerSnackbar('Batch updated successfully.');
    batchModalIsOpen.value = false;
    await load();
  } catch (err) {
    if (axios.isAxiosError(err)) {
      const msg = err.response?.data?.['errors']?.[0]?.message;
      SnackbarStore.triggerSnackbar(msg, 2500, 'error');
    } else {
      SnackbarStore.triggerSnackbar('Error updating batch.', 2500, 'error');
    }
  }
  updatingBatch.value = false;
  batchModalIsOpen.value = false;
};

const load = async () => {
  const {
    data: { data: batchesData, meta },
  } = await getBatches(props.project.id!, pagination.value);
  batches.value = batchesData;
  pagination.value.total = meta.total;
  pagination.value.page = meta.current_page;
};

const paperworkscopeOnChange = async () => {
  if (exportStartPaperworkScope.value.value === 'none') {
    includeKitRental.value = false;
    includeMileageForm.value = false;
    includeReimbursementForms.value = false;
  } else {
    includeKitRental.value = true;
    includeMileageForm.value = true;
    includeReimbursementForms.value = true;
  }
};

const goToBatchTimecards = (batch: Batch) => {
  const batchFilter: Filter = {
    id: 'batch',
    field: 'batchId',
    label: 'Batch',
    value: '',
    options: [
      {
        id: `option_${batch.id}`,
        value: batch.id,
        label: batch.name,
        active: true,
      },
    ],
    type: FilterUIType.MultiSelect,
    operator: FilterOperator.In,
    active: true,
  };
  const query = { initialFilters: convertFiltersToURLQuery([batchFilter]) };
  const searchParams = new URLSearchParams();
  searchParams.set('initialFilters', query.initialFilters);
  props.navigate?.({
    pathname: `/projects/${props.project.hashId}/admin/timecards`,
    search: searchParams.toString(),
  });
};

const timecardReportForBatch = async (batch: Batch) => {
  loadingTcReport.value[batch.id] = true;
  try {
    const { data } = await timecardReport(batch.id);
    if (data.preSignedUrl) {
      const win = window.open(data.preSignedUrl, '_blank');
      if (!win) {
        manualDownloadFileByUrl(data.preSignedUrl, 'Timecard Report');
      }
      SnackbarStore.triggerSnackbar('Timecard report generated successfully.');
    }
  } catch (err) {
    if (axios.isAxiosError(err)) {
      const msg = err.response?.data?.message;
      SnackbarStore.triggerSnackbar(msg, 2500, 'error');
    } else {
      SnackbarStore.triggerSnackbar(
        'Error on generating timecard report.',
        2500,
        'error',
      );
    }
  }
  loadingTcReport.value[batch.id] = false;
};
const pointZeroForBatch = async (batch: Batch) => {
  loadingPointZeroReport.value[batch.id] = true;
  try {
    const { data } = await pointZeroReport(batch.id);
    if (data.preSignedUrl) {
      const newWin = window.open(data.preSignedUrl);
      if (!newWin) {
        const blob = await blobDownload(data.preSignedUrl);
        const downloadUrl = window.URL.createObjectURL(blob);
        const filename = extractCSVFileNameFromUrl(data.preSignedUrl);
        manualDownloadFileByUrl(downloadUrl, filename);
      }
      SnackbarStore.triggerSnackbar('PointZero report generated successfully.');
    }
  } catch (err) {
    if (axios.isAxiosError(err)) {
      const msg = err.response?.data?.message;
      SnackbarStore.triggerSnackbar(msg, 2500, 'error');
    } else {
      SnackbarStore.triggerSnackbar(
        'Error on generating pointzero report.',
        2500,
        'error',
      );
    }
  }
  loadingPointZeroReport.value[batch.id] = false;
};

watch(
  pagination,
  async (newPagination, oldPagination) => {
    if (newPagination.page === oldPagination.page) {
      return;
    }
    const { page } = newPagination;
    const query = { ...props.route.query, page };
    const searchParams = new URLSearchParams();
    Object.entries(query).forEach(([key, value]) => {
      searchParams.set(key, (value as any) || '');
    });
    await props?.navigate?.(
      {
        search: searchParams.toString(),
      },
      { replace: true },
    );
    await load();
  },
  { deep: true },
);

onMounted(async () => {
  load();
});
</script>
