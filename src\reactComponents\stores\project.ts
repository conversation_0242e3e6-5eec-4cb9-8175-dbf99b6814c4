import { makeAutoObservable, action } from 'mobx';
import { configure } from 'mobx';
import type Project from '@/types/Project';
import {
  getProjectByHashId,
  isProjectAdmin,
  listProjectShootLocations,
} from '@/services/project';

import { getProjectMemberById } from '@/services/project-members';
import type ProjectMember from '@/types/ProjectMember';
import type ProjectShootLocation from '@/types/ProjectShootLocation';

import debug from 'debug';

const db = debug('projectStore');

configure({
  enforceActions: 'always',
  computedRequiresReaction: false, // TODO re-enable this after we don't use mobx in VUE
  reactionRequiresObservable: true,
  observableRequiresReaction: false,
  disableErrorBoundaries: true,
});

class ProjectStoreClass {
  private _isAdmin: boolean | null = null;
  private _loading = false;
  private _project: Project | null = null;
  private _ongoingProjFetch: Map<string, Promise<Project | null>> = new Map();
  private _ongoingMemFetch: Map<string, Promise<ProjectMember | null>> =
    new Map();

  private _projectMember: ProjectMember | null = null;
  private _workLocations: ProjectShootLocation[] = [];

  constructor() {
    makeAutoObservable(this, {}, { autoBind: true });
  }

  get isAdmin() {
    return this._isAdmin;
  }
  private set isAdmin(isAdmin: boolean | null) {
    this._isAdmin = isAdmin;
  }

  get project() {
    return this._project;
  }
  set project(project: Project | null) {
    this._project = project;
  }

  get loading() {
    return this._loading;
  }
  private set loading(loading: boolean) {
    this._loading = loading;
  }

  public get workLocations(): ProjectShootLocation[] {
    return this._workLocations;
  }
  public set workLocations(value: ProjectShootLocation[]) {
    this._workLocations = value;
  }

  //PROJECT MEMBER
  public get projectMember(): ProjectMember | null {
    return this._projectMember;
  }
  public set projectMember(value: ProjectMember | null) {
    this._projectMember = value;
  }

  /**
   * Fetches a project by its hashId.
   * If the project is already loaded and forceRefresh is false,
   * it returns the cached project.
   *
   * If there is an ongoing fetch for the same hashId,
   * it returns the ongoing promise.
   *
   * @param hashId - The hashId of the project to fetch
   * @param [forceRefresh=false] - Whether to force a refresh of the project
   * @returns A promise that resolves to the fetched project or null if not found
   */
  fetchProject(hashId: string, forceRefresh = false): Promise<Project | null> {
    if (this._project?.hashId === hashId && !forceRefresh) {
      db('fetchProject: using cached project', this._project);
      return Promise.resolve(this.project);
    }

    if (this._ongoingProjFetch.has(hashId) && !forceRefresh) {
      db('fetchProject: using ongoing fetch');
      return this._ongoingProjFetch.get(hashId)!;
    }

    this.project = null;
    this.isAdmin = null;
    this.loading = true;

    db('fetchProject: fetching new project');
    const fetchPromise = new Promise<Project | null>((resolve, reject) => {
      getProjectByHashId(hashId).then(
        action('fetchSuccess', ({ data: project }) => {
          this.loading = false;
          this.project = project;
          this._ongoingProjFetch.delete(hashId);
          resolve(this.project);
        }),
        action('fetchError', (error) => {
          this.loading = false;
          this.project = null;
          this._ongoingProjFetch.delete(hashId);
          reject(error);
        }),
      );
    });

    this._ongoingProjFetch.set(hashId, fetchPromise);
    return fetchPromise;
  }

  //TODO clear this when project changes
  fetchProjectMember(id: number | string) {
    if (this._projectMember?.id === id) {
      db(
        'fetchProjectMember: using cached project member',
        this._projectMember,
      );
      return Promise.resolve(this._projectMember);
    }

    if (this._ongoingMemFetch.has(String(id))) {
      db('fetchProjectMember: using ongoing fetch');
      return this._ongoingMemFetch.get(String(id))!;
    }

    db('fetchProjectMember: fetching new project member');
    const memberPromise = new Promise<ProjectMember | null>(
      (resolve, reject) => {
        getProjectMemberById(id).then(
          action('fetchSuccess', ({ data }) => {
            this.projectMember = data;
            this._ongoingMemFetch.delete(String(id));
            resolve(this.projectMember);
          }),
          action('fetchError', (error) => {
            this._projectMember = null;
            this._ongoingMemFetch.delete(String(id));
            reject(error);
          }),
        );
      },
    );
    this._ongoingMemFetch.set(String(id), memberPromise);
    return memberPromise;
  }

  fetchIsAdmin(projectId: number, forceRefresh = false): Promise<boolean> {
    if (this.isAdmin !== null && !forceRefresh) {
      db('fetchIsAdmin: using cached admin status', this.isAdmin);
      return Promise.resolve(this.isAdmin);
    }

    db('fetchIsAdmin: fetching new admin status');
    return new Promise((resolve, reject) => {
      isProjectAdmin(projectId).then(
        action('fetchSuccess', ({ data }) => {
          this.isAdmin = data as boolean;
          resolve(this.isAdmin);
        }),
        action('fetchError', (error) => {
          reject(error);
        }),
      );
    });
  }

  async fetchShootLocations(projectId: number | undefined) {
    try {
      if (!projectId && this.project?.id) {
        projectId = this.project.id;
        db('Use current project id for fetchShootLocations', projectId);
      }
      if (!projectId) {
        return;
      }

      const res = await listProjectShootLocations(projectId);
      const { data } = res;
      if (data) {
        this.workLocations = data;
      } else {
        this.workLocations = [];
      }
    } catch (error) {
      console.error(' fetch shoot location error: ', error);
      this.workLocations = [];
    }
  }
}

const ProjectStore = new ProjectStoreClass();

export default ProjectStore;
