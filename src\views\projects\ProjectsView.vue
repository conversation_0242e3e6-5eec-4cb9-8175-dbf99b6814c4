<template>
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pt-20">
    <div class="flex justify-between items-center space-x-1 my-4">
      <h3 class="text-lg font-semibold leading-6 dark:text-gray-200">
        Projects
      </h3>
      <Observer>
        <div
          class="flex space-x-2"
          v-if="PermissionStore.isCompanyAdmin || PermissionStore.isSiteAdmin"
        >
          <!-- <Button
          class="max-w-xs"
          size="sm"
        >
          Join
        </Button> -->
          <Button
            class="max-w-xs"
            size="sm"
            color="gray"
            @click="navigate('/projects/new')"
            data-testid="create-project-btn"
          >
            Create
          </Button>
        </div>
      </Observer>
    </div>
    <ProjectsTable />
  </div>
</template>

<script lang="ts">
import { defineComponent, provide, ref, watchEffect } from 'vue';
import Button from '@/components/library/Button.vue';
import ProjectsTable from '@/components/ProjectsTable.vue';
import PermissionStore from '@/reactComponents/stores/permission';
import { Observer } from 'mobx-vue-lite';

export default defineComponent({
  props: {
    navigate: {
      type: Function,
      required: true,
    },
    route: {
      type: Object,
      required: true,
    },
  },
  components: {
    Button,
    ProjectsTable,
    Observer,
  },
  setup(props: any) {
    const route = ref(props.route);

    watchEffect(() => {
      route.value = props.route;
    });

    provide('navigate', props.navigate);
    provide('route', route);
    return {
      PermissionStore,
    };
  },
});
</script>
