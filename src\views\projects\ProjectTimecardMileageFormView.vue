<template>
  <div class="flex justify-center mx-auto w-full py-2 px-5">
    <div class="max-w-7xl">
      <div>
        <h2 class="text-xl font-bold mb-3">Mileage Form</h2>
        <Toggle v-model="hasMileage" :disabled="editDisabled">
          Has Mileage
        </Toggle>
        <div v-if="hasMileage">
          <TextInput
            v-model="mileageForm.totalMileage"
            class="mt-2"
            label="Total Mileage"
            type="number"
            :disabled="editDisabled"
          />
          <DatePicker
            v-model="mileageForm.date"
            class="mt-2"
            label="Date"
            :disabled="editDisabled"
            @change="getTimecardDayWorkLocation"
          />
          <Dropdown
            class="grow"
            v-model="mileageForm.workLocation"
            label="Work Location"
            display-name="shootLocation.locationName"
            :menu-items="project.projectShootLocations"
          >
            <template #label>
              {{
                `${mileageForm.workLocation?.shootLocation.locationName} (${mileageForm.workLocation?.zip})`
              }}
            </template>
            <template #item="{ value: projectShootLocation }">
              {{
                `${projectShootLocation?.shootLocation.locationName} (${projectShootLocation?.zip})`
              }}
            </template>
          </Dropdown>
          <FileUpload
            v-model="mileageForm.documentId"
            class="my-1"
            :disabled="editDisabled"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import DatePicker from '@/components/library/DatePicker.vue';
import Dropdown from '@/components/library/Dropdown.vue';
import FileUpload from '@/components/library/FileUpload.vue';
import TextInput from '@/components/library/TextInput.vue';
import Toggle from '@/components/library/Toggle.vue';
import {
  deleteMileageForm,
  getMileageForm,
  updateOrCreateMileageForm,
} from '@/services/timecards';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import type Project from '@/types/Project';
import type ShootLocation from '@/types/ShootLocation';
import type Timecard from '@/types/Timecard';
import type TimecardDay from '@/types/TimecardDay';
import axios from 'axios';
import { DateTime } from 'luxon';
import { defineComponent, type PropType } from 'vue';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';

export default defineComponent({
  props: {
    project: {
      type: Object as PropType<Project>,
      required: true,
    },
    timecard: {
      type: Object as PropType<Timecard>,
      required: true,
    },
    editDisabled: {
      type: Boolean,
      default: false,
    },
    route: {
      type: Object as PropType<ParsedRoute>,
      required: true,
    },
    navigate: {
      type: Function as PropType<Function>,
      required: true,
    },
    componentLoaded: {
      type: Function as PropType<Function>,
      required: true,
    },
  },
  components: {
    TextInput,
    Toggle,
    FileUpload,
    DatePicker,
    Dropdown,
  },
  data() {
    return {
      payPeriod: {} as any,
      payPeriods: [] as any[],
      mileageForm: {
        totalMileage: 0 as number,
        date: DateTime.now(),
        workLocation: undefined as ShootLocation | undefined,
        documentId: undefined as number | undefined,
      } as any,
      loadingMileageForm: true,
      hasMileage: false,
    };
  },
  methods: {
    async save() {
      if (this.editDisabled) return;
      if (this.mileageForm.totalMileage === 0) {
        this.hasMileage = false;
      }
      if (this.hasMileage) {
        if (!this.mileageForm.documentId) {
          SnackbarStore.triggerSnackbar(
            'Please upload a document.',
            2500,
            'error',
          );
          throw Error('Please upload a document.');
        }
        try {
          await updateOrCreateMileageForm(
            parseInt(this.route.params.timecardId as string),
            {
              totalMileage: this.mileageForm.totalMileage,
              timecardId: parseInt(this.route.params.timecardId as string),
              date: this.mileageForm.date.toISODate(),
              workLocation: this.mileageForm.workLocation,
              documentId: this.mileageForm.documentId,
            },
          );
          SnackbarStore.triggerSnackbar('Mileage form saved', 2500, 'success');
        } catch (err) {
          if (axios.isAxiosError(err)) {
            const msg = err.response?.data?.['errors']?.[0]?.message;
            SnackbarStore.triggerSnackbar(msg, 2500, 'error');
          } else {
            SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
          }
          throw err;
        }
      }
      if (!this.hasMileage) {
        try {
          await deleteMileageForm(this.timecard.id);
        } catch (err) {
          if (axios.isAxiosError(err)) {
            const msg = err.response?.data?.['errors']?.[0]?.message;
            SnackbarStore.triggerSnackbar(msg, 2500, 'error');
          } else {
            SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
          }
          throw err;
        }
      }
    },
    async saveAndContinue() {
      await this.save();
      const curPath = this.route.location.pathname;
      const newUrl = curPath.replace('mileage', 'kit-rental');
      this.navigate({
        pathname: newUrl,
      });
    },
    getTimecardDayWorkLocation() {
      if (this.mileageForm.date) {
        const workLocation = this.timecard.timecardDays.find(
          (timecardDay: TimecardDay) => {
            return (
              timecardDay.date.toISODate() === this.mileageForm.date.toISODate()
            );
          },
        )?.projectShootLocation;
        if (workLocation) {
          this.mileageForm.workLocation = workLocation;
        }
      }
    },
  },
  async mounted() {
    this.loadingMileageForm = true;
    const { data } = await getMileageForm(
      parseInt(this.route.params.timecardId as string),
    );
    if (data.totalMileage) {
      this.hasMileage = true;
      this.mileageForm = {
        ...data,
        date: DateTime.fromISO(data.date),
      };
    }
    this.loadingMileageForm = false;
    this.componentLoaded();
  },
});
</script>
