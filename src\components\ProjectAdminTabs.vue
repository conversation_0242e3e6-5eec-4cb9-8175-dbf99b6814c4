<template>
  <div class="bg-gray-50 dark:bg-gray-700 w-full hidden sm:block">
    <div
      class="px-4 sm:px-6 lg:px-8 flex items-center justify-start"
      :class="{ 'mx-auto max-w-7xl': !fullWidthHeader }"
    >
      <PrimaryTab
        v-for="tab in tabs"
        :key="tab.label"
        :tab="tab"
        :currentTab="currentTab"
        @tabClick="goTo"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import type Project from '@/types/Project';
import { computed, inject, ref, type Ref } from 'vue';
import PrimaryTab from './library/PrimaryTab.vue';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';

const route = inject('route') as { value: ParsedRoute };
const navigate = inject('navigate') as Function;

const project: Ref<Project> = ref({} as Project);

defineProps(['fullWidthHeader']);

const tabs = computed(() => {
  return [
    {
      label: 'Members',
      key: 'members',
      isMatch: (route: ParsedRoute) => {
        return !route?.params?.timecardId;
      },
      routeName: 'project-admin-members-list',
      routePath: '/projects/:hashId/admin/members',
    },
    // {
    //   label: 'Timecards',
    //   key: 'timecards',
    //   routeName: 'project-admin-timecards',
    //   routePath: '/projects/:hashId/admin/timecards',
    // },
    // {
    //   label: 'Batches',
    //   key: 'batches',
    //   routeName: 'project-admin-batches',
    //   routePath: '/projects/:hashId/admin/batches',
    // },
    {
      label: 'Time',
      key: '/time',
      routeName: 'project-admin-time',
      routePath: '/projects/:hashId/admin/time',
    },
  ];
});

const currentTab = computed(() => {
  return tabs.value.find((tab) =>
    tab?.isMatch
      ? tab?.isMatch?.(route.value) &&
        route?.value?.location?.pathname!.includes(tab.key)
      : route?.value?.location?.pathname!.includes(tab.key),
  );
});

const goTo = (routeTab: any): void => {
  if (navigate) {
    const hashId = route.value.params.hashId;
    const pathname = routeTab.routePath.replace(':hashId', hashId);
    navigate({
      pathname: pathname,
      params: {
        hashId: project.value.hashId,
      },
    });
  }
};
</script>
