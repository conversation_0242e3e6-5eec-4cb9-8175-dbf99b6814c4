import React from 'react';
import PropTypes from 'prop-types';
import { SnackbarContent, closeSnackbar } from 'notistack';

import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import WarningIcon from '@mui/icons-material/Warning';

import { Box, IconButton, Tooltip, db } from '@/reactComponents/library';
import { useDidMount } from '@/reactComponents/utils/customHooks';

const variantIcon = {
  success: <CheckCircleIcon size={30} color="success" />,
  warning: <WarningIcon size={30} color="warning" />,
  error: <CancelIcon size={30} color="error" />,
  info: <HelpOutlineIcon size={30} color="gray" />,
};

const styles = {
  wholeSnack: {
    backgroundColor: 'background.paper',
    display: 'flex',
    width: '100%',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  regularSnack: {
    display: 'flex',
    alignItems: 'center',
    height: '100%',
    justifyContent: 'space-between',
    width: '100%',
  },
  funSizeSnack: {
    display: 'flex',
    alignItems: 'center',
    height: '100%',
    minWidth: '10px',
    backgroundColor: 'blue.400',
  },
};

const SnackbarComp = React.forwardRef(({ ...props }, ref) => {
  const { message, variant = 'info', id } = props;

  useDidMount(() => {
    db('Snack Message', variant, ' : ', message);
  }, []);

  //reposition the root of the snackbar container
  React.useEffect(() => {
    const SnackbarRoots = document.getElementsByClassName(
      'notistack-SnackbarContainer',
    );
    const SnackbarRoot = SnackbarRoots[0];
    if (SnackbarRoot) SnackbarRoot.style.bottom = '80px';
  }, []);

  let icon = variantIcon[variant] || variantIcon.info;

  return (
    <SnackbarContent ref={ref}>
      <Box sx={styles.wholeSnack}>
        <Box sx={styles.regularSnack}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{ pl: 1 }}>{icon}</Box>
            <Box sx={{ p: 2, maxWidth: '400px' }}>{message}</Box>
          </Box>
          <Tooltip title="Dismiss">
            <IconButton onClick={() => closeSnackbar(id)}>
              <CancelIcon />
            </IconButton>
          </Tooltip>
        </Box>
        <Box sx={styles.funSizeSnack}></Box>
      </Box>
    </SnackbarContent>
  );
});

SnackbarComp.propTypes = {
  id: PropTypes.number,
  message: PropTypes.string,
  variant: PropTypes.string,
};

export default SnackbarComp;
