export type ButtonDropdownProps = {
  label: string;
  field: string;
  options: any[];
  values: any[];
  onChange: (value: any, string: string) => void;
  children?: any;
  getOptionLabel?: (option: any) => string;
  isSelectedValue?: (selected: any, option: any) => boolean;
  onSearch?: () => void | any[];
  useTextSearch?: boolean;
  infiniteScrolling?: boolean;
  pagination: { limit: number; page: number };
  // onOpen,
  // onClose,
};

export type DropdownContentProps = {
  optionList: any[];
  selectedOptions: any[];
  isSelectedValue: (selected: any, option: any) => boolean;
  onSelect: (value: any, string: string) => void;
  useTextSearch?: boolean;
  onSearchChange: (search: string) => void;
  textSearch: string;
  getOptionLabel: (option: any) => string;
  isAsyncTextSearch: boolean;
};
