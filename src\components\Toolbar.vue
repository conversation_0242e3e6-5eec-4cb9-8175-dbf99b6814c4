<template>
  <!-- <Observer> -->
  <div class="w-full fixed shadow-sm">
    <Disclosure as="nav" class="top-nav-bg" v-slot="{ open }">
      <div class="mx-auto max-w-7xl px-2 sm:px-6 lg:px-8">
        <div class="relative flex h-16 items-center justify-between">
          <div class="inset-y-0 left-0 flex items-center md:hidden">
            <!-- Mobile menu button-->
            <DisclosureButton
              class="inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
            >
              <span class="sr-only">Open main menu</span>
              <Bars3Icon
                v-if="!open"
                class="block h-6 w-6"
                aria-hidden="true"
              />
              <XMarkIcon v-else class="block h-6 w-6" aria-hidden="true" />
            </DisclosureButton>
          </div>
          <div class="flex items-center justify-center">
            <div>
              <FPSBranding />
            </div>
            <Observer>
              <div v-if="AuthState.isLoggedIn" class="hidden md:block">
                <div class="ml-4 space-x-4 flex items-baseline">
                  <template v-for="item in navigation" :key="item.name">
                    <a
                      v-if="checkNavigationItem(item)"
                      @click="click(item)"
                      class="cursor-pointer px-3 py-2 rounded-md text-md font-medium dark:text-white text-white"
                      :aria-current="item.current ? 'page' : undefined"
                      >{{ item.name }}</a
                    >
                  </template>
                </div>
              </div>
            </Observer>
          </div>
          <div
            class="inset-y-0 right-0 flex items-center pr-2 gap-2 sm:static sm:inset-auto sm:ml-6 sm:pr-0"
          >
            <DarkModeToggle />
            <Observer>
              <template v-if="AuthState.isLoggedIn">
                <NotificationCenter />
                <!-- Profile dropdown -->
                <Menu as="div" class="relative ml-3">
                  <div>
                    <MenuButton
                      class="flex rounded-full bg-gray-800 text-sm focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800"
                    >
                      <span class="sr-only">Open user menu</span>
                      <Avatar
                        :firstName="AuthState.getUser.firstName"
                        :last-name="AuthState.getUser.lastName"
                        bgColor="gray"
                      />
                    </MenuButton>
                  </div>
                  <transition
                    enter-active-class="transition ease-out duration-100"
                    enter-from-class="transform opacity-0 scale-95"
                    enter-to-class="transform opacity-100 scale-100"
                    leave-active-class="transition ease-in duration-75"
                    leave-from-class="transform opacity-100 scale-100"
                    leave-to-class="transform opacity-0 scale-95"
                  >
                    <MenuItems
                      class="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white dark:bg-gray-800 py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                    >
                      <MenuItem v-slot="{ active }">
                        <a
                          @click="navigateTo({ pathname: '/profile' })"
                          :class="getMenuItemCss(active)"
                          >Your Profile</a
                        >
                      </MenuItem>
                      <MenuItem v-slot="{ active }">
                        <a
                          href="https://full-workshop-49c.notion.site/Customer-How-To-Guides-97feada33e944846857bb71c71d1a31b?pvs=74"
                          :class="getMenuItemCss(active)"
                          target="_blank"
                        >
                          <div class="flex space-x-1 items-center">
                            <div>User Manual</div>
                            <ArrowTopRightOnSquareIcon class="h-4 w-4" />
                          </div>
                        </a>
                      </MenuItem>
                      <MenuItem v-slot="{ active }" @click="handleLogout">
                        <a href="#" :class="getMenuItemCss(active)">Log out</a>
                      </MenuItem>
                    </MenuItems>
                  </transition>
                </Menu>
              </template>
            </Observer>
          </div>
        </div>
      </div>
      <DisclosurePanel class="md:hidden">
        <div class="space-y-1 p-2">
          <div
            v-for="(item, itemIndex) in navigation"
            :key="`${item.name}-${itemIndex}`"
          >
            <DisclosureButton
              v-if="hasPermission(item.permission)"
              as="a"
              :aria-current="item.current ? 'page' : undefined"
              class="cursor-pointer block rounded-md px-3 py-2 text-base font-medium"
              :class="{
                'bg-gray-900 text-white': item.current,
                'text-gray-300 hover:bg-gray-700 hover:text-white':
                  !item.current,
              }"
              @click="navigateTo({ pathname: item.pathname })"
            >
              {{ item.name }}
            </DisclosureButton>
          </div>
        </div>
      </DisclosurePanel>
    </Disclosure>
    <!-- </Observer> -->
  </div>
</template>

<script lang="ts">
import { defineComponent, provide } from 'vue';

import DarkModeToggle from '@/components/DarkModeToggle.vue';
import FPSBranding from '@/components/FPSBranding.vue';
import Avatar from '@/components/library/Avatar.vue';
import NotificationCenter from '@/components/NotificationCenter.vue';
import AuthStore from '@/reactComponents/stores/auth';
import PermissionStore from '@/reactComponents/stores/permission';
import SnackbarStore from '@/reactComponents/stores/snackbar';

// import { usePermissionStore } from '@/stores/permission'
// import { useSnackbarStore } from '@/stores/snackbar'

import {
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
  Menu,
  MenuButton,
  MenuItem,
  MenuItems,
} from '@headlessui/vue';
import {
  ArrowTopRightOnSquareIcon,
  Bars3Icon,
  XMarkIcon,
} from '@heroicons/vue/24/outline';
// import { useAppStore } from '../stores/app'
import AppStore from '@/reactComponents/stores/app';
import { Observer } from 'mobx-vue-lite';
// remove routerName field later
const navigation = [
  {
    name: 'Projects',
    routerName: 'projects',
    current: false,
    pathname: '/projects',
  },
  {
    name: 'Companies',
    routerName: 'companies',
    current: false,
    pathname: '/companies',
    validator: () => {
      const { isSiteAdmin, isCompanyAdmin } = PermissionStore.getAdmin();
      return isSiteAdmin || isCompanyAdmin;
    },
  },
  {
    name: 'Users',
    routerName: 'users',
    current: false,
    permission: 'manage_users',
    pathname: '/users',
  },
];

export default defineComponent({
  props: {
    navigate: {
      type: Function,
      default: null,
    },
    okta: {
      type: Object,
      default: null,
    },
  },
  components: {
    Disclosure,
    DisclosureButton,
    DisclosurePanel,
    Menu,
    MenuButton,
    MenuItem,
    MenuItems,
    Bars3Icon,
    XMarkIcon,
    ArrowTopRightOnSquareIcon,
    NotificationCenter,
    DarkModeToggle,
    FPSBranding,
    Avatar,
    Observer,
  },
  computed: {
    // ...mapState(useAuthStore, ['getUser', 'isLoggedIn']),
    // ...mapState(usePermissionStore, ['getPermissions']),
    // getUser() {
    //   return AuthStore.getUser;
    // },
    // isLoggedIn() {
    //   return AuthStore.isLoggedIn;
    // },
    // getPermissions() {
    //   return PermissionStore.getPermissions;
    // },
    // isDarkMode() {
    //   return useAppStore().getIsDarkModeEnabled
    // },
    // isDarkMode() {
    //   return AppStore.getIsDarkModeEnabled
    // }
  },
  setup(props, ctx) {
    provide('navigate', props.navigate);

    const AuthState = AuthStore;
    const PermissionState = PermissionStore;
    const AppState = AppStore;

    return {
      AuthState,
      PermissionState,
      AppState,
    };
  },
  data() {
    return {
      navigation,
    };
  },
  methods: {
    async logout() {
      const { okta } = this;
      if (okta) await okta.logout();

      AuthStore.logout();
    },
    hasPermission(permission: string | undefined) {
      return PermissionStore.hasPermission(permission);
    },
    clearPermissions() {
      PermissionStore.clearPermissions();
    },
    triggerSnackbar(message: string) {
      SnackbarStore.triggerSnackbar(message);
    },
    async handleLogout() {
      await this.logout();
      this.triggerSnackbar('Logged out successfully.');
      this.navigateTo({ pathname: '/login' });
      this.clearPermissions();
    },
    click(item: any) {
      this.navigateTo({ pathname: item.pathname });
    },
    navigateTo(to: string | object) {
      this.navigate(to);
    },
    getMenuItemCss(active: boolean) {
      return [
        active ? 'bg-gray-100 dark:bg-gray-500' : '',
        'block px-4 py-2 text-sm cursor-pointer',
      ];
    },
    checkNavigationItem(item: {
      permission?: string;
      validator?: Function;
    }): boolean {
      let ok: boolean = !item.permission || this.hasPermission(item.permission);
      if (!item.validator) return ok;

      return ok && item.validator();
    },
  },
  mounted() {},
});
</script>
<style>
.top-nav-bg {
  background: linear-gradient(
    91deg,
    #330570 10.27%,
    #611cba 39.95%,
    #7022bd 74.21%,
    #7c0f9b 99.08%
  );
  border-bottom: 1px solid rgba(143, 22, 224, 0.35);
}
</style>
