import React from 'react';
import PropTypes from 'prop-types';

import { Box } from '@/reactComponents/library';
import WarningAmberRoundedIcon from '@mui/icons-material/WarningAmberRounded';

import CalcIssueModal from '@/reactComponents/viewsV2/ProjectAdminMemberTimecardV2/CalcIssueModal';

const styles = {
  viewLink: {
    textDecoration: 'underline',
    color: 'info.main',
    cursor: 'pointer',
  },
  errorWarningBar: {
    position: 'sticky',
    width: '100%',
    height: '30px',
    top: '0px',
    zIndex: 18,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderTop: '1px solid',
    borderBottom: '1px solid',
    gap: 1,
    backgroundColor: 'warning.100',
    color: 'warning.700',
    borderColor: 'warning.500',
  },
  errorMsg: {
    backgroundColor: 'error.200',
    color: 'error.700',
    borderColor: 'error.500',
  },
};

const ErrorBar = (props) => {
  const { calcIssues } = props;

  const [calcIssueModalOpen, setCalcIssueModalOpen] = React.useState(false);

  const showIssueMsg = calcIssues?.length > 0;

  const hasErrors = calcIssues.some((issue) => issue.isError);

  const issueColor = hasErrors ? 'error.500' : 'warning.500';

  const warningText = hasErrors
    ? 'There are calculation errors and warnings'
    : 'There are calculation warnings';

  let issueMsgStyle = {
    ...styles.errorWarningBar,
    color: issueColor,
  };
  if (hasErrors) {
    issueMsgStyle = {
      ...issueMsgStyle,
      ...styles.errorMsg,
    };
  }

  return (
    <>
      {' '}
      {showIssueMsg > 0 && (
        <Box sx={issueMsgStyle}>
          <WarningAmberRoundedIcon />
          {warningText}
          <Box onClick={() => setCalcIssueModalOpen(true)} sx={styles.viewLink}>
            View
          </Box>
        </Box>
      )}
      <CalcIssueModal
        open={calcIssueModalOpen}
        setOpen={setCalcIssueModalOpen}
        calcIssues={calcIssues}
      />
    </>
  );
};

ErrorBar.propTypes = {
  calcIssues: PropTypes.array.isRequired,
};

export default ErrorBar;
