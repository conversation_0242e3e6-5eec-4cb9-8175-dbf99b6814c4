import { faker } from '@faker-js/faker';
import CompanyPage from '../../pageObjects/company/companyPage';
import CompanyList from '../../pageObjects/company/companyList';
import { interceptCreateCompany } from '../../support/apiCompanyInterceptors';
import CompanyView from '../../pageObjects/company/companyView';

describe('User Supervisor - Company Creation', () => {
  const companyList = new CompanyList();
  const companyPage = new CompanyPage();
  const companyView = new CompanyView();
  const companyName = `TEST-${faker.company.name()}`;
  const companyPhone = '**********';
  const state = 'California';
  const zipCode = '90001';
  const address = '2300 Empire Avenue';
  const city = 'Burbank';
  const taxClassification = 'Individual/sole proprietor or single-member LLC';
  const payFrequency = 'Weekly';
  const payDay = 'Monday';
  const addresses = `${address}, ${city}, ${state} ${zipCode}`;

  beforeEach(() => {
    // ----------- Arrange: Login as Supervisor -----------
    cy.log('Arrange: Login as Supervisor');
    cy.loginWithSession(
      Cypress.env('PHONE_NUMBER'),
      Cypress.env('OTC_CODE'),
      Cypress.env('BASE_URL'),
      'supervisor',
    );
  });

  it('Verify Supervisor is able to create a Client/Company admin', () => {
    // ----------- Arrange: Visit Companies page -----------
    cy.log('Arrange: Visit Companies page');
    cy.visit(`${Cypress.env('BASE_URL')}companies`);

    // ----------- Arrange: Intercept company creation API -----------
    cy.log('Arrange: Intercept company creation API');
    interceptCreateCompany();

    // ----------- Action: Click create company button -----------
    cy.log('Action: Click create company button');
    companyList.clickCreateCompanyButton();

    // ----------- Action: Fill out company form -----------
    cy.log('Action: Fill out company form');
    companyPage.fillOutCompanyForm(
      companyName,
      companyPhone,
      address,
      city,
      state,
      zipCode,
      taxClassification,
      payFrequency,
      payDay,
    );

    // ----------- Action: Submit create company -----------
    cy.log('Action: Submit create company');
    companyPage.submitCreateCompany();

    // ----------- Assert: Validate API response for company creation -----------
    cy.log('Assert: Validate API response for company creation');
    cy.wait('@createdCompany').then((interception) => {
      expect(interception.response?.statusCode).to.equal(200);
      expect(interception.response?.body.name).to.equal(companyName);
      expect(interception.response?.body.phone).to.equal(companyPhone);
      expect(interception.response?.body.addresses[0].zip).to.equal(zipCode);
      expect(interception.response?.body.addresses[0].street).to.equal(address);
      expect(interception.response?.body.addresses[0].city).to.equal(city);
      expect(interception.response?.body.taxClassificationId).to.equal(1);
      expect(interception.response?.body.payDayId).to.equal(1);
      expect(interception.response?.body.payDayFrequencyId).to.equal(1);

      // ----------- Assert: Validate company details in UI -----------
      cy.log('Assert: Validate company details in UI');
      companyList.goToProjectDetails(companyName);
      companyView.companyDashboardPage(
        companyPhone,
        addresses,
        taxClassification,
      );
    });
  });
});
