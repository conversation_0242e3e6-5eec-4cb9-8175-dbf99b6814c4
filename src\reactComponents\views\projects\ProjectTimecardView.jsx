import React from 'react';

import { VueContainer } from 'veaury';

import { useAuth, useReactRouter } from '../../AppHooks';
import { useOutletContext, Outlet } from 'react-router';
import ButtonVue from '../../../components/library/Button.vue';

import { getTimecard } from '../../../services/timecards';

import { useDidMount } from '@/reactComponents/utils/customHooks';

import ProjectStore from '@/reactComponents/stores/project';

const TimecardStatusId = {
  InProgress: 1,
  Submitted: 2,
  RequestedChanges: 3,
  Approved: 4,
};

// the only way to override default veaury wrapper style
// in veaury's source codes options.js file
const customOptions = {
  react: {
    componentWrap: 'div',
    slotWrap: 'div',
    componentWrapAttrs: {
      __use_react_component_wrap: '',
      style: {
        // all: 'unset',
      },
    },
    slotWrapAttrs: {
      __use_react_slot_wrap: '',
      style: {
        // all: 'unset',
      },
    },
    vueNamedSlotsKey: ['node:'],
  },
  vue: {
    // wrapper
    componentWrapHOC: (VueComponentMountAt) => {
      // portals
      return function ({ portals = [] } = {}) {
        return (
          <>
            {VueComponentMountAt}
            {portals.map(({ Portal, key }) => (
              <Portal key={key} />
            ))}
          </>
        );
      };
    },
    componentWrapAttrs: {
      'data-use-vue-component-wrap': '',
      style: {
        // all: 'unset',
      },
    },
    slotWrapAttrs: {
      'data-use-vue-slot-wrap': '',
      style: {
        // all: 'unset',
      },
    },
  },
};

const ProjectTimecardView = () => {
  useAuth();
  const { route, navigate } = useReactRouter();
  const context = useOutletContext();
  const project = context.project;

  const [timecard, setTimecard] = React.useState({});
  const [projectMember, setProjectMember] = React.useState({});
  const [loaded, setLoaded] = React.useState(false);
  const [editDisabled, setEditDisabled] = React.useState(false);
  const [saveAndExitLoading, setSaveAndExitLoading] = React.useState(false);
  const [saveAndContinueLoading, setSaveAndContinueLoading] =
    React.useState(false);
  const [componentDataLoaded, setComponentDataLoaded] = React.useState(false);
  const view = React.useRef();

  const exitText = React.useMemo(() => {
    if (editDisabled) {
      return 'Exit';
    }
    return 'Save & Exit';
  }, [editDisabled]);

  const isCompleteStage = React.useMemo(() => {
    return route?.location?.pathname?.includes?.('/complete');
  }, [route]);

  const continueText = React.useMemo(() => {
    if (editDisabled) {
      if (route?.location?.pathname?.includes?.('/complete')) {
        return 'Return';
      }
      return 'Continue';
    }
    if (route?.location?.pathname?.includes?.('/review')) {
      return 'Submit';
    }
    if (route?.location?.pathname?.includes?.('/complete')) {
      return 'Return';
    }
    return 'Save & Continue';
  }, [editDisabled, route]);

  const submitDisabled = React.useMemo(() => {
    if (editDisabled) {
      return false;
    }
    return !componentDataLoaded;
  }, [editDisabled, componentDataLoaded]);

  const exit = async () => {
    setSaveAndExitLoading(true);
    try {
      if (!isCompleteStage) {
        await view.current?.vueRef?.save();
      }
      navigate({
        pathname: `/projects/${project.hashId}/timecards`,
      });
    } catch (err) {
      console.warn('Exit failed', err);
    }
    setSaveAndExitLoading(false);
  };

  const saveAndContinue = async () => {
    setSaveAndContinueLoading(true);
    try {
      await view.current?.vueRef?.saveAndContinue();
      setComponentDataLoaded(false);
    } catch (err) {
      console.warn('Save failed', err);
    }
    setSaveAndContinueLoading(false);
  };

  const back = async () => {
    navigate(-1);
    setComponentDataLoaded(false);
  };
  const componentInitialDataLoaded = async () => {
    setComponentDataLoaded(true);
  };

  const load = async () => {
    setLoaded(false);
    const { data } = await getTimecard(parseInt(route.params.timecardId));

    data &&
      setTimecard({
        ...data,
        timecardDays: data.timecardDays.map((timecardDay) => {
          return {
            ...timecardDay,
            date: timecardDay.date.toUTC(),
          };
        }),
      });

    const projectMemberData = await ProjectStore.fetchProjectMember(
      data.projectMemberId,
    );
    setProjectMember(projectMemberData);
    setLoaded(true);
    setEditDisabled(
      data.status.id === TimecardStatusId.Submitted ||
        data.status.id === TimecardStatusId.Approved,
    );
  };

  const newContext = {
    project: project,
    timecard: timecard,
    projectMember: projectMember,
    refresh: load,
    componentLoaded: componentInitialDataLoaded,
    editDisabled: editDisabled,
    ref: view,
  };

  useDidMount(() => {
    load();
  }, []);

  return (
    <div className="max-w-lg mx-auto">
      {project?.id && <Outlet context={newContext} />}
      {loaded && (
        <div className="fixed bottom-0 left-0 z-10 flex mx-auto w-full pb-5 pt-2 py-2 px-5 bg-gray-50 dark:bg-gray-700">
          <div className="container mx-auto">
            <div className="flex justify-center space-x-2 pt-3">
              {!isCompleteStage && !editDisabled && (
                <VueContainer
                  component={ButtonVue}
                  className="max-w-sm space-x-2"
                  color="gray"
                  click={back}
                  veaury-options={customOptions}
                >
                  <span className="font-semibold">Back</span>
                </VueContainer>
              )}
              <VueContainer
                component={ButtonVue}
                className="max-w-sm"
                color={isCompleteStage ? 'main' : 'secondary'}
                click={exit}
                loading={saveAndExitLoading}
                disabled={submitDisabled}
                veaury-options={customOptions}
              >
                <span className="font-semibold">{exitText}</span>
              </VueContainer>
              {!isCompleteStage && !editDisabled && (
                <VueContainer
                  component={ButtonVue}
                  className="max-w-sm space-x-2"
                  click={saveAndContinue}
                  color="primary"
                  loading={saveAndContinueLoading}
                  disabled={submitDisabled}
                  veaury-options={customOptions}
                  data-testid="timecard-saveContinue-btn"
                >
                  <span className="font-semibold">{continueText}</span>
                </VueContainer>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProjectTimecardView;
