/// <reference types="cypress" />

import { faker } from '@faker-js/faker';
import BatchModals from '../../pageObjects/timecards/batchModals';
import TimecardModal from '../../pageObjects/timecards/timecardModal';
import {
  interceptAssignTimecardToBatch,
  interceptCreateBatch,
  interceptCreateTimecard,
  interceptDeleteTimecardFromBatch,
  interceptTimecardsBatch,
} from '../../support/apiTimecardInterceptors';
import {
  deleteBatch,
  deleteTimecard,
  fetchProjectIdentifiersByName,
} from '../../support/apiHelpers';

const batchModals = new BatchModals();
const timecardModal = new TimecardModal();

function formatPayPeriod(start: string, end: string): string {
  const [startYear, startMonth, startDay] = start.split('-').map(Number);
  const [endYear, endMonth, endDay] = end.split('-').map(Number);
  const startDate = new Date(startYear, startMonth - 1, startDay);
  const endDate = new Date(endYear, endMonth - 1, endDay);
  const formattedStart = `${String(startDate.getMonth() + 1).padStart(
    2,
    '0',
  )}/${String(startDate.getDate()).padStart(2, '0')}/${String(
    startDate.getFullYear(),
  ).slice(-2)}`;
  const formattedEnd = `${String(endDate.getMonth() + 1).padStart(
    2,
    '0',
  )}/${String(endDate.getDate()).padStart(2, '0')}/${String(
    endDate.getFullYear(),
  ).slice(-2)}`;
  return `${formattedStart} - ${formattedEnd}`;
}

function randomBatchName() {
  return `batch-${faker.number.int({ min: 1000, max: 9999 })}`;
}

function setupInterceptors() {
  interceptCreateBatch();
  interceptCreateTimecard();
  interceptAssignTimecardToBatch();
  interceptDeleteTimecardFromBatch();
}

function goToProjectTimeTab() {
  cy.visit(
    `${Cypress.env('BASE_URL')}projects/${Cypress.env(
      'projectHashId',
    )}/admin/time`,
  );
}

describe('User Project Admin - Timecard-Batch Actions', () => {
  const employeeName = Cypress.env('employeeName');
  const projectName = Cypress.env('projectName');
  let batchName: string;
  let projectId = '';

  beforeEach(() => {
    batchName = randomBatchName();

    cy.loginWithSession(
      Cypress.env('PHONE_NUMBER_PROJECT_ADMIN'),
      Cypress.env('OTC_CODE'),
      Cypress.env('BASE_URL'),
      'project-admin',
    );
    //Get Details by Api before test
    cy.then(() => {
      // Step 1: Get the project Id by project Name
      return fetchProjectIdentifiersByName(projectName).then((project) => {
        if (!project || !project.id) {
          throw new Error('Project not found or invalid Project ID');
        }
        projectId = project.id;
        Cypress.env('projectId', projectId);

        cy.log(`Project ID set: ${projectId}`);
        cy.log(`Project Hash id set: ${project.hashId}`);
        Cypress.env('projectHashId', project.hashId);
      });
    });
  });

  afterEach(() => {
    deleteTimecard(Cypress.env('timecardId')).then(() => {
      deleteBatch(Cypress.env('batchId'));
    });
  });

  //https://castandcrew.atlassian.net/browse/FPS-1528
  //https://castandcrew.atlassian.net/browse/FPS-1525
  it('Verify Project Admin can Assign and Disassociate Batch from a Timecard', () => {
    // ----------- Arrange: Go to Project Time Tab -----------
    cy.log('Arrange: Go to Project Time Tab');
    goToProjectTimeTab();
    setupInterceptors();

    // ----------- Action: Create Batch By UI -----------
    cy.log('Action: Create Batch By UI');
    batchModals.createBatch(batchName);

    // ----------- Assert: Verify Batch Created by API-----------
    cy.log('Assert: Verify Batch Created');
    cy.wait('@createdBatch').then((batchIntercept) => {
      const batchId = batchIntercept.response?.body.id;
      Cypress.env('batchId', batchId);

      // ----------- Action: Create Timecard By UI -----------
      cy.log('Action: Create Timecard By UI');
      timecardModal.createTimecard(employeeName);

      // ----------- Action: Assign Timecard to Batch  -----------
      cy.log('Action: Assign Timecard to Batch');
      batchModals.moveToBatchWithSelection(batchName);

      // ----------- Assert: Verify Timecard Assigned by API -----------
      cy.log('Assert: Verify Timecard Assigned');
      cy.wait('@createTimecard').then((intercept) => {
        const timecardId = intercept.response?.body.id;
        Cypress.env('timecardId', timecardId);

        cy.wait('@assignedTimecardFromBatch').then((assignIntercept) => {
          expect(
            assignIntercept.response?.statusCode,
            'Assign status code',
          ).to.equal(200);
          expect(
            assignIntercept.response?.body.id,
            'Assigned timecard id',
          ).to.eq(timecardId);
        });

        // ----------- Assert: Verify Timecard details in Batch by UI -----------
        interceptTimecardsBatch(Cypress.env('projectId'), batchId);
        cy.log('Assert: Verify Timecard details in Batch by UI');
        batchModals.goBackFromTimecard();
        cy.wait('@getTimecardsBatch').then((batchDetailsIntercept) => {
          const timecardDetails = batchDetailsIntercept.response?.body.data[0];
          const occupation = timecardDetails.occupation.name;
          const department = timecardDetails.projectMember.department.type.name;
          const gross = timecardDetails.gross ?? 0.0;
          const payPeriod = formatPayPeriod(
            timecardDetails.payPeriod.startsAt,
            timecardDetails.payPeriod.endsAt,
          );
          batchModals.validateBatchDetails(
            timecardId,
            'Submitted',
            employeeName,
            occupation,
            department,
            payPeriod,
            gross,
          );

          // ----------- Action: Remove Timecard from Batch -----------
          cy.log('Action: Remove Timecard from Batch');
          batchModals.removeTimecardFromBatch(timecardId);

          // ----------- Assert: Verify Timecard Removed By API-----------
          cy.log('Assert: Verify Timecard Removed');
          cy.wait('@deletedTimecardFromBatch').then((deleteIntercept) => {
            expect(
              deleteIntercept.response?.statusCode,
              'Delete status code',
            ).to.equal(200);
            expect(
              deleteIntercept.response?.body.id,
              'Removed timecard id',
            ).to.eq(timecardId);
            // ----------- Assert: Verify Timecard Removed By UI-----------
            batchModals.validateTimecardRemovedFromBatch(timecardId);
            cy.log(
              'Success: Verify Project Admin can Assign and Disassociate Batch from a Timecard',
            );
          });
        });
      });
    });
  });
});
