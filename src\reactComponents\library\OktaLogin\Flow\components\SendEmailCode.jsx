import React from 'react';
import PropTypes from 'prop-types';

import { Box, Button, Loader, Text } from '@/reactComponents/library';

import { <PERSON>ie } from '@/utils/cookie';

import {
  STEP_VERIFY_CODE_EMAIL,
  getTransactionStep,
  getTransactionId,
  authApiError,
} from '../../utils';
import OktaLoginContext from '../../Context';

const styles = {
  root: {
    display: 'flex',
    flexDirection: 'column',
    gap: 2,
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',
  },
  error: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: 2,
    px: 2,
  },
};

const SendEmailCode = ({ onSuccess }) => {
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState();
  const {
    client: authClient,
    transaction,
    setTransaction,
  } = React.useContext(OktaLoginContext);

  const setChallengeSending = (val) => Cookie.set('challengeSending', val);
  const isChallengeSending = () => Cookie.get('challengeSending') === 'true';

  React.useEffect(() => {
    if (isChallengeSending()) return;

    const sendChallengeCode = async () => {
      try {
        setLoading(true);
        setError(null);

        const transactionId = getTransactionId(transaction, 'email');
        if (!transactionId) throw new Error('No transaction ID found');

        const response = await authClient.proceed({
          authenticator: {
            methodType: 'email',
            id: transactionId,
          },
        });
        const nextStep = getTransactionStep(response);
        setTransaction(response);
        if (nextStep !== STEP_VERIFY_CODE_EMAIL) {
          window.setTimeout(() => setChallengeSending(false), 500);
          const error = authApiError(response);
          throw new Error(error || `Invalid Response: ${nextStep}`);
        }

        onSuccess({ nextStep });
      } catch (e) {
        console.warn('sendEmailCode:error', e.message || JSON.stringify(e));
        setError(e.message);
      } finally {
        setChallengeSending(false);
        setLoading(false);
      }
    };

    setChallengeSending(true);
    window.setTimeout(sendChallengeCode, 200);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Box sx={styles.root} data-testid="login-sendSMS-root">
      <Text variant="baseSemi">Seems like this is new device.</Text>
      {loading && <Loader message="Sending Email code" />}
      {!loading && error && (
        <Box sx={styles.error}>
          <Text color="error">{error}</Text>
          <Button
            onClick={async () => {
              window.location.reload();
            }}
          >
            Back
          </Button>
        </Box>
      )}
    </Box>
  );
};

SendEmailCode.propTypes = {
  onSuccess: PropTypes.func.isRequired,
};

export default SendEmailCode;
