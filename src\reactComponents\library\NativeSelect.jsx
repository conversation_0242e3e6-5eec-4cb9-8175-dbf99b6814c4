import React from 'react';
import PropTypes from 'prop-types';

import { tailwindInputFix } from '../theme/components/utils';

import {
  Box,
  InputLabel,
  NativeSelect as NativeSelectMUI,
} from '@/reactComponents/library';

/**
 * //TODO - this doesn't take the standard InputProps, so styling it is a bit of a pain
 *   //will address later if needed, but hoping to not need this component at all.
 *
 * This is a wrapper around the MUI NativeSelect component and is not nearly as feature rich as the
 * autocomplete component. It should only be used when autocomplete is not working on mobile devices.
 * @param {*} props
 * @returns
 */
const NativeSelect = (props) => {
  let {
    options = [],
    label = '',
    inputProps,
    InputProps,
    onChange,
    ...rest
  } = props;

  if (!inputProps) {
    inputProps = {};
  }
  if (!inputProps.className) {
    inputProps.className = '';
  }
  if (inputProps.className.includes(tailwindInputFix.trim()) === false) {
    inputProps.className += tailwindInputFix;
  }

  const defaultValue = options.length > 0 ? options[0].value : '';

  const handleChange = (event) => {
    const newValue = event.target.value;
    const selectedOption = options.find((option) => option.value === newValue);
    onChange(selectedOption);
  };

  return (
    <Box sx={{ minWidth: 120 }}>
      <InputLabel htmlFor="uncontrolled-native">{label}</InputLabel>
      <NativeSelectMUI
        sx={{
          border: '1px solid #e2e8f0',
          borderRadius: '8px',
          p: '4px 16px',
        }}
        inputProps={inputProps}
        defaultValue={defaultValue}
        onChange={handleChange}
        {...rest}
      >
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </NativeSelectMUI>
    </Box>
  );
};

NativeSelect.propTypes = {
  options: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string,
      label: PropTypes.string,
    }),
  ),
  label: PropTypes.string,
  inputProps: PropTypes.object,
  InputProps: PropTypes.object,
  onChange: PropTypes.func,
};

export default NativeSelect;
