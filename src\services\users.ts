import type { Pagination } from '@/types/Pagination';
import type ProjectMemberInvite from '@/types/ProjectMemberInvite';
import type { Tableview } from '@/types/Tableview';
import type User from '@/types/User';
import type UserCrew from '@/types/UserCrew';
import axios, { type AxiosResponse } from 'axios';
import { coreBaseUrl } from '../utils/constants';

export const enum PermissionKey {}

export const getUser = async (
  id: 'me' | number,
): Promise<AxiosResponse<User>> => {
  const url = `${coreBaseUrl()}/users/${id}`;
  const response = await axios.get(url, {
    withCredentials: true,
  });
  return response;
};

export const getMe = async (
  token: string,
  userData: { phone: string; email: string },
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/users/me`;
  const response = await axios.post(url, userData, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response;
};

export const updateUser = async (
  id: 'me' | number,
  data: User,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/users/${id}`;
  const response = await axios.patch(url, data, {
    withCredentials: true,
  });
  return response;
};

export const deleteUser = async (id: number): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/users/${id}`;
  const response = await axios.delete(url, {
    withCredentials: true,
  });
  return response;
};

export const getUserPermissions = async (): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/roles/permissions`;
  const response = await axios.get(url, {
    withCredentials: true,
  });
  return response;
};

export const listUsers = async (
  pagination?: Pagination,
  filters?: string,
  sorts?: string,
): Promise<AxiosResponse<Tableview<User>>> => {
  const url = `${coreBaseUrl()}/users`;
  const response = await axios.get(url, {
    withCredentials: true,
    params: {
      ...pagination,
      filters,
      sorts,
    },
  });
  return response;
};

export const inviteUserToProject = async (
  payload: ProjectMemberInvite,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/users/invite`;
  const response = await axios.post(url, payload, {
    withCredentials: true,
  });
  return response;
};

export const getInvite = async (
  inviteCode: string,
): Promise<AxiosResponse<ProjectMemberInvite>> => {
  const url = `${coreBaseUrl()}/users/invite/${inviteCode}`;
  const response = await axios.get(url, {
    withCredentials: true,
  });
  return response;
};

export const getUserCrewByUserId = async (
  userId: string | number,
): Promise<AxiosResponse<UserCrew>> => {
  const url = `${coreBaseUrl()}/users/${userId}/user-crew`;
  const response = await axios.get(url, {
    withCredentials: true,
  });
  return response;
};
