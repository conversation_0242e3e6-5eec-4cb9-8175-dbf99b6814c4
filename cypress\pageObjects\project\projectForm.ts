import { DateFormatter } from '../../utils/DateFormatter';

export default class Project {
  projectNameInput: string = '[data-testid="project-name-input"]';
  projectNumberInput: string = '[data-testid="project-number-input"]';
  projectDescriptionInput: string = '[data-testid="project-description-input"]';
  projectStarsOnDatePicker: string =
    '[data-testid="project-startsOn-datePicker"]';
  projectEndsOnDatePicker: string = '[data-testid="project-endsOn-datePicker"]';
  projectMinuteIncrementInput: string =
    '[data-testid="project-minuteIncrements-dropdown"]';
  projectMileageNumberInput: string =
    '[data-testid="project-mileageAicpNumber-input"]';
  projectTaxToggle: string = '[data-testid="project-tax-toggle"]';
  productionDropdown: string = '[data-testid="production-company-combobox"]';
  productionAddressDropdown: string =
    '[data-testid="production-address-dropdown"]';
  productionProjectType: string = '[data-testid="project-type-dropdown"]';
  locationAddButton: string = '[data-testid="location-add-btn"]';
  searchLocationInput: string = '[data-testid="location-search-input"]';
  confirmLocationButton: string = '[data-testid="location-confirm-btn"]';
  departmentsAddButton: string = '[data-testid="department-add-btn"]';
  departmentDropdown: string = '[data-testid="department-select-dropdown"]';
  departmentConfirmButton: string = '[data-testid="department-confirm-btn"]';
  confirmCreateProjectButton: string = '[data-testid="project-create-btn"]';
  settingProjectButton: string = '[data-testid="project-setting-btn"]';
  editProjectButton: string = '[data-testid="Edit project-btn"]';
  DayDefaultsProjectButton: string = '[data-testid="Day defaults-btn"]';
  customPaperworkProjectButton: string = '[data-testid="Custom paperwork-btn"]';

  goToEditProject() {
    cy.get(this.settingProjectButton).should('be.visible').click();
    cy.get(this.editProjectButton).should('be.visible').click();
  }

  goToCustomPaperworkProject() {
    cy.get(this.settingProjectButton).should('be.visible').click();
    cy.get(this.customPaperworkProjectButton).should('be.visible').click();
  }

  goToDayDefaultsProject() {
    cy.get(this.settingProjectButton).should('be.visible').click();
    cy.get(this.DayDefaultsProjectButton).should('be.visible').click();
  }

  private addProductionCompany(
    company: string,
    address: string,
    projectType: string,
  ) {
    cy.pickDropdownOption(this.productionDropdown, company);
    cy.pickDropdownOption(this.productionAddressDropdown, address);
    cy.get('div.animate-pulse').should('not.exist');
    cy.pickDropdownOption(this.productionProjectType, projectType);
  }

  private addWorkLocation(city: string) {
    cy.get(this.locationAddButton).click();
    cy.pickSearchOption(this.searchLocationInput, city);
    cy.get(this.confirmLocationButton).click();
    cy.get(this.confirmLocationButton).should('not.exist');
  }

  private addDepartment(department: string) {
    cy.get(this.departmentsAddButton).click();
    cy.pickDropdownOption(this.departmentDropdown, department);
    cy.get(this.departmentConfirmButton).click().should('not.exist');
  }

  fillOutProjectForm(
    projectName: string,
    projectId: string,
    description: string,
    duration: string,
    mileageAicpNumber: string,
    startsOn: Date,
    endsOn: Date,
    company: string,
    address: string,
    projectType: string,
    city: string,
    department: string,
  ) {
    cy.get(this.projectNameInput).type(projectName);
    cy.get(this.projectNumberInput).type(projectId);
    cy.get(this.projectDescriptionInput).type(description);

    cy.selectDay(
      this.projectStarsOnDatePicker,
      DateFormatter.formatDate(startsOn),
    );
    cy.selectDay(
      this.projectEndsOnDatePicker,
      DateFormatter.formatDate(endsOn),
    );
    cy.pickDropdownOption(this.projectMinuteIncrementInput, duration);
    cy.get(this.projectMileageNumberInput).type(mileageAicpNumber);
    cy.get(this.projectTaxToggle).click();
    cy.pickDropdownOption(this.projectMinuteIncrementInput, duration);
    cy.get(this.projectMileageNumberInput).type(mileageAicpNumber);
    cy.get(this.projectTaxToggle).click();

    this.addProductionCompany(company, address, projectType);

    this.addWorkLocation(city);

    this.addDepartment(department);
    cy.get(this.confirmCreateProjectButton).click();
  }

  fillOutProjectEdit(
    description: string,
    mileageAicpNumber: string,
    city: string,
  ): void {
    cy.get(this.projectDescriptionInput)
      .find('textarea')
      .should('be.visible')
      .should('not.be.disabled')
      .then(($textarea) => {
        cy.wrap($textarea)
          .invoke('val', '', { delay: 15 })
          .trigger('input')
          .type(description)
          .should('have.value', description);
      });

    cy.get(this.projectMileageNumberInput)
      .find('input, textarea')
      .should('be.visible')
      .clear()
      .type(mileageAicpNumber);

    this.addWorkLocation(city);

    cy.get(this.confirmCreateProjectButton).click();
  }
}
