import React from 'react';
import PropTypes from 'prop-types';

import {
  // LoanoutsLegacy,
  PlatformProvider,
  StateForms,
  // DirectDepositLegacy,
} from '@castandcrew/platform-components';

import { PageBackgroundBox } from '../../styledComponents';

import config from '@/vite-env';

const BASE_URL = config.okta.baseUrl;
const PLATFORM_CONFIG_PROFILE = {
  okta: {
    clients: {
      fps: {
        url: BASE_URL,
        clientId: config.okta.clientId,
        issuer: config.okta.issuer,
        devMode: true,
      },
    },
  },
  api: {
    dashboard: { url: config.apiDashboardUrl },
  },
};

// const directDepositConfig = {
//   serverUrl:
//     'https://jjdstnjwch-vpce-063282340477e592a.execute-api.us-west-2.amazonaws.com/qa',
// };

const LoanOutComp = (props) => {
  const { state = 'CA' } = props;

  const fedConfig = {
    variant: 'large',
    client: 'fps',
    contentFullWidth: true,
    onSuccess: (formData) => {
      console.warn('formData: ', formData);
      //will be called by Profile component when the fed form is successfully submitted and ready to move on
    },
    onError: (reason, error) => {
      console.error(reason, error);
      //will be called by Profile component when the fed form is submitted and there is an error
    },
    formId: 'Platform-StateForm-Federal',
    hideCta: true,
  };

  const stateConfig = {
    variant: 'large',
    client: 'fps',
    contentFullWidth: true,
    onSuccess: (formData) => {
      console.warn('formData: ', formData);
      //will be called by Profile component when the state form is successfully submitted and ready to move on
    },
    onError: (reason, error) => {
      console.error(reason, error);
      //will be called by Profile component when the state form is submitted and there is an error
    },
    formId: 'Platform-StateForm-State',
    hideCta: true,
  };

  return (
    <PageBackgroundBox>
      <h1>Tax Details</h1>
      <button
        onClick={() => {
          const fedForm = document.getElementById('Platform-StateForm-Federal');
          if (fedForm) {
            fedForm.requestSubmit();
          }
        }}
      >
        Submit Fed
      </button>
      <button
        onClick={() => {
          const stateForm = document.getElementById('Platform-StateForm-State');
          if (stateForm) {
            stateForm.requestSubmit();
          }
        }}
      >
        Submit State
      </button>
      <PlatformProvider config={PLATFORM_CONFIG_PROFILE}>
        <StateForms stateCode={'FED'} config={fedConfig} />
        <StateForms stateCode={state} config={stateConfig} />
      </PlatformProvider>
    </PageBackgroundBox>
  );
};

LoanOutComp.propTypes = {
  state: PropTypes.string.isRequired,
};

export default LoanOutComp;
