import React from 'react';

import { PlatformProvider, StateForms } from '@castandcrew/platform-components';

import { PageBackgroundBox } from '../../styledComponents';

import config from '@/vite-env';

const BASE_URL = config.okta.baseUrl;
const PLATFORM_CONFIG_PROFILE = {
  okta: {
    clients: {
      fps: {
        url: BASE_URL,
        clientId: config.okta.clientId,
        issuer: config.okta.issuer,
        devMode: true,
      },
    },
  },
  api: {
    dashboard: { url: config.apiDashboardUrl },
  },
};

const LoanOutComp = (props) => {
  return (
    <PageBackgroundBox
      component={'form'}
      id="loanOutOnboardForm"
      // onSubmit={handleSubmit(onSubmit)}
      onKeyDown={(e) => {
        if (e.ctrlKey && e.key === 'Enter') {
          const form = document.getElementById('loanOutOnboardForm');
          form.requestSubmit();
        }
      }}
    >
      <h1>Tax Details</h1>
      <PlatformProvider config={PLATFORM_CONFIG_PROFILE}>
        <StateForms stateCode={'FED'} />
        <StateForms stateCode={'CA'} />
        {/* <LoanoutsLegacy /> */}
      </PlatformProvider>
    </PageBackgroundBox>
  );
};

export default LoanOutComp;
