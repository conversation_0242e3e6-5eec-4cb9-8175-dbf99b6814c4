<template>
  <div class="min-h-full">
    <main>
      <div
        class="mx-auto mt-8 grid max-w-3xl grid-cols-1 gap-6 sm:px-6 lg:max-w-7xl lg:grid-flow-col-dense lg:grid-cols-3"
      >
        <div class="space-y-6 lg:col-span-2 lg:col-start-1">
          <section aria-labelledby="applicant-information-title">
            <div class="bg-white dark:bg-gray-900 shadow sm:rounded-lg">
              <div class="flex justify-between px-4 py-5 sm:px-6">
                <h2
                  id="applicant-information-title"
                  class="text-lg font-medium leading-6"
                >
                  Information
                </h2>
              </div>
              <div
                class="border-t border-gray-200 dark:border-gray-500 px-4 py-5 sm:px-6"
              >
                <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
                  <div
                    class="sm:col-span-1"
                    v-for="detail in userDetails"
                    :key="detail.label"
                  >
                    <dt
                      class="text-sm font-medium text-gray-500 dark:text-gray-400"
                    >
                      {{ detail.label }}
                    </dt>
                    <dd class="mt-1 text-sm">
                      <div v-if="detail.label">
                        {{ detail.value }}
                      </div>
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          </section>
        </div>
      </div>
    </main>
  </div>
</template>

<script lang="ts">
import type Role from '@/types/Role';
import { defineComponent } from 'vue';

export default defineComponent({
  props: ['user', 'roles'],
  data() {
    return {};
  },
  mounted() {},
  computed: {
    userDetails() {
      return [
        { label: 'Phone', value: this.user.phone },
        {
          label: 'Email',
          value: this.user.email,
        },
        {
          label: 'Role',
          value: this.roles?.find?.(
            (role: Role) => role.id === this.user.roleId,
          )?.name,
        },
      ];
    },
  },
});
</script>
