import React from 'react';
import PropTypes from 'prop-types';
import { DateTime } from 'luxon';
import { useParams, useNavigate } from 'react-router';
import { getPayPeriods, listProjectMembers } from '@/services/project';
import { createTimecardForCrewUser } from '@/services/project-members';

import { Modal, Autocomplete, Text } from '@/reactComponents/library';

import { useDidMount } from '@/reactComponents/utils/customHooks';

import {
  snackbarSuccess,
  snackbarAxiosErr,
  snackbarErr,
} from '@/reactComponents/library/Snackbar';

import { UNBATCHED_ID } from './utils';

const CreateTimecardModal = (props) => {
  const { open, setOpen, project, batch } = props;

  const { hashId } = useParams();
  const navigate = useNavigate();

  const isUnbatched = batch.id === UNBATCHED_ID;

  const [payPeriods, setPayPeriods] = React.useState([]);
  const [selectedMember, setSelectedMember] = React.useState(null);
  const [selectedPayPeriod, setSelectedPayPeriod] = React.useState(null);

  const [projectMembers, setProjectMembers] = React.useState([]);
  const [memberPagination, setMemberPagination] = React.useState(null);
  const [loadingMembers, setLoadingMembers] = React.useState(false);

  const [creating, setCreating] = React.useState(false);

  const fetchMembers = (search, infiniteScrolling = false) => {
    setLoadingMembers(true);
    const paginationObj = { limit: 50, page: 1 };

    if (infiniteScrolling) {
      paginationObj.page = memberPagination.current_page + 1;
    }

    listProjectMembers(
      project.id,
      undefined, //typeKey
      paginationObj,
      'crewSignedStartPaperwork', //filterString
      undefined, //sorts
      search,
    )
      .then(({ data: res }) => {
        const { data, meta } = res;

        setMemberPagination(meta);
        setProjectMembers((prev) => {
          if (infiniteScrolling) {
            return [...prev, ...data];
          }
          return data;
        });
      })
      .catch((err) => {
        snackbarAxiosErr(err, 'Failed to fetch project members');
      })
      .finally(() => {
        setLoadingMembers(false);
      });
  };

  useDidMount(() => {
    getPayPeriods(project.id)
      .then(({ data: payPeriods }) => {
        setPayPeriods(payPeriods);
      })
      .catch((err) => {
        snackbarAxiosErr(err, 'Failed to fetch pay periods');
      });
    fetchMembers();
  });

  const submitCreate = () => {
    if (!selectedMember?.id) {
      snackbarErr('Please select an employee.');
      return;
    }
    if (!selectedPayPeriod?.id) {
      snackbarErr('Please select a pay period.');

      return;
    }

    const payload = {
      projectId: project.id,
      payPeriodId: selectedPayPeriod.id,
      timeZone: DateTime.now().zoneName,
    };
    if (!isUnbatched) {
      payload.batchId = batch.id;
    }

    setCreating(true);
    createTimecardForCrewUser(selectedMember.id, payload)
      .then(({ data: newTimecard }) => {
        snackbarSuccess('Timecard created!!!');
        const url = `/projects/${hashId}/admin/members/${newTimecard.projectMemberId}/member/timecards/${newTimecard.id}`;
        navigate(url);
      })
      .catch((err) => {
        if (err?.response?.data) {
          snackbarAxiosErr(err.response.data.message);
        } else {
          snackbarAxiosErr(err, 'Failed to create timecard');
        }
        setCreating(false);
      });
  };

  if (!open) return null;

  return (
    <Modal
      title="Create timecard"
      open={open}
      setOpen={setOpen}
      submitText={'Create timecard'}
      onCancel={() => setOpen(false)}
      onSubmit={submitCreate}
      loading={creating}
    >
      <Autocomplete
        value={selectedMember}
        label="Employee"
        options={projectMembers}
        placeholder="Search for a employee"
        getOptionLabel={(option) =>
          `${option.user.firstName} ${option.user.lastName}`
        }
        onChange={(e, value) => {
          setSelectedMember(value);
        }}
        fetchOptions={fetchMembers}
        hasNextPage={!!memberPagination?.next_page_url}
        infiniteScrolling
        loading={loadingMembers}
      />
      <Autocomplete
        value={selectedPayPeriod}
        label="Pay Period"
        placeholder="Select"
        options={payPeriods}
        getOptionLabel={(option) =>
          `${option.startsAt.toFormat('ccc MM/dd')} - ${option.endsAt.toFormat(
            'ccc MM/dd',
          )}`
        }
        onChange={(e, value) => {
          setSelectedPayPeriod(value);
        }}
      />
      {!isUnbatched && (
        <Text variant="baseReg">{`Timecard will be added to batch: ${batch.name}`}</Text>
      )}
    </Modal>
  );
};

CreateTimecardModal.propTypes = {
  open: PropTypes.bool.isRequired,
  setOpen: PropTypes.func.isRequired,
  project: PropTypes.object.isRequired,
  batch: PropTypes.object.isRequired,
};

export default CreateTimecardModal;
