name: Cypress Component Tests

on:
  pull_request:
    branches:
      - devel

defaults:
  run:
    shell: bash

jobs:
  cypress_component_tests:
    name: Run Cypress Component Tests
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v2
        with:
          repository: ${{ github.event.pull_request.head.repo.full_name }}
          ref: ${{ github.event.pull_request.head.ref }}

      - name: Authenticate with private NPM package
        run: echo "//registry.npmjs.org/:_authToken=${{ secrets.NPM_TOKEN }}" > .npmrc

      - name: Install Dependencies
        run: npm install --legacy-peer-deps

      - name: Run Cypress Component Tests and Capture Result
        id: component_tests
        run: |
          npm run test:component
          if [ $? -ne 0 ]; then
            echo "result=❌ Component tests failed" >> $GITHUB_ENV
          else
            echo "result=✅ Component tests passed" >> $GITHUB_ENV
          fi

      - name: Output the result
        run: |
          echo "${{ env.result }}"
