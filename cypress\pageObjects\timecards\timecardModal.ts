/// <reference types="cypress" />

export default class TimecardModal {
  readonly createTimecardBtn = '[data-testid="create-timecard-btn"]';
  readonly searchEmployeeInput = 'input[placeholder="Search for a employee"]';
  readonly dateRangeInput = 'input[placeholder="Select"]';
  readonly modalFooterButton = '.modalFooter button';
  readonly closeModalButton = '[data-testid="CloseIcon"]';
  readonly moveToBatchBtn = '[data-testid="move-to-batch-btn"]';
  readonly cancelExportBtn = '[data-testid="Cancel-btn"]';
  readonly exportBtn = '[data-testid="Export-btn"]';
  readonly selectBatchInput = 'input[placeholder="Select Batch"]';
  readonly moreOptionsIcon = '[data-testid="MoreVertIcon"]';
  readonly snippetOutlinedIcon = '[data-testid="TextSnippetOutlinedIcon"]';

  createTimecard(employeeName: string): void {
    cy.get(this.createTimecardBtn).should('exist').click();
    // Select employee
    cy.get('input[placeholder="Search for a employee"]')
      .should('be.visible')
      .type(employeeName);
    cy.get('ul[role="listbox"]')
      .should('exist')
      .within(() => {
        cy.get('li')
          .should('have.length.greaterThan', 0)
          .first()
          .should('be.visible')
          .click();
      });
    // Select time range
    cy.get(this.dateRangeInput).should('be.visible').click();
    cy.get('ul[role="listbox"] li').first().should('be.visible').click();

    // Submit timecard
    cy.get(this.modalFooterButton)
      .contains(/^Create timecard$/)
      .should('be.visible')
      .click();
    // Close modal
    cy.get(this.closeModalButton)
      .first()
      .closest('button')
      .should('be.visible')
      .click();
  }

  moveToBatch(batchName: string): void {
    cy.get(this.moveToBatchBtn, { timeout: 10000 })
      .should('be.visible')
      .click();
    cy.get(this.selectBatchInput).should('be.visible').type(batchName);
    cy.contains(batchName).should('be.visible').click();
    cy.get(this.modalFooterButton)
      .contains(/^Move to batch$/)
      .should('be.visible')
      .click();
  }

  deleteTimecard(): void {
    cy.get(this.moreOptionsIcon).should('exist').click();
    cy.contains('Delete timecard').should('be.visible').click();
    cy.get(this.modalFooterButton)
      .contains(/^Delete$/)
      .should('be.visible')
      .click();
  }

  removeFromBatch(): void {
    cy.get(this.moreOptionsIcon).should('be.visible').click();
    cy.contains('Remove from batch').should('be.visible').click();
    cy.get(this.modalFooterButton)
      .contains(/^Submit$/)
      .should('be.visible')
      .click();
  }

  goToBatchOptions(option: string): void {
    cy.get(this.snippetOutlinedIcon).should('be.visible').click();
    cy.get(`[data-testid="label-${option}"]`).should('be.visible').click();
    cy.get(this.exportBtn).should('be.visible').click();
  }
}
