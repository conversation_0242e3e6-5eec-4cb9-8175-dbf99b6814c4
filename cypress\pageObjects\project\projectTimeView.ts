/// <reference types="cypress" />

export default class ProjectTimeView {
  readonly selectTimecardStatusDropdown =
    '[data-testid="dropdown-Timecard Status"]';
  readonly selectPayPeriodDropdown = '[data-testid="dropdown-Pay Period"]';
  readonly selectUnionDropdown = '[data-testid="dropdown-Union"]';
  readonly selectDeptDropdown = '[data-testid="dropdown-Dept"]';
  readonly selectEmployeeDropdown = '[data-testid="dropdown-Employee"]';

  projectTimeFilter({
    timecardStatus,
    payPeriod,
    union,
    dept,
    employee,
  }: {
    timecardStatus?: string;
    payPeriod?: string;
    union?: string;
    dept?: string;
    employee?: string;
  }): void {
    const filters = [
      { value: timecardStatus, selector: this.selectTimecardStatusDropdown },
      { value: payPeriod, selector: this.selectPayPeriodDropdown },
      { value: union, selector: this.selectUnionDropdown },
      { value: dept, selector: this.selectDeptDropdown },
      { value: employee, selector: this.selectEmployeeDropdown },
    ];

    filters.forEach(({ value, selector }) => {
      if (value) {
        cy.get(selector).should('be.visible').click();
        cy.contains('span', value).should('be.visible').click();
      }
    });
  }
}
