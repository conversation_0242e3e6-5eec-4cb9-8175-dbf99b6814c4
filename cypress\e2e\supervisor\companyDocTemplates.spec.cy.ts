/// <reference types="cypress" />

import { deleteTemplate, fetchCompanyByName } from '../../support/apiHelpers';
import 'cypress-file-upload';
import {
  interceptCreateTemplate,
  interceptEditTemplate,
} from '../../support/apiTimecardInterceptors';
import DocumentTemplates from '../../pageObjects/company/documentTemplates';
import { faker } from '@faker-js/faker';

const template = 'All';
const templateType = 'Union';
const templateLoan = 'Loan Out and Non Loan Out';
const documentTemplates = new DocumentTemplates();

describe('User Supervisor - Company custom documents create and edit', () => {
  beforeEach((): void => {
    // ----------- Arrange: Login as Supervisor -----------
    cy.log('Arrange: Login as Supervisor');
    cy.loginWithSession(
      Cypress.env('PHONE_NUMBER'),
      Cypress.env('OTC_CODE'),
      Cypress.env('BASE_URL'),
      'supervisor',
    );

    // ----------- Arrange: Get company ID by name -----------
    cy.log('Arrange: Get company ID by name');
    cy.then(() => {
      return fetchCompanyByName(Cypress.env('companyName')).then((company) => {
        const selectedCompany = company?.data?.[0];
        if (!company || !company.data[0].id) {
          throw new Error('company not found or invalid company ID');
        }
        Cypress.env('companyId', selectedCompany.id);
        cy.log(`company ID set: ${selectedCompany.id}`);
      });
    });
  });

  afterEach(() => {
    // ----------- Cleanup: Delete created template -----------
    cy.log('Cleanup: Delete created template');
    deleteTemplate(Cypress.env('companyId'), Cypress.env('documentId'));
  });

  it('Verify Supervisor is able to create Custom documents under a Company/Client', () => {
    const filenameTemplate = `Template-${faker.word.sample()}`;

    // ----------- Arrange: Visit Document Templates tab -----------
    cy.log('Arrange: Visit Document Templates tab');
    cy.visit(
      `${Cypress.env('BASE_URL')}companies/${Cypress.env(
        'companyId',
      )}/document-templates`,
    );

    // ----------- Arrange: Intercept template creation API -----------
    cy.log('Arrange: Intercept template creation API');
    interceptCreateTemplate();

    // ----------- Action: Create new paperwork template -----------
    cy.log('Action: Create new paperwork template');
    documentTemplates.createPaperWork();

    // ----------- Action: Upload paperwork file -----------
    cy.log('Action: Upload paperwork file');
    documentTemplates.uploadPaperWork();

    // ----------- Action: Fill out template schema -----------
    cy.log('Action: Fill out template schema');
    documentTemplates.fillTemplatesSchema();

    // ----------- Action: Fill out template information -----------
    cy.log('Action: Fill out template information');
    documentTemplates.fillTemplatesInformation(
      filenameTemplate,
      template,
      templateType,
      templateLoan,
    );

    // ----------- Action: Save template schema -----------
    cy.log('Action: Save template schema');
    documentTemplates.saveTemplatesSchema();

    // ----------- Assert: Validate template was created -----------
    cy.log('Assert: Validate template was created');
    cy.wait('@createdTemplate').then((interception): void => {
      // API Assert
      expect(interception.response?.statusCode).to.equal(200);
      Cypress.env('documentId', interception.response?.body.id);

      // UI Assert
      documentTemplates.documentTemplatePage(filenameTemplate);
    });
  });

  it('Verify Supervisor is able to edit Custom documents in Documents templates tab', () => {
    const filenameTemplate = `Template-${faker.word.sample()}`;
    const editFilenameTemplate = `Paper-${faker.word.sample()}`;

    // ----------- Arrange: Visit Document Templates tab -----------
    cy.log('Arrange: Visit Document Templates tab');
    cy.visit(
      `${Cypress.env('BASE_URL')}companies/${Cypress.env(
        'companyId',
      )}/document-templates`,
    );

    // ----------- Arrange: Intercept template creation and edit API -----------
    cy.log('Arrange: Intercept template creation and edit API');
    interceptCreateTemplate();
    interceptEditTemplate();

    // ----------- Action: Create new paperwork template -----------
    cy.log('Action: Create new paperwork template');
    documentTemplates.createPaperWork();

    // ----------- Action: Upload paperwork file -----------
    cy.log('Action: Upload paperwork file');
    documentTemplates.uploadPaperWork();

    // ----------- Action: Fill out template schema -----------
    cy.log('Action: Fill out template schema');
    documentTemplates.fillTemplatesSchema();

    // ----------- Action: Fill out template information -----------
    cy.log('Action: Fill out template information');
    documentTemplates.fillTemplatesInformation(
      filenameTemplate,
      template,
      templateType,
      templateLoan,
    );

    // ----------- Action: Save template schema -----------
    cy.log('Action: Save template schema');
    documentTemplates.saveTemplatesSchema();

    // ----------- Assert: Validate template was created -----------
    cy.log('Assert: Validate template was created');
    cy.wait('@createdTemplate').then((interception): void => {
      expect(interception.response?.statusCode).to.equal(200);
      Cypress.env('documentId', interception.response?.body.id);

      // ----------- Action: Edit the created template -----------
      cy.log('Action: Edit the created template');
      documentTemplates.editPaperWork(filenameTemplate);

      // ----------- Action: Edit template information -----------
      cy.log('Action: Edit template information');
      documentTemplates.editTemplatesInformation(
        editFilenameTemplate,
        template,
        'Non Union',
        'Loan Out',
      );

      // ----------- Action: Save edited template schema -----------
      cy.log('Action: Save edited template schema');
      documentTemplates.saveTemplatesSchema();

      // ----------- Assert: Validate template was edited -----------
      cy.log('Assert: Validate template was edited');
      cy.wait('@editedTemplate').then((interception): void => {
        // API Assert
        expect(interception.response?.statusCode).to.equal(200);
        // UI Assert
        documentTemplates.documentTemplatePage(editFilenameTemplate);
      });
    });
  });
});
