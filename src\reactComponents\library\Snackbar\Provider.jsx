import { SnackbarProvider } from 'notistack';
import PropTypes from 'prop-types';
import SnackbarComp from './Component';

import { useIsMobile } from '../../utils/customHooks';

const Provider = ({ children }) => {
  const isMobile = useIsMobile();

  const anchorOrigin = isMobile
    ? { vertical: 'top', horizontal: 'center' }
    : { vertical: 'bottom', horizontal: 'right' };

  return (
    <SnackbarProvider
      anchorOrigin={anchorOrigin}
      Components={{
        error: SnackbarComp,
        success: SnackbarComp,
        warning: SnackbarComp,
        info: SnackbarComp,
      }}
      style={{ bottom: '60', backgroundColor: 'red' }}
    >
      {children}
    </SnackbarProvider>
  );
};

Provider.propTypes = {
  children: PropTypes.node,
};

export default Provider;
