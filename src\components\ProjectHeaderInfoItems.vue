<template>
  <div class="flex flex-wrap mb-2">
    <div
      v-for="item in infoItems"
      :key="item.id"
      class="mt-1 mr-6 flex items-center text-sm whitespace-nowrap text-gray-500 dark:text-gray-400"
    >
      <Icon v-bind:name="item.icon.toString()" class="mr-1.5 w-4 h-4" />
      {{ item.text }}
    </div>
  </div>
</template>

<script lang="ts">
import Icon from '@/components/Icon.vue';
import type Project from '@/types/Project';
import { ChevronRightIcon } from '@heroicons/vue/20/solid';
import { DateTime } from 'luxon';
import { defineComponent } from 'vue';

export default defineComponent({
  components: {
    ChevronRightIcon,
    Icon,
  },
  props: {
    project: {
      type: Object as () => Project,
      required: true,
    },
  },
  computed: {
    infoItems() {
      return [
        {
          id: 'productionCompany',
          icon: 'folder',
          text: this.project?.productionCompany?.name || '',
        },
        {
          id: 'number',
          icon: 'hash-02',
          text: this.project?.number || '',
        },
        {
          id: 'code',
          icon: 'phone-01',
          text: this.project?.code || '',
        },
        {
          id: 'dates',
          icon: 'calendar-01',
          text:
            this.project?.startsAt && this.project?.endsAt
              ? `${this.formatDatetime(
                  this.project.startsAt,
                )} - ${this.formatDatetime(this.project.endsAt)}`
              : '',
        },
      ];
    },
  },
  methods: {
    formatDatetime(datetime: any): string {
      return DateTime.fromISO(datetime).toFormat('MM/dd');
    },
  },
});
</script>
