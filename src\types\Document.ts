export interface Document {
  id: number;
  documentId: string;
  projectMemberId: number;
  createdAt: string;
  updatedAt: string;
  supervisorSignedById: number | null;
  supervisorSignedAt: string | null;
  supervisorIpAddress: string | null;
  crewIpAddress: string | null;
  crewSignedAt: string | null;
  numPages: number;
  documentTemplateId: number;
  documentTemplate: DocumentTemplate;
}

export interface DocumentTemplate {
  id: number;
  name: string;
  documentId: string;
  schema: any;
  createdAt: string;
  updatedAt: string;
  sendToPayroll: boolean;
  isCustom: boolean;
  numberOfPages: number;
}
