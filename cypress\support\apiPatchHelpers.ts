/// <reference types="cypress" />

function formatDateWithTZ(dateStr, hour = '00:00:00.000', tz = '-05:00') {
  return `${dateStr}T${hour}${tz}`;
}

function setPayPeriodDates(timecard, startsAt, endsAt) {
  if (timecard.payPeriod) {
    timecard.payPeriod.startsAt = formatDateWithTZ(startsAt);
    timecard.payPeriod.endsAt = formatDateWithTZ(endsAt);
  }
}

function setReimbursementDate(reimbursements, startsAt) {
  if (Array.isArray(reimbursements) && reimbursements.length > 0) {
    const firstReimbursement = reimbursements[0];
    const baseDate = new Date(startsAt);
    baseDate.setDate(baseDate.getDate() + 1);
    firstReimbursement.date = new Date(
      Date.UTC(
        baseDate.getUTCFullYear(),
        baseDate.getUTCMonth(),
        baseDate.getUTCDate(),
        0,
        0,
        0,
        0,
      ),
    ).toISOString();
  }
}

function setTimecardDays(timecardDays, baseStartDate) {
  if (!Array.isArray(timecardDays)) return;
  timecardDays.forEach((day, index) => {
    const currentDay = new Date(baseStartDate);
    currentDay.setDate(currentDay.getDate() + index);

    day.startsAt = new Date(
      Date.UTC(
        currentDay.getUTCFullYear(),
        currentDay.getUTCMonth(),
        currentDay.getUTCDate(),
        8,
        0,
        0,
        0,
      ),
    ).toISOString();

    day.endsAt = new Date(
      Date.UTC(
        currentDay.getUTCFullYear(),
        currentDay.getUTCMonth(),
        currentDay.getUTCDate(),
        17,
        0,
        0,
        0,
      ),
    ).toISOString();

    const datePlusOne = new Date(currentDay);
    datePlusOne.setDate(datePlusOne.getDate() + 1);
    day.date = new Date(
      Date.UTC(
        datePlusOne.getUTCFullYear(),
        datePlusOne.getUTCMonth(),
        datePlusOne.getUTCDate(),
        8,
        0,
        0,
        0,
      ),
    ).toISOString();

    if (Array.isArray(day.meals)) {
      day.meals.forEach((meal) => {
        meal.startsAt = new Date(
          Date.UTC(
            currentDay.getUTCFullYear(),
            currentDay.getUTCMonth(),
            currentDay.getUTCDate(),
            13,
            0,
            0,
            0,
          ),
        ).toISOString();
        meal.endsAt = new Date(
          Date.UTC(
            currentDay.getUTCFullYear(),
            currentDay.getUTCMonth(),
            currentDay.getUTCDate(),
            14,
            0,
            0,
            0,
          ),
        ).toISOString();
      });
    }
  });
}

function updateTimecardDayIds(timecardDays, timecardId, timecardDayIds) {
  if (!Array.isArray(timecardDays)) return;
  timecardDays.forEach((day, index) => {
    day.timecardId = timecardId;
    if (Array.isArray(timecardDayIds) && timecardDayIds[index]) {
      day.id = timecardDayIds[index];
    }
  });
}

function updateReimbursementLocations(reimbursements, projectId) {
  if (!Array.isArray(reimbursements)) return;
  reimbursements.forEach((reimbursement) => {
    if (reimbursement.workLocation) {
      reimbursement.workLocation.projectId = projectId;
      if (reimbursement.workLocation.shootLocation) {
        reimbursement.workLocation.shootLocation.projectId = projectId;
      }
    }
  });
}

export function addTimecardLine(): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/timecards/${Cypress.env(
    'timecardId',
  )}/`;
  return cy.fixture('e2ePayloads/timecards/addTimecardLine').then((data) => {
    const timecardId = Cypress.env('timecardId');
    data.id = timecardId;
    if (data.timecard) {
      data.timecard.id = timecardId;
      if (Array.isArray(data.timecard.timecardDays)) {
        data.timecard.timecardDays.forEach((day) => {
          day.timecardId = timecardId;
        });
      }
    }
    return cy
      .request({
        method: 'PATCH',
        url,
        body: data,
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then((response) => {
        return response;
      });
  });
}

export function addProjectOnboarding(data: object): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/projects/${Cypress.env(
    'projectId',
  )}/members`;
  return cy.request({
    method: 'PATCH',
    url,
    body: JSON.stringify(data),
    failOnStatusCode: false,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

export function addTimecardLineReimbursements(
  startsAt: string,
  endsAt: string,
): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/timecards/${Cypress.env(
    'timecardId',
  )}/`;
  return cy
    .fixture('e2ePayloads/timecards/addTimecardLineReimbursement')
    .then((data) => {
      const timecardId = Cypress.env('timecardId');
      const projectId = Cypress.env('projectId');
      const timecardDayIds = Cypress.env('timecardDayIds');
      const payPeriod = Cypress.env('payPeriodId');

      // Set main IDs
      data.timecard.id = timecardId;
      data.timecard.projectId = projectId;
      data.timecard.payPeriodId = payPeriod;
      if (data.timecard.payPeriod) data.timecard.payPeriod.id = payPeriod;
      if (data.timecard.project) data.timecard.project.id = projectId;

      // Set dates and details
      setPayPeriodDates(data.timecard, startsAt, endsAt);
      setReimbursementDate(data.timecard.reimbursements, startsAt);
      setTimecardDays(data.timecard.timecardDays, new Date(startsAt));
      updateTimecardDayIds(
        data.timecard.timecardDays,
        timecardId,
        timecardDayIds,
      );
      updateReimbursementLocations(data.timecard.reimbursements, projectId);

      return cy
        .request({
          method: 'PATCH',
          url,
          body: data,
          headers: {
            'Content-Type': 'application/json',
          },
        })
        .then((response) => response);
    });
}

export function addTimecardInformation(data: object): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/timecards/${
    data['timecard']['id']
  }/`;
  return cy
    .request({
      method: 'PATCH',
      url,
      body: JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json',
      },
    })
    .then((response) => {
      return response;
    });
}
