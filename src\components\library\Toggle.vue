<!-- This example requires Tailwind CSS v2.0+ -->
<template>
  <SwitchGroup as="div" class="flex items-center" :disabled="disabled">
    <Switch
      :model-value="modelValue"
      @update:modelValue="(event: any) => onInput(event)"
      :class="[
        modelValue ? 'bg-indigo-600' : 'bg-gray-300 dark:bg-gray-600',
        'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2',
      ]"
    >
      <span
        aria-hidden="true"
        :class="[
          modelValue ? 'translate-x-5' : 'translate-x-0',
          'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
        ]"
      />
    </Switch>
    <SwitchLabel v-if="$slots.default" as="span" class="ml-3">
      <slot />
    </SwitchLabel>
  </SwitchGroup>
</template>

<script lang="ts">
import { Switch, SwitchGroup, SwitchLabel } from '@headlessui/vue';
import { defineComponent } from 'vue';

export default defineComponent({
  emits: ['update:modelValue'],
  props: {
    modelValue: {
      default: false,
      type: Boolean,
    },
    disabled: {
      default: false,
      type: Boolean,
    },
  },
  components: {
    Switch,
    SwitchGroup,
    SwitchLabel,
  },
  methods: {
    onInput(value: any) {
      if (this.disabled) return;
      this.$emit('update:modelValue', value);
    },
  },
});
</script>
