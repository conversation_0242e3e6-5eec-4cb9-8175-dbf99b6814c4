import { useCallback, useEffect, useState } from 'react';
import { useAuth, useReactRouter } from '../../../AppHooks';
import { Button } from '@/reactComponents/library';
import { snackbarAxiosErr } from '@/reactComponents/library/Snackbar';
import { needsCrewOnboarding } from '@/services/user-crew';
import { isProjectOnboarded } from '@/services/project';
import { getLoanOut } from '@/services/loan-out';
import ProjectStore from '@/reactComponents/stores/project';
import ProjectPersonalOnboardingOption from './ProjectPersonalOnboardingOption';

const ProjectPersonalOnboardingView = () => {
  useAuth();
  const { navigate, route } = useReactRouter();
  const project = ProjectStore.project;

  const [pages, setPages] = useState([]);
  const [needsPersonalOnboarding, setNeedsPersonalOnboarding] = useState(false);
  const [wasProjectOnboarded, setWasProjectOnboarded] = useState(false);
  const [loading, setLoading] = useState(true);
  const [loanOut, setLoanOut] = useState(null);

  const fetchLoanOut = useCallback(async () => {
    try {
      const loanOutData = await getLoanOut();
      setLoanOut(loanOutData.data);
    } catch (err) {
      snackbarAxiosErr(err, 'Error fetching LoanOut data');
    }
  }, []);

  const fetchData = useCallback(async () => {
    try {
      const [needsOnboardingData, wasProjectOnboardedData] = await Promise.all([
        needsCrewOnboarding(),
        isProjectOnboarded(project.id),
      ]);

      setNeedsPersonalOnboarding(needsOnboardingData.data);
      setWasProjectOnboarded(wasProjectOnboardedData.data);

      if (needsOnboardingData.data) {
        setLoading(false);
        return;
      }

      await fetchLoanOut();

      setLoading(false);
    } catch (error) {
      snackbarAxiosErr(error, 'Error fetching onboarding data');
    }
  }, [project?.id, fetchLoanOut]);

  const goHome = () => {
    const departmentId = route?.query.departmentId?.toString();
    const searchParams = new URLSearchParams();
    searchParams.set('departmentId', departmentId || '');

    navigate({
      pathname: `/projects/${project.hashId.toString()}`,
      search: searchParams.toString(),
    });
  };

  const projectOnboard = () => {
    navigate({
      pathname: `/projects/${project.hashId.toString()}/onboarding`,
    });
  };

  const getFilteredPages = useCallback(() => {
    const personalOnboardingPages = {
      personalInfoOnboarding: {
        id: 0,
        label: 'Personal Info (Required)',
        pathname: needsPersonalOnboarding ? '/profile/create' : '/profile/edit',
        required: true,
        completed: !needsPersonalOnboarding,
        description: 'Required personal information for your account.',
        cta: needsPersonalOnboarding ? 'Start' : 'Edit',
      },
      projectOnboarding: {
        id: 1,
        label: 'Loan Out (Optional)',
        pathname: '/loan-outs',
        required: false,
        completed: !!loanOut?.id,
        description: 'Only create a loan out if you need one.',
        cta: !wasProjectOnboarded ? 'Start' : 'View',
      },
    };

    const filteredPages = [personalOnboardingPages.personalInfoOnboarding];
    if (!needsPersonalOnboarding) {
      return [...filteredPages, personalOnboardingPages.projectOnboarding];
    }
    return filteredPages;
  }, [loanOut, needsPersonalOnboarding, wasProjectOnboarded]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    if (!loading) {
      const filteredPages = getFilteredPages();
      setPages(filteredPages);
    }
  }, [loading, getFilteredPages]);

  return (
    <div className="flex justify-center mx-auto w-full py-2 px-5">
      <div className="max-w-7xl">
        <h2 className="text-center text-3xl font-light leading-9">
          Personal Onboarding
        </h2>
        {!loading && (
          <ul className="space-y-3 pt-3 mb-12">
            {pages.map((page) => (
              <li
                key={page.id}
                className="overflow-hidden px-4 sm:rounded-md sm:px-6"
              >
                <ProjectPersonalOnboardingOption page={page} />
              </li>
            ))}
          </ul>
        )}
        <div className="flex justify-center space-x-2">
          <Button variant="secondary" size="small" onClick={goHome}>
            Project Home
          </Button>
          <Button color="primary" size="small" onClick={projectOnboard}>
            Project Onboarding
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ProjectPersonalOnboardingView;
