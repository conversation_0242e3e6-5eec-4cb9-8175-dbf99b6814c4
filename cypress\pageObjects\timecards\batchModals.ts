/// <reference types="cypress" />

export default class BatchModals {
  readonly createBatchBtn = '[data-testid="create-batch-btn"]';
  readonly createBatchInput = '[data-testid="create-batch-input"]';
  readonly editBatchInput = '[data-testid="edit-batch-input"]';
  readonly batchList = '[data-testid="batch-scroll-list"]';
  readonly editIcon = '[data-testid="EditIcon"]';
  readonly deleteIcon = '[data-testid="DeleteIcon"]';
  readonly ArrowBackIcon = '[data-testid="ArrowBackIcon"]';
  readonly moreOptionsIcon = '[data-testid="MoreVertIcon"]';
  readonly modalFooterButton = '.modalFooter button';
  readonly closeButton = '[data-testid="CloseIcon"]';
  readonly removeFromBatchOption = 'Remove from batch';
  readonly moveToBatchBtn = '[data-testid="move-to-batch-btn"]';
  readonly selectBatchInput = 'input[placeholder="Select Batch"]';
  readonly batchFilterBtn = '[data-testid="batch-filter-btn"]';
  readonly submitBatchBtn = '[data-testid="submit-batch-btn"]';

  batchSelector(id: string): string {
    return `[data-testid="batch-${id}"]`;
  }

  timecardSelector(id: string): string {
    return `[data-testid="timecard-row-${id}"]`;
  }

  createBatch(batchName: string): void {
    cy.get(this.createBatchBtn).should('be.visible').click();
    cy.get(this.createBatchInput).should('be.visible').type(batchName);
    this.confirmModal('Create');
  }

  editBatch(currentName: string, newName: string): void {
    cy.get(this.batchList)
      .contains(currentName)
      .scrollIntoView()
      .should('exist')
      .closest('li')
      .within(() => {
        cy.get(this.moreOptionsIcon).scrollIntoView().should('exist').click();
      });
    cy.get(this.editIcon).scrollIntoView().should('exist').click();
    cy.get(this.editBatchInput)
      .scrollIntoView()
      .should('exist')
      .clear()
      .type(newName);
    this.confirmModal('Submit');
    cy.get(this.batchList)
      .contains(newName)
      .scrollIntoView()
      .should('exist')
      .click();
  }

  deleteBatch(batchName: string): void {
    cy.get(this.batchList)
      .contains(batchName)
      .scrollIntoView()
      .should('exist')
      .closest('li')
      .within(() => {
        cy.get(this.moreOptionsIcon).scrollIntoView().should('exist').click();
      });
    cy.get(this.deleteIcon).scrollIntoView().should('exist').click();
    this.confirmModal('Submit');
    cy.get(this.batchList).contains(batchName).should('not.exist');
  }

  openMoreOptions(timecardId: string): void {
    cy.get(this.timecardSelector(timecardId))
      .should('be.visible')
      .within(() => {
        cy.get(this.moreOptionsIcon).closest('button').click();
      });
  }

  removeTimecardFromBatch(timecardId: string): void {
    this.openMoreOptions(timecardId);
    cy.contains(this.removeFromBatchOption).should('be.visible').click();
    this.confirmModal('Submit');
  }

  selectTimecardInBatch(timecardId: string): void {
    cy.get(this.timecardSelector(timecardId), { timeout: 10000 }).click();
  }

  validateBatchDetails(
    timecardId: string,
    status: string,
    employeeName: string,
    occupation: string,
    department: string,
    PayPeriod: string,
    gross: string,
  ): void {
    cy.get(this.timecardSelector(timecardId)).within(() => {
      cy.contains('span', employeeName).should('exist');
      cy.contains('span', occupation).should('exist');
      cy.contains('span', department).should('exist');
      cy.contains('div', status).should('exist');
      cy.contains('span', PayPeriod).should('exist');
      cy.contains('span', gross).should('exist');
    });
  }

  goBackFromTimecard(): void {
    cy.get(this.ArrowBackIcon).closest('button').click();
  }

  closeModal(): void {
    cy.get(this.closeButton)
      .first()
      .closest('button')
      .should('be.visible')
      .click();
  }

  private confirmModal(buttonText: string): void {
    cy.get(this.modalFooterButton)
      .contains(new RegExp(`^${buttonText}$`, 'i'))
      .should('be.visible')
      .click();
  }

  moveToBatchWithSelection(batchName: string): void {
    // 1. Open the move-to-batch modal
    cy.get(this.moveToBatchBtn, { timeout: 10000 })
      .should('be.visible')
      .click();

    // 2. Type and select the target batch
    cy.get(this.selectBatchInput).type(batchName);
    cy.contains(batchName).should('be.visible').click();

    // 3. Confirm the move action
    cy.get(this.modalFooterButton)
      .contains(/^Move to batch$/)
      .should('be.visible')
      .click();
  }

  unbatchedTimecard(): void {
    cy.get(this.batchList)
      .contains('Unbatched Timecards')
      .should('be.visible')
      .click();
  }

  validateBatchStatus(batchId: string, status: string): void {
    const selector = this.batchSelector(batchId);
    const statusRegex = new RegExp(`^${status.trim()}$`, 'i');
    cy.log(`Validating batch [${batchId}] with expected status: "${status}"`);
    cy.get(selector).should('exist').and('be.visible').click();

    cy.get(selector)
      .should('exist')
      .within(() => {
        cy.contains(statusRegex, { timeout: 10000 }).should('exist');
      });
  }
  validateBatchDeleted(newBatchName: string): void {
    cy.get(this.batchList).contains(newBatchName).should('not.exist');
  }

  validateTimecardRemovedFromBatch(timecardId: string): void {
    cy.get(this.timecardSelector(timecardId)).should('not.exist');
  }

  submitBatchToPayroll() {
    cy.get(this.submitBatchBtn).should('exist').click();
  }
}
