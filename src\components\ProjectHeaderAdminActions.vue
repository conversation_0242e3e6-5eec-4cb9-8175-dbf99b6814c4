<template>
  <div class="flex flex-row items-center justify-between">
    <div class="hidden sm:flex w-full items-center justify-end">
      <button
        type="button"
        @click="linkModal = true"
        class="button-link bg-white pr-3 pl-3 pt-2 pb-3"
      >
        <Icon name="link" class="w-5 h-4" />
      </button>
      <Modal v-model="linkModal">
        <div class="flex justify-between items-center mb-2">
          <h1 class="text-lg font-semibold">Project Links</h1>
          <XMarkIcon
            class="w-6 h-6 text-gray-400 cursor-pointer hover:text-gray-500"
            @click="linkModal = false"
          />
        </div>
        <div
          v-for="department in project.departments"
          :key="department.id"
          class="flex justify-between items-center"
        >
          <div>{{ department.type.name }}</div>
          <div
            class="flex p-2 cursor-pointer text-sm font-semibold text-indigo-600 rounded-md border-indigo-400 hover:text-indigo-700 dark:text-indigo-400 dark:hover:text-indigo-300"
            @click="
              copyProjectLinkToClipboard(department.id!, department.type.name!)
            "
          >
            <LinkIcon class="w-4 h-4 mt-0.5 mr-1" />
          </div>
        </div>
      </Modal>
    </div>
    <div class="flex justify-end xl:mt-0 xl:ml-4">
      <div class="hidden sm:flex justify-end">
        <DropdownMenu :item-groups="settings">
          <template #buttonContent>
            <MenuButton
              class="button-link bg-white flex items-center pr-4 pl-4 pt-2 pb-2"
            >
              <Icon name="settings" class="w-5 h-5 mr-2" />
              <div
                class="flex justify-between text-sm font-semibold text-gray-900"
              >
                Settings
              </div>
            </MenuButton>
          </template>
        </DropdownMenu>
      </div>

      <DropdownMenu class="sm:hidden" :item-groups="moreItems">
        <template #buttonContent>
          <MenuButton
            class="inline-flex items-center gap-x-1.5 rounded-md bg-white dark:bg-gray-800 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-200 shadow-sm ring-1 ring-inset ring-gray-300 hover:ring-gray-400"
          >
            More
            <ChevronDownIcon
              class="-mr-1 h-5 w-5 text-gray-400"
              aria-hidden="true"
            />
          </MenuButton>
        </template>
      </DropdownMenu>
    </div>
  </div>
</template>

<script lang="ts">
import Icon from '@/components/Icon.vue';
import type { MenuItemGroup } from '@/types/Menu';
import type Project from '@/types/Project';
import { MenuButton } from '@headlessui/vue';
import { ChevronDownIcon, XMarkIcon } from '@heroicons/vue/20/solid';
import { LinkIcon } from '@heroicons/vue/24/solid';
import { DateTime } from 'luxon';
import { defineComponent, inject } from 'vue';
import { exportProjectToCSV } from '../services/export';
// import { useSnackbarStore } from '../stores/snackbar';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import type { SnackType } from '@/types/Snackbar';

import DropdownMenu from './library/DropdownMenu.vue';
import Modal from './library/Modal.vue';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';
export default defineComponent({
  components: {
    MenuButton,
    ChevronDownIcon,
    LinkIcon,
    DropdownMenu,
    Modal,
    XMarkIcon,
    Icon,
  },
  props: {
    project: {
      type: Object as () => Project,
      required: true,
    },
  },
  setup(props, ctx) {
    const navigate: Function | undefined = inject('navigate');
    const route: ParsedRoute | undefined = inject('route');

    return {
      navigate,
      route,
    };
  },
  computed: {
    viewsActions(): MenuItemGroup[] {
      const goTo = (routeName: string): void => {
        if (this.navigate) {
          this.navigate({
            pathname: routeName,
          });
        }
      };

      const groups: MenuItemGroup[] = [
        [
          {
            label: 'Timecards',
            action: () => {
              const path = `/projects/${this.project.hashId}/admin/timecards`;
              goTo(path);
            },
            active: this?.route?.location?.pathname?.includes('timecards'),
          },
          {
            label: 'Batches',
            action: () => {
              const path = `/projects/${this.project.hashId}/admin/batches`;
              goTo(path);
            },
            active: this?.route?.location?.pathname?.includes('batches'),
          },
          {
            label: 'Members',
            action: () => {
              const path = `/projects/${this.project.hashId}/admin/members`;
              goTo(path);
            },
            active: this?.route?.location?.pathname?.includes('members'),
          },
          {
            label: 'Templates',
            action: () => {
              const path = `/projects/${this.project.hashId}/admin/document-templates`;
              goTo(path);
            },
            active: this?.route?.location?.pathname?.includes('templates'),
          },
        ],
      ];

      const filteredGroups = [];
      for (const group of groups) {
        const filteredGroup = group.filter((item) => !item.hide);
        if (filteredGroup.length > 0) {
          filteredGroups.push(filteredGroup);
        }
      }
      return filteredGroups;
    },
    moreItems(): MenuItemGroup[] {
      return [
        ...this.viewsActions,
        [
          {
            label: 'Edit Project',
            action: () => {
              if (this.navigate) {
                this.navigate({
                  pathname: `/projects/${this.project.hashId}/edit`,
                });
              }
            },
          },
        ],
      ];
    },
    settings(): MenuItemGroup[] {
      return [
        [
          {
            label: 'Edit Project',
            action: () => {
              if (this.navigate) {
                this.navigate({
                  pathname: `/projects/${this.project.hashId}/edit`,
                });
              }
            },
            active: this?.route?.location?.pathname?.includes('project'),
          },
          {
            label: 'Custom Paperwork',
            action: () => {
              if (this.navigate) {
                this.navigate({
                  pathname: `/projects/${this.project.hashId}/admin/document-templates`,
                });
              }
            },
            active: this?.route?.location?.pathname?.includes('templates'),
          },
        ],
      ];
    },
  },
  data() {
    return {
      linkModal: false,
    };
  },
  methods: {
    triggerSnackbar(message: string, timeout: number, type: SnackType) {
      SnackbarStore.triggerSnackbar(message, timeout, type);
    },
    formatDatetime(datetime: any): string {
      return DateTime.fromISO(datetime).toFormat('MM/dd');
    },
    exportProject(): void {
      window.open(exportProjectToCSV(this.project.id!));
    },
    copyProjectLinkToClipboard(
      departmentId: number,
      departmentTypeName: string,
    ): void {
      let url = `${window.location.origin}/v2/projects/${this.project.hashId}?onboard=true`;
      if (departmentId) {
        url += `&departmentId=${departmentId}`;
      }
      navigator.clipboard.writeText(url);
      this.triggerSnackbar(
        `${departmentTypeName} project link copied to clipboard`,
        2500,
        'success',
      );
    },
  },
});
</script>
<style>
.button-link {
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  border: 1px solid var(--Gray-300, #d0d5dd);
  background: var(--Base-White, #fff);
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
}
</style>
