<template>
  <div class="dark:border-gray-50">
    <table class="min-w-full divide-y divide-gray-300">
      <thead class="light:bg-gray-50">
        <tr>
          <th
            v-for="(header, headerIndex) in headers"
            :key="`header-${headerIndex}`"
            class="py-3 pl-4 pr-3 text-left text-xs font-medium uppercase tracking-wide text-gray-500 dark:text-gray-50 sm:pl-6"
          >
            {{ header.name }}
          </th>
          <th
            v-if="$slots.action"
            class="relative py-3.5 pl-3 pr-4 sm:pr-6"
          ></th>
        </tr>
      </thead>
      <tbody class="divide-y divide-gray-200 light:bg-white">
        <tr
          v-for="(item, itemIndex) in data"
          :key="`item-${itemIndex}`"
          @click="() => $emit('rowClick', item)"
          class="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800"
        >
          <td
            v-for="(header, headerIndex) in headers"
            :key="`header-${headerIndex}`"
            class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-50"
          >
            <slot :name="header.value" :value="getValue(item, header.value)" />
            <span v-if="!$slots[header.value]">{{
              getValue(item, header.value)
            }}</span>
          </td>
          <td
            v-if="$slots.action"
            class="flex justify-end whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-50 text-right"
          >
            <slot name="action" :value="item" />
          </td>
        </tr>
      </tbody>
    </table>
    <nav
      class="light:bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 dark:text-gray-50"
      aria-label="Pagination"
    >
      <div class="hidden sm:block">
        <p
          v-if="pagination.total > 0"
          class="text-sm text-gray-700 dark:text-gray-50"
        >
          Showing
          {{ ' ' }}
          <span class="font-medium">{{ getStartIndex() }}</span>
          {{ ' ' }}
          to
          {{ ' ' }}
          <span class="font-medium">{{ getEndIndex() }}</span>
          {{ ' ' }}
          of
          {{ ' ' }}
          <span class="font-medium">{{ pagination.total }}</span>
          {{ ' ' }}
          results
        </p>
        <p v-else class="text-sm text-gray-700 dark:text-gray-50">
          Showing
          {{ ' ' }}
          <span class="font-medium">{{ pagination.total }}</span>
          {{ ' ' }}
          results
        </p>
      </div>
      <div class="flex-1 flex justify-between sm:justify-end">
        <div
          :class="prevButtonStyle()"
          @click="prevPage()"
          class="relative inline-flex items-center px-4 py-2 border light:bg-white dark:bg-gray-900 text-sm font-medium rounded-md dark:text-gray-200"
        >
          Previous
        </div>
        <div
          :class="nextButtonStyle()"
          @click="nextPage()"
          class="ml-3 relative inline-flex items-center px-4 py-2 border bg-white dark:bg-gray-900 text-sm font-medium rounded-md dark:text-gray-200"
        >
          Next
        </div>
      </div>
    </nav>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'Card',
  props: ['headers', 'data', 'pagination'],
  data() {
    return {
      styles: {
        enabled:
          'cursor-pointer dark:bg-gray-900 dark:border-gray-50 hover:bg-gray-50 text-gray-700 border-gray-300',
        disabled: 'cursor-default text-gray-500 border-gray-200',
      },
    };
  },
  methods: {
    prevPage() {
      if (!this.canGoPrev()) return;
      this.$emit('pageChange', {
        ...this.pagination,
        page: this.pagination.page - 1,
      });
    },
    getValue(item: any, value: string) {
      let tunneledValue = item;
      value.split('.').forEach((val: string) => {
        if (tunneledValue) {
          tunneledValue = tunneledValue[val];
        }
      });
      return tunneledValue;
    },
    nextPage() {
      if (!this.canGoNext()) return;
      this.$emit('pageChange', {
        ...this.pagination,
        page: this.pagination.page + 1,
      });
    },
    getStartIndex() {
      return (this.pagination.page - 1) * this.pagination.limit + 1;
    },
    canGoPrev(): boolean {
      return this.pagination.page > 1;
    },
    canGoNext(): boolean {
      return (
        this.pagination.page * this.pagination.limit < this.pagination.total
      );
    },
    prevButtonStyle() {
      return this.styles[this.canGoPrev() ? 'enabled' : 'disabled'];
    },
    nextButtonStyle() {
      return this.styles[this.canGoNext() ? 'enabled' : 'disabled'];
    },
    getEndIndex() {
      return Math.min(
        this.pagination.page * this.pagination.limit,
        this.pagination.total,
      );
    },
  },
});
</script>
