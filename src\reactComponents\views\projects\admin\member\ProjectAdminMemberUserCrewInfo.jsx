import { applyPureVueInReact } from 'veaury';
import ProjectAdminMemberUserCrewInfoVue from '../../../../../views/projects/admin/member/ProjectAdminMemberUserCrewInfo.vue';
import { useAuth } from '../../../../AppHooks';
import { useOutletContext } from 'react-router';
const ReactProjectAdminMemberUserCrewInfo = applyPureVueInReact(
  ProjectAdminMemberUserCrewInfoVue,
);

const ProjectAdminMemberUserCrewInfo = () => {
  useAuth();
  const context = useOutletContext();
  return (
    <ReactProjectAdminMemberUserCrewInfo
      projectMember={context.projectMember}
    />
  );
};

export default ProjectAdminMemberUserCrewInfo;
