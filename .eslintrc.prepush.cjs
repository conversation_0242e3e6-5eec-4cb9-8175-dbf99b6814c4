/* eslint-env node */
require('@rushstack/eslint-patch/modern-module-resolution');

const config = require('./.eslintrc.cjs');

// pre-push won't prevent the push if there are only warnings.
// So we need to make sure that all rules are set to error

//modify to switch warn to error
config.rules['no-console'][0] = 'error';
config.rules['no-unused-vars'] = 'error';
config.rules['react/no-unused-state'] = 'error';
config.rules['react/jsx-no-duplicate-props'] = 'error';
config.rules['react/no-array-index-key'] = 'error';
config.rules['react/prop-types'] = 'error';
//add new error rules
config.rules['react-hooks/exhaustive-deps'] = 'error';

module.exports = config;
