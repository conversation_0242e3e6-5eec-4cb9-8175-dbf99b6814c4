import { applyPureVueInReact } from 'veaury';
import ProjectAdminTimecardsViewVue from '../../../../views/projects/admin/ProjectAdminTimecardsView.vue';
import { useAuth, useReactRouter } from '../../../AppHooks';
import { useOutletContext } from 'react-router';

const ReactProjectAdminTimecardsView = applyPureVueInReact(
  ProjectAdminTimecardsViewVue,
);

const ProjectAdminTimecardsView = () => {
  useAuth();
  const { navigate, route } = useReactRouter();
  const context = useOutletContext();
  return (
    <ReactProjectAdminTimecardsView
      isAdmin={context.isAdmin}
      project={context.project}
      navigate={navigate}
      route={route}
    />
  );
};

export default ProjectAdminTimecardsView;
