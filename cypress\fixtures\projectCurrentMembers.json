{"meta": {"total": 1, "per_page": 25, "current_page": 1, "last_page": 1, "first_page": 1, "first_page_url": "/?page=1", "last_page_url": "/?page=1", "next_page_url": null, "previous_page_url": null}, "data": [{"id": 839, "projectId": 1, "userId": 73, "createdAt": "2025-05-09T21:01:24.499+00:00", "updatedAt": "2025-05-09T21:01:24.986+00:00", "projectMemberTypeId": 2, "rate": "200.0000", "rateTypeId": 1, "occupationId": 5009, "hireLocationCity": null, "hireLocationStateId": null, "startDate": "2025-05-09T21:01:22.398+00:00", "lineNumber": null, "loanOutStatusId": 1, "departmentId": 1006, "isActive": true, "projectLocationId": 1398, "hourlyRate": "200.0000", "overTime1_5x": "300.0000", "overTime2_0x": "400.0000", "hireLocationId": null, "requiresHireLocation": false, "isOnCall": false, "isExempt": false, "isHalfDayAllowed": true, "isNdbAllowed": true, "shootLineNumber": null, "startPaperwork": [{"id": 3469, "documentId": "86925b66-50f9-4583-ae48-62df31897e25", "projectMemberId": 839, "createdAt": "2025-05-09T21:01:27.120+00:00", "updatedAt": "2025-05-09T21:01:27.120+00:00", "supervisorSignedById": null, "supervisorSignedAt": null, "supervisorIpAddress": null, "crewIpAddress": null, "crewSignedAt": "2025-05-09T21:01:27.120Z", "numPages": 1, "documentTemplateId": 18171, "crewSigned": true}, {"id": 3467, "documentId": "308559d4-e686-4538-bcbd-ff93e0aa0e24", "projectMemberId": 839, "createdAt": "2025-05-09T21:01:26.369+00:00", "updatedAt": "2025-05-09T21:01:26.369+00:00", "supervisorSignedById": null, "supervisorSignedAt": null, "supervisorIpAddress": null, "crewIpAddress": null, "crewSignedAt": "2025-05-09T21:01:26.369Z", "numPages": 1, "documentTemplateId": 18172, "crewSigned": true}, {"id": 3468, "documentId": "8a775b62-0024-4346-b949-fbb329d19014", "projectMemberId": 839, "createdAt": "2025-05-09T21:01:26.854+00:00", "updatedAt": "2025-05-09T21:01:26.854+00:00", "supervisorSignedById": null, "supervisorSignedAt": null, "supervisorIpAddress": null, "crewIpAddress": null, "crewSignedAt": "2025-05-09T21:01:26.854Z", "numPages": 1, "documentTemplateId": 18178, "crewSigned": true}, {"id": 3470, "documentId": "83566f59-07db-4e8a-8ea2-ea1d1992cb51", "projectMemberId": 839, "createdAt": "2025-05-09T21:01:27.319+00:00", "updatedAt": "2025-05-09T21:01:27.319+00:00", "supervisorSignedById": null, "supervisorSignedAt": null, "supervisorIpAddress": null, "crewIpAddress": null, "crewSignedAt": "2025-05-09T21:01:27.319Z", "numPages": 1, "documentTemplateId": 18180, "crewSigned": true}], "projectMemberType": {"id": 2, "name": "Crew", "key": "crew", "description": "Project Crew Member", "createdAt": "2025-01-31T09:35:17.072+00:00", "updatedAt": "2025-01-31T09:35:17.072+00:00"}, "occupation": {"id": 5009, "name": "1ST ASSISTANT COSTUMER", "key": "1ST ASSISTANT COSTUMER", "createdAt": "2025-02-07T13:21:07.017+00:00", "updatedAt": "2025-05-09T21:01:41.534+00:00", "castAndCrewId": null, "payrollOccupationCode": "ACU1"}, "department": {"id": 1006, "projectId": 1, "typeId": 2, "createdAt": "2025-05-09T21:01:15.238+00:00", "updatedAt": "2025-05-09T21:01:15.238+00:00", "type": {"id": 2, "name": "General", "key": "general", "description": "The non-specified general department that people default to.", "createdAt": "2025-01-31T09:35:17.007+00:00", "updatedAt": "2025-01-31T09:35:17.007+00:00"}}, "user": {"id": 73, "phone": "+573116141649", "firstName": "<PERSON><PERSON>", "middleName": null, "lastName": "Alcaraz", "email": "<EMAIL>", "createdAt": "2025-04-24T18:22:19.050+00:00", "updatedAt": "2025-04-24T18:22:19.050+00:00", "roleId": 2, "isActive": true, "userCrew": {"id": 73, "dateOfBirth": "1998-07-17T07:00:00.000+00:00", "socialSecurityNumber": "*********", "citizenshipStatusId": 1, "ethnicityId": 3, "personalAddressId": 103, "createdAt": "2025-04-24T21:01:57.739+00:00", "updatedAt": "2025-04-24T21:01:57.739+00:00", "genderId": 2, "proofOfIdentityId": 37, "foreignPassportIssuingCountry": null, "workAuthorizationExpirationDate": null, "maritalStatusId": 1, "withholdingsBasedOnDependents": null, "exemptFromWithholdingsBasedOnDependents": false, "workAuthorizationTypeId": null, "workAuthorizationNumber": null, "withholdingsFromOtherIncome": null, "extraWithholdings": null, "withholdingsFromDeductions": null, "multipleJobsOrSpouseWorks": false, "eorEmployeeId": null, "loanOut": null}}}]}