import { applyPureVueInReact } from 'veaury';
import ProjectAdminMemberStartPaperworkVue from '../../../../../views/projects/admin/member/ProjectAdminMemberStartPaperwork.vue';
import { useAuth, useReactRouter } from '../../../../AppHooks';
import { useOutletContext } from 'react-router';

const ReactProjectAdminMemberStartPaperwork = applyPureVueInReact(
  ProjectAdminMemberStartPaperworkVue,
);

const ProjectAdminMemberStartPaperwork = () => {
  useAuth();
  const { navigate, route } = useReactRouter();
  const context = useOutletContext();
  return (
    <ReactProjectAdminMemberStartPaperwork
      projectMember={context.projectMember}
      user={context.user}
      userCrew={context.userCrew}
      project={context.project}
      refresh={context.refresh}
      navigate={navigate}
      route={route}
    />
  );
};

export default ProjectAdminMemberStartPaperwork;
