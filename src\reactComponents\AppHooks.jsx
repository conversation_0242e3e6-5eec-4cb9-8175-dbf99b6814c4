import React from 'react';
import PropTypes from 'prop-types';
import {
  // Navigate,
  Outlet,
  useLocation,
  useMatch,
  useMatches,
  useNavigate,
  useOutletContext,
  useParams,
  useSearchParams,
} from 'react-router';
import AuthStore from './stores/auth';
import PermissionStore from './stores/permission';
import ProjectStore from './stores/project';

import { PermissionKeys } from '@/types/Permission';

export const useAuth = () => {
  const navigate = useNavigate();
  const matches = useMatches();
  const location = useLocation();

  const beforeEach = React.useCallback(async () => {
    const match = matches[matches.length - 1];
    const loc = location;
    let fullPath = `${loc.pathname}${loc.search || ''}`;
    let searchParams = new URLSearchParams();
    searchParams.append('redirect', fullPath);
    const meta = match.handle;

    if (meta?.authRequired && !AuthStore.isLoggedIn) {
      navigate({
        pathname: '/login',
        search: searchParams.toString(),
      });
      return;
    }
    if (meta?.onlyLoggedOut && AuthStore.isLoggedIn) {
      navigate('/');
      return;
    }
    if (meta?.isProjectAdmin && match.params?.hashId) {
      await PermissionStore.fetchPermissions();

      if (
        PermissionStore.hasPermission(PermissionKeys.ADMIN_VIEW_ON_ALL_PROJECTS)
      ) {
        return;
      }
      const projectData = await ProjectStore.fetchProject(match.params.hashId);
      const isAdmin = await ProjectStore.fetchIsAdmin(projectData?.id); // remove non-null assertion
      if (!isAdmin) {
        const hashId = match.params?.hashId;
        navigate(`/projects/${hashId}`);
        return;
      }
    }
  }, [location, matches, navigate]);

  React.useEffect(() => {
    beforeEach();
  }, [beforeEach]);
};

function parseQueryObjFromURLSearchParams(searchParams) {
  const query = {};
  if (!searchParams || searchParams?.size === 0) return query;
  searchParams.forEach((value, key) => {
    query[key] = value;
  });
  return query;
}

export const useReactRouter = () => {
  const navigate = useNavigate();
  const matches = useMatches();
  const params = useParams();
  const searchParams = useSearchParams();
  const location = useLocation();
  const queryParams = parseQueryObjFromURLSearchParams(searchParams[0]);
  const match = matches[matches.length - 1];
  location.fullPath = `${location.pathname}${location.search || ''}`;
  const route = {
    match: match || null,
    query: queryParams,
    params: params,
    location: location,
  };

  if (queryParams?.showfpserror === 'true') {
    throw new Error('Fps Error');
  }

  return { navigate, matches, params, searchParams, queryParams, route };
};

export const NavigateRelativeTo = (props) => {
  const { to, match, isOutlet } = props;
  const location = useLocation();
  const navigate = useNavigate();
  const isMatching = useMatch(match);
  const goTo = `${location.pathname}/${to}`;
  const context = useOutletContext();
  React.useEffect(() => {
    if (isMatching) navigate(goTo);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return isOutlet ? <Outlet context={{ ...context }} /> : <></>;
};

NavigateRelativeTo.propTypes = {
  to: PropTypes.string.isRequired,
  match: PropTypes.string.isRequired,
  isOutlet: PropTypes.bool,
};

export default useAuth;
