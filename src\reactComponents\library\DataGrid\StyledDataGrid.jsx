import { DataGridPro } from '@mui/x-data-grid-pro';
import { styled } from '@mui/material';

export const StyledWorkDaysGrid = styled(DataGridPro)(({ theme }) => [
  {
    color: theme.palette.gray[600],
    '& .MuiDataGrid-columnHeader': {
      backgroundColor: theme.palette.gray[50],
      borderRight: 'none',
      borderLeft: 'none',
      cursor: 'default',
      '&:focus': {
        outline: `1px solid ${theme.palette.pink[500]}`,
        outlineOffset: '-3px',
        borderRadius: '5px',
      },
    },
    '& .MuiDataGrid-columnHeaderTitle': {
      fontSize: '14px',
      fontWeight: '600',
      color: theme.palette.gray[600],
    },
    '&  .MuiDataGrid-columnHeaderTitleContainer': {
      justifyContent: 'center',
    },
    '& .MuiDataGrid-columnSeparator': {
      color: theme.palette.gray[100],
    },
    '& .MuiDataGrid-row': {
      '&.Mui-selected': {
        backgroundColor: theme.palette.gray[200],
        '&:hover, &.Mui-hovered': {
          backgroundColor: theme.palette.gray[50],
        },
      },
      '&:hover, &.Mui-hovered': {
        backgroundColor: theme.palette.gray[50],
        textDecoration: 'underline',
      },
    },
    '& .MuiDataGrid-cell--pinnedLeft': {
      backgroundColor: '#fff',
    },
    '& .MuiDataGrid-cell--pinnedRight': {
      backgroundColor: '#fff',
    },
    '& .MuiDataGrid-cell': {
      '&:focus': {
        outline: `1px solid ${theme.palette.pink[600]}`,
        outlineOffset: '-2px',
        borderRadius: '5px',
      },
    },
    '& .MuiDataGrid-cell--editing': {
      outline: `none !important`,
    },
    '& .MuiDataGrid-filler': {
      backgroundColor: theme.palette.gray[50],
    },
    '& .MuiDataGrid-cell--editing:focus-within': {
      '&:focus-within': {
        outline: `1px solid ${theme.palette.pink[600]}`,
        outlineOffset: '-2px',
        borderRadius: '5px',
      },
    },
  },
  theme.applyStyles('dark', {
    color: theme.palette.gray[100],
    '& .MuiDataGrid-row': {
      '&.Mui-selected': {
        backgroundColor: theme.palette.gray[800],
        '&:hover, &.Mui-hovered': {
          backgroundColor: theme.palette.gray[600],
        },
      },
      '&:hover, &.Mui-hovered': {
        backgroundColor: theme.palette.gray[600],
      },
    },
    '& .MuiDataGrid-columnHeader': {
      backgroundColor: theme.palette.background.default,
      borderRight: 'none',
      borderLeft: 'none',
      cursor: 'default',
      '&:focus': {
        outline: `1px solid ${theme.palette.pink[500]}`,
        outlineOffset: '-3px',
        borderRadius: '5px',
      },
    },
    '& .MuiDataGrid-columnHeaderTitle': {
      fontSize: '14px',
      fontWeight: '600',
      color: theme.palette.gray[100],
    },
    '& .MuiDataGrid-cell--pinnedLeft': {
      backgroundColor: theme.palette.background.default,
    },
    '& .MuiDataGrid-cell--pinnedRight': {
      backgroundColor: theme.palette.background.default,
    },
    '& .MuiDataGrid-filler': {
      backgroundColor: 'transparent',
    },
    // '& .MuiDataGrid-scrollbar': {
    //   backgroundColor: theme.palette.gray[800],
    // },
  }),
]);
