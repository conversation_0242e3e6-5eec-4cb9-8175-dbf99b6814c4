<template>
  <div class="max-w-screen-md">
    <div id="pdf" />
    <div
      v-if="loadingPdf"
      class="inline-block h-12 w-12 animate-spin rounded-full border-2 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"
      role="status"
    >
      <span
        class="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]"
        >Loading...</span
      >
    </div>
  </div>
</template>

<script lang="ts">
// import { useSnackbarStore } from '@/stores/snackbar';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import axios from 'axios';
import * as pdfjslib from 'pdfjs-dist';
import type { RenderParameters } from 'pdfjs-dist/types/src/display/api';
import { defineComponent } from 'vue';

pdfjslib.GlobalWorkerOptions.workerSrc =
  'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.10.111/build/pdf.worker.min.js';

export default defineComponent({
  props: {
    url: {
      type: String,
      required: true,
    },
  },
  components: {},
  data() {
    return {
      pdfSrc: null,
      loadingPdf: false as boolean,
      currentPage: 1,
      numPages: 0,
    };
  },
  async mounted() {
    this.loadingPdf = true;
    try {
      const pdf = await pdfjslib.getDocument(this.url).promise;
      const numPages = pdf.numPages;

      this.loadingPdf = false;
      for (let pageNumber = 1; pageNumber <= numPages; pageNumber++) {
        const page = await pdf.getPage(pageNumber);

        let pdfDiv = document.getElementById('pdf')!;
        let canvas = document.createElement('canvas');
        canvas.style.display = 'block';
        var viewport = page.getViewport({ scale: 0.5 });
        var scale = pdfDiv.clientWidth / viewport.width;
        viewport = page.getViewport({ scale });
        let context: CanvasRenderingContext2D | null = canvas.getContext('2d');
        canvas.height = viewport.height;
        canvas.width = viewport.width;
        canvas.style.width = '100%';

        let renderContext: RenderParameters = {
          canvasContext: context!,
          viewport,
        };
        let renderTask = page.render(renderContext as any);
        pdfDiv?.appendChild(canvas);
        await renderTask.promise;
      }
    } catch (err) {
      if (axios.isAxiosError(err)) {
        const msg = err.response?.data?.['errors']?.[0]?.message;
        SnackbarStore.triggerSnackbar(msg, 2500, 'error');
      } else {
        SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
      }
    }
    this.loadingPdf = false;
  },
});
</script>
