import axios, { type AxiosResponse } from 'axios';
import { coreBaseUrl } from '../utils/constants';

export const getStates = async (): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/addresses/states`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const getProductionCompanyAddresses = async (
  productionCompanyId: number,
) => {
  const url = `${coreBaseUrl()}/addresses/${productionCompanyId}/company`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};
