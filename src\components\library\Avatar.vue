<template>
  <div
    class="relative rounded-full border-gray-100 border border-1px"
    :class="[sizeClass, bgClass]"
  >
    <div
      class="absolute inset-0 flex items-center justify-center font-semibold border border-gray-100 overflow-hidden text-gray-600 dark:bg-gray-700 dark:text-white"
      :class="[bgClass]"
    >
      {{ initials }}
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  props: {
    firstName: String,
    lastName: String,
    size: {
      type: String,
      default: 'default',
    },
    bgColor: {
      type: String,
      default: 'default',
    },
  },
  data() {
    return {
      sizeClass: this.getsizeClass(),
      bgClass: this.getbgClass(),
    };
  },
  computed: {
    initials(): string | string[] {
      const firstInitial = this.$props.firstName?.charAt(0).toUpperCase() || '';
      const lastInitial = this.$props.lastName?.charAt(0).toUpperCase() || '';
      return `${firstInitial}${lastInitial}`;
    },
  },
  methods: {
    getbgClass(): string {
      switch (this.bgColor) {
        case 'white':
          return 'rounded-full bg-white';
        case 'gray':
          return 'rounded-full bg-gray-100';
        default:
          return 'rounded-full bg-gray-100';
      }
    },
    getsizeClass(): string {
      switch (this.size) {
        case '2xl':
          return 'text-2xl w-16 h-16';
        case 'lg':
          return 'text-lg w-16 h-16';
        case 'md':
          return 'text-md w-12 h-12';
        case 'sm':
          return 'text-sm w-10 h-10';
        default:
          return 'text-sm w-8 h-8';
      }
    },
  },
});
</script>
