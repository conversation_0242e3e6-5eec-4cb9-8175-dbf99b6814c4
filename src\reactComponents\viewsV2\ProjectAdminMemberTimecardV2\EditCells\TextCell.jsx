import React from 'react';
import PropTypes from 'prop-types';
import _cloneDeep from 'lodash/cloneDeep';

import { GridTextField } from '@/reactComponents/library';

import { isRentalDay } from '../timecardUtils';
import { snackbarInfo } from '@/reactComponents/library/Snackbar';
import { formatDecimal } from '@/reactComponents/utils/stringNum';

const TextCell = (props) => {
  const {
    column,
    reimbursement,
    updateCell,
    updateRow,
    timecardDays,
    error,
    disabled: cellDisabled,
  } = props;
  const { columnId, isNum, disabled, isIntOnly, isStrNum, decimalPlaces } =
    column;

  let value = reimbursement[columnId];

  const [localVal, setLocalVal] = React.useState(value || '');

  const valueUpdateRef = React.useRef(false);
  React.useEffect(() => {
    valueUpdateRef.current = true;
  }, [value]);

  React.useEffect(() => {
    if (valueUpdateRef.current === false) return;

    let newVal = value ?? '';

    if (decimalPlaces && newVal) {
      newVal = formatDecimal(newVal, decimalPlaces);
    }

    setLocalVal(newVal);
  }, [decimalPlaces, value]);

  const onChange = (event) => {
    const val = event.target.value;
    if (isNum || isStrNum) {
      if (isNaN(val) && val !== '.') {
        snackbarInfo('Only numbers are allowed');
        return;
      }
    }
    if (isIntOnly) {
      if (isNaN(val)) {
        snackbarInfo('Only numbers are allowed');
        return;
      }
      if (/^[0-9]*$/.test(val) === false) {
        snackbarInfo('Only whole numbers are allowed');
        return;
      }
    }

    setLocalVal(val);
  };

  const onBlur = (e) => {
    let newVal = localVal;

    if (isNum) {
      newVal = Number(newVal) || 0;
    }

    if (newVal === value) {
      //nothing to update, value is the same
      return;
    }

    if (columnId === 'rentalRate') {
      const updated = _cloneDeep(reimbursement);
      const count =
        updated.rateType?.key === 'weekly'
          ? 1
          : updated.rateType?.key === 'daily'
          ? timecardDays.filter(isRentalDay).length
          : 0;
      const totalAmount = Math.floor(newVal * count * 100) / 100;
      updated.totalAmount = totalAmount || 0;
      updated.rentalRate = newVal;
      updateRow(updated);
    } else if (columnId === 'rate') {
      const updated = _cloneDeep(reimbursement);
      const count = updated.totalMileage || updated.quantity;
      const totalAmount = Math.floor(newVal * count * 100) / 100;

      updated.totalAmount = totalAmount || 0;
      updated.rate = newVal;
      updateRow(updated);
    } else if (columnId === 'totalMileage') {
      const updated = _cloneDeep(reimbursement);
      const count = updated.rate;
      const totalAmount = Math.floor(count * newVal * 100) / 100;

      updated.totalAmount = totalAmount || 0;
      updated.totalMileage = newVal;
      updateRow(updated);
    } else if (columnId === 'quantity') {
      const updated = _cloneDeep(reimbursement);
      const count = updated.rate;
      const totalAmount = Math.floor(count * newVal * 100) / 100;

      updated.totalAmount = totalAmount || 0;
      updated.quantity = newVal;
      updateRow(updated);
    } else {
      //standard single update
      updateCell(newVal);
    }
  };

  return (
    <GridTextField
      value={localVal}
      error={error}
      onChange={onChange}
      onBlur={onBlur}
      disabled={disabled || cellDisabled}
    />
  );
};

TextCell.propTypes = {
  column: PropTypes.object.isRequired,
  reimbursement: PropTypes.object.isRequired,
  updateCell: PropTypes.func.isRequired,
  updateRow: PropTypes.func,
  timecardDays: PropTypes.array,
  decimalPlaces: PropTypes.array,
  error: PropTypes.bool,
  disabled: PropTypes.bool,
};

export default TextCell;
