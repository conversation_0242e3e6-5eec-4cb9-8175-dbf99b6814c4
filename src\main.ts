import { createPinia } from 'pinia';
import { createApp } from 'vue';

import Hotjar from '@hotjar/browser';
import NotificationCenterPlugin from '@novu/notification-center-vue';
import '@novu/notification-center-vue/dist/style.css';
import axios from 'axios';
import App from './App.vue';
import router from './router';
import isoConverter from './utils/isoConverter';

import './assets/main.css';

const siteId = 3711426;
const hotjarVersion = 6;
Hotjar.init(siteId, hotjarVersion);

axios.interceptors.response.use((response) => {
  isoConverter.adaptIsoStrings(response.data);
  return response;
});

const app = createApp(App);

app.use(createPinia());
app.use(router);
app.use(NotificationCenterPlugin);

app.mount('#app');
