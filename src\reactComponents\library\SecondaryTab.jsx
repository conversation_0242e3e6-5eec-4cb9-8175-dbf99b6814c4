import PropTypes from 'prop-types';

const SecondaryTab = (props) => {
  const { tab, currentTab, tabClick } = props;

  let className =
    'whitespace-nowrap rounded-md py-2 px-3 text-base leading-6 font-semibold cursor-pointer select-none mr-4';

  if (tab.key === currentTab?.key) {
    className += ' bg-white dark:bg-gray-700 text-pink-700  dark:text-pink-100';
  } else {
    className +=
      ' border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-700';
  }

  return (
    <span className={className} onClick={() => tabClick(tab)}>
      {tab.label}
    </span>
  );
};

SecondaryTab.propTypes = {
  tab: PropTypes.shape({
    key: PropTypes.string,
    label: PropTypes.string,
  }).isRequired,
  currentTab: PropTypes.shape({
    key: PropTypes.string,
    label: PropTypes.string,
  }),
  tabClick: PropTypes.func.isRequired,
};

export default SecondaryTab;
