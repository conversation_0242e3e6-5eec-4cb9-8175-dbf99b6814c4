<template>
  <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
    <section aria-labelledby="filter-heading">
      <h2 id="filter-heading" class="sr-only">Filters</h2>
    </section>
    <div class="flex justify-between items-start mb-3">
      <div class="flex -start space-x-2"></div>
      <div class="flex justify-end space-x-2">
        <Button
          v-if="isProjectMemberSigned"
          size="sm"
          color="gray"
          @click="openCreateTimecardModal"
        >
          <div class="flex items-center space-x-2">
            <PlusCircleIcon class="h-4 w-4" />
            <div>Create Timecard</div>
          </div>
        </Button>
      </div>
    </div>
    <Modal v-model="createTimecardModalIsOpen">
      <div class="mb-40">
        <div class="flex justify-between items-center">
          <h2 class="text-xl font-bold pb-2">Create Timecard</h2>
          <XMarkIcon
            @click="createTimecardModalIsOpen = false"
            class="h-6 w-6 hover:text-gray-300 cursor-pointer"
          />
        </div>
        <Combobox
          v-if="!projectMemberId"
          label="Employee"
          v-model="projectMember"
          :items="projectMembers"
          :search="projectMemberSearch"
          :loading="isLoadingProjectMembers"
          :infiniteScroll="true"
          :pagination="paginationProjectMembers"
          @update:pagination="(pagination: PaginationType) => (updatePagination(pagination))"
          @update:search="(x: any) => (projectMemberSearch = x)"
          @change="payPeriod = null"
          class="w-full mb-3"
          display-name="name"
        >
          <template #label="{ value: modelValue }">
            {{ modelValue?.name || 'Select' }}
          </template>
          <template #item="{ value: item }">
            {{ item?.name }}
          </template>
        </Combobox>
        <Dropdown
          v-model="payPeriod"
          :menu-items="payPeriods"
          :loading="isLoadingPayPeriods"
          label="Pay Period"
          display-name="name"
        />
        <div class="flex justify-center space-x-2 pt-3">
          <Button @click="createTimecardModalIsOpen = false" color="gray">
            Cancel
          </Button>
          <Button
            @click="createTimecardForUser"
            :loading="isCreateTimecard"
            color="primary"
          >
            Create Timecard
          </Button>
        </div>
      </div>
    </Modal>
    <div class="flex min-w-full grow">
      <TableFilters :filters="filters" @update:filters="updateFilters" />
    </div>

    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-500">
      <thead>
        <tr>
          <th
            v-for="(header, index) in headers"
            :key="index"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            {{ header }}
          </th>
        </tr>
      </thead>

      <tbody class="divide-y divide-gray-200 dark:divide-gray-500">
        <!-- Table rows -->
        <tr
          v-for="(timecard, timecardIndex) in timecards"
          :key="`${timecard.id}-${timecardIndex}`"
        >
          <td class="px-6 py-4">
            <span
              class="cursor-pointer text-gray-900 dark:text-gray-200 hover:dark:text-gray-400 whitespace-nowrap font-semibold"
              @click="
                goToTimecardFromAllTimecards(
                  timecard.projectMemberId,
                  timecard.id,
                )
              "
            >
              {{ timecard.payPeriod!.startsAt.toFormat('MM/dd') }} -
              {{ timecard.payPeriod!.endsAt.toFormat('MM/dd') }}
            </span>
          </td>
          <!-- HIDE IF USER CREW ID IS SPECIFIED -->
          <td v-if="!projectMemberId" class="px-6 py-4 whitespace-nowrap">
            <div
              class="cursor-pointer"
              @click="goToProjectMember(timecard.projectMemberId)"
            >
              <span
                class="truncate text-sm font-medium text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300"
              >
                {{
                  `${timecard.userCrew.user.firstName} ${timecard.userCrew.user.lastName}`
                }}
              </span>
              <br />
              <span
                class="text-sm dark:text-gray-500 dark:hover:text-gray-400 text-gray-400 hover:text-gray-500"
                >{{ timecard.userCrew.user.email }}</span
              >
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <TimecardStatusIcon :timecard="timecard" />
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <Observer>
              <div v-if="!timecard.batch && isAdmin">
                <Button
                  @click.stop="openBatchModal(timecard.id)"
                  class="mt-2"
                  size="xs"
                  color="gray"
                  :disabled="!timecard.isActive"
                >
                  Add to Batch
                </Button>
              </div>
              <div v-else class="flex justify-start items-center space-x-2">
                <Badge
                  type="success"
                  :text="timecard.batch.capsPayId + ' - ' + timecard.batch.name"
                />
                <Observer>
                  <XMarkIcon
                    v-if="isAdmin && !removingFromBatch"
                    class="w-5 h-5 text-gray-400 hover:text-gray-200 cursor-pointer"
                    @click="removeFromBatch(timecard)"
                  />
                  <div v-else-if="removingFromBatch" class="ml-2">
                    <Spinner class="h-5 w-5 text-gray-400" />
                  </div>
                </Observer>
              </div>
            </Observer>
          </td>
          <td
            class="px-6 py-4 whitespace-nowrap text-center text-sm text-semibold"
          >
            <Badge
              :type="timecard.isActive ? 'success' : 'error'"
              :text="timecard.isActive ? 'Active' : 'Deleted'"
            >
            </Badge>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="flex justify-end">
              <NewspaperIcon
                class="h-5 w-5 text-gray-400 hover:text-gray-700 cursor-pointer"
                aria-hidden="true"
                @click="
                  goToTimecardFromAllTimecards(
                    timecard.projectMemberId,
                    timecard.id,
                  )
                "
              />
            </div>
          </td>
        </tr>
      </tbody>
    </table>

    <TimecardsTableBatchModal
      v-model="batchModalIsOpen"
      :batches="batches"
      :current-timecard-id="currentTimecardId"
      :project-id="props.project.id!"
      @refresh:timecards="getTimecards()"
      @refresh:batches="loadBatches()"
    />

    <Pagination
      v-if="pagination.total > pagination.limit"
      v-model="pagination"
    />
  </div>
</template>

<script lang="ts" setup>
// test: has Observer, need to test re-render
import Badge from '@/components/library/Badge.vue';
import Button from '@/components/library/Button.vue';
import Combobox from '@/components/library/Combobox.vue';
import Dropdown from '@/components/library/Dropdown.vue';
import Modal from '@/components/library/Modal.vue';
import Pagination from '@/components/library/Pagination.vue';
import Spinner from '@/components/library/Spinner.vue';
import TimecardStatusIcon from '@/components/TimecardStatusIcon.vue';
import {
  getBatches,
  getCurrentMember,
  getPayPeriods,
  listProjectMembers,
  listProjectTimecards,
} from '@/services/project';
import {
  createTimecardForCrewUser,
  getProjectMemberById,
} from '@/services/project-members';
import { getTimecardStatuses } from '@/services/timecard-statuses';
import {
  removeBatchFromTimecard,
  type CreateTimecardPayload,
} from '@/services/timecards';
// import { usePermissionStore } from '@/stores/permission';
// import { useSnackbarStore } from '@/stores/snackbar';
import PermissionStoreInstance from '@/reactComponents/stores/permission';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import { Observer } from 'mobx-vue-lite';
import type Batch from '@/types/Batch';
import { FilterOperator, FilterUIType, type Filter } from '@/types/Filter';
import type { Pagination as PaginationType } from '@/types/Pagination';
import { PermissionKeys } from '@/types/Permission';
import type Project from '@/types/Project';
import type ProjectMember from '@/types/ProjectMember';
import type Timecard from '@/types/Timecard';
import {
  applyQueryStringToFilters,
  convertFiltersToQuery,
} from '@/utils/filter';
import { convertSortsToQuery } from '@/utils/sort';
import { NewspaperIcon } from '@heroicons/vue/20/solid';
import { PlusCircleIcon, XMarkIcon } from '@heroicons/vue/24/outline';
import axios, { type AxiosResponse } from 'axios';
import { DateTime } from 'luxon';
import { computed, inject, onMounted, ref, watch, type Ref } from 'vue';
// import { useRoute, useRouter } from 'vue-router';
import TableFilters from './TableFilters.vue';
import TimecardsTableBatchModal from './TimecardsTableBatchModal.vue';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';
import { Cookie } from '@/utils/cookie';

// const route = useRoute();
// const router = useRouter();

const route = inject('route') as { value: ParsedRoute };
const navigate = inject('navigate') as Function;

const projectMemberId: string | null =
  route.value.params.projectMemberId?.toString() || null;

const props = defineProps<{
  project: Project;
  isAdmin: boolean;
}>();

const pagination = ref<PaginationType>({
  page: 1,
  limit: 10,
  total: 0,
} as PaginationType);

const timecards: Ref<Timecard[]> = ref([] as Timecard[]);
const payPeriod: Ref<any> = ref({} as any);
const payPeriods: Ref<any[]> = ref([] as any[]);
const isLoadingPayPeriods: Ref<boolean> = ref(false);
const projectMemberSearch: Ref<string> = ref('');
const projectMember: Ref<ProjectMember> = ref({} as ProjectMember);
const projectMembers: Ref<ProjectMember[]> = ref([] as ProjectMember[]);
const isLoadingProjectMembers: Ref<boolean> = ref(false);
const isCreateTimecard: Ref<boolean> = ref(false);
const isProjectMemberSigned: Ref<boolean> = ref(false);
const paginationProjectMembers = ref<PaginationType>({
  page: 1,
  limit: 50,
  total: 0,
} as PaginationType);

const filtersId = computed(() => `timecard-filters-${props.project.hashId}`);

const filters: Ref<Filter[]> = ref([
  {
    id: 'status',
    field: 'statusId',
    label: 'Status',
    value: '',
    options: [],
    type: FilterUIType.MultiSelect,
    operator: FilterOperator.In,
    active: false,
  },
  {
    id: 'batch',
    field: 'batchId',
    label: 'Batch',
    value: '',
    options: [],
    type: FilterUIType.MultiSelect,
    operator: FilterOperator.In,
    active: false,
  },
  {
    id: 'department_type_name',
    name: 'Department',
    field: 'projectMember.departmentId',
    label: 'Department',
    value: '',
    type: FilterUIType.MultiSelect,
    operator: FilterOperator.In,
    active: false,
    options: props.project.departments.map((department) => ({
      id: `${department.id}_${department.type.key}`,
      label: department.type.name,
      value: department.id!,
      active: false,
    })),
  },
  {
    id: 'created_at',
    field: 'createdAt',
    label: 'Created At',
    value: null,
    type: FilterUIType.Hidden,
    operator: FilterOperator.None,
    active: false,
    sortable: true,
    sortDirection: '',
  },
  {
    id: 'week',
    field: 'pay_periods.starts_at',
    label: 'Week',
    value: null,
    type: FilterUIType.Hidden,
    operator: FilterOperator.None,
    active: false,
    sortable: true,
    sortDirection: '',
  },
  {
    id: 'user_crew',
    field: 'userCrewId',
    label: 'User Crew',
    value: null,
    type: FilterUIType.Hidden,
    operator: FilterOperator.None,
    active: false,
    sortable: true,
    sortDirection: '',
  },
] as Filter[]);

const filtersToJson = (filters: Filter[]) =>
  filters
    .filter((f) => f.type !== FilterUIType.Hidden)
    .reduce((acc, f) => {
      switch (f.type) {
        case FilterUIType.MultiSelect:
          acc[f.id] = f.options?.filter((o) => o.active).map((o) => o.value);
          break;
        default:
          break;
      }
      return acc;
    }, {} as Record<string, any>);

const getSessionFilters = () => {
  try {
    const cookie = Cookie.get(filtersId.value);
    // sessionStorage.getItem(filtersId.value)
    return JSON.parse(cookie || '{}');
  } catch {
    return {};
  }
};

const setSessionFilters = (filters: Filter[]) => {
  const data = filtersToJson(filters);
  Cookie.set(filtersId.value, JSON.stringify(data));
  // sessionStorage.setItem(filtersId.value, JSON.stringify(data));
};

const updateFilters = (newFilters: Filter[]) => {
  if (!isLoaded.value) return;

  filters.value = newFilters;
  setSessionFilters(newFilters);
};

const sortOptions = computed(() => {
  return filters.value.filter(({ sortable }) => sortable);
});

const activeFilters = computed(() => {
  return filters.value.filter(({ active }) => active);
});

const isAdmin = computed(
  () =>
    props.isAdmin ||
    PermissionStoreInstance.hasPermission(
      PermissionKeys.ADMIN_VIEW_ON_ALL_PROJECTS,
    ),
);

watch(activeFilters, async () => getTimecards());

const headers = computed(() => {
  return [
    'Pay Period',
    'Project Member',
    'Status',
    'Batch # / Name',
    'Active/Deleted',
    '',
  ].filter((h) => !projectMemberId || h !== 'Project Member');
});

watch(
  pagination,
  async (newPagination, oldPagination) => {
    if (newPagination.page === oldPagination.page) {
      return;
    }
    const { page } = newPagination;
    const query = { ...route.value.query, page };
    const searchParams = new URLSearchParams();
    Object.entries(query).forEach(([key, value]) => {
      searchParams.append(key, value as any);
    });
    await navigate(
      {
        search: searchParams.toString(),
      },
      { replace: true },
    );

    await getTimecards();
  },
  { deep: true },
);

watch(projectMemberSearch, async () => {
  paginationProjectMembers.value = resetPagination(
    paginationProjectMembers.value,
  );
  getProjectMembers();
});

const goToTimecardFromAllTimecards = (
  projectMemberId: number,
  timecardId: number,
) => {
  navigate(
    {
      pathname: `/projects/${props.project
        .hashId!}/admin/members/${projectMemberId}/member/timecards/${timecardId}`,
    },
    {
      state: { from: 'project-admin-timecards' },
    },
  );
};

const createTimecardForUser = async () => {
  if (!projectMemberId) {
    await createTimecardFromAllTimecards();
  } else {
    await createTimecardFromMemberPage();
  }
};

const createTimecardFromAllTimecards = async () => {
  if (!projectMember.value.id) {
    SnackbarStore.triggerSnackbar('Please select an employee.', 2500, 'error');
    return;
  }
  if (!payPeriod.value?.id) {
    SnackbarStore.triggerSnackbar('Please select a pay period.', 2500, 'error');
    return;
  }
  isCreateTimecard.value = true;
  const projectMemberId = projectMember.value.id;
  const createTimecardPayload: CreateTimecardPayload = {
    projectId: props.project.id!,
    payPeriodId: payPeriod.value.id,
    timeZone: DateTime.now().zoneName!,
  };
  const { data }: AxiosResponse<any, any> = await createTimecardForCrewUser(
    projectMemberId,
    createTimecardPayload,
  );
  const timecard: Timecard = data;
  const timecardId = timecard?.id;

  navigate({
    pathname: `/projects/${props.project
      .hashId!}/admin/members/${projectMemberId}/member/timecards/${timecardId}`,
  });
  isCreateTimecard.value = false;
};

const createTimecardFromMemberPage = async () => {
  if (!payPeriod.value.id) {
    SnackbarStore.triggerSnackbar('Please select a pay period.', 2500, 'error');
    return;
  }
  isCreateTimecard.value = true;
  const createTimecardPayload: CreateTimecardPayload = {
    projectId: props.project.id!,
    payPeriodId: payPeriod.value.id,
    timeZone: DateTime.now().zoneName!,
  };
  const { data }: AxiosResponse<any, any> = await createTimecardForCrewUser(
    Number(projectMemberId),
    createTimecardPayload,
  );
  const timecard: Timecard = data;
  const timecardId = timecard?.id;

  navigate({
    pathname: `/projects/${props.project
      .hashId!}/admin/members/${projectMemberId}/member/timecards/${timecardId}`,
  });
  isCreateTimecard.value = false;
};

const goToProjectMember = (projectMemberId: number) => {
  navigate({
    pathname: `/projects/${props.project
      .hashId!}/admin/members/${projectMemberId}/member`,
  });
};

const batchModalIsOpen: Ref<boolean> = ref(false);
const createTimecardModalIsOpen: Ref<boolean> = ref(false);
const batches: Ref<Batch[]> = ref([]);

const statuses: Ref<any[]> = ref([]);

const currentTimecardId: Ref<number | null> = ref(null);

const openBatchModal = (timecardId: number) => {
  batchModalIsOpen.value = true;
  currentTimecardId.value = timecardId;
};

const openCreateTimecardModal = async () => {
  createTimecardModalIsOpen.value = true;
  paginationProjectMembers.value = resetPagination(
    paginationProjectMembers.value,
  );
  projectMemberSearch.value = '';
  projectMember.value = {} as ProjectMember;
  payPeriod.value = {};
  if (!projectMemberId) {
    getProjectMembers();
  }
  getAllPayPeriods(props.project.id!.toString());
};

const removingFromBatch = ref(false);

const removeFromBatch = async (timecard: any) => {
  removingFromBatch.value = true;
  try {
    await removeBatchFromTimecard(timecard.id);
    await getTimecards();
    SnackbarStore.triggerSnackbar('Removed from batch.', 2500, 'success');
  } catch (err) {
    if (axios.isAxiosError(err)) {
      const msg = err.response?.data?.['errors']?.[0]?.message;
      SnackbarStore.triggerSnackbar(msg, 4000, 'error');
    } else {
      SnackbarStore.triggerSnackbar('Failed adding to batch.', 4000, 'error');
    }
  }
  removingFromBatch.value = false;
};

const updatePagination = (pagination: PaginationType): void => {
  paginationProjectMembers.value = pagination;
  getProjectMembers();
};

const resetPagination = (pagination: PaginationType) => {
  pagination.page = 1;
  return pagination;
};

const getAllPayPeriods = async (id: string): Promise<void> => {
  isLoadingPayPeriods.value = true;
  const projectId = Number(id);
  const { data } = await getPayPeriods(projectId);
  payPeriods.value = data.map((pp: any) => {
    const startsAt = DateTime.fromISO(pp.startsAt);
    const endsAt = DateTime.fromISO(pp.endsAt);
    return {
      id: pp.id,
      name: `${startsAt.toFormat('ccc MM/dd')} - ${endsAt.toFormat(
        'ccc MM/dd',
      )}`,
      value: pp.id,
      startsAt,
      endsAt,
    };
  });
  isLoadingPayPeriods.value = false;
};

const getProjectMembers = async (): Promise<void> => {
  isLoadingProjectMembers.value = true;
  const {
    data: { data: members, meta },
  } = await listProjectMembers(
    props.project.id!,
    undefined,
    {
      page: paginationProjectMembers.value.page,
      limit: paginationProjectMembers.value.limit,
    },
    undefined,
    undefined,
    projectMemberSearch.value,
  );
  const paperWorCompletdMembers = members.filter(
    (member: any) => member.startPaperwork.length != 0,
  );
  paperWorCompletdMembers.map((emp: any) => {
    emp.name = `${emp.user.firstName} ${emp.user.lastName}`;
  });
  if (paginationProjectMembers.value.page === 1) {
    projectMembers.value = [];
  }
  projectMembers.value.push(...paperWorCompletdMembers);
  paginationProjectMembers.value.total = meta.total;
  isLoadingProjectMembers.value = false;
};

const getTimecards = async () => {
  const allFilters = [...activeFilters.value];
  if (projectMemberId) {
    allFilters.push({
      id: 'project_member',
      field: 'projectMemberId',
      label: 'ProjectMemberId',
      value: projectMemberId,
      type: FilterUIType.Text,
      operator: FilterOperator.Equals,
      active: true,
    });
  }
  const timecardFilters = convertFiltersToQuery(allFilters);
  const sort = convertSortsToQuery(sortOptions.value);
  // const sort = route.query.sort as string
  const {
    data: { data: members, meta },
  } = await listProjectTimecards(
    props.project.id!,
    timecardFilters,
    sort,
    pagination.value,
  );
  timecards.value = members;
  pagination.value.total = meta.total;
  pagination.value.page = meta.current_page;
};

const loadBatches = async () => {
  const pagination: any = {
    page: 1,
    limit: 100,
  };
  const {
    data: { data: batchesData },
  } = await getBatches(props.project.id!, pagination);
  batches.value = batchesData;
  const batchFilter = filters.value.find((f) => f.id === 'batch');
  if (!batchFilter) {
    return;
  }

  const sessionBatches = getSessionFilters().batch || [];
  batchFilter.options = batchesData.map((b) => ({
    id: `option_${b.id}`,
    value: b.id,
    label: b.capsPayId ? b.capsPayId + ' - ' + b.name : b.name,
    active: sessionBatches.includes(b.id) || false,
  }));
  batchFilter.active = sessionBatches.length > 0;
};

const loadStatuses = async () => {
  const { data: timecardStatusData } = await getTimecardStatuses();
  statuses.value = timecardStatusData;
  const timecardStatusFilter = filters.value.find((f) => f.id === 'status');
  if (!timecardStatusFilter) {
    return;
  }

  const sessionStatuses = getSessionFilters().status || [];
  timecardStatusFilter.options = timecardStatusData.map((b: any) => ({
    id: `option_${b.id}`,
    value: b.id,
    label: b.name,
    active: sessionStatuses.includes(b.id) || false,
  }));
  timecardStatusFilter.active = sessionStatuses.length > 0;
};

const getProjectMember = async () => {
  const { data } = await getProjectMemberById(projectMemberId!);
  projectMember.value = data;
  isProjectMemberSigned.value = data.startPaperwork?.[0]?.crewSigned || false;
};

const isLoaded = ref(false);
const load = async () => {
  await loadBatches();
  await loadStatuses();
  await getProjectMember();
  // let querySort = route.query.sort as string
  // sortOptions.value?.forEach((so) => {
  //   so.current = querySort === so.value
  // })
  let queryPage = route.value.query.page as string;
  pagination.value.page = parseInt(queryPage) || 1;
  await getTimecards();
  isLoaded.value = true;
};

const parseInitialFilters = async () => {
  const query = route.value.query;
  const initialFilters = query.initialFilters as string;
  const sessionFilters = getSessionFilters();
  if (!initialFilters && !sessionFilters) {
    return;
  }

  filters.value = applyQueryStringToFilters(initialFilters, filters.value);
  delete query.initialFilters;

  const searchParams = new URLSearchParams();
  Object.entries(query).forEach(([key, value]) => {
    searchParams.append(key, value as string);
  });

  await navigate(
    {
      search: searchParams.toString(),
    },
    { replace: true },
  );
};

const preSelectDepartments = (departmentIds: Number[]) => {
  const departmentFilter = filters.value.find(
    (filter) => filter.id === 'department_type_name',
  );
  if (!departmentFilter) {
    return;
  }
  departmentFilter.options?.forEach((option: any) => {
    option.active = departmentIds.includes(option.value);
  });
  departmentFilter.active = departmentIds.length > 0;
};

onMounted(async () => {
  await load();
  parseInitialFilters();
  try {
    let departmentIds = [];
    if (props.project) {
      const { data: authMember } = await getCurrentMember(props.project.id);
      if (authMember?.departmentId) departmentIds.push(authMember.departmentId);
    }
    const sessionDepartments = getSessionFilters().department_type_name || [];
    if (sessionDepartments?.length > 0) {
      departmentIds = departmentIds.concat(sessionDepartments);
    }
    const uniqueIds = departmentIds.filter((val, i, a) => a.indexOf(val) === i);
    preSelectDepartments(uniqueIds);
  } catch (err) {
    console.warn('Error fetching current member', err);
  }
});
</script>
