<template>
  <div
    class="relative flex min-h-screen flex-col justify-center overflow-hidden"
  >
    <div
      class="overflow-hidden max-w-lg grid grid-cols-1 sm:grid-cols-2 gap-4 sm:divide-y-0 m-auto px-4 sm:px-0"
    >
      <div
        v-for="action in actions"
        :key="action.title"
        @click="routeTo(action)"
        class="cursor-pointer relative group bg-white hover:bg-gray-50 dark:bg-gray-700 hover:dark:bg-gray-600 p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 rounded-lg"
        :data-testid="`card-${action.title}`"
      >
        <div>
          <span
            :class="[
              action.iconBackground,
              action.iconForeground,
              'rounded-lg inline-flex p-3 ring-4 ring-white dark:ring-gray-700',
            ]"
          >
            <component :is="action.icon" class="h-6 w-6" aria-hidden="true" />
          </span>
        </div>
        <div class="mt-8">
          <h3 class="text-lg font-medium">
            <a class="focus:outline-none">
              <!-- Extend touch target to entire panel -->
              <span class="absolute inset-0" aria-hidden="true" />
              {{ action.title }}
            </a>
          </h3>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
            {{ action.body }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
//TODO - UNUSED fps-1338-add-landing-react-component
import Card from '@/components/library/Card.vue';
import { getUser } from '@/services/users';
import { useAuthStore } from '@/stores/auth';
import type User from '@/types/User';
import {
  ClockIcon,
  DocumentTextIcon,
  MagnifyingGlassIcon,
  UserIcon,
} from '@heroicons/vue/24/outline';
import { mapState } from 'pinia';
import { defineComponent } from 'vue';

export default defineComponent({
  props: {
    navigate: {
      type: Function,
      default: null,
    },
  },
  components: {
    Card,
  },
  data() {
    return {
      user: {} as User,
      actions: [
        {
          title: 'User Manual',
          url: 'https://full-workshop-49c.notion.site/Customer-How-To-Guides-97feada33e944846857bb71c71d1a31b?pvs=74',
          icon: DocumentTextIcon,
          iconForeground: 'text-indigo-700',
          iconBackground: 'bg-indigo-50 dark:bg-indigo-100',
          body: 'Confused where to start? Click here to take a look at our user manual.',
        },
        {
          title: 'Projects',
          path: 'projects',
          icon: ClockIcon,
          iconForeground: 'text-teal-700',
          iconBackground: 'bg-teal-50 dark:bg-teal-100',
          body: 'Click here to navigate to your projects.',
        },
        {
          title: 'Profile',
          path: 'profile',
          icon: UserIcon,
          iconForeground: 'text-purple-700',
          iconBackground: 'bg-purple-50 dark:bg-purple-100',
          body: 'Manage your personal info.',
        },
        {
          title: 'FAQ',
          path: 'faq',
          icon: MagnifyingGlassIcon,
          iconForeground: 'text-sky-700',
          iconBackground: 'bg-sky-50 dark:bg-sky-100',
          body: 'View frequently asked questions.',
        },
      ],
    };
  },
  computed: {
    ...mapState(useAuthStore, ['isLoggedIn', 'getUser']),
  },
  methods: {
    routeTo(action: any) {
      if (action.path) {
        if (this.navigate && typeof this.navigate === 'function') {
          this.navigate(action.path);
        }
      } else {
        window.open(action.url, '_blank');
      }
    },
  },
  async mounted() {
    // TODO once this page is dynamic we don't have to do this, but until there are other requests
    // we need this to make sure it forces a check on import
    await getUser('me');
  },
});
</script>
