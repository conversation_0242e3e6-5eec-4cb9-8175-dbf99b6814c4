import React from 'react';
import PropTypes from 'prop-types';
import _cloneDeep from 'lodash/cloneDeep';
import { DateTime } from 'luxon';
import { Box, Text, DatePicker } from '@/reactComponents/library';
import { PageBackgroundBox, PageSectionsBox } from '../../styledComponents';

import {
  // i9DocumentTypes,
  // i9DocEmpAuthTypes,
  // permResidentWorkAuthTypes, //TODO remove once we're fully useing BE
  // alienWorkAuthTypes,
  requiredEstEmpValidation,
} from '../../utils';
import PageTitle from '../PageTitle';
import { snackbarErr } from '@/reactComponents/library/Snackbar';
import { I9ExpReq } from '@/reactComponents/viewsV2/Onboarding/types';
import PersonOutlineIcon from '@mui/icons-material/PersonOutline';

import { syncProfileData } from '@/services/onboarding';

import {
  Checkbox as CheckboxSplt,
  TextField as TextFieldSplt,
  CheckboxGroup as CheckboxGroupSplt,
  Autocomplete as AutocompleteSplt,
  // DatePicker as DatePickerSplt,
  Section as SectionSplt,
} from '@/reactComponents/spotlight';

import { useForm, Controller } from 'react-hook-form';

import {
  defaultI9Values,
  setI9IncomingValues,
  stringDateFormat,
} from '../../utils';

// import { toJS } from 'mobx';

const styles = {
  docDetails: {
    display: 'flex',
    flexDirection: 'column',
    gap: 2,
  },
  sectionContent: {
    display: 'flex',
    flexDirection: 'column',
    flexWrap: 'wrap',
    gap: 2,
    mt: 2,
    minWidth: 'min(700px, 90vw)',
    maxWidth: '700px',
  },
  sectionIcon: {
    display: 'flex',
    flexShrink: 0,
    width: '48px',
    height: '48px',
    borderRadius: '48px',
    backgroundColor: 'gray.100',
    justifyContent: 'center',
    alignItems: 'center',
  },
};

const I9 = (props) => {
  const {
    init,
    i9Data,
    citizenStatuses,
    docTypes,
    empAuthTypes,
    workAuthTypes,
    onSubmit,
    setUnsaved,
    getWorkAuthTypes,
  } = props;

  React.useEffect(() => {
    init();
  }, [init]);

  const {
    control,
    formState: { isDirty, errors, submitCount },
    setValue,
    watch,
    handleSubmit,
  } = useForm({
    defaultValues: _cloneDeep(defaultI9Values),
  });

  React.useEffect(() => {
    if (i9Data) {
      setI9IncomingValues(i9Data, setValue);
    }
  }, [i9Data, setValue]);

  setUnsaved(isDirty);

  // Set up subscription on value in react-hook-form
  // keeping for reference in case we need to something like this
  // React.useEffect(() => {

  //   const subscription = watch((formValues, { name, type }) => {
  //     if (type !== 'change') return;
  //     if (name === 'employmentAuthorizationType') {
  //
  //   });
  //   return () => subscription.unsubscribe();
  // }, [setValue, trigger, watch]);

  const setSocialSecurityCard = (isSsnCard) => {
    if (isSsnCard) {
      setValue('employmentAuthorizationIssuingAuthority', 'Social Security ', {
        shouldValidate: true,
      });
      setValue('employmentAuthorizationDocumentNumber', 'YOUR SSN HERE', {
        shouldValidate: true,
      });
    } else {
      setValue('employmentAuthorizationIssuingAuthority', '', {
        shouldValidate: submitCount !== 0,
      });
      setValue('employmentAuthorizationDocumentNumber', '', {
        shouldValidate: submitCount !== 0,
      });
    }
  };

  const cascadeChanges = (fieldName, prevValue, newValue) => {
    if (fieldName === 'proofOfIdentity') {
      if (
        newValue?.provesEmploymentEligibility === false &&
        (!prevValue || prevValue?.provesEmploymentEligibility === true)
      ) {
        const ssnCardOption = empAuthTypes.find(
          (option) => option.key === 'ssn-card',
        );
        if (ssnCardOption) {
          setValue('employmentAuthorizationType', ssnCardOption, {
            shouldValidate: true,
          });
        }
        setSocialSecurityCard(true);
      } else if (
        newValue?.provesEmploymentEligibility === true ||
        newValue === null
      ) {
        setValue('employmentAuthorizationType', null, {
          shouldValidate: true,
        });
        setValue('employmentAuthorizationExpirationDate', '', {
          shouldValidate: true,
        });
        setSocialSecurityCard(false);
      }
    } else if (fieldName === 'employmentAuthorizationType') {
      if (newValue?.key === 'ssn-card') {
        setSocialSecurityCard(true);
      } else if (prevValue?.key === 'ssn-card') {
        setSocialSecurityCard(false);
      }
    }
  };

  const errorCountRef = React.useRef();
  errorCountRef.current = Object.keys(errors).length;
  React.useEffect(() => {
    if (submitCount > 0 && errorCountRef.current > 0) {
      snackbarErr(
        `Please correct the errors(${errorCountRef.current}) in the form`,
      );
    }
  }, [submitCount]);

  // This will trigger a render on every change to the form
  // helps to remove validation on require fields when entering new values
  const form = watch();

  //un comment to observe form state
  // console.info(JSON.stringify(form, null, 2));
  // console.info('errors: ', errors);

  const showEmpAuth =
    form.proofOfIdentity &&
    form.proofOfIdentity.provesEmploymentEligibility === false;
  const proofOfIdentityExpDate =
    form?.proofOfIdentity?.expiryRequirement || I9ExpReq.NA;
  let empAuthExp =
    form?.employmentAuthorizationType?.expiryRequirement || I9ExpReq.NA;

  const statusKey = form.citizenshipStatus ? form.citizenshipStatus.key : null;

  let workAuthSelectLabel = '';
  let workAuthNumberLabel = '';
  let workAuthExp = I9ExpReq.NA;

  switch (statusKey) {
    case 'alien_authorized_to_work':
      workAuthNumberLabel = 'Document Number';
      if (form.workAuthorizationType?.key) {
        workAuthNumberLabel = form.workAuthorizationType.name;
        workAuthExp = form.workAuthorizationType.expiryRequirement;
      }
      workAuthSelectLabel = 'Work Authorization Type';

      break;
    case 'permanent_resident':
      workAuthNumberLabel = 'Document Number';
      if (form.workAuthorizationType?.key) {
        workAuthNumberLabel = form.workAuthorizationType.name;
        workAuthExp = form.workAuthorizationType.expiryRequirement;
      }
      workAuthSelectLabel = 'Work Authorization Type';

      break;
    case 'non_citizen_national':
      workAuthNumberLabel = 'USCIS Number';
      if (form.workAuthorizationType?.key) {
        workAuthExp = form.workAuthorizationType.expiryRequirement;
      }
      break;

    default:
      break;
  }

  const isSsnCard = form.employmentAuthorizationType?.key === 'ssn-card';

  return (
    <form
      style={{ width: '100%' }}
      id="i9OnboardingForm"
      onSubmit={handleSubmit(onSubmit)}
      onKeyDown={(e) => {
        if (e.ctrlKey && e.key === 'Enter') {
          const form = document.getElementById('i9OnboardingForm');
          form.requestSubmit();
        }
      }}
      tabIndex={-1}
    >
      <PageBackgroundBox>
        <PageTitle
          topText={'I-9'}
          bottomText={'Identity & Employment Eligibility Verification'}
        />
        <PageSectionsBox>
          <button onClick={syncProfileData}>SYNC</button>
          <SectionSplt
            title={[
              <div
                key={'Section 1. Employee Information & Attestation'}
                className="spotlight_featured-section-title"
              >
                <Box sx={styles.sectionIcon}>
                  <PersonOutlineIcon />
                </Box>
                <h2 className="spotlight_heading-xs">
                  Section 1. Employee Information & Attestation
                </h2>
              </div>,
            ]}
          >
            <Box sx={styles.sectionContent}>
              <Text>
                Select one of the following to attest to your citizenship or
                immigration status.
              </Text>
              <Controller
                name="citizenshipStatus"
                control={control}
                rules={{
                  required: 'This field is required',
                }}
                render={({ field: { value, onChange, onBlur, ref } }) => (
                  <AutocompleteSplt
                    options={citizenStatuses}
                    getOptionLabel={(option) => option.name}
                    isOptionEqualToValue={(option, value) =>
                      option.id === value.id
                    }
                    value={value}
                    aria-label="Citizens Status"
                    onChange={(event, newValue) => {
                      onChange(newValue);
                      const key = newValue?.key;
                      if (
                        key === 'alien_authorized_to_work' ||
                        key === 'permanent_resident'
                      ) {
                        getWorkAuthTypes(key);
                      }
                    }}
                    renderInput={(params) => {
                      return (
                        <TextFieldSplt
                          label="Citizenship / Immigration Status"
                          placeholder="Select"
                          error={errors.citizenshipStatus?.message}
                          onBlur={onBlur}
                          helperText={errors.citizenshipStatus?.message}
                          {...params}
                          value={params.value || ''}
                          inputRef={ref}
                        />
                      );
                    }}
                  />
                )}
              />
              {workAuthSelectLabel && (
                <Controller
                  name="workAuthorizationType"
                  control={control}
                  rules={{ required: 'This field is required' }}
                  render={({ field: { value, onChange, onBlur, ref } }) => {
                    return (
                      <AutocompleteSplt
                        options={workAuthTypes}
                        getOptionLabel={(option) => option.name}
                        isOptionEqualToValue={(option, value) =>
                          option.id === value.id
                        }
                        value={value}
                        aria-label="work authorization type"
                        onChange={(event, newValue) => {
                          onChange(newValue);
                        }}
                        renderInput={(params) => (
                          <TextFieldSplt
                            label={workAuthSelectLabel}
                            placeholder="Select"
                            error={errors.workAuthorizationType?.message}
                            onBlur={onBlur}
                            inputRef={ref}
                            helperText={errors.workAuthorizationType?.message}
                            {...params}
                            value={params.value || ''}
                          />
                        )}
                      />
                    );
                  }}
                />
              )}
              {workAuthNumberLabel && (
                <Box sx={styles.docDetails}>
                  {(workAuthExp === I9ExpReq.Required ||
                    workAuthExp === I9ExpReq.Optional) && (
                    <Controller
                      name="workAuthorizationExpirationDate"
                      control={control}
                      rules={{
                        required: 'This field is required',
                      }}
                      render={({ field: { onChange, value, onBlur, ref } }) => (
                        <>
                          <Box>
                            <DatePicker
                              minDate={DateTime.now()}
                              value={
                                value === ''
                                  ? null
                                  : DateTime.fromFormat(value, stringDateFormat)
                              }
                              inputRef={ref}
                              onBlur={onBlur}
                              onChange={(newValue) => {
                                if (newValue?.isValid === true) {
                                  const strVal =
                                    newValue.toFormat(stringDateFormat);
                                  onChange(strVal);
                                } else if (newValue === null) {
                                  onChange('');
                                } else {
                                  console.error(
                                    'Unexpected date value: ',
                                    newValue,
                                  );
                                }
                              }}
                              slotProps={{
                                field: { clearable: true },
                                textField: {
                                  helperText:
                                    errors.workAuthorizationExpirationDate
                                      ?.message,
                                  error:
                                    !!errors.workAuthorizationExpirationDate
                                      ?.message,
                                  label: 'Expiration Date',
                                },
                              }}
                            />
                          </Box>
                          {/* <Box sx={{ maxWidth: '225px' }}>
                              <DatePickerSplt
                                label={'Spotlight Expiration @@ Date'}
                                error={'This is An Error'}
                                helperText={'This is a helper text'}
                                // value={date}
                                onChange={(newVal) => {
                                  onChange(newVal.toString());
                                }}
                              />
                            </Box> */}
                        </>
                      )}
                    />
                  )}
                  <Controller
                    name="workAuthorizationNumber"
                    control={control}
                    rules={{
                      validate: (value, form) => {
                        const { citizenshipStatus } = form;
                        const key = citizenshipStatus?.key;
                        if (
                          key === 'alien_authorized_to_work' ||
                          key === 'permanent_resident' ||
                          key === 'non_citizen_national'
                        ) {
                          if (!value) {
                            return 'This field is required';
                          }
                          return true;
                        }
                      },
                    }}
                    render={({ field: { onChange, value, onBlur, ref } }) => (
                      <TextFieldSplt
                        label={workAuthNumberLabel}
                        isInvalid={!!errors.workAuthorizationNumber}
                        errorMessage={errors.workAuthorizationNumber?.message}
                        inputRef={ref}
                        onBlur={onBlur}
                        value={value}
                        onChange={(e) => {
                          onChange(e.target.value);
                        }}
                      />
                    )}
                  />
                </Box>
              )}
              <Text variant="baseSemi" sx={{ color: 'text.secondary' }}>
                I am aware that federal law provides for imprisonment and/or
                fines for false statements, or the use of false documents, in
                connection with the completion of this form. I attest, under
                penalty of perjury, that this information, including my
                selection of the box attesting to my citizenship or immigration
                status, is true and correct.
              </Text>
              <Controller
                name="sec1Acknowledgement"
                control={control}
                rules={{
                  required: 'This is required',
                }}
                render={({ field: { value, onChange, ref } }) => {
                  return (
                    // TODO - add ref to checkbox group PLT-422
                    <div tabIndex={-1}>
                      <CheckboxGroupSplt
                        isInvalid={errors.sec1Acknowledgement}
                        errorMessage={errors.sec1Acknowledgement?.message}
                        value={value ? ['sec1Acknowledgement'] : []}
                        onChange={(val) => {
                          onChange(val.includes('sec1Acknowledgement'));
                        }}
                      >
                        <CheckboxSplt
                          value="sec1Acknowledgement"
                          label="I acknowledge this section"
                        />
                      </CheckboxGroupSplt>
                    </div>
                  );
                }}
              />
            </Box>
          </SectionSplt>
          {/* <Section title="Section 2. Proof of Identity & Employment Authorization"> */}
          <SectionSplt
            title={[
              <div
                key="spotlight_featured-section-title"
                className="spotlight_featured-section-title"
              >
                <Box sx={styles.sectionIcon}>
                  <PersonOutlineIcon />
                </Box>
                <h2 className="spotlight_heading-xs">
                  Section 2. Proof of Identity & Employment Authorization
                </h2>
              </div>,
            ]}
          >
            <Box sx={styles.sectionContent}>
              <Box>
                <Controller
                  name="proofOfIdentity"
                  control={control}
                  rules={{
                    required: 'This field is required',
                  }}
                  render={({ field: { value, onChange, onBlur, ref } }) => (
                    <AutocompleteSplt
                      value={value}
                      options={docTypes}
                      getOptionLabel={(option) => option.name}
                      isOptionEqualToValue={(option, value) =>
                        option.id === value.id
                      }
                      onChange={(event, newValue) => {
                        cascadeChanges('proofOfIdentity', value, newValue);

                        onChange(newValue);
                      }}
                      renderInput={(params) => (
                        <TextFieldSplt
                          label={'Document to establish identity'}
                          error={errors.proofOfIdentity?.message}
                          helperText={errors.proofOfIdentity?.message}
                          onBlur={onBlur}
                          placeholder="Select"
                          inputRef={ref}
                          {...params}
                        />
                      )}
                    />
                  )}
                />
              </Box>
              <Box sx={styles.docDetails}>
                <Controller
                  name="proofOfIdentityIssuingAuthority"
                  control={control}
                  rules={{
                    required: 'This field is required',
                  }}
                  render={({ field: { value, onChange, onBlur, ref } }) => (
                    <TextFieldSplt
                      value={value}
                      label="Issuing Authority"
                      onBlur={onBlur}
                      isInvalid={!!errors.proofOfIdentityIssuingAuthority}
                      errorMessage={
                        errors.proofOfIdentityIssuingAuthority?.message
                      }
                      onChange={(e) => {
                        onChange(e.target.value);
                      }}
                      inputRef={ref}
                    />
                  )}
                />
                <Controller
                  name="proofOfIdentityDocumentNumber"
                  control={control}
                  rules={{
                    required: 'This field is required',
                  }}
                  render={({ field: { value, onChange, onBlur, ref } }) => (
                    <TextFieldSplt
                      value={value}
                      label="Document Number"
                      onBlur={onBlur}
                      isInvalid={!!errors.proofOfIdentityDocumentNumber}
                      errorMessage={
                        errors.proofOfIdentityDocumentNumber?.message
                      }
                      onChange={(e) => {
                        onChange(e.target.value);
                      }}
                      inputRef={ref}
                    />
                  )}
                />

                {(proofOfIdentityExpDate === I9ExpReq.Required ||
                  proofOfIdentityExpDate === I9ExpReq.Optional) && (
                  <Controller
                    name="proofOfIdentityExpirationDate"
                    control={control}
                    rules={{
                      required:
                        proofOfIdentityExpDate === I9ExpReq.Required
                          ? 'This field is required'
                          : '',
                    }}
                    render={({ field: { onChange, value, onBlur, ref } }) => {
                      const dateTimeVal =
                        value === ''
                          ? null
                          : DateTime.fromFormat(value, stringDateFormat);
                      return (
                        <Box>
                          <DatePicker
                            minDate={DateTime.now()}
                            value={dateTimeVal}
                            onBlur={onBlur}
                            inputRef={ref}
                            onChange={(newValue) => {
                              if (newValue?.isValid === true) {
                                const strVal =
                                  newValue.toFormat(stringDateFormat);
                                onChange(strVal);
                              } else if (newValue === null) {
                                onChange('');
                              } else {
                                console.error(
                                  'Unexpected date value: ',
                                  newValue,
                                );
                              }
                            }}
                            slotProps={{
                              field: { clearable: true },
                              textField: {
                                helperText:
                                  errors.proofOfIdentityExpirationDate?.message,
                                error:
                                  !!errors.proofOfIdentityExpirationDate
                                    ?.message,
                                label: 'Expiration Date',
                              },
                            }}
                          />
                        </Box>
                      );
                    }}
                  />
                )}
              </Box>
              {showEmpAuth && (
                <>
                  <Box>
                    <Controller
                      name="employmentAuthorizationType"
                      control={control}
                      rules={{
                        validate: requiredEstEmpValidation,
                      }}
                      render={({ field: { value, onChange, onBlur, ref } }) => (
                        <AutocompleteSplt
                          value={value}
                          options={empAuthTypes}
                          getOptionLabel={(option) => option.name}
                          isOptionEqualToValue={(option, value) =>
                            option.id === value.id
                          }
                          onChange={(event, newValue) => {
                            cascadeChanges(
                              'employmentAuthorizationType',
                              value,
                              newValue,
                            );

                            onChange(newValue);
                          }}
                          renderInput={(params) => (
                            <TextFieldSplt
                              label="Document to establish employment authorization"
                              placeholder="Select"
                              helperText={
                                errors.employmentAuthorizationType?.message
                              }
                              error={
                                errors.employmentAuthorizationType?.message
                              }
                              onBlur={onBlur}
                              inputRef={ref}
                              {...params}
                            />
                          )}
                        />
                      )}
                    />
                  </Box>
                  <Box sx={styles.docDetails}>
                    <Controller
                      name="employmentAuthorizationIssuingAuthority"
                      control={control}
                      rules={{ validate: requiredEstEmpValidation }}
                      render={({ field: { onChange, onBlur, ref } }) => (
                        <TextFieldSplt
                          value={watch(
                            'employmentAuthorizationIssuingAuthority',
                          )}
                          label="Issuing Authority"
                          onBlur={onBlur}
                          isInvalid={
                            !!errors.employmentAuthorizationIssuingAuthority
                          }
                          errorMessage={
                            errors.employmentAuthorizationIssuingAuthority
                              ?.message
                          }
                          onChange={(e) => {
                            onChange(e.target.value);
                          }}
                          inputRef={ref}
                          isDisabled={isSsnCard}
                        />
                      )}
                    />
                    <Controller
                      name="employmentAuthorizationDocumentNumber"
                      control={control}
                      rules={{ validate: requiredEstEmpValidation }}
                      render={({ field: { onChange, onBlur, ref } }) => {
                        return (
                          <TextFieldSplt
                            label="Document Number"
                            value={watch(
                              'employmentAuthorizationDocumentNumber',
                            )}
                            onBlur={onBlur}
                            inputRef={ref}
                            isMasked={isSsnCard}
                            isInvalid={
                              !!errors.employmentAuthorizationDocumentNumber
                            }
                            errorMessage={
                              errors.employmentAuthorizationDocumentNumber
                                ?.message
                            }
                            onChange={(e) => {
                              onChange(e.target.value);
                            }}
                            isDisabled={isSsnCard}
                          />
                        );
                      }}
                    />

                    {(empAuthExp === I9ExpReq.Optional ||
                      empAuthExp === I9ExpReq.Required) && (
                      <Box>
                        <Controller
                          name="employmentAuthorizationExpirationDate"
                          control={control}
                          rules={{
                            required:
                              proofOfIdentityExpDate === I9ExpReq.Required
                                ? 'This field is required'
                                : '',
                          }}
                          render={({
                            field: { value, onChange, onBlur, ref },
                          }) => (
                            <DatePicker
                              minDate={DateTime.now()}
                              value={
                                value === ''
                                  ? null
                                  : DateTime.fromFormat(value, stringDateFormat)
                              }
                              onBlur={onBlur}
                              inputRef={ref}
                              onChange={(newValue) => {
                                if (newValue?.isValid === true) {
                                  const strVal =
                                    newValue.toFormat(stringDateFormat);
                                  onChange(strVal);
                                } else if (newValue === null) {
                                  onChange('');
                                } else {
                                  console.error(
                                    'Unexpected date value: ',
                                    newValue,
                                  );
                                }
                              }}
                              slotProps={{
                                field: { clearable: true },
                                textField: {
                                  helperText:
                                    errors.employmentAuthorizationExpirationDate
                                      ?.message,
                                  error:
                                    !!errors
                                      .employmentAuthorizationExpirationDate
                                      ?.message,
                                  label: 'Expiration Date',
                                },
                              }}
                            />
                          )}
                        />
                      </Box>
                    )}
                  </Box>
                </>
              )}
            </Box>
          </SectionSplt>
        </PageSectionsBox>
      </PageBackgroundBox>
    </form>
  );
};

I9.propTypes = {
  init: PropTypes.func.isRequired,
  citizenStatuses: PropTypes.array.isRequired,
  docTypes: PropTypes.array.isRequired,
  workAuthTypes: PropTypes.array.isRequired,
  onSubmit: PropTypes.func.isRequired,
  setUnsaved: PropTypes.func.isRequired,
  empAuthTypes: PropTypes.array.isRequired,
  getWorkAuthTypes: PropTypes.func.isRequired,
  i9Data: PropTypes.object,
};

export default I9;
