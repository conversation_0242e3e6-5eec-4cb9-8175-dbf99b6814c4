// this is the Old admin timecard, we probably dont need this componenet anymore
import { applyPureVueInReact } from 'veaury';
import ProjectAdminMemberTimecardDetailVue from '../../../../../views/projects/admin/member/ProjectAdminMemberTimecardDetail.vue';
import { useAuth, useReactRouter } from '../../../../AppHooks';
import { useOutletContext } from 'react-router';
const ReactProjectAdminMemberTimecardDetail = applyPureVueInReact(
  ProjectAdminMemberTimecardDetailVue,
);

const ProjectAdminMemberTimecardDetail = () => {
  useAuth();
  const context = useOutletContext();
  const { navigate, route } = useReactRouter();
  // console.log('Project admin member timecard detail', context);
  return (
    <ReactProjectAdminMemberTimecardDetail
      projectMember={context.projectMember}
      project={context.project}
      isAdmin={context.isAdmin}
      navigate={navigate}
      route={route}
      refresh={context.refresh}
    />
  );
};

export default ProjectAdminMemberTimecardDetail;
