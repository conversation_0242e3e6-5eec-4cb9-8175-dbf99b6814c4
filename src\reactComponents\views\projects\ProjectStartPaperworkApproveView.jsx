import { applyPureVueInReact } from 'veaury';
import ProjectStartPaperworkApproveViewVue from '../../../views/projects/ProjectStartPaperworkApproveView.vue';
import { useAuth, useReactRouter } from '../../AppHooks';
import { useOutletContext } from 'react-router';
const ReactProjectStartPaperworkApproveView = applyPureVueInReact(
  ProjectStartPaperworkApproveViewVue,
);

const ProjectStartPaperworkApproveView = () => {
  useAuth();
  const { navigate, route } = useReactRouter();
  const context = useOutletContext();
  return (
    <ReactProjectStartPaperworkApproveView
      route={route}
      navigate={navigate}
      project={context.project}
    />
  );
};

export default ProjectStartPaperworkApproveView;
