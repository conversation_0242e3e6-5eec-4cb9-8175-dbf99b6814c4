import type ProjectMember from '@/types/ProjectMember';
import axios, { type AxiosResponse } from 'axios';
import { coreBaseUrl } from '../utils/constants';
import type { CreateTimecardPayload } from './timecards';

export const deleteProjectMember = async (
  projectMemberId: number | string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/project-members/${projectMemberId}`;
  const response = await axios.delete(url, { withCredentials: true });
  return response;
};

export const reactivateProjectMember = async (
  projectMemberId: number | string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/project-members/${projectMemberId}/reactivate`;
  const response = await axios.patch(url, {}, { withCredentials: true });
  return response;
};

export const updateProjectMember = async (
  projectMemberId: string | number,
  onboarding: any,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/project-members/${projectMemberId}`;
  const response = await axios.patch(url, onboarding, {
    withCredentials: true,
  });
  return response;
};

export const getAvailablePayPeriods = async (
  projectMemberId: number | string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/project-members/${projectMemberId}/pay-periods/available`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const makeAdmin = async (
  projectMemberId: number | string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/project-members/${projectMemberId}/make-admin`;
  const response = await axios.patch(url, {}, { withCredentials: true });
  return response;
};

export const revokeAdmin = async (
  projectMemberId: number | string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/project-members/${projectMemberId}/revoke-admin`;
  const response = await axios.patch(url, {}, { withCredentials: true });
  return response;
};

export const getWorkZones = async (
  projectMemberId: number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/project-members/${projectMemberId}/work-zones`;
  const response = await axios.get(url, {
    withCredentials: true,
  });
  return response;
};

export const getWorkStatuses = async (
  projectMemberId: number | string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/project-members/${projectMemberId}/work-statuses`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const getProjectMemberById = async (
  projectMemberId: string | number,
): Promise<AxiosResponse<ProjectMember>> => {
  const url = `${coreBaseUrl()}/project-members/${projectMemberId}`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const getProjectMemberStartPaperwork = async (
  projectMemberId: number | string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/project-members/${projectMemberId}/start-paperwork`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const adminPreviewProjectMemberStartPaperwork = (
  projectMemberId: number | string,
): string => {
  const url = `${coreBaseUrl()}/project-members/${projectMemberId}/start-paperwork/admin/preview`;
  return url;
};

export const adminSignProjectMemberStartPaperwork = async (
  projectMemberId: string | number,
  signature: string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/project-members/${projectMemberId}/start-paperwork/admin/sign`;
  const response = await axios.post(
    url,
    { signature },
    { withCredentials: true },
  );
  return response;
};

export const createTimecardForCrewUser = async (
  id: number | string,
  createTimecard: CreateTimecardPayload,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/project-members/${id}/timecards`;
  const response = await axios.post(url, createTimecard, {
    withCredentials: true,
  });
  return response;
};

export const listProjectMemberTimecards = async (
  projectMemberId: number | string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/project-members/${projectMemberId}/timecards`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const listProjectMemberDocuments = async (
  projectMemberId: string | number,
  useLoanOut: boolean,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/project-members/${projectMemberId}/start-paperwork-draft`;
  const response = await axios.get(url, {
    withCredentials: true,
    params: {
      useLoanOut,
    },
  });
  return response;
};

export const requestLoanOutApproval = async (
  projectMemberId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/project-members/${projectMemberId}/loan-outs/request`;
  const response = await axios.post(url, {}, { withCredentials: true });
  return response;
};

export const approveLoanOut = async (
  projectMemberId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/project-members/${projectMemberId}/loan-outs/approve`;
  const response = await axios.patch(url, {}, { withCredentials: true });
  return response;
};

export const declineLoanOut = async (
  projectMemberId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/project-members/${projectMemberId}/loan-outs/decline`;
  const response = await axios.patch(url, {}, { withCredentials: true });
  return response;
};
