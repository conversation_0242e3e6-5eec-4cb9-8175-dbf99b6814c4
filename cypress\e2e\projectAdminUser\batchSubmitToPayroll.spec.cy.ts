/// <reference types="cypress" />

import BatchModals from '../../pageObjects/timecards/batchModals';
import TimecardModal from '../../pageObjects/timecards/timecardModal';
import {
  interceptAssignTimecardToBatch,
  interceptCreateBatch,
  interceptCreateTimecard,
  interceptTimecardsBatch,
  interceptGetApprovedTimecard,
  interceptSubmittedToPayroll,
} from '../../support/apiTimecardInterceptors';

import TimecardDetails from '../../pageObjects/timecards/timecardDetails';
import { randomBatchName } from '../../utils/batchUtils';
import { getDetailsByProject } from '../../support/apiFlows/getDetailsByProject';

const batchModals = new BatchModals();
const timecardModal = new TimecardModal();
const timecardDetails = new TimecardDetails();

function formatPayPeriod(start: string, end: string): string {
  const [startYear, startMonth, startDay] = start.split('-').map(Number);
  const [endYear, endMonth, endDay] = end.split('-').map(Number);
  const startDate = new Date(startYear, startMonth - 1, startDay);
  const endDate = new Date(endYear, endMonth - 1, endDay);
  const formattedStart = `${String(startDate.getMonth() + 1).padStart(
    2,
    '0',
  )}/${String(startDate.getDate()).padStart(2, '0')}/${String(
    startDate.getFullYear(),
  ).slice(-2)}`;
  const formattedEnd = `${String(endDate.getMonth() + 1).padStart(
    2,
    '0',
  )}/${String(endDate.getDate()).padStart(2, '0')}/${String(
    endDate.getFullYear(),
  ).slice(-2)}`;
  return `${formattedStart} - ${formattedEnd}`;
}

function setupInterceptors() {
  interceptCreateBatch();
  interceptCreateTimecard();
  interceptAssignTimecardToBatch();
}

describe('User Project Admin - Submit to Payroll', () => {
  const employeeName = 'test-project-admin test-project-admin';
  const projectName = Cypress.env('projectNameShort');
  let batchName: string;
  let payPeriod: string;
  let occupation: string;
  let department: string;

  beforeEach(() => {
    batchName = randomBatchName();

    cy.loginWithSession(
      Cypress.env('PHONE_NUMBER_PROJECT_ADMIN'),
      Cypress.env('OTC_CODE'),
      Cypress.env('BASE_URL'),
      'project-admin',
    );

    // Get Details by Api before test
    getDetailsByProject(projectName);
  });

  it('Verify Project Admin can Submit Batch with Approved Timecard to Payroll', () => {
    // ----------- Arrange: Go to Project Time Tab -----------
    cy.log('Arrange: Go to Project Time Tab');
    cy.goToProjectTimeTab();
    setupInterceptors();

    // ----------- Action: Create Batch By UI -----------
    cy.log('Action: Create Batch By UI');
    batchModals.createBatch(batchName);

    // ----------- Assert: Verify Batch Created by API -----------
    cy.log('Assert: Verify Batch Created');
    cy.wait('@createdBatch').then((batchIntercept) => {
      const batchId = batchIntercept.response?.body.id;
      expect(
        batchIntercept.response?.statusCode,
        'Created Batch status code',
      ).to.equal(200);
      Cypress.env('batchId', batchId);

      // ----------- Action: Create Timecard By UI -----------
      cy.log('Action: Create Timecard By UI and Assign Timecard to Batch');
      timecardModal.createTimecard(employeeName);
      batchModals.moveToBatchWithSelection(batchName);

      // ----------- Assert: Verify Timecard is Created and assigned by API -----------
      cy.log('Assert: Verify Timecard is Created and assigned');
      cy.wait('@createTimecard').then((createIntercept) => {
        expect(
          createIntercept.response?.statusCode,
          'Created timecard status code',
        ).to.equal(200);
        const timecardId = createIntercept.response?.body.id;
        const timecardDaysId: string =
          createIntercept?.response?.body.timecardDays?.[2]?.id;
        Cypress.env('timecardId', timecardId);

        cy.wait('@assignedTimecardFromBatch').then((assignIntercept) => {
          expect(
            assignIntercept.response?.statusCode,
            'Assign Timecard status code',
          ).to.equal(200);
          expect(
            assignIntercept.response?.body.id,
            'Assigned timecard id',
          ).to.eq(timecardId);
        });

        // ----------- Assert: Verify Timecard details in Batch by UI -----------
        interceptTimecardsBatch(Cypress.env('projectId'), batchId);
        cy.log('Assert: Verify Timecard details in Batch by UI');
        batchModals.goBackFromTimecard();
        cy.wait('@getTimecardsBatch').then((batchDetailsIntercept) => {
          const timecard = batchDetailsIntercept.response?.body.data[0];
          occupation = timecard.occupation.name;
          department = timecard.projectMember.department.type.name;
          const gross = timecard.gross ?? 0.0;
          payPeriod = formatPayPeriod(
            timecard.payPeriod.startsAt,
            timecard.payPeriod.endsAt,
          );
          batchModals.validateBatchDetails(
            timecardId,
            'Submitted',
            employeeName,
            occupation,
            department,
            payPeriod,
            gross,
          );
        });

        // ----------- Action: Submit Batch to Payroll -----------
        cy.log('Action: Submit Batch to Payroll');
        interceptSubmittedToPayroll(batchId);
        batchModals.submitBatchToPayroll();
        cy.contains(
          'Batch cannot be submitted with unapproved timecards. Please approve all timecards before submitting to payroll.',
        ).should('exist');
        cy.get('[data-testid="Ok-btn"]').click();
        // ----------- Assert: Verify Submit to Payroll checking on the API -----------
        cy.log('Assert: Verify Submit to Payroll checking on the API');
        cy.wait('@submittedToPayroll', { timeout: 10000 }).then((intercept) => {
          expect(intercept.response?.statusCode, 'Assign status code').to.equal(
            400,
          );
        });

        // ----------- Action: Approve Timecard -----------
        cy.log('Action: Approve and sign Timecard');
        batchModals.selectTimecardInBatch(timecardId);
        timecardDetails.fillOutTimecardDetails(timecardDaysId);
        timecardDetails.save();
        timecardDetails.approve();
        cy.signatureApproval();

        // ----------- Assert: Verify Timecard Approval checking on the API -----------
        cy.log('Assert: Verify Timecard Approval checking on the API');
        interceptGetApprovedTimecard();
        cy.wait('@getApprovedTimecard', { timeout: 10000 }).then(
          (getIntercept) => {
            const statusName: string =
              getIntercept.response?.body?.status?.name;
            cy.log(`Final timecard status: ${statusName}`);
            expect(statusName, 'Timecard should be approved').to.eq('Approved');

            // ---------- Action: Go Back to Main page and Send to Payroll -----------
            cy.log('Action: Go Back to Main page and Send to Payroll');
            interceptSubmittedToPayroll(batchId);
            batchModals.goBackFromTimecard();
            batchModals.submitBatchToPayroll();

            // ----------- Assert: Verify Submit to Payroll checking on the API -----------
            cy.log('Assert: Verify Submit to Payroll checking on the API');
            cy.wait('@submittedToPayroll', { timeout: 10000 }).then(
              (intercept) => {
                expect(
                  intercept.response?.statusCode,
                  'Assign status code',
                ).to.equal(200);
                // ----------- Assert: Verify Submit to Payroll option by UI -----------
                cy.log('Assert: Verify Submit to Payroll option by UI');
                batchModals.validateBatchStatus(
                  batchId,
                  'Submitted to Payroll',
                );

                // ----------- Assert: Verify Timecard details in Batch by UI -----------
                cy.log('Assert: Verify Timecard details in Batch by UI');
                batchModals.validateBatchDetails(
                  timecardId,
                  'Processing',
                  employeeName,
                  occupation,
                  department,
                  payPeriod,
                  '80.00',
                );
              },
            );
          },
        );
      });
    });
  });
});
