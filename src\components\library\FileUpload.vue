<template>
  <div class="max-w-xl">
    <label
      class="flex justify-center w-full h-32 px-4 transition bg-white dark:bg-transparent border-2 border-gray-300 dark:border-gray-500 border-dashed rounded-md appearance-none cursor-pointer hover:border-gray-400 focus:outline-none"
    >
      <span v-if="!modelValue" class="flex items-center space-x-2">
        <ArrowUpTrayIcon class="w-6 h-6 text-gray-400" />
        <span class="font-medium text-gray-600 dark:text-gray-400">
          Drop PDF to Attach, or
          <span class="text-blue-600 underline">browse</span>
        </span>
      </span>
      <span v-else class="flex items-center space-x-2">
        <Badge type="success" text="" size="icon" class="w-6 h-6">
          <template #icon>
            <Icon name="checked" class="w-4 h-4" />
          </template>
        </Badge>
        <span class="font-medium text-gray-600 dark:text-gray-400">
          File successfully uploaded. <br />
          Click
          <span @click.prevent="reset()" class="text-blue-600 underline"
            >here</span
          >
          to reset.
        </span>
      </span>
      <input
        type="file"
        name="file_upload"
        class="hidden"
        @change="filesChange(($event.target as any).files!)"
      />
    </label>
  </div>
</template>

<script lang="ts">
import Icon from '@/components/Icon.vue';
import Badge from '@/components/library/Badge.vue';
import { uploadPdf } from '@/services/pdf';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import { ArrowUpTrayIcon, CheckCircleIcon } from '@heroicons/vue/24/outline';
import { defineComponent } from 'vue';

const toBase64 = (file: File) =>
  new Promise<string>((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });

export default defineComponent({
  props: ['modelValue'],
  data() {
    return {};
  },
  methods: {
    reset() {
      this.$emit('update:modelValue', null);
    },
    async filesChange(files: FileList) {
      try {
        const document: string = await toBase64(files[0]);
        const { data } = await uploadPdf({
          document,
          name: 'mileage_form_upload',
        });
        this.$emit('update:modelValue', data.documentId);
      } catch (err) {
        SnackbarStore.triggerSnackbar(
          'Error uploading file, must be a valid pdf',
          2500,
          'error',
        );
      }
    },
  },
  components: { ArrowUpTrayIcon, CheckCircleIcon, Badge, Icon },
});
</script>
