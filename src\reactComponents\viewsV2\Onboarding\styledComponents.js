import { styled, Box } from '@/reactComponents/library';
import NonShrinkBox from '@/reactComponents/viewsV2/sharedComponents/NonShrinkBox';

export const MainTitleBox = styled(Box, { label: 'TitleBox' })(({ theme }) => ({
  display: 'flex',
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: theme.spacing(2),
  [theme.breakpoints.down('md')]: {},
  backgroundColor: theme.palette.background.default,
}));

export const MainBox = styled(Box, { label: 'MainBox' })(({ theme }) => ({
  display: 'flex',
  flexDirection: 'row',
  width: '100%',
  overflowY: 'hidden',
  flex: 1,

  [theme.breakpoints.down('md')]: {
    flexDirection: 'column',
  },
}));

export const StepperBox = styled(Box, { label: 'StepperBox' })(({ theme }) => {
  return {
    display: 'flex',
    flexDirection: 'column',
    width: '300px',
    flexShrink: 0,
    backgroundColor: theme.palette.background.paper,
    padding: `${theme.spacing(2)} ${theme.spacing(1)}`,
    overflow: 'hidden',

    [theme.breakpoints.down('md')]: {
      color: 'black',
      width: '100%',
    },
  };
});
export const FormBox = styled(Box, { label: 'FormBox' })(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  flex: 1,
  border: `1px solid ${theme.palette.gray[300]}`,
  [theme.breakpoints.down('md')]: {
    //height set by ref in RootModal
  },
}));

export const FooterBox = styled(Box, { label: 'FooterBox' })(({ theme }) => ({
  display: 'flex',
  flexDirection: 'row',
  justifyContent: 'space-between',
  padding: theme.spacing(2),
  borderTop: `1px solid ${theme.palette.gray[300]}`,
  gap: theme.spacing(1),
}));

export const ContentBox = styled(Box, { label: 'ContentBox' })(({ theme }) => ({
  display: 'flex',
  flexDirection: 'row',
  flex: 1,
  overflowY: 'auto',
  width: '100%',
  justifyContent: 'center',
  backgroundColor: 'lightpink',
}));

// page sections

export const PageBackgroundBox = styled(Box, { label: 'PageBackgroundBox' })(
  ({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    alignItems: 'center',
    backgroundColor: theme.palette.background.default,
  }),
);
export const PageSectionsBox = styled(NonShrinkBox, {
  label: 'PageSectionsBox',
})(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(2),
  padding: theme.spacing(2),
  alignItems: 'center',
  maxWidth: '800px',
}));
