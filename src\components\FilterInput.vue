<template>
  <div class="flex align-middle">
    <TextInput
      v-if="type === FilterUIType.Text"
      :label="label"
      :modelValue="props.modelValue.value"
      @update:modelValue="emitFilter($event)"
    />
    <div v-if="type === FilterUIType.MultiSelect">
      <div
        class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1"
      >
        {{ label }}
      </div>
      <div
        v-for="(option, optionIdx) in props.modelValue.options"
        :key="option.value"
        class="flex items-center"
      >
        <input
          :id="`filter-${props.modelValue.id}-${optionIdx}`"
          :name="`${props.modelValue.id}[]`"
          :value="option.value"
          type="checkbox"
          :checked="option.active"
          class="h-4 w-4 rounded border-gray-300 dark:border-gray-400 dark:bg-gray-400 text-indigo-600 focus:ring-indigo-500"
          @change="toggleFilterOption(props.modelValue, option)"
        />
        <label
          :for="`filter-${props.modelValue.id}-${optionIdx}`"
          class="ml-3 whitespace-nowrap pr-6 text-sm text-gray-900 dark:text-gray-200"
          >{{ option.label }}</label
        >
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import TextInput from '@/components/library/TextInput.vue';
import { FilterUIType, type Filter, type FilterOption } from '@/types/Filter';
import { deepClone } from '@/utils/deepClone';
import { computed } from 'vue';

const props = defineProps<{
  modelValue: Filter;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: Filter): void;
}>();

const toggleFilterOption = (filter: Filter, option: FilterOption) => {
  const val = deepClone<Filter>(filter);
  if (!val.options) {
    return;
  }
  const idx = val.options.findIndex(
    (o: FilterOption) => o.value === option.value,
  );
  val.options[idx].active = !val.options[idx].active;
  val.active = val.options.some((o) => o.active);
  emit('update:modelValue', val);
};

const emitFilter = (value: string | number | null | undefined) => {
  const val = deepClone(props.modelValue);
  val.value = value;
  emit('update:modelValue', val);
};

const type = computed(() => props.modelValue.type);
const label = computed(() => props.modelValue.label);
</script>
