import axios, { type AxiosResponse } from 'axios';
import { coreBaseUrl } from '../utils/constants';

export const syncProfileData = async (): Promise<AxiosResponse> => {
  const url = `${coreBaseUrl()}/onboarding/user-information/sync`;
  const data = {};
  return await axios({
    url,
    data,
    method: 'POST',
    responseType: 'json',
  });
};

export const updateI9 = async (data: any): Promise<AxiosResponse> => {
  const url = `${coreBaseUrl()}/onboarding/i9`;
  return await axios({
    url,
    data,
    method: 'POST',
    responseType: 'json',
  });
};
