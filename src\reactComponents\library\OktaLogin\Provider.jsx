import React from 'react';
import PropTypes from 'prop-types';

import { Box, Loader } from '@/reactComponents/library';
import Context from './Context';
import Client from './Client';

import config from '@/vite-env';

const styles = {
  loadingContainer: {
    position: 'fixed',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.75)',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
  },
};

const Provider = ({ children }) => {
  const [loadingMessage, setLoadingMessage] = React.useState();
  const client = new Client({
    issuer: config.okta.issuer,
    clientId: config.okta.clientId,
    redirectUri: window.location.origin,
  });
  const [user, setUser] = React.useState(null);
  const [transaction, setTransaction] = React.useState(null);
  const [form, setForm] = React.useState({
    phone: '',
    encryptedPhone: '',
    email: '',
    firstName: '',
    lastName: '',
  });

  const contextValue = React.useMemo(
    () => ({
      client,
      user,
      isAuthenticated: Boolean(user),
      transaction,
      setTransaction,
      contextForm: form,
      setContextForm: setForm,
      login: (user) => setUser(user),
      logout: async () => {
        setLoadingMessage('Logging out...');
        setTransaction(null);
        setUser(null);
        await client.logout();
        setLoadingMessage(null);
      },
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [transaction, user],
  );

  return (
    <Context.Provider value={contextValue}>
      {children}
      {loadingMessage && (
        <Box sx={styles.loadingContainer}>
          <Loader message={loadingMessage} />
        </Box>
      )}
    </Context.Provider>
  );
};
Provider.propTypes = {
  children: PropTypes.node.isRequired,
};

export default Provider;
