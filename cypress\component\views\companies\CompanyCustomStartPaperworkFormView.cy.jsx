import { createMemoryRouter, RouterProvider, Outlet } from 'react-router-dom';
import { SnackbarProvider } from 'notistack';
import PropTypes from 'prop-types';

import CompanyCustomStartPaperworkFormView from '@/reactComponents/views/companies/CompanyCustomStartPaperworkFormView';
import 'cypress-file-upload';

const mockCompany = {
  id: 123,
  name: 'Test Company',
};

const LayoutWithOutletContext = ({ context }) => {
  return (
    <SnackbarProvider>
      <Outlet context={context} />
    </SnackbarProvider>
  );
};

LayoutWithOutletContext.propTypes = {
  context: PropTypes.object.isRequired,
};

// TestWrapper to simulate React Router context
const TestWrapper = () => {
  const router = createMemoryRouter(
    [
      {
        path: '/',
        handle: { editMode: false },
        element: (
          <LayoutWithOutletContext
            context={{ editMode: false, company: mockCompany }}
          />
        ),
        children: [
          {
            path: '/',
            element: <CompanyCustomStartPaperworkFormView />,
            handle: { editMode: false },
          },
        ],
      },
      {
        path: '/companies/123/document-templates',
        element: (
          <div data-testid="dashboard-document-templates">
            dashboard-document-templates
          </div>
        ),
      },
    ],
    { initialEntries: ['/'] },
  );

  return <RouterProvider router={router} />;
};

TestWrapper.propTypes = {
  company: PropTypes.object.isRequired,
};

describe('CompanyCustomStartPaperworkFormView component testing', () => {
  beforeEach(() => {
    cy.intercept(
      'GET',
      '**/v2/api/core/project-document-template-work-location-settings',
      {
        statusCode: 200,
        body: [
          {
            id: 1,
            name: 'All',
            key: 'all',
            description: 'Should be generated for all locations.',
          },
          {
            id: 2,
            name: 'Specific Work Locations',
            key: 'specific_work_locations',
            description:
              'Should be generated for selected locations Including or Not including.',
          },
        ],
      },
    ).as('workLocationSetting');

    cy.intercept('GET', '**/v2/api/core/project-document-template-types/', {
      statusCode: 200,
      body: {
        data: [
          {
            id: 1,
            key: 'non_union',
            name: 'Non Union',
            description: 'Only for non union project members',
          },
          {
            id: 2,
            key: 'union',
            name: 'Union',
            description: 'Only for union project members',
          },
          {
            id: 3,
            key: 'all',
            name: 'Non-Union and Union',
            description: 'For all project members',
          },
          {
            id: 4,
            key: 'specific_unions',
            name: 'Specific Union(s)',
            description:
              'For when there is a specific set of unions that apply to this document template',
          },
        ],
      },
    }).as('documentTemplatesTypes');

    cy.intercept(
      'GET',
      '**/v2/api/core/project-document-template-loan-out-settings',
      {
        statusCode: 200,
        body: [
          {
            id: 1,
            name: 'Loan Out and Non Loan Out',
            key: 'loan_out_and_non_loan_out',
            description:
              'Should be generated regardless of if the user is using a loan out or not.',
          },
          {
            id: 2,
            name: 'Loan Out',
            key: 'loan_out',
            description:
              'Should only be generated if the user is using a loan out.',
          },
          {
            id: 3,
            name: 'Non Loan Out',
            key: 'non_loan_out',
            description:
              'Should only be generated if the user is not using a loan out.',
          },
        ],
      },
    ).as('documentTemplatesLoanOut');

    cy.intercept('GET', '**/v2/api/core/inclusion-statuses', {
      statusCode: 200,
      body: [
        {
          id: 1,
          name: 'Including',
          key: 'including',
          description: 'Including the selections',
        },
        {
          id: 2,
          name: 'Not Including',
          key: 'not_including',
          description: 'Not including the selections',
        },
      ],
    }).as('inclusionStatuses');

    cy.intercept(
      'GET',
      '**/v2/api/core/shoot-locations/?search=&page=1&limit=50**',
      {
        statusCode: 200,
        body: {
          workLocationList: [
            {
              locationId: 29,
              locationName: 'Alaska',
              locationCode: 'AK',
              state: 'AK',
              zipcode: '99801',
              country: 'US',
            },
            {
              locationId: 30,
              locationName: 'Alabama',
              locationCode: 'AL',
              state: 'AL',
              zipcode: '36101',
              country: 'US',
            },
          ],
        },
      },
    ).as('shootLocation');

    cy.intercept('GET', '**/v2/api/core/unions/?search=&page=1&limit=50**', {
      statusCode: 200,
      body: {
        unionList: [
          {
            id: 579,
            name: '1',
            number: '1',
            unionLocalId: 579,
            description: 'LOCAL ONE',
          },
          {
            id: 928,
            name: '10',
            number: '10',
            unionLocalId: 928,
            description: '10',
          },
        ],
      },
    }).as('unions');
    cy.mount(<TestWrapper />);
  });

  it('should present the Company Custom StartPaperwork component correctly', () => {
    // ----------- Main Labels and Fields -----------
    cy.contains('Create Custom Start Paperwork').should('be.visible');
    cy.contains('Name').should('be.visible');
    cy.get('[data-testid="Name-input"]').should('be.visible').and('be.enabled');
    cy.contains('Schema').should('be.visible');
    cy.get('[data-testid="custom-paperwork-schema-input"]').should(
      'be.visible',
    );
    cy.contains('Countersign Schema').should('be.visible');
    cy.get('[data-testid="custom-paperwork-countersign-schema-input"]').should(
      'be.visible',
    );
    cy.contains('Work Location Setting').should('be.visible');

    // ----------- Work Location Dropdown -----------
    cy.get('[data-testid="custom-paperwork-work-location-dropdown"]')
      .should('be.visible')
      .click();
    cy.get('[data-testid="custom-paperwork-work-location-dropdown"]')
      .contains('[role="menuitem"]', 'Specific Work Locations')
      .should('exist');
    cy.get('[data-testid="custom-paperwork-work-location-dropdown"]')
      .contains('[role="menuitem"]', 'All')
      .should('exist')
      .click();

    // ----------- Union Setting Dropdown -----------
    cy.contains('Union Setting').should('be.visible');
    cy.get('[data-testid="custom-paperwork-union-setting-dropdown"]')
      .should('be.visible')
      .click();
    cy.get('[data-testid="custom-paperwork-union-setting-dropdown"]')
      .contains('[role="menuitem"]', 'Union')
      .should('exist');
    cy.get('[data-testid="custom-paperwork-union-setting-dropdown"]')
      .contains('[role="menuitem"]', 'Non-Union and Union')
      .should('exist');
    cy.get('[data-testid="custom-paperwork-union-setting-dropdown"]')
      .contains('[role="menuitem"]', 'Specific Union(s)')
      .should('exist');
    cy.get('[data-testid="custom-paperwork-union-setting-dropdown"]')
      .contains('[role="menuitem"]', 'Non Union')
      .should('exist')
      .click();

    // ----------- Loan Out Setting Dropdown -----------
    cy.contains('Loan Out Setting').should('be.visible');
    cy.get('[data-testid="custom-paperwork-loan-out-dropdown"]')
      .should('be.visible')
      .click();
    cy.get('[data-testid="custom-paperwork-loan-out-dropdown"]')
      .contains('[role="menuitem"]', 'Loan Out and Non Loan Out')
      .should('exist');
    cy.get('[data-testid="custom-paperwork-loan-out-dropdown"]')
      .contains('[role="menuitem"]', 'Loan Out')
      .should('exist');
    cy.get('[data-testid="custom-paperwork-loan-out-dropdown"]')
      .contains('[role="menuitem"]', 'Non Loan Out')
      .should('exist')
      .click();

    // ----------- Toggles -----------
    cy.contains('Send to Payroll').should('be.visible');
    cy.contains('Optional document').should('be.visible');
    cy.get('[data-testid="custom-send-payroll-toggle"]').should('exist');
    cy.get('[data-testid="custom-optional-document-toggle"]').should('exist');
    cy.get('[data-testid="custom-send-payroll-toggle"] [role="switch"]').should(
      'have.attr',
      'aria-checked',
      'true',
    );
    cy.get(
      '[data-testid="custom-optional-document-toggle"] [role="switch"]',
    ).should('have.attr', 'aria-checked', 'false');

    // ----------- File Upload Section -----------
    cy.get('label[for="file_upload"], label input[type="file"]').should(
      'exist',
    );
    cy.get('label')
      .should('have.class', 'flex')
      .and('contain.text', 'Drop PDF to Attach')
      .and('contain.text', 'browse');
    cy.get('label svg[data-slot="icon"]').should('exist');
    cy.get('label input[type="file"]')
      .should('exist')
      .and('have.class', 'hidden');

    // ----------- Action Buttons -----------
    cy.contains('button', 'Save')
      .should('exist')
      .and('be.visible')
      .and('not.be.disabled');
    cy.contains('button', 'Back')
      .should('exist')
      .and('be.visible')
      .and('not.be.disabled');
  });

  it('should click the back option any moment', () => {
    cy.contains('button', 'Back').should('exist').click();
    cy.get('[data-testid="dashboard-document-templates"]').should('exist');
  });

  it('should show Including/Not Including options and a search field when Specific Work Locations is selected', () => {
    // Open the Work Location Setting dropdown and select "Specific Work Locations"
    cy.get('[data-testid="custom-paperwork-work-location-dropdown"]').click();
    cy.get('[data-testid="custom-paperwork-work-location-dropdown"]')
      .contains('[role="menuitem"]', 'Specific Work Locations')
      .click();

    // Validate the Including/Not Including options are visible
    cy.get('[data-testid="custom-paperwork-inclusion-dropdown"]').click();
    cy.contains('Including').should('be.visible');
    cy.contains('Not Including').should('be.visible').click();

    // Validate the search field for locations is visible
    cy.get('[data-testid="custom-paperwork-locations-dropdown"]')
      .should('be.visible')
      .click();
    cy.get('[data-testid="custom-paperwork-locations-dropdown"]')
      .parentsUntil('[data-testid="custom-paperwork-locations-dropdown"]')
      .find('input[type="checkbox"]')
      .check();
  });

  it('should show Including/Not Including options and a search field when Specific Union(s) is selected', () => {
    // Open the Union Setting dropdown and select "Specific Union(s)"
    cy.get('[data-testid="custom-paperwork-union-setting-dropdown"]').click();
    cy.get('[data-testid="custom-paperwork-union-setting-dropdown"]')
      .contains('[role="menuitem"]', 'Specific Union(s)')
      .click();

    // Validate the Including/Not Including options are visible
    cy.get('[data-testid="custom-specific-unions-dropdown"]').click();
    cy.contains('Including').should('be.visible');
    cy.contains('Not Including').should('be.visible').click();

    // Validate the search field for unions is visible
    cy.get('[data-testid="custom-unions-locations-dropdown"]')
      .should('be.visible')
      .click();
    cy.get('[data-testid="custom-unions-locations-dropdown"]')
      .parentsUntil('[data-testid="custom-unions-locations-dropdown"]')
      .find('input[type="checkbox"]')
      .check();
  });

  it('should toggle the send payroll and optional document switches', () => {
    // Click to turn off send payroll toggle
    cy.get(
      '[data-testid="custom-send-payroll-toggle"] [role="switch"]',
    ).click();
    cy.get('[data-testid="custom-send-payroll-toggle"] [role="switch"]').should(
      'have.attr',
      'aria-checked',
      'false',
    );
    // Click to turn on optional document toggle
    cy.get(
      '[data-testid="custom-optional-document-toggle"] [role="switch"]',
    ).click();
    cy.get(
      '[data-testid="custom-optional-document-toggle"] [role="switch"]',
    ).should('have.attr', 'aria-checked', 'true');

    // Click again to toggle back
    cy.get(
      '[data-testid="custom-send-payroll-toggle"] [role="switch"]',
    ).click();
    cy.get('[data-testid="custom-send-payroll-toggle"] [role="switch"]').should(
      'have.attr',
      'aria-checked',
      'true',
    );

    cy.get(
      '[data-testid="custom-optional-document-toggle"] [role="switch"]',
    ).click();
    cy.get(
      '[data-testid="custom-optional-document-toggle"] [role="switch"]',
    ).should('have.attr', 'aria-checked', 'false');
  });
});
