import React from 'react';

import OnboardingStore from '../store';

import { Loader } from '@/reactComponents/library';

import I9 from './pages/I9';
import Project from './pages/Project';
import Review from './pages/Review';

import { observer } from 'mobx-react-lite';
const LazyLoanOut = React.lazy(() => import('./pages/LoanOut'));
const LazyPersonal = React.lazy(() => import('./pages/Personal'));

export const preLoadPersonal = () => {
  import('./pages/Personal');
};

const Steps = () => {
  const onboardingStep = OnboardingStore.onboardingStep;

  let content;
  switch (onboardingStep?.id) {
    case 'i9':
      content = (
        <I9
          init={OnboardingStore.initI9}
          i9Data={OnboardingStore.i9Data}
          citizenStatuses={OnboardingStore.citizenStatuses}
          docTypes={OnboardingStore.i9DocTypes}
          empAuthTypes={OnboardingStore.i9DocEmpAuthTypes}
          workAuthTypes={OnboardingStore.i9DocWorkAuthTypes}
          onSubmit={OnboardingStore.submitI9}
          setUnsaved={OnboardingStore.setUnsaved}
          getWorkAuthTypes={OnboardingStore.getWorkAuthTypes}
        />
      );
      break;
    case 'personal':
      content = (
        <React.Suspense
          fallback={
            <div>
              <Loader />
            </div>
          }
        >
          <LazyPersonal />
        </React.Suspense>
      );
      break;
    case 'project':
      content = <Project />;
      break;
    case 'loan-out':
      content = (
        <React.Suspense
          fallback={
            <div>
              <Loader />
            </div>
          }
        >
          <LazyLoanOut />
        </React.Suspense>
      );
      break;
    case 'review':
      content = <Review />;
      break;
    default:
      content = <div>Default Content</div>;
      break;
  }

  return content;
};

Steps.propTypes = {};

export default observer(Steps);
