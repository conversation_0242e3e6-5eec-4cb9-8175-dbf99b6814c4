import React from 'react';
import { Box } from '@mui/material';
import { StyledWorkDaysGrid } from '../../library/DataGrid/StyledDataGrid';
import {
  GridRow as MuiDataGridRow,
  GRID_TREE_DATA_GROUPING_FIELD,
} from '@mui/x-data-grid-pro';

import PropTypes from 'prop-types';
import { useGridApiRef } from '@mui/x-data-grid-pro';

import {
  LEFT_COLUMNS,
  timecardColumns,
  ADDITIONAL_FIELDS,
  Tree_Data_Grouping_Col_Def as RIGHT_COLUMNS,
  TIMECARD_TABLE_FIELDS,
} from './timecardTableUtils';

//components
import AdditionalFields from './AdditionalFields';
import TableReRateRow from './TableReRateRow';

import _cloneDeep from 'lodash/cloneDeep';

const pinnedColumns = {
  left: ['isActive', 'date'],
  // right: ['__row__actions__'],
  right: [GRID_TREE_DATA_GROUPING_FIELD],
};

const TimecardTable = (props) => {
  const {
    timecard = { timecardDays: [] },
    triggerRecalculateAdditionalFields,
    reRateRows = [],
    trackingDetails,
    tableColumns = timecardColumns,
    updateTimecard,
    displayErrors,
    readOnlyMode,
  } = props;
  const { timecardDays } = timecard;
  const apiRef = useGridApiRef();

  React.useEffect(() => {
    if (apiRef.current.state) {
      apiRef.current.state.displayErrors = displayErrors;
    }
  }, [apiRef, displayErrors]);

  const rows = [...timecardDays, ...reRateRows];

  const [additionalFields, setAdditionalFields] = React.useState(
    _cloneDeep(ADDITIONAL_FIELDS),
  );
  const hiddenColumns = React.useMemo(() => {
    const hiddenColumns = additionalFields?.reduce?.((hiddenCols, option) => {
      if (option.selected === false) {
        option.tableFields.forEach((field) => {
          hiddenCols[field] = false;
        });
      } else if (option.selected === true) {
        if (option.label === 'Grace') {
          const meal2Visible = additionalFields.find(
            (f) => f.label === 'Meal 2',
          )?.selected;
          if (!meal2Visible) {
            hiddenCols[TIMECARD_TABLE_FIELDS.meal2Grace] = false;
          }
        }
      }
      return hiddenCols;
    }, {});
    if (!trackingDetails?.allowed) {
      hiddenColumns['lineNumber'] = false;
    }
    if (!timecard?.holidayDates || timecard?.holidayDates?.length === 0) {
      hiddenColumns['hasHoliday'] = false;
    }
    if (timecard.timecardDays) {
      const hasAnyNDB = timecard.timecardDays.some(
        (day) => day.isActive && day.hasNdb,
      );
      if (!hasAnyNDB) {
        hiddenColumns['generalCrewCall'] = false;
      } else {
        hiddenColumns['generalCrewCall'] = true;
      }
    }
    return hiddenColumns;
  }, [additionalFields, trackingDetails, timecard]);

  return (
    <Box>
      <AdditionalFields
        additionalFields={additionalFields}
        setAdditionalFields={setAdditionalFields}
        triggerRecalculateAdditionalFields={triggerRecalculateAdditionalFields}
        timecard={timecard}
        updateTimecard={updateTimecard}
        readOnlyMode={readOnlyMode}
      />
      <StyledWorkDaysGrid
        apiRef={apiRef}
        disableVirtualization // important for tab key navigation
        hideFooter
        disableColumnMenu
        disableColumnSorting
        disableColumnReorder
        treeData
        rows={rows}
        columns={[...LEFT_COLUMNS, ...tableColumns]}
        pageSize={7}
        getRowHeight={({ model: row }) => {
          const isTreeRow = 'parentRowId' in row;
          return isTreeRow ? 72 : 60;
        }}
        columnHeaderHeight={44}
        pinnedColumns={pinnedColumns}
        editMode="row"
        isCellEditable={(params) => {
          if (readOnlyMode) return false;
          if (!params.row.isActive) return false;
          if (
            params.field === 'hasHoliday' &&
            timecard?.holidayDates?.length > 0
          ) {
            return timecard.holidayDates.some((holiday) => {
              const rowDate = params.row.date?.toISODate();
              return holiday?.holidayDate?.toISODate() === rowDate;
            });
          }
          return true;
        }}
        onCellClick={(params, e, actions) => {
          if (params.cellMode === 'view' && params.isEditable) {
            actions.api.startRowEditMode({ id: params.id });
          }
        }}
        slots={{
          row: CustomRow,
        }}
        columnVisibilityModel={hiddenColumns}
        isGroupExpandedByDefault={(rowInfo) => {
          const id = rowInfo?.id;
          let row = apiRef.current?.getRow?.(id);
          if (!row) {
            // for initial timecard load
            row = timecard?.timecardDays?.find((day) => day.id === id);
          }
          return !!row?.isActive;
        }}
        getTreeDataPath={(row) => {
          const isTreeChild = 'parentRowId' in row;
          const path = isTreeChild ? [row.parentRowId, row.id] : [row.id];
          return path;
        }}
        groupingColDef={RIGHT_COLUMNS}
      />
    </Box>
  );
};

const CustomRow = React.forwardRef((props, ref) => {
  const isTreeRow = 'parentRowId' in props.row;
  if (isTreeRow) {
    return <TableReRateRow ref={ref} {...props} />;
  }
  // const handleKeyDown = (e) => {
  //   console.log('---Row Key Down:', e, e.key);
  //   console.log(props);
  // };
  return <MuiDataGridRow ref={ref} {...props} />;
});

CustomRow.propTypes = {
  row: PropTypes.object.isRequired,
};

TimecardTable.propTypes = {
  timecard: PropTypes.object.isRequired,
  trackingDetails: PropTypes.object.isRequired,
  triggerRecalculateAdditionalFields: PropTypes.object.isRequired,
  reRateRows: PropTypes.array.isRequired,
  tableColumns: PropTypes.array.isRequired,
  updateTimecard: PropTypes.func.isRequired,
  displayErrors: PropTypes.object.isRequired,
  readOnlyMode: PropTypes.bool.isRequired,
};

export default TimecardTable;
