<script setup lang="ts">
import Toolbar from '@/components/Toolbar.vue';
import Snackbar from '@/components/library/Snackbar.vue';
import { isLoggedIn } from '@/services/auth';
import { usePermissionStore } from '@/stores/permission';
import { onMounted, onUnmounted, ref, watch } from 'vue';
import { RouterView } from 'vue-router';
import { useAppStore } from './stores/app';
import { useAuthStore } from './stores/auth';
import { useSnackbarStore } from './stores/snackbar';

const snackbarMsg = ref(null as string | null);
const snackbarType = ref(null as string | null);

const snackbar = ref(null as typeof Snackbar | null);

const permissionStore = usePermissionStore();
const snackbarStore = useSnackbarStore();
const appStore = useAppStore();
const authStore = useAuthStore();

const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
const handleDarkModeChange = () => {
  appStore.setColorModeSettings();
};

snackbarStore.$subscribe((_, state) => {
  if (!state) return;
  const { msg, duration = 2500, type } = state.notification!;
  snackbarMsg.value = msg;
  snackbarType.value = type;
  snackbar.value!.show(duration);
});

onMounted(async () => {
  const loggedIn = (await isLoggedIn()).data;
  if (loggedIn) {
    await permissionStore.setPermissions();
  }
  handleDarkModeChange();
  darkModeMediaQuery.addEventListener('change', handleDarkModeChange);
});

onUnmounted(() => {
  darkModeMediaQuery.removeEventListener('change', handleDarkModeChange);
});

watch(
  () => appStore.getIsDarkModeEnabled,
  (newVal) => {
    if (newVal) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  },
  { immediate: true },
);
</script>

<style>
@import '@/assets/base.css';
</style>

<template>
  <Toolbar class="z-20" />
  <Snackbar ref="snackbar" :msg="snackbarMsg" :type="snackbarType" />
  <RouterView class="pt-16" />
</template>
