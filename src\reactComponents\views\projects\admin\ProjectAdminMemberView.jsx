// Note: React re-write of ProjectAdminMemberView.vue
import React from 'react';
import { useAuth, useReactRouter, NavigateRelativeTo } from '../../../AppHooks';
import { useOutletContext, Outlet } from 'react-router';

import SecondaryTab from '../../../library/SecondaryTab';

import { useDidMount } from '@/reactComponents/utils/customHooks';
import ProjectStore from '@/reactComponents/stores/project';
import ProjectAdminMemberHeader from './member/ProjectAdminMemberHeader';

const tabs = [
  {
    label: 'Personal Info',
    key: 'personalInfo',
    path: 'personal-info',
    routeName: 'project-admin-member-detail-personal-info',
  },
  {
    label: 'Onboarding',
    key: 'onboarding',
    path: 'onboarding',
    routeName: 'project-admin-member-detail-onboarding',
  },
  {
    label: 'Loan Out',
    key: 'loanOut',
    path: 'loan-out',
    routeName: 'project-admin-member-detail-loan-out',
  },
  {
    label: 'Start Paperwork',
    key: 'startPaperwork',
    path: 'start-paperwork',
    routeName: 'project-admin-member-detail-start-paperwork',
  },
  {
    label: 'Timecards',
    key: 'timecards',
    path: 'timecards',
    routeName: 'project-admin-member-detail-timecards',
  },
];

const ProjectAdminMemberView = () => {
  useAuth();
  const { navigate, route } = useReactRouter();
  const match = route?.match;
  const handle = match?.handle;
  const redirect = handle?.redirect;
  const hideMemberHeader = route?.match?.handle?.hideMemberHeader;

  const context = useOutletContext();
  const project = context.project;
  const isAdmin = context.isAdmin;

  const currentTab = tabs.find((tab) =>
    route.location.pathname.includes(tab.path),
  );

  const [state, setState] = React.useState({
    user: null,
    userCrew: {},
    startPaperwork: null,
    projectMember: undefined,
    loanOut: null,
    userId: null,
  });

  const load = async () => {
    await getProjectMember();
  };

  const getProjectMember = async () => {
    const projectMember = await ProjectStore.fetchProjectMember(
      route.params.projectMemberId.toString(),
    );

    setState({
      ...state,
      projectMember: projectMember,
      user: projectMember.user,
      userId: projectMember.userId,
    });
  };

  const goTo = (tab) => {
    const path = `/projects/${project.hashId}/admin/members/${route.params.projectMemberId}/member/${tab.path}`;
    navigate({
      pathname: path,
    });
  };

  useDidMount(() => {
    load();
  }, []);

  //TODO do we need to 80% width everywhere
  const v2Spacing = hideMemberHeader
    ? ''
    : 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8';

  return (
    <div className={v2Spacing}>
      {redirect && (
        <NavigateRelativeTo to={redirect.to} match={redirect.match} />
      )}
      {!hideMemberHeader && (
        <div className="py-2 mb-6">
          {state.projectMember && (
            <ProjectAdminMemberHeader refresh={load} className="mb-2" />
          )}
          {tabs.map((tab) => {
            return (
              <SecondaryTab
                key={tab.label}
                tab={tab}
                currentTab={currentTab}
                tabClick={goTo}
              />
            );
          })}
        </div>
      )}
      {state.user && state.projectMember && (
        <Outlet
          context={{
            user: state.user,
            userCrew: state.userCrew,
            project: project,
            isAdmin: isAdmin,
            projectMember: state.projectMember,
            refresh: load,
            hideMemberHeader: hideMemberHeader,
          }}
        />
      )}
    </div>
  );
};

export default ProjectAdminMemberView;
