/// <reference types="cypress" />

export default class DocumentTemplates {
  createPaperWork(): void {
    cy.get('[data-testid="company-paperwork-add-btn"]').should('exist').click();
  }

  uploadPaperWork(): void {
    cy.get('input[name="file_upload"]')
      .should('exist')
      .should('have.attr', 'type', 'file')
      .attachFile('customPaperwork.pdf', { force: true });

    cy.contains('File successfully uploaded.').should('exist');
  }

  fillTemplatesInformation(
    name?: string,
    option?: string,
    unionSetting?: string,
    loanOutSetting?: string,
  ): void {
    if (name) {
      cy.get('[data-testid="custom-paperwork-name-input"] input').type(name);
    }
    if (option) {
      cy.get(
        'div[data-testid="custom-paperwork-work-location-dropdown"] button',
      ).click();
      cy.get('[role="menuitem"]').contains(option).click();
      cy.get(
        'div[data-testid="custom-paperwork-work-location-dropdown"] button',
      ).should('contain.text', option);
    }
    if (unionSetting) {
      cy.get('[data-testid="custom-paperwork-union-setting-dropdown"]').click();
      cy.get('[role="menuitem"]').contains(unionSetting).click();
      cy.get(
        'div[data-testid="custom-paperwork-union-setting-dropdown"] button',
      ).should('contain.text', unionSetting);
    }
    if (loanOutSetting) {
      cy.get('[data-testid="custom-paperwork-loan-out-dropdown"]').click();
      cy.get('[role="menuitem"]').contains(loanOutSetting).click();
      cy.get(
        'div[data-testid="custom-paperwork-loan-out-dropdown"] button',
      ).should('contain.text', loanOutSetting);
    }
  }

  editTemplatesInformation(
    name?: string,
    option?: string,
    unionSetting?: string,
    loanOutSetting?: string,
  ): void {
    cy.wait(8000);
    if (name) {
      cy.get('[data-testid="custom-paperwork-name-input"] input').clear();
      cy.get('[data-testid="custom-paperwork-name-input"] input').type(name);
    }
    if (option) {
      cy.get(
        'div[data-testid="custom-paperwork-work-location-dropdown"] button',
      ).click();
      cy.get('[role="menuitem"]').contains(option).click();
    }
    if (unionSetting) {
      cy.get('[data-testid="custom-paperwork-union-setting-dropdown"]').click();
      cy.get('[role="menuitem"]').contains(unionSetting).click();
    }
    if (loanOutSetting) {
      cy.get('[data-testid="custom-paperwork-loan-out-dropdown"]').click();
      cy.get('[role="menuitem"]').contains(loanOutSetting).click();
    }
  }

  fillTemplatesSchema(): void {
    cy.get('[data-testid="custom-paperwork-countersign-schema-input"]').type(
      '{"schemas":[{}]}',
      { parseSpecialCharSequences: false },
    );
  }
  saveTemplatesSchema(): void {
    cy.wait(1000);
    cy.contains('button', 'Save').should('be.visible').click();
  }

  editPaperWork(documentName: string): void {
    cy.get(`[data-testid="custom-doc-edit-${documentName}"]`)
      .should('exist')
      .click();
  }
  deletePaperWork(documentName: string): void {
    cy.get(`[data-testid="custom-doc-delete-${documentName}"]`)
      .should('exist')
      .click();
  }

  documentTemplatePage(documentName: string) {
    cy.contains(
      'Custom documents Activate or inactivate custom documents, while also editing assignment rules.',
    ).should('be.visible');
    cy.get(`[data-testid="custom-doc-${documentName}"]`).should('exist');
  }
}
