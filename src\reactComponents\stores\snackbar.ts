import { makeAutoObservable } from 'mobx';
// import { defineStore } from 'pinia'
import type { SnackType } from '@/types/Snackbar';
export interface Notification {
  msg: string;
  type: SnackType;
  duration: number;
}

class Snackbar {
  notification: Notification | null = null;

  constructor() {
    makeAutoObservable(this);
  }

  get getNotification() {
    return this.notification;
  }

  triggerSnackbar(
    msg: string,
    duration: number = 2500,
    type: SnackType = 'success',
  ) {
    console.debug('snack msg: ', msg);
    this.notification = { msg, duration, type };
  }
}

const SnackbarStore = new Snackbar();
export default SnackbarStore;
