import React from 'react';
import { Outlet, useNavigate } from 'react-router';
import { applyPureVueInReact } from 'veaury';

import OktaLoginContext from '@/reactComponents/library/OktaLogin/Context';

import Toolbar from '../components/Toolbar.vue';
const ReactToolbar = applyPureVueInReact(Toolbar);

const RouteFrame = () => {
  const navigate = useNavigate();
  const oktaContext = React.useContext(OktaLoginContext);

  return (
    <React.Fragment>
      <ReactToolbar className="z-20" navigate={navigate} okta={oktaContext} />
      <Outlet />
    </React.Fragment>
  );
};

export default RouteFrame;
