import React from 'react';
import PropTypes from 'prop-types';

import { <PERSON><PERSON>, <PERSON><PERSON>, Text } from '@/reactComponents/library';

import OktaLoginContext from '../Context';
import {
  STEP_IDENTIFY,
  STEP_EMAIL_CODE,
  STEP_PHONE_CODE,
  STEP_VERIFY_CODE_SMS,
  STEP_VERIFY_CODE_EMAIL,
  STEP_REGISTER,
  STEP_UNKNOWN,
  STEP_ERROR,
  STEP_SUCCESS,
  getTransactionStep,
} from '../utils';

import {
  Identify,
  SendEmailCode,
  SendSMSCode,
  VerifyCode,
  Register,
} from './components';

const styles = {
  error: {
    color: 'error.main',
  },
};

const Passwordless = (props) => {
  const { onError, onSuccess } = props;
  const {
    client: authClient,
    transaction,
    setTransaction,
    setContextForm,
  } = React.useContext(OktaLoginContext);

  const [loading, setLoading] = React.useState(false);
  const [step, setStep] = React.useState(getTransactionStep(transaction));
  const [form, setForm] = React.useState({
    phone: '',
    encryptedPhone: '',
    email: '',
    firstName: '',
    lastName: '',
    user: null,
  });

  // Start IDX Auth transaction
  React.useEffect(() => {
    if (transaction) return;

    const start = async () => {
      if (loading) return;

      try {
        setLoading(true);
        const session = await authClient.sessionExists();
        if (session) {
          setTransaction({ status: 'SUCCESS' });
          setStep(STEP_SUCCESS);
          setLoading(false);
          onSuccess(form);
          return;
        }
        if (authClient.canProceed()) {
          await authClient.cancel();
        }

        const response = await authClient.authenticate();
        setTransaction(response);

        const nextStep = getTransactionStep(response);
        setStep(nextStep);

        setLoading(false);
      } catch (e) {
        setLoading(false);
        await authClient.logout();
        window.location.reload();
      }
    };
    start();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!step || loading)
    return <Loader message="Connecting to authentication provider" />;

  return (
    <>
      {step === STEP_IDENTIFY && (
        <Identify
          onSuccess={({ nextStep, form: newForm }) => {
            setForm({ ...form, ...newForm });
            setContextForm((prev) => ({ ...prev, ...newForm }));
            setStep(nextStep);
          }}
          onError={(err) => onError(err)}
        />
      )}
      {step === STEP_EMAIL_CODE && (
        <SendEmailCode onSuccess={({ nextStep }) => setStep(nextStep)} />
      )}
      {step === STEP_PHONE_CODE && (
        <SendSMSCode onSuccess={({ nextStep }) => setStep(nextStep)} />
      )}
      {[STEP_VERIFY_CODE_SMS, STEP_VERIFY_CODE_EMAIL].includes(step) && (
        <VerifyCode
          type={step === STEP_VERIFY_CODE_EMAIL ? 'email' : 'sms'}
          onSuccess={({ nextStep }) => {
            setStep(nextStep);
            if (nextStep === STEP_SUCCESS) {
              onSuccess(form);
            }
          }}
        />
      )}
      {step === STEP_REGISTER && (
        <Register form={form} onSuccess={({ nextStep }) => setStep(nextStep)} />
      )}
      {(step === STEP_UNKNOWN || step === STEP_ERROR) && (
        <>
          <Text sx={styles.error}>An error occurred.</Text>
          <Button onClick={() => authClient.logout()}>Try Again</Button>
        </>
      )}
    </>
  );
};
Passwordless.propTypes = {
  onError: PropTypes.func.isRequired,
  onSuccess: PropTypes.func.isRequired,
};

export default Passwordless;
