import React from 'react';
import _cloneDeep from 'lodash/cloneDeep';
import { use<PERSON>ara<PERSON>, Link } from 'react-router';

import {
  // approveTimecard,
  getTimecard,
  // submitTimecard,
  updateTimecard as saveTimecardApi,
  getHoursToGrossDetails,
  requestChanges,
  calculateGross,
  changeTimecardPayPeriod,
} from '@/services/timecards';
import {
  guaranteedHoursFilter,
  supervisorRateTypeFilter,
} from '@/utils/filterRateTypes';
import { listTimecardReimbursements } from '@/services/reimbursements';

import {
  listProjectHireLocations,
  getContractSettingsAndScheduleInfo,
  listDayTemplates,
} from '@/services/project';
import { getWorkStatuses, getWorkZones } from '@/services/project-members';
import { getBatchDetails } from '@/services/batch';
import {
  listProjectShootLocations,
  listProjectOccupations,
} from '../../../services/project';

import { useAuth } from '@/reactComponents/AppHooks';
import { useOutletContext } from 'react-router';

import { But<PERSON>, Box } from '@/reactComponents/library';
import UnsavedChangesModal from '@/reactComponents/viewsV2/sharedComponents/UnsavedChangesModal';

import TcHeader from './TcHeader';
import HeaderActions from './HeaderActions';
import TimecardDetails from './TimecardDetails';
import TimecardDays from './TimecardDays';
import Reimbursements from './Reimbursements';
import TcFooter from './TcFooter';
import SaveModal from './SaveModal';
import LoadingSkeleton from './LoadingSkeleton';
import RequestChangesModal from './RequestChangesModal';
import ErrorBar from './ErrorBar';

import {
  RootBox,
  FullWidthPageBox,
  SubPageBox,
} from '../../library/styledComponents';

import {
  timecardOnLoad,
  reimbursementsOnLoad,
  fetchTrackingDetails,
  validateTimecard,
  INITIAL_TC_ERRORS,
  prepTimecardForSave,
  getCalcIssues,
  changeWorkWeek,
} from './timecardUtils';
import { sleep } from '@/reactComponents/utils/async';

import { useDidMount } from '@/reactComponents/utils/customHooks';

import {
  snackbarSuccess,
  snackbarAxiosErr,
  snackbarErr,
  snackbarWarn,
} from '@/reactComponents/library/Snackbar';

import { listRateTypes } from '@/services/rate-types';
import { BatchStatusCapsPayEnum } from '@/types/Batch';

const anyLoading = (loading) => Object.values(loading).some((l) => l);

const TimecardV2 = (props) => {
  useAuth();
  const context = useOutletContext();
  const { project, projectMember: member } = context;

  const [timecard, setTimecard] = React.useState(null);
  const [dayTemplates, setDayTemplates] = React.useState([]);
  const [tcErrors, setTcErrors] = React.useState(INITIAL_TC_ERRORS);
  const [showErrors, setShowErrors] = React.useState(false);
  const [reimbursements, setReimbursements] = React.useState(null);
  const [loading, setLoading] = React.useState({
    timecard: true,
    reimbursements: true,
  });
  const [loadError, setLoadError] = React.useState(false);
  //TODO - implement tracking details in new timecard UI
  // eslint-disable-next-line no-unused-vars
  const [trackingDetails, setTrackingDetails] = React.useState(null);
  const [workLocations, setWorkLocations] = React.useState([]);
  const [workZones, setWorkZones] = React.useState([]);
  const [workStatuses, setWorkStatuses] = React.useState([]);
  const [hireLocations, setHireLocations] = React.useState([]);

  const [occupations, setOccupations] = React.useState([]);
  const [loadingOccupations, setLoadingOccupations] = React.useState(false);
  const [batchDetails, setBatchDetails] = React.useState({});
  const [hoursToGrossDetails, setHoursToGrossDetails] = React.useState(null);

  const [saving, setSaving] = React.useState(false);
  const [calculating, setCalculating] = React.useState(false);
  const [timecardDirty, setTimecardDirty] = React.useState(false);
  const [ReimbursementsDirty, setReimbursementsDirty] = React.useState(false);
  const [saveModalOpen, setSaveModalOpen] = React.useState(false);
  const [contractSettings, setContractSettings] = React.useState(null);
  const [rateTypesAll, setRateTypesAll] = React.useState(null);
  const [requestChangesModalOpen, setRequestChangesModalOpen] =
    React.useState(false);

  const [calcIssues, setCalcIssues] = React.useState([]);
  const [weekChanged, setWeekChanged] = React.useState(false);
  const [didFetchOccupations, setDidFetchOccupations] = React.useState(false);

  const { timecardId } = useParams();

  const isLoading = anyLoading(loading);
  // Prevent navigation if `isDirty` is true

  const timeMinuteIncrement = React.useMemo(() => {
    return project?.minuteIncrement?.key || 15;
  }, [project]);

  const fetchHireLocations = React.useCallback(async (tc, member) => {
    try {
      if (tc.isHireLocationRequired) {
        //TODO this misses the case when occupation is removed, but we still have hireLocations set to true
        // should maybe move this check to the prevOccIDRef useEffect below but this is blanket fix.
        if (!tc.occupation?.payrollOccupationCode) return;

        const { data } = await listProjectHireLocations(
          tc.project.id,
          tc.workLocation?.payrollProjectLocationId || null,
          tc.union?.castAndCrewId || tc.union.id,
          tc.occupation?.payrollOccupationCode,
        );
        setHireLocations(data.hireLocationList);
      }
    } catch (err) {
      snackbarAxiosErr(err, 'Error fetching hire locations');
    }
  }, []);

  const prevOccIdRef = React.useRef(null);
  React.useEffect(() => {
    const timecardOccId = timecard?.occupation?.id || null;
    if (timecardOccId !== prevOccIdRef.current) {
      fetchHireLocations(timecard, member);
    }
    prevOccIdRef.current = timecardOccId;
  }, [fetchHireLocations, member, timecard]);

  /**
   * Fetch reimbursements
   * @param {Object} options - {reload: boolean} if true, will set loading to true when loading and false when done
   *                           used when we need to show the loading skeleton
   * */

  const fetchReimbursements = React.useCallback(
    async (options = {}) => {
      const { reload = false } = options;
      try {
        if (reload) {
          setLoading((loading) => ({ ...loading, reimbursements: true }));
        }
        const { data } = await listTimecardReimbursements(timecardId);
        const reimbursements = reimbursementsOnLoad(data);
        setReimbursements(reimbursements);
        setReimbursementsDirty(false);
      } catch (error) {
        snackbarAxiosErr(error, 'Error fetching reimbursements');
        setLoadError(true);
      } finally {
        if (reload) {
          setLoading((loading) => ({ ...loading, reimbursements: false }));
        }
      }
    },
    [timecardId],
  );

  const fetchDayTemplates = async () => {
    try {
      const { data } = await listDayTemplates(member.projectId);
      if (data) {
        setDayTemplates(data);
      } else {
        setDayTemplates([]);
      }
    } catch (err) {
      snackbarAxiosErr(err, 'Error fetching day templates');
    }
  };

  const fetchWorkStatuses = async () => {
    try {
      const { data } = await getWorkStatuses(member.id);
      setWorkStatuses(data);
    } catch (err) {
      snackbarAxiosErr(err, 'Error fetching work statuses');
    }
  };
  const fetchWorkZones = async () => {
    try {
      const { data } = await getWorkZones(member.id);
      setWorkZones(data);
    } catch (err) {
      snackbarAxiosErr(err, 'Error fetching work zones');
    }
  };
  const fetchWorkLocations = React.useCallback(async (project) => {
    try {
      const { data } = await listProjectShootLocations(project.id);
      setWorkLocations(data);
    } catch (err) {
      snackbarAxiosErr(err, 'Error fetching work locations');
    }
  }, []);

  const fetchBatchDetails = async (batchId) => {
    if (!batchId) {
      setBatchDetails({});
      return;
    }
    try {
      const { data } = await getBatchDetails(batchId);
      setBatchDetails(data);
    } catch (err) {
      snackbarAxiosErr(err, 'Error fetching batch details');
    }
  };

  const occupationPaginationState = React.useRef({
    pagination: {
      page: 1,
      limit: 50,
      total: 0,
    },
    hasNextPage: true,
  });

  const fetchOccupations = React.useCallback(
    async (jobTitleSearch = '', infiniteScroll = false) => {
      //
      try {
        if (infiniteScroll) {
          const hasNextPage = occupationPaginationState.current.hasNextPage;
          if (!hasNextPage) return;
          occupationPaginationState.current.pagination.page++;
        }
        if (!infiniteScroll) {
          occupationPaginationState.current.pagination.page = 1;
          occupationPaginationState.current.hasNextPage = true;
        }
        setLoadingOccupations(true);
        await sleep(5); //let loading propagate before searching
        if (!timecard) return;
        const { data } = await listProjectOccupations(
          timecard.project.id,
          timecard.workLocation?.payrollProjectLocationId || null,
          [timecard.union] || null,
          jobTitleSearch,
          occupationPaginationState.current.pagination,
        );
        if (infiniteScroll) {
          if (
            data.jobTitleList.length <
            occupationPaginationState.current.pagination.limit
          ) {
            occupationPaginationState.current.hasNextPage = false;
          }
          if (data.jobTitleList.length > 0) {
            setOccupations((prev) => {
              return prev.concat(data.jobTitleList || []);
            });
          }
        } else {
          setOccupations(data.jobTitleList);
        }
      } catch (error) {
        snackbarAxiosErr(error, 'Error fetching occupations');
      } finally {
        setLoadingOccupations(false);
      }
    },
    [
      occupationPaginationState,
      timecard,
      setOccupations,
      setLoadingOccupations,
    ],
  );

  const fetchHoursToGrossDetails = React.useCallback(async (tc) => {
    try {
      if (!tc || !tc.capsPayId) {
        setCalcIssues([]);
        return;
      }

      const { data } = await getHoursToGrossDetails(tc.id);

      if (data) {
        setHoursToGrossDetails(data);
        const { calculationErrorMessage, calculationWarningMessage } = data;
        const newCalcIssues = getCalcIssues(
          !calculationErrorMessage,
          calculationWarningMessage,
          calculationErrorMessage,
        );
        setCalcIssues(newCalcIssues);
      } else {
        setCalcIssues([]);
      }
    } catch (err) {
      snackbarAxiosErr(err, 'Error fetching totals');
    }
  }, []);

  const contractSettingsCacheRef = React.useRef({});
  const fetchContractSettingsAndScheduleInfo = React.useCallback(
    async (tc, member) => {
      let cacheKey = `${tc.id}-`;
      cacheKey += `${member.projectId}-`;
      cacheKey += `${member.workLocation?.payrollProjectLocationId}-`;
      cacheKey += `${tc.occupation?.payrollOccupationCode}-`;
      cacheKey += `${tc.timecardDays[0]?.date?.toISODate()}-`;
      cacheKey += `${tc.occupation?.id}-`;
      cacheKey += `${tc.hireLocation?.payrollHireLocationId}`;

      if (contractSettingsCacheRef.current[cacheKey]) {
        return contractSettingsCacheRef.current[cacheKey];
      }
      const { data } = await getContractSettingsAndScheduleInfo(
        member.projectId,
        member.workLocation?.payrollProjectLocationId,
        tc.occupation?.payrollOccupationCode,
        tc.timecardDays[0]?.date?.toISODate(),
        tc.union?.name,
        tc.union?.castAndCrewId || tc.union.id,
        tc.hireLocation?.payrollHireLocationId || null,
      );

      if (data?.item) {
        data.item.isExempt = data?.item?.isExemptIndicator;
        if (data?.item?.isForcePayPremiumOt) {
          data.item.payPremiumOvertime = true;
          data.item.disablePayPremiumOt = true;
        } else {
          data.item.disablePayPremiumOt = false;
        }
      }
      if (data?.item?.isHireLocationRequired && !tc.hireLocation) {
        fetchHireLocations(tc, member);
      }
      const updateCache = {
        [cacheKey]: {
          ...data.item,
          isHireLocationRequired: data?.isHireLocationRequired,
        },
      };
      contractSettingsCacheRef.current = updateCache;
      return {
        ...data.item,
        isHireLocationRequired: data?.isHireLocationRequired,
      };
    },
    [fetchHireLocations],
  );

  const finalizeNewTimecard = React.useCallback(
    (freshTimecard, projectMember, tracking, contractSettings) => {
      const preppedTimecard = timecardOnLoad(
        freshTimecard,
        projectMember,
        tracking,
        contractSettings,
      );

      setTimecard(preppedTimecard);
      setContractSettings(contractSettings);
      fetchHoursToGrossDetails(freshTimecard);
      fetchBatchDetails(freshTimecard.batchId);

      setShowErrors(false);
      setTimecardDirty(false);
      setLoading((loading) => ({ ...loading, timecard: false }));
    },
    [fetchHoursToGrossDetails],
  );

  const fetchRateTypes = async () => {
    const { data: rateTypesData } = await listRateTypes();
    setRateTypesAll(rateTypesData);
  };

  //Initial Load
  const loadData = async () => {
    try {
      fetchDayTemplates();
      fetchWorkStatuses();
      fetchWorkZones();
      fetchReimbursements({ reload: true });
      fetchRateTypes();
      setLoading((loading) => ({ ...loading, timecard: true }));
      let [tracking, tcAndContract] = await Promise.all([
        fetchTrackingDetails(member.projectId),
        fetchTimecardAndContract(timecardId, member),
      ]);
      setTrackingDetails(tracking);

      const [freshTimecard, contractSettings] = tcAndContract;

      finalizeNewTimecard(freshTimecard, member, tracking, contractSettings);
    } catch (err) {
      snackbarAxiosErr(err, 'Error loading timecard');
      setLoadError(true);
    }
  };

  const fetchTimecardAndContract = React.useCallback(
    async (timecardId, projectMember) => {
      const { data: freshTimecard } = await getTimecard(timecardId);
      const contractSettings = await fetchContractSettingsAndScheduleInfo(
        freshTimecard,
        projectMember,
      );
      return [freshTimecard, contractSettings];
    },
    [fetchContractSettingsAndScheduleInfo],
  );

  /**
   *
   * @param {Object} options:
   *      - {reload: boolean} if true, will set loading flag during fetch
   *                           used when we need to show the loading skeleton
   */
  const refreshTimecard = React.useCallback(
    async (options = {}) => {
      const { reload = false } = options;
      try {
        const [freshTimecard, contractSettings] =
          await fetchTimecardAndContract(timecardId, member);
        finalizeNewTimecard(
          freshTimecard,
          member,
          trackingDetails,
          contractSettings,
        );
        await fetchReimbursements({ reload });
      } catch (error) {
        snackbarAxiosErr(error, 'Error refreshing timecard');
        setLoadError(true);
      }
    },
    [
      fetchTimecardAndContract,
      finalizeNewTimecard,
      member,
      timecardId,
      trackingDetails,
      fetchReimbursements,
    ],
  );

  const reloadTimecard = React.useCallback(async () => {
    setLoading((loading) => ({ ...loading, timecard: true }));
    await refreshTimecard({ reload: true });
    setLoading((loading) => ({ ...loading, timecard: false }));
  }, [refreshTimecard]);

  const initLoadFinishedRef = React.useRef(false);
  React.useEffect(() => {
    if (initLoadFinishedRef.current) {
      reloadTimecard();
    }
  }, [reloadTimecard, timecardId]);

  React.useEffect(() => {
    //Should only re-run on project change - fetch call should be static (useCallback)
    if (project) {
      fetchWorkLocations(project);
    }
  }, [fetchWorkLocations, project]);

  React.useEffect(() => {
    if (timecard && !didFetchOccupations) {
      fetchOccupations();
      setDidFetchOccupations(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timecard, didFetchOccupations]);

  React.useEffect(() => {
    const isLoading = anyLoading(loading);
    if (!isLoading && !initLoadFinishedRef.current) {
      initLoadFinishedRef.current = true;
    }
  }, [loading]);

  useDidMount(() => {
    loadData();
  });

  //central updateTimecard.
  const updateTimecard = React.useCallback(
    async (updatedVals) => {
      setTimecardDirty(true);

      // adding cascading changes
      if (updatedVals.occupation || updatedVals.hireLocation) {
        let newTimecard = { ...timecard, ...updatedVals };
        const contractSettings = await fetchContractSettingsAndScheduleInfo(
          newTimecard,
          member,
        );
        setContractSettings(contractSettings);
        updatedVals = { ...updatedVals, ...contractSettings };
        if (!updatedVals.isHireLocationRequired) {
          updatedVals.hireLocation = null;
          updatedVals.hireLocationId = null;
        }

        const validRateTypes = supervisorRateTypeFilter(
          contractSettings,
          rateTypesAll,
          member?.rateType,
        );

        if (validRateTypes.length === 1) {
          updatedVals.rateType = validRateTypes[0];

          const guarHours = guaranteedHoursFilter(
            contractSettings,
            rateTypesAll,
            member?.rateType,
          );
          updatedVals.guarHours = guarHours.toFixed(2);
        }
      }

      setTimecard((prev) => {
        if (!prev) return null;

        //change payPeriod / work week
        if (updatedVals.newWorkWeek) {
          setWeekChanged(true);
          prev = changeWorkWeek(
            prev,
            updatedVals.newWorkWeek,
            setReimbursements,
          );
          if (updatedVals.newWorkWeek.isOldPayPeriod) {
            //they switched back without saving, no need to make date call
            updatedVals.newWorkWeek = null;
          }
        }

        if (updatedVals.kitRental === null) {
          prev.timecardDays.forEach((day) => {
            day.isRentalDay = false;
          });
        }

        const updatedTc = { ..._cloneDeep(prev), ...updatedVals };
        return updatedTc;
      });
    },
    [fetchContractSettingsAndScheduleInfo, member, rateTypesAll, timecard],
  );

  React.useEffect(() => {
    const errors = validateTimecard(
      timecard,
      false,
      reimbursements,
      trackingDetails,
    );
    setTcErrors(errors);
  }, [timecard, reimbursements, trackingDetails]);

  React.useEffect(() => {
    if (!weekChanged) return;

    setWeekChanged(false);
    const outOfWeek = reimbursementsOutOfWeek(timecard, reimbursements);
    if (outOfWeek.length > 0) {
      snackbarWarn(
        'This timecard has reimbursements outside of the work week.',
      );
    }
  }, [timecard, reimbursements, weekChanged]);

  const updateReimbursements = React.useCallback((updateVals, index) => {
    setReimbursements((prev) => {
      setReimbursementsDirty(true);
      if (!prev) return null;

      const reimbursements = _cloneDeep(prev);
      if (updateVals === null) {
        reimbursements.splice(index, 1);
      } else {
        reimbursements[index] = { ...reimbursements[index], ...updateVals };
      }
      return reimbursements;
    });
  }, []);

  //save timecard and reimbursements
  const handleSave = async () => {
    if (tcErrors.isValid === false) {
      setShowErrors(true);
      snackbarErr('Please fix the errors before saving.');
    } else {
      setSaveModalOpen(true);
    }
  };

  const handleRequestChanges = async () => {
    setRequestChangesModalOpen(true);
  };

  const requestChangeMethod = async (requestChangeNote) => {
    setSaving(true);
    try {
      await requestChanges(timecard.id, requestChangeNote.trim());
      setRequestChangesModalOpen(false);
      snackbarSuccess('Request changes updated successfully');
      await refreshTimecard();
    } catch (err) {
      snackbarAxiosErr(err, 'Error on requesting changes');
    }
    setSaving(false);
  };

  const onSaveAll = async (auditNotes) => {
    setSaving(true);

    try {
      if (timecard.newWorkWeek) {
        await changeTimecardPayPeriod(timecard.id, timecard.newWorkWeek.id);
        updateTimecard({ newWorkWeek: null });
      }

      const savedTimecard = await saveTimecard(
        timecard,
        reimbursements,
        auditNotes,
      );

      setSaveModalOpen(false);
      snackbarSuccess('Timecard updated successfully.');

      fetchCalcInfo(savedTimecard);
      await refreshTimecard();
    } catch (err) {
      snackbarAxiosErr(err, 'Error saving timecard');
    } finally {
      setSaving(false);
    }
  };

  const fetchCalcInfo = React.useCallback(
    async (tc) => {
      try {
        setCalculating(true);
        if (!tc || !tc.capsPayId) return;

        await calculateGross(tc.id);
        await fetchHoursToGrossDetails(tc);
      } catch (error) {
        snackbarAxiosErr(error, 'Calculation Error');
      } finally {
        setCalculating(false);
      }
    },
    [fetchHoursToGrossDetails],
  );

  const saveTimecard = async (newTimecard, reimbursements, auditNotes) => {
    const payload = JSON.parse(JSON.stringify(newTimecard));
    payload.reimbursements = JSON.parse(JSON.stringify(reimbursements));

    const preppedTimecard = prepTimecardForSave(payload);

    const { data } = await saveTimecardApi(preppedTimecard.id.toString(), {
      timecard: preppedTimecard,
      auditNotes,
    });

    setTimecardDirty(false);
    return data;
    // this.lineNumberModified = true;
  };

  const hasUnsavedChanges = timecardDirty || ReimbursementsDirty;
  const saveDisabled =
    !hasUnsavedChanges || (tcErrors.isValid === false && showErrors) || saving;

  const reimbursementsOutOfWeek = (timecard, reimbursements) => {
    const ret = [];
    if (!timecard) return ret;

    const isDateOut = (d) => d < weekStart || d > weekEnd;
    const { mileageForm } = timecard || {};
    const weekStart = timecard.payPeriod.startsAt;
    const weekEnd = timecard.payPeriod.endsAt;
    if (mileageForm && isDateOut(mileageForm.date)) {
      ret.push({ _kind: 'mileage', id: mileageForm.id });
    }

    (reimbursements || [])
      .filter((r) => isDateOut(r.date))
      .forEach((r) => ret.push({ _kind: 'other', id: r.id }));

    return ret;
  };
  const readOnlyMode = React.useMemo(() => {
    const batchId = timecard?.batchId || null;
    return batchId && batchDetails.batchStatus !== BatchStatusCapsPayEnum.Open;
  }, [batchDetails, timecard]);

  return (
    <RootBox>
      {isLoading || loadError ? (
        <>
          {/* EMPTY STATE */}
          {loadError && (
            <FullWidthPageBox
              sx={{
                minHeight: '90vh',
                height: '100%',
                justifyContent: 'center',
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 2,
                  alignItems: 'center',
                }}
              >
                <Box sx={{ fontWeight: 'bold' }}>
                  Error loading timecard. Please try again
                </Box>
                <Link to={-1}>
                  <Button variant="secondary">Go back</Button>
                </Link>
              </Box>
            </FullWidthPageBox>
          )}
          {isLoading && !loadError && (
            <FullWidthPageBox
              sx={{ minHeight: '100vh', justifyContent: 'center' }}
            >
              <LoadingSkeleton />
            </FullWidthPageBox>
          )}
          {/* END EMPTY STATE */}
        </>
      ) : (
        <>
          <ErrorBar calcIssues={calcIssues} />
          <FullWidthPageBox>
            <TcHeader member={member} project={project} timecard={timecard} />
            <SubPageBox>
              <HeaderActions
                timecard={timecard}
                member={member}
                batchDetails={batchDetails}
                refreshTimecard={refreshTimecard}
                updateTimecard={updateTimecard}
                readOnlyMode={readOnlyMode}
              />
              <TimecardDetails
                timecard={timecard}
                occupations={occupations}
                member={member}
                batchDetails={batchDetails}
                readOnlyMode={readOnlyMode}
                updateTimecard={updateTimecard}
                hireLocations={hireLocations}
                fetchOccupations={fetchOccupations}
                loadingOccupations={loadingOccupations}
                occupationsHasNextPage={
                  occupationPaginationState.current.hasNextPage
                }
                contractSettings={contractSettings}
                rateTypesAll={rateTypesAll}
                displayErrors={showErrors ? tcErrors : INITIAL_TC_ERRORS}
              />
              <TimecardDays
                timecard={timecard}
                dayTemplates={dayTemplates}
                readOnlyMode={readOnlyMode}
                updateTimecard={updateTimecard}
                workLocations={workLocations}
                workStatuses={workStatuses}
                workZones={workZones}
                occupations={occupations}
                fetchOccupations={fetchOccupations}
                loadingOccupations={loadingOccupations}
                occupationsHasNextPage={
                  occupationPaginationState.current.hasNextPage
                }
                timeMinuteIncrement={timeMinuteIncrement}
                trackingDetails={trackingDetails}
                total={hoursToGrossDetails?.laborTotal}
                member={member}
                displayErrors={showErrors ? tcErrors : INITIAL_TC_ERRORS}
              />
              <Reimbursements
                timecard={timecard}
                updateTimecard={updateTimecard}
                reimbursements={reimbursements}
                updateReimbursements={updateReimbursements}
                workLocations={workLocations}
                trackingDetails={trackingDetails}
                total={hoursToGrossDetails?.nonLaborTotal}
                readOnlyMode={readOnlyMode}
                member={member}
                displayErrors={showErrors ? tcErrors : INITIAL_TC_ERRORS}
              />
            </SubPageBox>
          </FullWidthPageBox>
          <TcFooter
            handleSave={handleSave}
            timecard={timecard}
            saveDisabled={saveDisabled}
            total={hoursToGrossDetails?.grandTotal}
            onRequestChanges={handleRequestChanges}
            refreshTimecard={refreshTimecard}
            calculating={calculating}
            timecardDirty={timecardDirty}
            reimbursementsDirty={ReimbursementsDirty}
            fetchCalcInfo={fetchCalcInfo}
            batchDetails={batchDetails}
            tcErrors={tcErrors}
          />
        </>
      )}

      <SaveModal
        saveModalOpen={saveModalOpen}
        setSaveModalOpen={setSaveModalOpen}
        onSaveAll={onSaveAll}
        saving={saving}
      />
      <RequestChangesModal
        open={requestChangesModalOpen}
        setOpen={setRequestChangesModalOpen}
        timecardRequestChangeNote={timecard?.requestedChanges}
        requestChangeMethod={requestChangeMethod}
        saving={saving}
      />
      <UnsavedChangesModal hasUnsavedChanges={hasUnsavedChanges} />
    </RootBox>
  );
};

export default TimecardV2;
