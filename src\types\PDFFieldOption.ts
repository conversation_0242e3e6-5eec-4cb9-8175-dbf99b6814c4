export interface PDFFieldOption {
  label: string;
  value: string;
  type: 'text' | 'image';
}

export interface StyledPDFFieldOption extends PDFFieldOption {
  height?: number;
  width?: number;
  fontSize?: number;
  position?: { x: number; y: number };
  alignment?: 'left' | 'center' | 'right';
  verticalAlignment?: 'top' | 'middle' | 'bottom';
  lineHeight?: number;
  fontColor?: string;
  backgroundColor?: string;
}
