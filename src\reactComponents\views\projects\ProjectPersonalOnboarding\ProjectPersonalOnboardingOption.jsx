import { useReactRouter } from '@/reactComponents/AppHooks';
import { Icon, PillBadge } from '@/reactComponents/library';
import { ExclamationCircleIcon } from '@heroicons/react/24/solid';
import { Button } from '@/reactComponents/library';
import PropTypes from 'prop-types';

const ProjectPersonalOnboardingOption = ({ page }) => {
  const { navigate } = useReactRouter();

  return (
    <div className="bg-white dark:bg-gray-900 shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <div className="flex">
          {page.completed ? (
            <PillBadge variant="successIcon">
              <Icon name="checked" className="w-4 h-4" />
            </PillBadge>
          ) : (
            <ExclamationCircleIcon className="text-yellow-500 dark:text-yellow-400 w-6 h-6 mr-2" />
          )}
          <h3 className="text-base font-semibold leading-6">{page.label}</h3>
        </div>
        <div className="mt-2 max-w-xl text-sm text-gray-500 dark:text-gray-400">
          <p>{page.description}</p>
        </div>
        <div className="mt-5">
          {page.cta && (
            <Button
              onClick={() => navigate(page.pathname)}
              color="secondary"
              size="small"
              variant="secondary"
            >
              {page.cta}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

ProjectPersonalOnboardingOption.propTypes = {
  page: PropTypes.shape({
    id: PropTypes.number,
    label: PropTypes.string.isRequired,
    pathname: PropTypes.string.isRequired,
    required: PropTypes.bool.isRequired,
    completed: PropTypes.bool.isRequired,
    description: PropTypes.string.isRequired,
    cta: PropTypes.string.isRequired,
  }).isRequired,
};

export default ProjectPersonalOnboardingOption;
