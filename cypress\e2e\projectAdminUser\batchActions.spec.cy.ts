/// <reference types="cypress" />

import BatchModals from '../../pageObjects/timecards/batchModals';
import { fetchProjectIdentifiersByName } from '../../support/apiHelpers';
import { randomBatchName } from '../../utils/batchUtils';
import {
  interceptCreateBatch,
  interceptDeleteBatch,
  interceptEditBatch,
} from '../../support/apiTimecardInterceptors';

const batchModals = new BatchModals();

function setupBatchInterceptors() {
  interceptCreateBatch();
  interceptEditBatch();
  interceptDeleteBatch();
}

function goToProjectTimeTab() {
  cy.visit(
    `${Cypress.env('BASE_URL')}projects/${Cypress.env(
      'projectHashId',
    )}/admin/time`,
  );
}

describe('User Project Admin - Batch Actions ( Create - Edit - Delete ) ', () => {
  const projectName = Cypress.env('projectName');
  let batchName: string;
  let newBatchName: string;

  beforeEach(() => {
    batchName = randomBatchName();
    newBatchName = randomBatchName();

    cy.loginWithSession(
      Cypress.env('PHONE_NUMBER_PROJECT_ADMIN'),
      Cypress.env('OTC_CODE'),
      Cypress.env('BASE_URL'),
      'project-admin',
    );

    cy.then(() => {
      return fetchProjectIdentifiersByName(projectName).then((project) => {
        if (!project || !project.id) {
          throw new Error('Project not found or invalid Project ID');
        }
        Cypress.env('projectId', project.id);
        Cypress.env('projectHashId', project.hashId);
      });
    });
  });

  //https://castandcrew.atlassian.net/browse/FPS-1526
  it('Verify Project Admin is able to Create, Edit and Delete Batch without timecards associated', () => {
    // ----------- Arrange: Go to Project Time Tab -----------
    cy.log('Go to Project');
    goToProjectTimeTab();
    setupBatchInterceptors();

    // ----------- Action: Create Batch By UI-----------
    cy.log('Create Batch By UI');
    batchModals.createBatch(batchName);

    // ----------- Assert: Verify Batch Created checking on the API response-----------
    cy.log('Verify Batch Created checking on the API response');
    cy.wait('@createdBatch').then((interception) => {
      expect(interception.response?.statusCode, 'Create status code').to.equal(
        200,
      );
      expect(
        interception.response?.body.name,
        'Batch name after edit',
      ).to.equal(batchName);
      expect(
        interception.response?.body.projectId,
        'Project Id associated',
      ).to.equal(Cypress.env('projectId'));
      expect(interception.response?.body.statusId, 'Status Open').to.equal(3);

      // ----------- Action: Edit Batch By UI-----------
      cy.log('Edit Batch By UI');
      batchModals.editBatch(batchName, newBatchName);

      // ----------- Assert: Verify Batch Edited checking on the API response-----------
      cy.log('Verify Batch Edited checking on the API response');
      cy.wait('@editedBatch').then((interception) => {
        Cypress.env('batchId', interception.response?.body.id);
        batchModals.validateBatchStatus(interception.response?.body.id, 'Open');
        expect(
          interception.response?.statusCode,
          'Edit batch status code',
        ).to.equal(200);
        expect(
          interception.response?.body.name,
          'Batch name after edit',
        ).to.equal(newBatchName);

        // ----------- Action: Delete Batch By UI-----------
        cy.log('Delete Batch By UI');
        batchModals.deleteBatch(newBatchName);

        // ----------- Assert: Verify Batch Deleted checking on the API response-----------
        cy.log('Verify Batch Deleted checking on the API response');
        cy.wait('@deletedBatch').then((interception) => {
          expect(
            interception.response?.statusCode,
            'Delete batch status code',
          ).to.equal(200);
        });
        batchModals.validateBatchDeleted(newBatchName);
        cy.log(
          'Success: Verify Project Admin is able to Create, Edit and Delete Batch without timecards associated',
        );
      });
    });
  });
});
