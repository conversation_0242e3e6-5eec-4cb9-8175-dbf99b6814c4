import React from 'react';
import PropTypes from 'prop-types';

import { PickersDay } from '@/reactComponents/library';
import DefaultDaysStore from './store';

import { dateFmtStr } from './utils';

const CalendarDay = (props) => {
  const { day, dates } = props;
  const { disabled } = props;
  const activeDates = DefaultDaysStore.activeDays.map((day) =>
    day.date.toFormat(dateFmtStr),
  );
  const dayStr = day.toFormat(dateFmtStr);
  const isSelected = dates.includes(dayStr);
  const isActiveDate = activeDates.includes(dayStr);

  return (
    <PickersDay
      {...props}
      selected={isSelected || isActiveDate}
      disabled={isActiveDate || disabled}
    />
  );
};

CalendarDay.propTypes = {
  day: PropTypes.object.isRequired,
  dates: PropTypes.array.isRequired,
  disabled: PropTypes.bool,
};

export default CalendarDay;
