<template>
  <section>
    <div class="shadow sm:rounded-lg bg-white dark:bg-gray-900">
      <div
        class="flex justify-between items-center px-4 py-5 sm:px-6 bg-gray-50 dark:bg-gray-700 sm:rounded-t-lg"
      >
        <h2
          id="applicant-information-title"
          class="text-lg font-medium leading-6"
        >
          Start Paperwork
        </h2>
        <Button
          v-if="hasStartPaperwork"
          size="sm"
          :loading="downloadingStartPaperwork"
          @click="openStartPaperwork"
        >
          Download All
        </Button>
      </div>
      <div
        class="border-t border-gray-200 dark:border-gray-600 px-4 py-5 sm:px-6"
      >
        <div v-if="startPaperwork?.length">
          <div
            v-for="(sp, spIndex) in startPaperwork"
            :key="`${sp.documentId}-${spIndex}`"
            class="flex flex-row items-center mb-3 space-x-3"
          >
            <ArrowDownTrayIcon
              v-if="sp.crewSigned"
              class="w-5 h-5 mr-2 cursor-pointer text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"
              @click="
                downloadSingleDocument(sp.documentTemplate?.name, sp.documentId)
              "
            />
            <FlagIcon
              v-if="!sp.crewSigned"
              class="w-5 h-5 mr-2 text-red-600 dark:text-red-400"
            />
            <div class="flex flex-col" style="min-width: 300px">
              <p class="text-md">
                {{ toTitle(`${sp.documentTemplate?.name}`) }}
              </p>
              <p
                v-if="sp.crewSigned"
                class="text-sm text-gray-500 dark:text-gray-400"
              >
                Signed by {{ crewMemberFullName }} at
                {{ sp?.crewSignedAt?.toFormat('MM/dd/yy HH:mm:ss a') }}
              </p>
              <p
                v-if="!sp.crewSigned"
                class="text-sm text-gray-500 dark:text-gray-400"
              >
                {{ crewMemberFullName }} declined to sign
                {{ sp?.crewSignedAt?.toFormat('MM/dd/yy HH:mm:ss a') }}
              </p>
            </div>
            <div class="flex flex-col">
              <p class="text-sm text-gray-500 dark:text-gray-400">
                Send to Payroll
              </p>
              <p class="text-md">
                {{
                  sp.documentTemplate?.sendToPayroll && sp.crewSigned
                    ? 'Yes'
                    : 'No'
                }}
              </p>
            </div>
          </div>
          <Modal v-model="deletingStartPaperworkModal">
            <h2 class="font-semibold mb-2">Delete Start Paperwork</h2>
            <div class="flex justify-start space-x-2">
              <Button
                class="mt-2"
                @click="deleteStartPaperwork"
                size="sm"
                color="primary"
                :loading="deletingStartPaperwork"
                >Delete</Button
              >
              <Button
                class="mt-2"
                @click="deletingStartPaperworkModal = false"
                size="sm"
                color="secondary"
                >Cancel</Button
              >
            </div>
          </Modal>
        </div>
        <div v-else>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            No start paperwork submitted yet.
          </p>
        </div>
        <div
          v-if="startPaperworkSubmittedByCrew"
          class="flex justify-start space-x-2"
        >
          <Button
            v-if="!hasSupervisorSignedStartPaperwork"
            class="mt-2"
            @click="goToSignStartPaperwork"
            size="sm"
            >View and Sign Start Paperwork</Button
          >
          <Button
            class="mt-2"
            @click="deletingStartPaperworkModal = true"
            size="sm"
            color="error"
            >Delete Start Paperwork</Button
          >
        </div>
      </div>
    </div>
    <Modal v-model="downloadStartPaperworkModal.value">
      <h2 class="font-semibold mb-2">Download Start Paperwork</h2>
      <p class="mb-2">
        Start paperwork has not been signed by supervisor - are you sure you
        want to download?
      </p>
      <div class="flex justify-end space-x-2">
        <Button
          class="mt-2"
          size="sm"
          color="error"
          :loading="downloadingStartPaperwork"
          @click="closeDownloadStartPaperworkModal"
        >
          Cancel
        </Button>
        <Button
          class="mt-2"
          :loading="downloadingStartPaperwork"
          size="sm"
          @click="
            createDownloadLink(
              downloadStartPaperworkModal.pdfUrl,
              downloadStartPaperworkModal.documentName,
            )
          "
        >
          Download
        </Button>
      </div>
    </Modal>
  </section>
</template>

<script lang="ts">
import Button from '@/components/library/Button.vue';
import Modal from '@/components/library/Modal.vue';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';
import { getPdfUrl } from '@/services/pdf';
import { getCrewStartingPaperworkPdf } from '@/services/project';
import { getProjectMemberStartPaperwork } from '@/services/project-members';
import { deleteStartPaperwork } from '@/services/start-paperworks';
// import { useSnackbarStore } from '@/stores/snackbar';
import type Project from '@/types/Project';
import type ProjectMember from '@/types/ProjectMember';
import type { StartPaperwork } from '@/types/StartPaperwork';
import type User from '@/types/User';
import type UserCrew from '@/types/UserCrew';
import { toTitle } from '@/utils/string';
import { ArrowDownTrayIcon, FlagIcon } from '@heroicons/vue/24/outline';
import axios from 'axios';
// import { mapActions } from 'pinia';
import { defineComponent, type PropType } from 'vue';

export default defineComponent({
  components: { ArrowDownTrayIcon, FlagIcon, Button, Modal },
  props: {
    project: { type: Object as PropType<Project>, required: true },
    projectMember: { type: Object as PropType<ProjectMember>, required: true },
    user: { type: Object as PropType<User>, required: true },
    userCrew: { type: Object as PropType<UserCrew>, required: true },
    navigate: { type: Function, required: false },
    refresh: { type: Function, required: false },
    route: { type: Object as PropType<ParsedRoute>, required: true },
  },
  data() {
    return {
      toTitle,
      deletingStartPaperworkModal: false,
      deletingStartPaperwork: false,
      downloadingStartPaperwork: false,
      downloadStartPaperworkModal: {
        value: false,
        documentName: '',
        pdfUrl: '',
      },
      startPaperwork: null as StartPaperwork[] | null,
    };
  },
  computed: {
    hasStartPaperwork(): boolean {
      return !!this.startPaperwork?.length;
    },
    hasSupervisorSignedStartPaperwork(): boolean {
      return !!this.startPaperwork?.[0]?.supervisorSignedById;
    },
    userCrewId(): number {
      return this.userCrew?.id;
    },
    crewMemberFullName(): string {
      if (!this.user) return '';
      const { firstName, lastName } = this.user;
      return `${firstName} ${lastName}`;
    },
    startPaperworkSubmittedByCrew(): boolean {
      return !!this.startPaperwork?.[0]?.projectMemberId;
    },
  },
  async mounted() {
    await this.load();
  },
  methods: {
    async openStartPaperwork(): Promise<void> {
      // const pdfUrl = getCrewStartingPaperworkPdf(
      //   this.project.id!,
      //   this.userCrewId
      // )
      // const documentName = 'start_paperwork'

      // // TODO: add details to modal to allow for download
      // if (!this.hasSupervisorSignedStartPaperwork) {
      //   this.openDownloadStartPaperworkModal(documentName, pdfUrl)
      //   return
      // }

      // this.createDownloadLink(pdfUrl, documentName)
      this.downloadingStartPaperwork = true;
      const response = await getCrewStartingPaperworkPdf(
        this.project.id!,
        this.projectMember.userId,
      );
      const href = response.data.filePath;
      const link = document.createElement('a');

      link.href = href;
      link.setAttribute('download', response.data.fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      this.downloadingStartPaperwork = false;
    },
    openDownloadStartPaperworkModal(
      pdfUrl: string,
      documentName: string,
    ): void {
      this.downloadStartPaperworkModal = {
        value: true,
        pdfUrl,
        documentName,
      };
    },
    closeDownloadStartPaperworkModal(): void {
      this.downloadStartPaperworkModal = {
        value: false,
        pdfUrl: '',
        documentName: '',
      };
    },
    downloadSingleDocument(documentName: string, documentId: string) {
      const pdfUrl = getPdfUrl(documentId);
      if (!this.hasSupervisorSignedStartPaperwork) {
        this.openDownloadStartPaperworkModal(pdfUrl, documentName);
        return;
      } else {
        this.createDownloadLink(pdfUrl, documentName);
        return;
      }
    },
    createDownloadLink(url: string, prefix: string) {
      this.downloadingStartPaperwork = true;
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute(
        'download',
        `${prefix}_${this.user.firstName}_${this.user.lastName}_${this.project.name}_${this.project.number}.pdf`,
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      this.downloadingStartPaperwork = false;
    },
    async deleteStartPaperwork() {
      this.deletingStartPaperwork = true;
      try {
        await deleteStartPaperwork(this.projectMember.id);
        SnackbarStore.triggerSnackbar(
          'Successfully deleted start paperwork.',
          2500,
          'success',
        );
      } catch (err: any) {
        const errorMessage = axios.isAxiosError(err)
          ? err.response?.data?.['errors']?.[0]?.message
          : err.toString();
        SnackbarStore.triggerSnackbar(errorMessage, 2500, 'error');
      } finally {
        this.deletingStartPaperwork = false;
        this.deletingStartPaperworkModal = false;
        this.load();
        this.refresh?.();
      }
    },
    goToSignStartPaperwork() {
      this.navigate?.({
        pathname: `/projects/${this.project.hashId}/admin/members/${this.projectMember.id}/member/start-paperwork/sign`,
      });
    },
    async load() {
      const projectMemberId = this.$props.route.params
        .projectMemberId as string;
      const { data } = await getProjectMemberStartPaperwork(projectMemberId);
      this.startPaperwork = data as StartPaperwork[];
    },
  },
});
</script>
