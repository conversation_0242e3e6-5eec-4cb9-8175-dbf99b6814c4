/// <reference types="cypress" />

import TimecardDetails from '../../pageObjects/timecards/timecardDetails';
import TimecardModal from '../../pageObjects/timecards/timecardModal';
import {
  deleteTimecard,
  fetchProjectIdentifiersByName,
  fetchProjectMemberByName,
} from '../../support/apiHelpers';
import {
  interceptCreateTimecard,
  interceptApprovedTimecard,
  interceptGetApprovedTimecard,
} from '../../support/apiTimecardInterceptors';

describe('User Project Admin - Timecard creation and approval', () => {
  const timecardDetails = new TimecardDetails();
  const timecardModal = new TimecardModal();
  const employeeName = 'PA-test PA-test';

  beforeEach((): void => {
    cy.loginWithSession(
      Cypress.env('PHONE_NUMBER_PROJECT_ADMIN'),
      Cypress.env('OTC_CODE'),
      Cypress.env('BASE_URL'),
      'project-admin',
    );
    cy.visit(Cypress.env('BASE_URL'));

    //Get Details by Api before test
    cy.then(() => {
      // Step 1: Get the project Id by project Name
      return fetchProjectIdentifiersByName(Cypress.env('projectName')).then(
        (project) => {
          if (!project || !project.id) {
            throw new Error('Project not found or invalid Project ID');
          }
          Cypress.env('projectId', project.id);
          cy.log(`Project ID set: ${project.id}`);
          Cypress.env('projectHashId', project.hashId);

          // Step 2: Get the member by first/last name
          return fetchProjectMemberByName('PA-Test', 'PA-test').then(
            (member) => {
              if (!member || !member.id) {
                throw new Error(
                  'Project member not found or invalid Member ID',
                );
              }
              Cypress.env('projectMemberId', member.id);
              cy.log(`Project Member ID set: ${member.id}`);
            },
          );
        },
      );
    });
  });

  afterEach(() => {
    // ----------- Cleanup: Delete the created timecard -----------
    cy.log('Cleanup: Delete the created timecard');
    deleteTimecard(Cypress.env('timecardId'));
  });

  it('Should allow a Project Admin to submit a timecard on behalf of a Crew user', () => {
    interceptCreateTimecard(); // Intercepts timecard creation
    interceptApprovedTimecard(); // Intercepts PATCH approval call

    // Navigate to the project and open the supervisor view
    cy.visit(
      `${Cypress.env('BASE_URL')}projects/${Cypress.env(
        'projectHashId',
      )}/admin/time`,
    );

    // Start timecard creation flow
    timecardModal.createTimecard(employeeName);

    // Wait for the timecard creation response
    cy.wait('@createTimecard').then((interception): void => {
      const response = interception.response?.body;
      Cypress.env('timecardId', response.id);
      const timecardDaysId: string = response?.timecardDays?.[1]?.id;

      // Complete the timecard form with valid data
      timecardDetails.fillOutTimecardDetails(timecardDaysId);

      // Save, approve and sign the timecard
      timecardDetails.save();
      timecardDetails.approve();
      cy.signatureApproval();

      interceptGetApprovedTimecard(); // Intercepts final GET check

      // Validate Timecard approval
      cy.wait('@approvedTimecard').then((approvalIntercept): void => {
        expect(approvalIntercept.response?.statusCode).to.eq(200);
        cy.log('Timecard approval request was successful.');
      });

      // Validate the final timecard status is "Approved"
      cy.wait('@getApprovedTimecard').then((getIntercept): void => {
        const statusName: string = getIntercept.response?.body?.status?.name;
        cy.log(`Final timecard status: ${statusName}`);
        expect(statusName).to.eq('Approved');
        cy.log(
          'Success: Project Admin is able to submit a timecard on behalf of a Crew user',
        );
      });
    });
  });
});
