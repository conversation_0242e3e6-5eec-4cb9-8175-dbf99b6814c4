import React from 'react';

import { Box } from '@/reactComponents/library';
import { StepperNav } from '@/reactComponents/spotlight';
import OnboardingStore from '@/reactComponents/viewsV2/Onboarding/store';
import { useIsMobile } from '@/reactComponents/utils/customHooks';

import { observer } from 'mobx-react-lite';
import { toJS } from 'mobx';

const StepperComp = observer(() => {
  let steps = OnboardingStore.onboardingSteps;
  const [open, setOpen] = React.useState(true);

  const isMobile = useIsMobile();
  React.useEffect(() => {
    if (isMobile) {
      setOpen(false);
    } else {
      setOpen(true);
    }
  }, [isMobile]);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
      }}
    >
      <StepperNav
        label={'Onboarding'}
        steps={toJS(steps)}
        open={open}
        onPress={(index, name) => {
          OnboardingStore.navToStep(index);
        }}
        onChange={(e) => {
          setOpen((v) => !v);
        }}
      />
    </Box>
  );
});

StepperComp.propTypes = {};

export default StepperComp;
