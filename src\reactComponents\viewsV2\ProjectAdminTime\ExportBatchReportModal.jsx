import React from 'react';
import PropTypes from 'prop-types';

import {
  Modal,
  Box,
  Autocomplete,
  Switch,
  InputLabel,
} from '@/reactComponents/library';

import {
  snackbarSuccess,
  snackbarAxiosErr_Deprecated,
} from '@/reactComponents/library/Snackbar';

import { downloadBatchPdfs, downloadBatchZip } from '@/services/batch';

const exportFormats = [
  { label: 'One PDF for all Members', value: 'pdf' },
  { label: 'Individual PDFs for each Member', value: 'zip' },
];

const exportOrderOptions = [
  { label: 'Start Paperwork, then Timecards', value: 'startPaperworkFirst' },
  { label: 'Timecards, then Start Paperwork', value: 'timecardFirst' },
];

const exportStartPaperworkScopes = [
  { label: 'All Documentation', value: 'all' },
  { label: 'Timecards and Documents flagged for Payroll', value: 'payroll' },
  { label: 'Timecards Only', value: 'none' },
];

const styles = {
  dropBox: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    gap: 1,
  },
  switchBoxBox: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
  },
  switchBox: { display: 'flex', alignItems: 'center', width: '100%' },
};

const ExportBatchReportModal = (props) => {
  const { open, setOpen, batch } = props;

  const [paperworkScope, setPaperworkScope] = React.useState(
    exportStartPaperworkScopes[0],
  );
  const [exportFormat, setExportFormat] = React.useState(exportFormats[0]);
  const [exportOrder, setExportOrder] = React.useState(exportOrderOptions[0]);
  const [includeKitRental, setIncludeKitRental] = React.useState(true);
  const [includeMileageForm, setIncludeMileageForm] = React.useState(true);
  const [includeReimbursementForm, setIncludeReimbursementForm] =
    React.useState(true);

  const [loadingExport, setLoadingExport] = React.useState(false);

  const handleSubmit = async () => {
    setLoadingExport(true);
    try {
      const formatValue = exportFormat.value;
      const scope = paperworkScope.value;
      const order = exportOrder.value;

      if (formatValue === 'zip') {
        const response = await downloadBatchZip(
          batch?.id,
          scope,
          includeKitRental,
          includeMileageForm,
          includeReimbursementForm,
        );
        const href = response.data.filePath;
        const link = document.createElement('a');

        link.href = href;
        link.setAttribute('download', response.data.fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
      } else {
        const response = await downloadBatchPdfs(
          batch?.id,
          scope,
          includeKitRental,
          includeMileageForm,
          includeReimbursementForm,
          order,
        );
        const href = response.data.filePath;
        const link = document.createElement('a');

        link.href = href;
        link.setAttribute('download', response.data.fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
      }
      snackbarSuccess('Batch exported successfully.');
    } catch (err) {
      snackbarAxiosErr_Deprecated(err, 'Error exporting batch');
    }

    setOpen(false);
    setLoadingExport(false);
  };

  if (!open) return null;

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Export PDFs"
      onCancel={() => setOpen(false)}
      onSubmit={handleSubmit}
      submitText="Export"
      sx={{ alignItems: 'flex-start' }}
      loading={loadingExport}
    >
      <Box sx={styles.dropBox}>
        <Autocomplete
          label="One PDF vs Multiple PDFs"
          options={exportFormats}
          value={exportFormat}
          onChange={(e, newValue) => setExportFormat(newValue)}
          disableClearable
        />
        <Autocomplete
          label="Export order"
          options={exportOrderOptions}
          value={exportOrder}
          onChange={(e, newValue) => setExportOrder(newValue)}
          disableClearable
        />
        <Autocomplete
          label="Paperwork scope"
          options={exportStartPaperworkScopes}
          value={paperworkScope}
          onChange={(e, newValue) => setPaperworkScope(newValue)}
          disableClearable
        />
      </Box>
      <Box sx={styles.switchBoxBox}>
        <Box sx={styles.switchBox}>
          <Switch
            checked={includeKitRental}
            onChange={(e) => setIncludeKitRental(e.target.checked)}
            data-testid="switch-include-kit-rental"
          />
          <InputLabel>Include Kit Rental</InputLabel>
        </Box>
        <Box sx={styles.switchBox}>
          <Switch
            checked={includeMileageForm}
            onChange={(e) => setIncludeMileageForm(e.target.checked)}
            data-testid="switch-include-mileage-form"
          />
          <InputLabel>Include Mileage Form</InputLabel>
        </Box>
        <Box sx={styles.switchBox}>
          <Switch
            checked={includeReimbursementForm}
            onChange={(e) => setIncludeReimbursementForm(e.target.checked)}
            data-testid="switch-include-reimbursement-form"
          />
          <InputLabel>Include Reimbursement Form</InputLabel>
        </Box>
      </Box>
    </Modal>
  );
};

ExportBatchReportModal.propTypes = {
  open: PropTypes.bool.isRequired,
  setOpen: PropTypes.func.isRequired,
  batch: PropTypes.object.isRequired,
};

export default ExportBatchReportModal;
