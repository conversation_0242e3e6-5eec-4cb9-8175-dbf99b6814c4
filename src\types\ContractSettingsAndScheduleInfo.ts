import type { DateTime } from 'luxon';

export interface ContractSettingsAndScheduleInfo {
  payrollContractId: string;
  isDriveTimeAllowed: boolean;
  isHalfDayAllowed: boolean;
  isNdbAllowed: boolean;
  holidayDates: DateTime[];
  isAllowWalkingMeal: boolean;
  rateUnits: CapsPayRateUnits;
  isHireLocationRequired: boolean;
  isExemptIndicator: boolean;
  isOnCallIndicator: boolean;
  isHideDriveTime: boolean;
  isIgnorePremiumOt: boolean;
}
export interface CapsPayRateUnits {
  items: CapsPayRateUnit[];
}

export interface CapsPayRateUnit {
  value: string;
  display: string;
}
