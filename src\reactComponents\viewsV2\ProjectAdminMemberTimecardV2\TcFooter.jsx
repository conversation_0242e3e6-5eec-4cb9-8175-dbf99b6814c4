import React from 'react';
import PropTypes from 'prop-types';
import { snackbarErr } from '@/reactComponents/library/Snackbar';
import { getTimecardHtgReport } from '@/services/timecards';

import {
  Button,
  ButtonGroup,
  Box,
  Tooltip,
  CircularProgress,
} from '@/reactComponents/library';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';

//utils
import { makeSaveTooltip } from '@/reactComponents/viewsV2/ProjectAdminMemberTimecardV2/timecardUtils';
import { TimecardStatusId } from '@/utils/enum';
import { BatchStatusCapsPayEnum } from '@/types/Batch';

import ApprovalModal from '@/reactComponents/viewsV2/ProjectAdminMemberTimecardV2/ApproveModal';

const styles = {
  footer: {
    display: 'flex',
    width: '100%',
    justifyContent: 'center',
    padding: '18px 24px',
    backgroundColor: 'background.paper',
    borderTop: '1px solid',
    borderColor: 'divider',
    position: 'sticky',
    bottom: 0,
    boxShadow: '0px -2px 4px 2px rgba(0, 0, 0, 0.1)',
  },
  subFooter: {
    width: '80rem',
    display: 'flex',
    justifyContent: 'space-between',
  },
};

const TcFooter = (props) => {
  const {
    handleSave = () => {},
    saveDisabled,
    timecard,
    total,
    refreshTimecard,
    calculating,
    timecardDirty,
    reimbursementsDirty,
    onRequestChanges = () => {},
    fetchCalcInfo,
    batchDetails,
    tcErrors,
  } = props;

  const [approveModalOpen, setApproveModalOpen] = React.useState(false);

  const [isLoadingTimecardHtgReport, setIsLoadingTimecardHtgReport] =
    React.useState(false);

  const downloadTimecardHtgReport = async () => {
    setIsLoadingTimecardHtgReport(true);
    try {
      const { data } = await getTimecardHtgReport(
        timecard.id,
        timecard.batchId,
      );

      if (!data.preSignedUrl) {
        throw new Error('No preSignedUrl returned from API');
      }

      window.open(data.preSignedUrl, '_blank');
    } catch (error) {
      snackbarErr('There was an error downloading the HTG details report.');
    } finally {
      setIsLoadingTimecardHtgReport(false);
    }
  };

  const totalLabel = (total ?? 0).toLocaleString('en-US', {
    style: 'currency',
    currency: 'USD',
  });

  const hasUnsavedChanges = timecardDirty || reimbursementsDirty;
  const timecardStatusId = timecard?.status?.id;

  const hoursGrossDisabled = !timecard?.capsPayId || !timecard?.batchId;

  const requestChangesDisabled =
    (batchDetails?.batchStatus &&
      batchDetails.batchStatus !== BatchStatusCapsPayEnum.Open) ||
    hasUnsavedChanges;

  const approveDisabled =
    timecardStatusId === TimecardStatusId.Approved ||
    timecardStatusId === TimecardStatusId.RequestedChanges ||
    timecardStatusId === TimecardStatusId.Processing ||
    timecardStatusId === TimecardStatusId.Paid ||
    hasUnsavedChanges;

  const isValid = tcErrors.isValid;

  let saveTitle = undefined;
  if (!isValid) {
    saveTitle = (
      <Box>
        {makeSaveTooltip(tcErrors).map((str, i) => (
          // eslint-disable-next-line react/no-array-index-key
          <Box key={`${str}${i}`}>{str}</Box>
        ))}
      </Box>
    );
  }

  return (
    <Box sx={styles.footer} id="timecardFooterContainer" zIndex={10}>
      <Box sx={styles.subFooter}>
        <ButtonGroup>
          <Box>
            <Box sx={{ fontWeight: 'bold' }}>Gross Total: {totalLabel}</Box>
            <Box>(including reimbursements)</Box>
          </Box>
          <Tooltip
            title={
              hoursGrossDisabled
                ? 'Enabled for Approved & Batched Timecards'
                : ''
            }
          >
            <span>
              <Button
                variant="secondary"
                onClick={downloadTimecardHtgReport}
                disabled={hoursGrossDisabled}
                endIcon={
                  isLoadingTimecardHtgReport && <CircularProgress size={15} />
                }
              >
                HTG Details
              </Button>
            </span>
          </Tooltip>
        </ButtonGroup>
        <ButtonGroup>
          {hasUnsavedChanges && (
            <Box sx={{ color: 'warning.500' }}>
              <ErrorOutlineIcon />
              Unsaved Changes
            </Box>
          )}
          <Tooltip arrow title={saveTitle}>
            <Box>
              <Button
                disabled={saveDisabled}
                variant="secondary"
                onClick={handleSave}
                sx={{ width: '120px' }}
                startIcon={calculating ? <CircularProgress size={15} /> : null}
              >
                {calculating ? 'Calculating' : 'Save'}
              </Button>
            </Box>
          </Tooltip>
          <Button
            variant="primaryOutlined"
            onClick={onRequestChanges}
            disabled={requestChangesDisabled}
          >
            Request Changes
          </Button>
          <Button
            variant="primary"
            disabled={approveDisabled}
            onClick={() => {
              setApproveModalOpen(true);
            }}
          >
            Approve
          </Button>
        </ButtonGroup>
        <ApprovalModal
          open={approveModalOpen}
          setOpen={setApproveModalOpen}
          timecard={timecard}
          refreshTimecard={refreshTimecard}
          fetchCalcInfo={fetchCalcInfo}
        />
      </Box>
    </Box>
  );
};

TcFooter.propTypes = {
  handleSave: PropTypes.func.isRequired,
  saveDisabled: PropTypes.bool.isRequired,
  timecard: PropTypes.object.isRequired,
  total: PropTypes.number,
  refreshTimecard: PropTypes.func,
  calculating: PropTypes.bool,
  timecardDirty: PropTypes.bool,
  reimbursementsDirty: PropTypes.bool,
  onRequestChanges: PropTypes.func.isRequired,
  fetchCalcInfo: PropTypes.func.isRequired,
  calcIssues: PropTypes.array,
  batchDetails: PropTypes.object,
  tcErrors: PropTypes.object.isRequired,
};

export default TcFooter;
