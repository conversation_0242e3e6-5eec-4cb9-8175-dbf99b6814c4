import { applyPureVueInReact } from 'veaury';
import ProjectAdminMemberTimecardsVue from '../../../../../views/projects/admin/member/ProjectAdminMemberTimecards.vue';
import { useAuth, useReactRouter } from '../../../../AppHooks';
import { useOutletContext } from 'react-router';

const ReactProjectAdminMemberTimecards = applyPureVueInReact(
  ProjectAdminMemberTimecardsVue,
);

const ProjectAdminMemberTimecards = () => {
  useAuth();
  const { navigate, route } = useReactRouter();
  const context = useOutletContext();
  return (
    <ReactProjectAdminMemberTimecards
      projectMember={context.projectMember}
      project={context.project}
      navigate={navigate}
      route={route}
    />
  );
};

export default ProjectAdminMemberTimecards;
