import { crewCreateTimecard } from '../apiCreateHelpers';
import {
  fetchMemberCurrentByProject,
  fetchProjectInformation,
} from '../apiHelpers';
import { addTimecardInformation } from '../apiPatchHelpers';

export function createTimecardCrewFlow(
  payPeriod: object,
): Cypress.Chainable<any> {
  return cy
    .fixture('e2ePayloads/timecards/createTimecardCrew')
    .then((baseTimecard) => {
      return cy
        .then(() => {
          return crewCreateTimecard();
        })
        .then((timecardRes) => {
          const timecard = timecardRes.body;
          timecard.timecardDays[0].hoursWorked = '8.00';
          baseTimecard['projectId'] = Cypress.env('projectId');
          baseTimecard['payPeriodId'] = payPeriod['id'];
          baseTimecard['payPeriod'] = payPeriod;
          baseTimecard['timecardDays'] = timecard.timecardDays;
          baseTimecard['id'] = timecard.id;
          return fetchMemberCurrentByProject()
            .then((member) => {
              baseTimecard['occupation'] = member.occupation;
              baseTimecard['rateType'] = member.rateType;
              const { shootLocation, ...workLocation } = member.workLocation;
              baseTimecard['workLocation'] = workLocation;
              baseTimecard['union'] = member.unions[0];
              baseTimecard['userCrewId'] = member.userId;
              baseTimecard['createdById'] = member.userId;
              baseTimecard['rate'] = member.rate;
              baseTimecard['projectMemberId'] = member.id;
              baseTimecard['occupationId'] = member.occupation.id;
              baseTimecard['unionId'] = member.unions[0].id;
              baseTimecard['projectLocationId'] = workLocation.id;
              baseTimecard['hourlyRate'] = member.hourlyRate;
              baseTimecard['hourlyRate'] = member.hourlyRate;
              baseTimecard['hourlyRate'] = member.hourlyRate;
              baseTimecard['hourlyRate'] = member.hourlyRate;
              baseTimecard['hourlyRate'] = member.hourlyRate;
              baseTimecard['hourlyRate'] = member.hourlyRate;
              baseTimecard['hourlyRate'] = member.hourlyRate;
              baseTimecard['hourlyRate'] = member.hourlyRate;
              baseTimecard['hourlyRate'] = member.hourlyRate;
            })
            .then(() => {
              return fetchProjectInformation().then((project) => {
                baseTimecard['project'] = project;
              });
            });
        })
        .then(() => {
          addTimecardInformation({ timecard: baseTimecard }).then((res) => {
            return res;
          });
        });
    });
}
