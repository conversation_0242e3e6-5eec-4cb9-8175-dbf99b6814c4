import { logout } from '@/services/auth';
import { getUser } from '@/services/users';
import type User from '@/types/User';
import { makeAutoObservable } from 'mobx';

import PermissionStore from './permission';

const loadFromStorage = (): User | undefined => {
  const userString = localStorage.getItem('user');
  if (!userString) return;
  return JSON.parse(userString) as User | undefined;
};

const clearCookies = () => {
  document.cookie.split(';').forEach((c) => {
    document.cookie =
      c.trim().split('=')[0] +
      '=;expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/';
  });
};

class AuthStore {
  constructor() {
    makeAutoObservable(this);
  }

  user: User | undefined = loadFromStorage();

  get getUser() {
    return this.user as User;
  }

  get isLoggedIn() {
    return !!this.user;
  }

  login(user: User) {
    this.user = user;
    localStorage.setItem('user', JSON.stringify(this.user));
  }

  async reloadUser() {
    const { data: user } = await getUser('me');

    this.login(user);
  }

  logout() {
    this.user = undefined;
    logout();
    PermissionStore.clearPermissions();
    PermissionStore.clearAdmin();
    localStorage.removeItem('user');
    clearCookies();
  }
}
const newAuthStore = new AuthStore();

export default newAuthStore;
