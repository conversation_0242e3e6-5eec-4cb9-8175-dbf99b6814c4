//For local dev, add the following to your .env file:
// VITE_PASSWORDLESS_OKTA_ISSUER=https://dev-123456.okta.com/oauth2/default

const config = {
  okta: {
    issuer: import.meta.env.VITE_PASSWORDLESS_OKTA_ISSUER,
    clientId: import.meta.env.VITE_PASSWORDLESS_OKTA_CLIENT_ID,
    baseUrl: import.meta.env.VITE_PASSWORDLESS_OKTA_BASE_URL,
  },
  apiDashboardUrl: import.meta.env.VITE_API_DASHBOARD_URL,
  muiLicenseKey: import.meta.env.VITE_MUI_PRO_LICENSE_KEY,
};

export default config;
