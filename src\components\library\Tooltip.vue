<template>
  <div class="group">
    <slot />
    <div
      id="tooltip-default"
      class="absolute group-hover:opacity-100 opacity-0 group-hover:inline-block hidden z-10 transition-opacity rounded-md dark:bg-gray-800 dark:text-gray-200 p-2 w-50"
    >
      <span class="text-center">{{ text }}</span>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'Tooltip',
  props: ['text'],
  data() {
    return {};
  },
  methods: {},
};
</script>
