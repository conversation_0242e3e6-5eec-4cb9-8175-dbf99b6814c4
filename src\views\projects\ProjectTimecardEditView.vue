<template>
  <div v-if="submittedOrApproved">
    <ProjectTimecardApprovedSummary :timecard="timecard" class="mb-24" />
  </div>
  <div v-else class="flex justify-center mx-auto w-full py-2">
    <div class="max-w-7xl pb-20">
      <div class="text-center max-w-screen-sm">
        <div
          v-if="
            timecard.requestedChanges &&
            timecard.status.id === TimecardStatusId.RequestedChanges
          "
          class="border-2 border-red-400 rounded-lg my-3 pb-2"
        >
          <div class="text-red-400 flex items-center justify-center">
            <ExclamationTriangleIcon class="h-5 w-5 inline-block" />
            <span class="ml-1 font-light text-lg">Requested Changes</span>
          </div>
          <div>
            The following changes were requested by your production supervisor:
          </div>
          <div class="dark:text-gray-300">{{ timecard.requestedChanges }}</div>
        </div>
        <div v-if="payPeriod.id">
          {{
            `${payPeriod.startsAt.toFormat(
              'ccc. MM/dd',
            )} - ${payPeriod.endsAt.toFormat('ccc. MM/dd')}`
          }}
        </div>
        <div
          class="pb-3 mb-3 flex justify-between items-baseline border-b-2 border-gray-300 dark:border-gray-600 cursor-pointer mt-4 overflow-x-scroll md:overflow-x-hidden"
        >
          <div
            class="px-2"
            v-for="(timecardDay, timecardDayIndex) in timecard.timecardDays"
            :key="`timecard-day-${timecardDayIndex}`"
          >
            <div
              :class="{
                'border-blue-500 bg-blue-900 dark:bg-blue-500 dark:border-blue-200 text-white':
                  timecardDay.isActive,
              }"
              class="h-8 w-9 pt-1 rounded border text-center border-gray-300 dark:border-gray-400"
              @click="select(timecardDay, timecardDayIndex)"
              :data-testid="`timecard-day-${timecardDay.date.toFormat('cccc')}`"
            >
              {{ timecardDay.date.toFormat('ccc') }}
            </div>
          </div>
        </div>
        <div class="text-left">
          <template
            v-for="(timecardDay, timecardDayIndex) in activeTimecardDays"
            :key="`timecard-day-template-${timecardDayIndex}`"
          >
            <TimecardDayForm
              class="my-4"
              :timecard-day="timecardDay"
              :disabled="editDisabled"
              :index="timecardDayIndex"
              :hourly-exempt="timecard.isExempt"
              @update="updateTimecardDay($event)"
              :project="project"
              :project-shoot-locations="listProjectShootLocations"
              :work-status="workStatusList"
              @copy-last-day="copyLastDay($event)"
            />
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import ProjectTimecardApprovedSummary from '@/components/ProjectTimecardApprovedSummary.vue';
import TimecardDayForm from '@/components/TimecardDayForm.vue';
import Toggle from '@/components/library/Toggle.vue';
import {
  getCurrentMember,
  listProjectShootLocations,
  listDayTemplates,
  applyDayTemplateForRow,
} from '@/services/project';
import { getWorkStatuses } from '@/services/project-members';
import { getTimecard, updateTimecard } from '@/services/timecards';
import type PayPeriod from '@/types/PayPeriod';
import type Project from '@/types/Project';
import type ProjectMember from '@/types/ProjectMember';
import type Timecard from '@/types/Timecard';
import type TimecardDay from '@/types/TimecardDay';
import { WorkStatusKeys } from '@/types/WorkStatus';
import { TimecardStatusId } from '@/utils/enum';
import { ExclamationTriangleIcon } from '@heroicons/vue/24/outline';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import axios from 'axios';
import { DateTime } from 'luxon';
import { defineComponent, type PropType } from 'vue';
export default defineComponent({
  props: {
    project: {
      type: Object as PropType<Project>,
      required: true,
    },
    projectMember: {
      type: Object as PropType<ProjectMember>,
      required: true,
    },
    editDisabled: {
      type: Boolean,
      default: false,
    },
    route: {
      type: Object as PropType<any>,
      required: true,
    },
    navigate: {
      type: Function as PropType<Function>,
      required: true,
    },
    refresh: {
      type: Function as PropType<Function>,
      required: true,
    },
    componentLoaded: {
      type: Function as PropType<Function>,
      required: true,
    },
  },
  components: {
    ExclamationTriangleIcon,
    TimecardDayForm,
    Toggle,
    ProjectTimecardApprovedSummary,
  },
  data() {
    return {
      timecard: {} as Timecard,
      dayTemplates: [] as any[],
      payPeriod: {} as PayPeriod,
      listProjectShootLocations: [] as any[],
      workStatusList: [] as any[],
      TimecardStatusId,
    };
  },
  computed: {
    activeTimecardDays(): TimecardDay[] {
      if (!this.timecard?.timecardDays) return [];
      return this.timecard.timecardDays.filter(({ isActive }) => isActive);
    },
    submittedOrApproved(): boolean {
      return (
        this.timecard?.status?.id == TimecardStatusId.Submitted ||
        this.timecard?.status?.id == TimecardStatusId.Approved
      );
    },
  },
  methods: {
    updateTimecardDay(timecardDay: TimecardDay) {
      const timecard = { ...this.timecard };
      const timecardDayIndex = timecard.timecardDays.findIndex(
        (tcDay: TimecardDay) => tcDay.id === timecardDay.id,
      );
      timecard.timecardDays[timecardDayIndex] = timecardDay;
      this.timecard = timecard;
    },
    select(timecardDay: TimecardDay, index: number) {
      if (this.editDisabled) {
        return;
      }
      const isActive = timecardDay.isActive;
      const newDay = applyDayTemplateForRow(timecardDay, this.dayTemplates);
      if (newDay) {
        this.timecard.timecardDays[index] = newDay;
      }
      this.timecard.timecardDays[index].isActive = !isActive;
    },
    async saveAndContinue() {
      await this.save();
      const curPath = this.route.location.pathname;
      this.navigate({
        pathname: `${curPath}/mileage`,
      });
    },
    async save() {
      if (this.editDisabled) return;
      this.checkForNdb(this.timecard);
      const payload: Timecard = JSON.parse(JSON.stringify(this.timecard));
      try {
        await updateTimecard(this.timecard.id, { timecard: payload });
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
        throw err;
      }
      this.refresh();
    },
    checkForNdb(timecard: Timecard) {
      timecard.timecardDays.forEach((day) => {
        if (!day.hasNdb) {
          day.generalCrewCall = null;
        }
      });
    },
    copyLastDay(timecardDayId: number): void {
      const timecardDays = this.timecard.timecardDays;
      const currentDayIndex = this.findTimecardDayIndexById(timecardDayId);
      const previousDay = this.findPreviousTimecardDay(currentDayIndex);

      if (!previousDay) {
        return;
      }

      const currentDay = timecardDays[currentDayIndex];
      const currentDate =
        typeof currentDay.startsAt === 'string'
          ? DateTime.fromISO(currentDay.startsAt)
          : currentDay.startsAt;
      const prevStartsAt =
        typeof previousDay.startsAt === 'string'
          ? DateTime.fromISO(previousDay.startsAt)
          : previousDay.startsAt;
      const prevEndsAt =
        typeof previousDay.endsAt === 'string'
          ? DateTime.fromISO(previousDay.endsAt)
          : previousDay.endsAt;

      currentDay.rate = previousDay.rate;
      currentDay.zipCode = previousDay.zipCode;
      currentDay.comments = previousDay.comments;
      currentDay.startsAt = this.combineDateAndTime(currentDate, prevStartsAt);
      currentDay.endsAt = this.combineDateAndTime(currentDate, prevEndsAt);
      currentDay.projectShootLocation = previousDay.projectShootLocation;
      currentDay.workStatus = previousDay.workStatus;
      currentDay.hasNdb = previousDay.hasNdb;

      currentDay.meals = previousDay.meals.map((meal) => {
        const prevMealStartsAt =
          typeof meal.startsAt === 'string'
            ? DateTime.fromISO(meal.startsAt)
            : meal.startsAt;
        const prevMealEndsAt =
          typeof meal.endsAt === 'string'
            ? DateTime.fromISO(meal.endsAt)
            : meal.endsAt;

        return {
          timecardDayId: meal.timecardDayId,
          startsAt: this.combineDateAndTime(currentDate, prevMealStartsAt),
          endsAt: this.combineDateAndTime(currentDate, prevMealEndsAt),
        };
      });
    },
    findTimecardDayIndexById(timecardDayId: number): number {
      return this.timecard.timecardDays.findIndex(
        (timecardDay) => timecardDay.id === timecardDayId,
      );
    },
    findPreviousTimecardDay(currentDayIndex: number): TimecardDay | undefined {
      const timecardDays = this.timecard.timecardDays;
      return timecardDays
        .slice(0, currentDayIndex)
        .reverse()
        .find((timecardDay) => timecardDay.isActive);
    },
    combineDateAndTime(date: DateTime, time: DateTime): DateTime {
      return DateTime.fromObject(
        {
          year: date.year,
          month: date.month,
          day: date.day,
          hour: time.hour,
          minute: time.minute,
          second: time.second,
          millisecond: time.millisecond,
        },
        { zone: 'utc' },
      );
    },
    async loadDayTemplates() {
      const { data: dayTemplates } = await listDayTemplates(this.project.id!);
      this.dayTemplates = dayTemplates;
      this.dayTemplates.forEach((dayTemplate) => {
        dayTemplate.isActive = false;
      });
    },
    async loadShootLocations() {
      const { data } = await listProjectShootLocations(this.project.id!);
      this.listProjectShootLocations = data;
    },
    async loadWorkStatus() {
      const { data: authProjectMember } = await getCurrentMember(
        this.project.id,
      );
      const projectMemberId: any = authProjectMember?.id;

      const { data: workStatuses } = await getWorkStatuses(projectMemberId);
      const getWorkStatusList = workStatuses;
      if (getWorkStatusList.length > 0) {
        this.workStatusList = getWorkStatusList.filter(
          (eachWorkStatus: any) =>
            eachWorkStatus?.key?.toUpperCase() === WorkStatusKeys.KEY_WORK ||
            eachWorkStatus?.key?.toUpperCase() === WorkStatusKeys.KEY_IDLE ||
            eachWorkStatus?.key?.toUpperCase() === WorkStatusKeys.KEY_TRAVEL,
        );
      }
    },
  },
  async mounted() {
    await this.loadDayTemplates();
    const { data: timecard } = await getTimecard(
      parseInt(this.route.params.timecardId as string),
    );
    this.timecard = timecard as Timecard;
    this.timecard.timecardDays.forEach((day: TimecardDay) => {
      day.startsAt = day.startsAt.toUTC();
      day.endsAt = day.endsAt.toUTC();
      day.meals.forEach((meal) => {
        meal.startsAt = meal.startsAt.toUTC();
        meal.endsAt = meal.endsAt.toUTC();
      });
    });
    this.payPeriod = timecard.payPeriod;
    await this.loadShootLocations();
    this.componentLoaded();
    await this.loadWorkStatus();
  },
});
</script>

<style></style>
