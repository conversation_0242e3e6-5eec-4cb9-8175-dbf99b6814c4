export function manualDownloadFileByUrl(
  downloadUrl: string,
  filename: string,
): void {
  const url = window.URL;
  const link = document.createElement('a');
  link.target = '_blank';
  link.style.cssText = 'display: none;';
  link.href = downloadUrl;
  link.setAttribute('download', filename);
  document.body.appendChild(link);
  link.click();
  setTimeout(function () {
    url.revokeObjectURL(downloadUrl);
    document.body.removeChild(link);
  }, 100);
}

export const extractCSVFileNameFromUrl = (url: string): string => {
  const urlParts = url.split('/');
  const fileNameWithQuery = urlParts.find((part: string) =>
    part.includes('.csv'),
  );
  if (fileNameWithQuery) {
    const fileName = fileNameWithQuery.split('?')[0];
    // fileName = fileName.replace('.csv', '.xlsx');
    return fileName;
  } else {
    return 'default.csv';
  }
};
