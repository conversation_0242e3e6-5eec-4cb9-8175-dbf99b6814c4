import type { DateTime } from 'luxon';

export interface TemplateTimecard {
  id: number;
  timecardDays: TemplateDay[];
}

export interface TemplateDayDTO {
  // [key: string]: any; // Allows arbitrary string keys
  isActive: boolean;
  id: number | null;
  date: DateTime | null;
  generalCrewCall: DateTime | null;
  startsAt: DateTime | null;
  endsAt: DateTime | null;

  workStatusId: number | null;
  workZoneId: number | null;
  hasNdb: boolean;
  projectLocationId: number | null;
  hasWalkingMeal: boolean;
  hasMealGraceOne: boolean;
  hasMealGraceTwo: boolean;
  hasWrapGrace: boolean;
  hasHalfDay: boolean;
  driveTime: number | null;
  hotelToSetTime: number | null;
  setToHotelTime: number | null;
  meals: TemplateDayMeal[]; // Array of meals for the day

  //values we get from BE but don't use in the UI
  hasHoliday: boolean;
  mealPenalties: number | null;
  projectId: number | null;
  isRentalDay: boolean;
  createdAt: DateTime | null;
  updatedAt: DateTime | null;
  zipCode: string | null;
  comments: string | null;
}

export interface TemplateDay extends TemplateDayDTO {
  date: DateTime;
  isNewDay: boolean;
  updated: boolean;
}

export interface TemplateDayMeal {
  id?: number;
  timecardDayId: number | null;
  startsAt: DateTime | null;
  endsAt: DateTime | null;
}
