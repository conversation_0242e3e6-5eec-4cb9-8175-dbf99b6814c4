import { applyPureVueInReact } from 'veaury';
import ProjectsCodeVue from '@/views/projects/ProjectCodeView.vue';
import { useAuth } from '@/reactComponents/AppHooks';
import { useNavigate } from 'react-router';
const ReactProjectsCodeView = applyPureVueInReact(ProjectsCodeVue);

const ProjectCodeView = () => {
  useAuth();
  const navigate = useNavigate();
  return <ReactProjectsCodeView navigate={navigate} />;
};

export default ProjectCodeView;
