class TimecardFormPageObject {
  payPeriodDropdown: string = '[data-testid="payPeriod-select-dropdown"]';
  payPeriodContinueButton: string = '[data-testid="payPeriod-continue-btn"]';
  selectDay: string = '[data-testid="timecard-day-{{day}}"]';
  workStatus: string = '[data-testid="timecard-workStatus-dropdown-{{day}}"]';
  workLocation: string =
    '[data-testid="timecard-workLocation-dropdown-{{day}}"]';
  startstAt: string =
    '[data-testid="timecard-startsAt-{{day}}"] > .cursor-pointer > .appearance-none';
  mealStart: string =
    '[data-testid="timecard-mealStart-{{day}}"] > .cursor-pointer > .appearance-none';
  mealEnd: string =
    '[data-testid="timecard-mealEnd-{{day}}"] > .cursor-pointer > .appearance-none';
  endsAt: string =
    '[data-testid="timecard-endsAt-{{day}}"] > .cursor-pointer > .appearance-none';
  decreaseMeal: string = '[data-testid="timecard-removeMeal-btn-{{day}}"]';
  addMeal: string = '[data-testid="timecard-addMeal-btn-{{day}}"]';
  timecardNotes: string = '[data-testid="timecard-notes-{{day}}"]';
  ndbToggle: string = '[data-testid="timecard-ndb-toggle-{{day}}"]';
  copyLastDayButton: string = '[data-testid="timecard-ndb-toggle-{{day}}"]';
  saveAndContinueButton: string = '[data-testid="timecard-saveContinue-btn"]';
  addMoreTimeCreteButton: string = '[data-testid="addMoreTime-create-btn"]';

  private fillOutTime(timeData: TimeInformation[]) {
    const days = timeData.map((timeInfo) => timeInfo.day);
    days.forEach((day) => {
      cy.get(this.selectDay.replace('{{day}}', day)).click();
    });
    cy.get('div.animate-pulse', { timeout: 6000 }).should('not.exist');
    timeData.forEach((timeInfo) => {
      if (timeInfo.copyLastDay) {
        cy.get(this.copyLastDayButton.replace('{{day}}', timeInfo.day)).click();
      } else {
        cy.pickDropdownOption(
          this.workStatus.replace('{{day}}', timeInfo.day),
          timeInfo.workStatus,
        );
        cy.pickDropdownOption(
          this.workLocation.replace('{{day}}', timeInfo.day),
          timeInfo.workLocation,
        );
        cy.selectTime(
          this.startstAt.replace('{{day}}', timeInfo.day),
          timeInfo.startsAt,
        );
        if (timeInfo.mealStart.length > 0) {
          timeInfo.mealStart.forEach((meal) => {
            cy.selectTime(
              this.mealStart.replace('{{day}}', timeInfo.day),
              meal,
            );
          });
        }
        if (timeInfo.mealEnd.length > 0) {
          timeInfo.mealEnd.forEach((meal) => {
            cy.selectTime(this.mealEnd.replace('{{day}}', timeInfo.day), meal);
          });
        }
        cy.selectTime(
          this.endsAt.replace('{{day}}', timeInfo.day),
          timeInfo.endsAt,
        );
        cy.get(this.timecardNotes.replace('{{day}}', timeInfo.day)).type(
          timeInfo.notes,
        );
        if (timeInfo.ndb) {
          cy.get(this.ndbToggle.replace('{{day}}', timeInfo.day)).click();
        }
      }
    });
  }

  startTimecard(timePeriod: string) {
    cy.get(this.payPeriodDropdown).click();
    cy.get('.MuiAutocomplete-listbox').should('be.visible');
    cy.get('.MuiAutocomplete-listbox').contains(timePeriod).click();
    cy.get(this.payPeriodContinueButton).click();
  }

  fillOutTimecardForm(timeData: TimeInformation[]) {
    this.fillOutTime(timeData);
    cy.get(this.saveAndContinueButton).should('be.visible');
    cy.get(this.saveAndContinueButton).click();
    cy.get(this.saveAndContinueButton).should('be.visible');
    cy.get(this.saveAndContinueButton).click();
    cy.get(this.saveAndContinueButton).should('be.visible');
    cy.get(this.saveAndContinueButton).click();
    cy.get(this.saveAndContinueButton).should('be.visible');
    cy.get(this.saveAndContinueButton).click();
    cy.signatureApproval();
    cy.get(this.saveAndContinueButton).should('be.visible').click();
    cy.wait('@submitTimecard').then(() => {
      cy.get('button').contains('Exit').should('be.visible').click();
    });
  }
}

type TimeInformation = {
  day: string;
  workStatus: string;
  workLocation: string;
  startsAt: string;
  mealStart: string[];
  mealEnd: string[];
  endsAt: string;
  notes: string;
  ndb: boolean;
  copyLastDay: boolean;
};

export { TimecardFormPageObject, TimeInformation };
