import axios, { type AxiosResponse } from 'axios';
import { coreBaseUrl } from '../utils/constants';

export const activeMemberBatchApprove = async (
  memberId: number,
  productionCompanyId: number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/production-company-members/${memberId}/activate-batch-approver`;
  const response = await axios.post(
    url,
    { productionCompanyId },
    { withCredentials: true },
  );
  return response;
};

export const inactiveMemberBatchApprove = async (
  memberId: number,
  productionCompanyId: number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/production-company-members/${memberId}/inactivate-batch-approver`;
  const response = await axios.post(
    url,
    { productionCompanyId },
    { withCredentials: true },
  );
  return response;
};
