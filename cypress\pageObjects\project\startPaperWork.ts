export class StartPaperworkPageObject {
  signPaperwork() {
    cy.get('button[type="submit"]')
      .contains('Generate Paperwork')
      .should('be.visible')
      .click();
    cy.get('button[type="submit"]')
      .contains('Sign')
      .should('be.visible')
      .click();
    cy.signatureApproval();
    cy.get('button[type="submit"]')
      .contains('Submit')
      .should('be.visible')
      .click();
  }
}
