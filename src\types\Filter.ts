import type { SortDirection } from '@/utils/enum';

export interface Filter {
  id: string;
  field: string;
  value:
    | string
    | string[]
    | number
    | number[]
    | Date
    | Date[]
    | null
    | undefined;
  active: boolean;
  operator: FilterOperator | null;
  label?: string;
  subject?: 'timccard' | 'batch';
  type?: FilterUIType;
  sortable?: boolean;
  sortDirection?: SortDirection | '';
  options?: FilterOption[];
}

export interface FilterOption {
  id: string;
  value: string | number;
  label: string;
  active: boolean;
}

export enum FilterUIType {
  Checkbox = 'checkbox',
  Radio = 'radio',
  Select = 'select',
  MultiSelect = 'multiselect',
  Slider = 'slider',
  Date = 'date',
  DateRange = 'dateRange',
  Text = 'text',
  Number = 'number',
  NumberRange = 'numberRange',
  Hidden = 'hidden',
}

export enum FilterOperator {
  Equals = 'eq',
  GreaterThan = 'gt',
  LessThan = 'lt',
  GreaterThanOrEqual = 'gte',
  LessThanOrEqual = 'lte',
  Like = 'like',
  ILike = 'ilike',
  In = 'in',
  None = '',
}
