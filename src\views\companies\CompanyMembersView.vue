<template>
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pt-40">
    <div class="flex justify-between items-center space-x-1 my-4">
      <h3 class="text-lg font-semibold leading-6">Members</h3>
      <Button @click="addMemberModal = true"> Add </Button>
      <Modal v-model="addMemberModal">
        <div class="flex justify-between">
          <h2 class="text-lg font-light">Add Member</h2>
          <XMarkIcon @click="addMemberModal = false" class="w-6" />
        </div>
        <TextInput v-model="newMemberEmail" label="Email" />
        <div class="flex justify-center">
          <Button @click="addMember"> Submit </Button>
        </div>
      </Modal>
    </div>
    <div class="bg-white dark:bg-gray-900 shadow sm:rounded-md">
      <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-500">
        <li
          v-for="(member, memberIndex) in members"
          :key="`${member.id}-${memberIndex}`"
        >
          <a
            class="block hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
            :class="{
              'rounded-t-md': memberIndex === 0,
              'rounded-b-md': memberIndex === members.length - 1,
            }"
            @click="goToMemberDetails(member)"
          >
            <div class="px-4 py-4 sm:px-6">
              <div class="flex items-center justify-between">
                <p
                  class="truncate text-sm font-medium text-indigo-600 dark:text-indigo-400"
                >
                  {{
                    `${member.user.firstName}
                  ${
                    member.user.middleName ? member.user.middleName + ' ' : ''
                  }${member.user.lastName}`
                  }}
                </p>
              </div>
              <div class="mt-2 flex flex-wrap justify-between items-start">
                <div
                  class="sm:flex sm:flex-wrap justify-start sm:space-x-2 items-center sm:w-3/5 md:w-2/3 lg:w-1/2"
                >
                  <div
                    class="flex items-center space-x-2 justify-start text-sm text-gray-500 dark:text-gray-400"
                  >
                    <PhoneIcon
                      class="h-4 w-4 text-gray-400 dark:text-gray-500"
                      aria-hidden="true"
                    />
                    <p>{{ member.user.phone }}</p>
                  </div>
                  <div
                    class="flex items-center space-x-2 justify-start text-sm text-gray-500 dark:text-gray-400"
                  >
                    <EnvelopeIcon
                      class="h-5 w-5 text-gray-400 dark:text-gray-500"
                      aria-hidden="true"
                    />
                    <div>
                      {{ member.user.email }}
                    </div>
                  </div>
                </div>
                <div
                  class="flex items-center text-sm text-gray-500 dark:text-gray-400 space-x-2"
                  @click.stop
                >
                  <Checkbox
                    label="Batch approver"
                    name="batch-approve"
                    id="batch-approve"
                    @update:modelValue="(val) => batchApproveCheck(val, member)"
                    :modelValue="member.isBatchApprover"
                  ></Checkbox>
                </div>
                <div
                  class="flex items-center text-sm text-gray-500 dark:text-gray-400"
                >
                  <CalendarIcon
                    class="h-5 w-5 text-gray-400 dark:text-gray-500"
                    aria-hidden="true"
                  />
                  <p>
                    Created on
                    {{ ' ' }}
                    <time :datetime="member.user.createdAt?.toISO()">{{
                      member?.user?.createdAt?.toFormat('MM/dd')
                    }}</time>
                  </p>
                </div>
              </div>
            </div>
          </a>
        </li>
      </ul>
    </div>
  </div>
</template>

<script lang="ts">
import Button from '@/components/library/Button.vue';
import Modal from '@/components/library/Modal.vue';
import TextInput from '@/components/library/TextInput.vue';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import {
  addProductionCompanyMember,
  listProductionCompanyMembers,
} from '@/services/production-company';
import {
  activeMemberBatchApprove,
  inactiveMemberBatchApprove,
} from '@/services/production-company-members';
import { formatPhoneNumber } from '@/utils/phone';
import { EnvelopeIcon, XMarkIcon } from '@heroicons/vue/20/solid';
import { CalendarIcon, PhoneIcon } from '@heroicons/vue/24/outline';
import axios from 'axios';
import { defineComponent } from 'vue';
import Checkbox from '@/components/library/Checkbox.vue';

export default defineComponent({
  props: ['company', 'navigate'],
  components: {
    TextInput,
    Button,

    Modal,
    PhoneIcon,
    CalendarIcon,
    EnvelopeIcon,
    XMarkIcon,
    Checkbox,
  },
  data() {
    return {
      members: [] as any[],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
      },
      headers: [
        { name: 'ID', value: 'id' },
        { name: 'Name', value: 'name' },
        { name: 'Phone', value: 'phone' },
        { name: 'Created At', value: 'createdAt' },
      ],
      addMemberModal: false as boolean,
      newMemberEmail: '' as string,
    };
  },
  methods: {
    formatPhoneNumber,
    async addMember() {
      try {
        const { data } = await addProductionCompanyMember(
          this.company.id,
          this.newMemberEmail,
        );
        this.addMemberModal = false;
        SnackbarStore.triggerSnackbar(
          'Successfully added production company member.',
          2500,
          'success',
        );
        this.members.push(data);
        this.newMemberEmail = '';
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
      }
      this.addMemberModal = false;
    },
    goToMemberDetails(member: any) {
      this.navigate({
        pathname: `/companies/${this.company.id}/members/${member.user.id}`,
      });
    },
    async batchApproveCheck(isBatchApprover: any, member: any) {
      try {
        if (isBatchApprover) {
          await activeMemberBatchApprove(member.id, this.company.id);
          SnackbarStore.triggerSnackbar(
            'Member successfully updated as Active Batch Approver.',
            1500,
            'success',
          );
        } else {
          await inactiveMemberBatchApprove(member.id, this.company.id);
          SnackbarStore.triggerSnackbar(
            'Member successfully updated as Inactive Batch Approver.',
            1500,
            'success',
          );
        }
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
      }
    },
  },
  async mounted() {
    const {
      data: { data },
    } = await listProductionCompanyMembers(this.company.id, {
      page: 1,
      limit: 100,
    });
    this.members = data;
  },
});
</script>
