import { applyPureVueInReact } from 'veaury';
import ProjectAdminBatchesViewVue from '../../../../views/projects/admin/ProjectAdminBatchesView.vue';
import { useAuth, useReactRouter } from '../../../AppHooks';
import { useOutletContext } from 'react-router';

const ReactProjectAdminBatchesView = applyPureVueInReact(
  ProjectAdminBatchesViewVue,
);

const ProjectAdminBatchesView = () => {
  useAuth();
  const { navigate, route } = useReactRouter();
  const context = useOutletContext();
  return (
    <ReactProjectAdminBatchesView
      project={context.project}
      isAdmin={context.isAdmin}
      navigate={navigate}
      route={route}
    />
  );
};

export default ProjectAdminBatchesView;
