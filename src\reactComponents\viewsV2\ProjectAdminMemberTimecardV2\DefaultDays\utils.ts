import { DateTime } from 'luxon';
import _clonedeep from 'lodash/cloneDeep';
import _get from 'lodash/get';
import _set from 'lodash/set';
import type {
  TemplateDay,
  TemplateDayMeal,
  TemplateDayDTO,
} from '@/types/TemplateDay';

export const dateFmtStr = 'yyyy-MM-dd';

let newId = 999999000;
export const getNewDayId = () => {
  newId += 1;
  return newId;
};

const BASE_DAY = {
  id: 0,
  isActive: true,
  date: null,
  generalCrewCall: null,
  startsAt: null,
  meals: [
    {
      timecardDayId: 0,
      startsAt: null,
      endsAt: null,
    },
  ],
  endsAt: null,

  workStatusId: null,
  workZoneId: null,
  hasNdb: false,
  projectLocationId: null,
  hasWalkingMeal: false,
  hasMealGraceOne: false,
  hasMealGraceTwo: false,
  hasWrapGrace: false,
  hasHalfDay: false,
  driveTime: null,
  hotelToSetTime: null,
  setToHotelTime: null,

  hasHoliday: false,
  mealPenalties: null,
  projectId: null,
  isRentalDay: false,
  createdAt: null,
  updatedAt: null,
  zipCode: null,
  comments: null,
} as TemplateDayDTO;

export const createDay = (dateStr: string) => {
  const date = DateTime.fromFormat(dateStr, dateFmtStr, { zone: 'UTC' });
  const day: TemplateDay = {
    ..._clonedeep(BASE_DAY),
    date: date.set({ hour: 0, minute: 0 }),
    updated: true,
    isNewDay: true,
  };

  day.id = getNewDayId();

  day.endsAt = date.set({ hour: 17, minute: 0 });
  day.meals[0].timecardDayId = day.id;
  day.meals[0].startsAt = date.set({ hour: 13, minute: 0 });
  day.meals[0].endsAt = date.set({ hour: 14, minute: 0 });

  return day as TemplateDay;
};

const dateTimeFields = [
  'startsAt',
  'endsAt',
  'generalCrewCall',
  'meals[0].startsAt',
  'meals[0].endsAt',
  'meals[1].startsAt',
  'meals[1].endsAt',
];

export const copyDay = (dateStr: string, sourceDay: TemplateDay) => {
  const date = DateTime.fromFormat(dateStr, dateFmtStr, { zone: 'UTC' });
  const day = _clonedeep(sourceDay);
  day.id = getNewDayId();
  day.isNewDay = true;
  day.updated = true;

  day.date = date;
  const year = date.year;
  const month = date.month;
  const dayOfMonth = date.day;

  dateTimeFields.forEach((field) => {
    const value = _get(day, field);
    if (value) {
      _set(day, field, value.set({ year, month, day: dayOfMonth }));
    }
  });
  day.meals.forEach((meal: TemplateDayMeal) => (meal.timecardDayId = day.id));

  return day;
};

export const sortDays = (a: TemplateDay, b: TemplateDay) => {
  const dateA = a.date;
  const dateB = b.date;
  return dateA < dateB ? -1 : dateA > dateB ? 1 : 0;
};
