import { applyPureVueInReact } from 'veaury';
import ProjectCustomStartPaperworkFormViewVue from '../../../../views/projects/admin/ProjectCustomStartPaperworkFormView.vue';
import { useAuth, useReactRouter } from '../../../AppHooks';
import { useOutletContext } from 'react-router';

const ReactProjectCustomStartPaperworkFormView = applyPureVueInReact(
  ProjectCustomStartPaperworkFormViewVue,
);

const ProjectCustomStartPaperworkFormView = () => {
  useAuth();
  const { navigate, route } = useReactRouter();
  const editMode = !!route?.match?.handle?.editMode;
  const context = useOutletContext();
  return (
    <ReactProjectCustomStartPaperworkFormView
      project={context.project}
      navigate={navigate}
      route={route}
      editMode={editMode}
    />
  );
};

export default ProjectCustomStartPaperworkFormView;
