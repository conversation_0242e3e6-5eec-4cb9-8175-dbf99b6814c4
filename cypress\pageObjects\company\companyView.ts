/// <reference types="cypress" />

export default class CompanyView {
  readonly companyNameInput = '[data-testid="Name-input"]';

  companyDashboardPage(
    phone: string,
    addresses: string,
    taxClassification: string,
  ) {
    cy.contains('Information').should('be.visible');
    cy.contains(phone).should('be.visible');
    cy.contains('Payroll Provider').should('be.visible');
    cy.contains(addresses).should('be.visible');
    cy.contains(taxClassification).should('be.visible');
  }
}
