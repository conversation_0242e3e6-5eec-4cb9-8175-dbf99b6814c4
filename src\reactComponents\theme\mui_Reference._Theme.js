//Not used by the app, just a reference for the theme object

// eslint-disable-next-line no-unused-vars
const Reference_Theme = {
  breakpoints: {
    keys: ['xs', 'sm', 'md', 'lg', 'xl'],
    values: { xs: 0, sm: 600, md: 900, lg: 1200, xl: 1536 },
    unit: 'px',
  },
  direction: 'ltr',
  components: {
    MuiButton: {
      defaultProps: { variant: 'primary' },
      styleOverrides: {
        root: {
          borderRadius: '8px',
          textTransform: 'none',
          minWidth: '40px',
          variants: [
            { props: { variant: 'primary' } },
            { props: { variant: 'primaryOutlined' } },
            { props: { variant: 'secondary' } },
          ],
        },
      },
    },
    MuiAccordion: { styleOverrides: {} },
    MuiAutocomplete: { styleOverrides: {} },
    MuiAccordionSummary: { styleOverrides: {} },
    MuiAccordionDetails: { styleOverrides: {} },
    MuiTypography: {
      defaultProps: { component: 'span' },
      styleOverrides: {
        root: {
          variants: [
            {
              props: { variant: 'disReg' },
              style: { fontSize: '1.5rem', fontWeight: 400 },
            },
            {
              props: { variant: 'disMed' },
              style: { fontSize: '1.5rem', fontWeight: 500 },
            },
            {
              props: { variant: 'disSemi' },
              style: { fontSize: '1.5rem', fontWeight: 600 },
            },
            {
              props: { variant: 'disStrong' },
              style: { fontSize: '1.5rem', fontWeight: 700 },
            },
            {
              props: { variant: 'lgReg' },
              style: { fontSize: '1.125rem', fontWeight: 400 },
            },
            {
              props: { variant: 'lgMed' },
              style: { fontSize: '1.125rem', fontWeight: 500 },
            },
            {
              props: { variant: 'lgSemi' },
              style: { fontSize: '1.125rem', fontWeight: 600 },
            },
            {
              props: { variant: 'lgStrong' },
              style: { fontSize: '1.125rem', fontWeight: 700 },
            },
            {
              props: { variant: 'baseReg' },
              style: { fontSize: '1rem', fontWeight: 400 },
            },
            {
              props: { variant: 'baseMed' },
              style: { fontSize: '1rem', fontWeight: 500 },
            },
            {
              props: { variant: 'baseSemi' },
              style: { fontSize: '1rem', fontWeight: 600 },
            },
            {
              props: { variant: 'baseStrong' },
              style: { fontSize: '1rem', fontWeight: 700 },
            },
            {
              props: { variant: 'smReg' },
              style: { fontSize: '0.875rem', fontWeight: 400 },
            },
            {
              props: { variant: 'smMed' },
              style: { fontSize: '0.875rem', fontWeight: 500 },
            },
            {
              props: { variant: 'smSemi' },
              style: { fontSize: '0.875rem', fontWeight: 600 },
            },
            {
              props: { variant: 'smStrong' },
              style: { fontSize: '0.875rem', fontWeight: 700 },
            },
            {
              props: { variant: 'xsReg' },
              style: { fontSize: '0.75rem', fontWeight: 400 },
            },
            {
              props: { variant: 'xsMed' },
              style: { fontSize: '0.75rem', fontWeight: 500 },
            },
            {
              props: { variant: 'xsSemi' },
              style: { fontSize: '0.75rem', fontWeight: 600 },
            },
            {
              props: { variant: 'xsStrong' },
              style: { fontSize: '0.75rem', fontWeight: 700 },
            },
            {
              props: { variant: 'xxsReg' },
              style: { fontSize: '0.625rem', fontWeight: 400 },
            },
            {
              props: { variant: 'xxsMed' },
              style: { fontSize: '0.625rem', fontWeight: 500 },
            },
            {
              props: { variant: 'xxsSemi' },
              style: { fontSize: '0.625rem', fontWeight: 600 },
            },
            {
              props: { variant: 'xxsStrong' },
              style: { fontSize: '0.625rem', fontWeight: 700 },
            },
          ],
        },
      },
    },
    MuiChip: { styleOverrides: { root: { borderRadius: '8px' } } },
    MuiInputBase: { styleOverrides: {} },
    MuiTooltip: {
      styleOverrides: {
        tooltip: { variants: [{ props: { variant: 'html' } }] },
      },
    },
    MuiTab: { styleOverrides: {} },
    MuiTabs: { styleOverrides: {} },
  },
  palette: {
    mode: 'light',
    primary: {
      50: '#FDF2FA',
      200: 'rgb(251 207 232)',
      500: 'rgb(236 72 153)',
      600: '#DD2590',
      700: '#C11574',
      main: 'rgb(236 72 153)',
      contrastText: '#fff',
      light: 'rgb(239, NaN, NaN)',
      dark: '#C11574',
    },
    pink: {
      50: '#FDF2FA',
      200: 'rgb(251 207 232)',
      500: 'rgb(236 72 153)',
      600: '#DD2590',
      700: '#C11574',
      main: 'rgb(236 72 153)',
      contrastText: '#fff',
    },
    secondary: {
      main: '#9E77ED',
      light: 'rgb(177, 146, 240)',
      dark: 'rgb(110, 83, 165)',
      contrastText: '#fff',
    },
    background: { default: '#FCFCFD', paper: '#FFF', border: '#E4E7EC' },
    gray: {
      25: '#FCFCFD',
      50: '#F9FAFB',
      100: '#F2F4F7',
      200: '#E4E7EC',
      300: '#D0D5DD',
      400: '#98A2B3',
      500: '#667085',
      600: '#475467',
      700: '#344054',
      800: '#182230',
      900: '#101828',
      950: '#0C111D',
      main: '#667085',
      light: 'rgb(132, 140, 157)',
      dark: 'rgb(71, 78, 93)',
      contrastText: '#fff',
    },
    green: { 400: '#3CCB7F', 500: '#16B364', main: '#16B364' },
    blue: {
      25: '#F5FAFF',
      50: '#EFF8FF',
      100: '#D1E9FF',
      200: '#B2DDFF',
      300: '#84CAFF',
      400: '#53B1FD',
      500: '#2E90FA',
      600: '#1570EF',
      700: '#175CD3',
      800: '#1849A9',
      900: '#194185',
      main: '#2E90FA',
    },
    blueLight: { 50: '#F0F9FF', 200: '#B9E6FE', 700: '#026aa2' },
    indigo: {
      25: '#F5F8FF',
      50: '#EEF4FF',
      100: '#E0EAFF',
      200: '#C7D7FE',
      300: '#A4BCFD',
      400: '#8098F9',
      500: '#6172F3',
      600: '#444CE7',
      700: '#3538CD',
      800: '#2D31A6',
      900: '#2D3282',
      950: '#15F235B',
      main: '#6172F3',
    },
    yellow: { 400: '#FAC515', 500: '#EAAA08', main: '#EAAA08' },
    success: {
      25: '#F6FEF9',
      50: '#ECFDF3',
      100: '#DCFAE6',
      200: '#ABEFC6',
      300: '#75E0A7',
      400: '#47CD89',
      500: '#17B26A',
      600: '#079455',
      700: '#067647',
      800: '#085D3A',
      900: '#074D31',
      950: '#053321',
      main: '#17B26A',
      light: '#75E0A7',
      dark: '#067647',
      contrastText: 'rgba(0, 0, 0, 0.87)',
    },
    warning: {
      25: '#FFFCF5',
      50: '#FFFAEB',
      100: '#FEF0C7',
      200: '#FEDF89',
      300: '#FEC84B',
      400: '#FDB022',
      500: '#F79009',
      600: '#DC6803',
      700: '#B54708',
      800: '#93370D',
      900: '#7A2E0E',
      950: '#4E1D09',
      main: '#F79009',
      light: '#FEC84B',
      dark: '#B54708',
      contrastText: 'rgba(0, 0, 0, 0.87)',
    },
    error: {
      25: '#FFFBFA',
      50: '#FEF3F2',
      100: '#FEE4E2',
      200: '#FECDCA',
      300: '#FDA29B',
      400: '#F97066',
      500: '#F04438',
      600: '#D92D20',
      700: '#B42318',
      800: '#912018',
      900: '#7A271A',
      950: '#55160C',
      main: '#F04438',
      light: '#FDA29B',
      dark: '#B42318',
      contrastText: '#fff',
    },
    common: { black: '#000', white: '#fff' },
    info: {
      main: '#0288d1',
      light: '#03a9f4',
      dark: '#01579b',
      contrastText: '#fff',
    },
    grey: {
      50: '#fafafa',
      100: '#f5f5f5',
      200: '#eeeeee',
      300: '#e0e0e0',
      400: '#bdbdbd',
      500: '#9e9e9e',
      600: '#757575',
      700: '#616161',
      800: '#424242',
      900: '#212121',
      A100: '#f5f5f5',
      A200: '#eeeeee',
      A400: '#bdbdbd',
      A700: '#616161',
    },
    contrastThreshold: 3,
    tonalOffset: 0.2,
    text: {
      primary: 'rgba(0, 0, 0, 0.87)',
      secondary: 'rgba(0, 0, 0, 0.6)',
      disabled: 'rgba(0, 0, 0, 0.38)',
    },
    divider: 'rgba(0, 0, 0, 0.12)',
    action: {
      active: 'rgba(0, 0, 0, 0.54)',
      hover: 'rgba(0, 0, 0, 0.04)',
      hoverOpacity: 0.04,
      selected: 'rgba(0, 0, 0, 0.08)',
      selectedOpacity: 0.08,
      disabled: 'rgba(0, 0, 0, 0.26)',
      disabledBackground: 'rgba(0, 0, 0, 0.12)',
      disabledOpacity: 0.38,
      focus: 'rgba(0, 0, 0, 0.12)',
      focusOpacity: 0.12,
      activatedOpacity: 0.12,
    },
  },
  shape: { borderRadius: 8 },
  colorSchemes: {
    dark: {
      palette: {
        common: { black: '#000', white: '#fff' },
        mode: 'dark',
        primary: {
          50: '#FDF2FA',
          200: 'rgb(251 207 232)',
          500: 'rgb(236 72 153)',
          600: '#DD2590',
          700: '#C11574',
          main: 'rgb(236 72 153)',
          contrastText: '#fff',
          light: 'rgb(239, NaN, NaN)',
          dark: '#C11574',
        },
        secondary: {
          main: '#9E77ED',
          light: 'rgb(177, 146, 240)',
          dark: 'rgb(110, 83, 165)',
          contrastText: '#fff',
        },
        error: {
          25: '#FFFBFA',
          50: '#FEF3F2',
          100: '#FEE4E2',
          200: '#FECDCA',
          300: '#FDA29B',
          400: '#F97066',
          500: '#F04438',
          600: '#D92D20',
          700: '#B42318',
          800: '#912018',
          900: '#7A271A',
          950: '#55160C',
          main: '#F04438',
          light: '#FDA29B',
          dark: '#B42318',
          contrastText: '#fff',
        },
        warning: {
          25: '#FFFCF5',
          50: '#FFFAEB',
          100: '#FEF0C7',
          200: '#FEDF89',
          300: '#FEC84B',
          400: '#FDB022',
          500: '#F79009',
          600: '#DC6803',
          700: '#B54708',
          800: '#93370D',
          900: '#7A2E0E',
          950: '#4E1D09',
          main: '#F79009',
          light: '#FEC84B',
          dark: '#B54708',
          contrastText: 'rgba(0, 0, 0, 0.87)',
        },
        info: {
          main: '#29b6f6',
          light: '#4fc3f7',
          dark: '#0288d1',
          contrastText: 'rgba(0, 0, 0, 0.87)',
        },
        success: {
          25: '#F6FEF9',
          50: '#ECFDF3',
          100: '#DCFAE6',
          200: '#ABEFC6',
          300: '#75E0A7',
          400: '#47CD89',
          500: '#17B26A',
          600: '#079455',
          700: '#067647',
          800: '#085D3A',
          900: '#074D31',
          950: '#053321',
          main: '#17B26A',
          light: '#75E0A7',
          dark: '#067647',
          contrastText: 'rgba(0, 0, 0, 0.87)',
        },
        grey: {
          50: '#fafafa',
          100: '#f5f5f5',
          200: '#eeeeee',
          300: '#e0e0e0',
          400: '#bdbdbd',
          500: '#9e9e9e',
          600: '#757575',
          700: '#616161',
          800: '#424242',
          900: '#212121',
          A100: '#f5f5f5',
          A200: '#eeeeee',
          A400: '#bdbdbd',
          A700: '#616161',
        },
        contrastThreshold: 3,
        tonalOffset: 0.2,
        text: {
          primary: '#fff',
          secondary: 'rgba(255, 255, 255, 0.7)',
          disabled: 'rgba(255, 255, 255, 0.5)',
          icon: 'rgba(255, 255, 255, 0.5)',
        },
        divider: 'rgba(255, 255, 255, 0.12)',
        background: { paper: '#121212', default: '#344054', border: '#E4E7EC' },
        action: {
          active: '#fff',
          hover: 'rgba(255, 255, 255, 0.08)',
          hoverOpacity: 0.08,
          selected: 'rgba(255, 255, 255, 0.16)',
          selectedOpacity: 0.16,
          disabled: 'rgba(255, 255, 255, 0.3)',
          disabledBackground: 'rgba(255, 255, 255, 0.12)',
          disabledOpacity: 0.38,
          focus: 'rgba(255, 255, 255, 0.12)',
          focusOpacity: 0.12,
          activatedOpacity: 0.24,
        },
        pink: {
          50: '#FDF2FA',
          200: 'rgb(251 207 232)',
          500: 'rgb(236 72 153)',
          600: '#DD2590',
          700: '#C11574',
          main: 'rgb(236 72 153)',
          contrastText: '#fff',
        },
        gray: {
          25: '#FCFCFD',
          50: '#F9FAFB',
          100: '#F2F4F7',
          200: '#E4E7EC',
          300: '#D0D5DD',
          400: '#98A2B3',
          500: '#667085',
          600: '#475467',
          700: '#344054',
          800: '#182230',
          900: '#101828',
          950: '#0C111D',
          main: '#667085',
        },
        green: { main: '#00FFFF' },
        blue: {
          25: '#F5FAFF',
          50: '#EFF8FF',
          100: '#D1E9FF',
          200: '#B2DDFF',
          300: '#84CAFF',
          400: '#53B1FD',
          500: '#2E90FA',
          600: '#1570EF',
          700: '#175CD3',
          800: '#1849A9',
          900: '#194185',
          main: '#2E90FA',
        },
        blueLight: { 50: '#F0F9FF', 200: '#B9E6FE', 700: '#026aa2' },
        indigo: {
          25: '#F5F8FF',
          50: '#EEF4FF',
          100: '#E0EAFF',
          200: '#C7D7FE',
          300: '#A4BCFD',
          400: '#8098F9',
          500: '#6172F3',
          600: '#444CE7',
          700: '#3538CD',
          800: '#2D31A6',
          900: '#2D3282',
          950: '#15F235B',
          main: '#6172F3',
        },
        yellow: { 400: '#FAC515', 500: '#EAAA08', main: '#EAAA08' },
      },
    },
    light: {
      palette: {
        mode: 'light',
        primary: {
          50: '#FDF2FA',
          200: 'rgb(251 207 232)',
          500: 'rgb(236 72 153)',
          600: '#DD2590',
          700: '#C11574',
          main: 'rgb(236 72 153)',
          contrastText: '#fff',
          light: 'rgb(239, NaN, NaN)',
          dark: '#C11574',
        },
        pink: {
          50: '#FDF2FA',
          200: 'rgb(251 207 232)',
          500: 'rgb(236 72 153)',
          600: '#DD2590',
          700: '#C11574',
          main: 'rgb(236 72 153)',
          contrastText: '#fff',
        },
        secondary: {
          main: '#9E77ED',
          light: 'rgb(177, 146, 240)',
          dark: 'rgb(110, 83, 165)',
          contrastText: '#fff',
        },
        background: { default: '#FCFCFD', paper: '#FFF', border: '#E4E7EC' },
        gray: {
          25: '#FCFCFD',
          50: '#F9FAFB',
          100: '#F2F4F7',
          200: '#E4E7EC',
          300: '#D0D5DD',
          400: '#98A2B3',
          500: '#667085',
          600: '#475467',
          700: '#344054',
          800: '#182230',
          900: '#101828',
          950: '#0C111D',
          main: '#667085',
          light: 'rgb(132, 140, 157)',
          dark: 'rgb(71, 78, 93)',
          contrastText: '#fff',
        },
        green: { 400: '#3CCB7F', 500: '#16B364', main: '#16B364' },
        blue: {
          25: '#F5FAFF',
          50: '#EFF8FF',
          100: '#D1E9FF',
          200: '#B2DDFF',
          300: '#84CAFF',
          400: '#53B1FD',
          500: '#2E90FA',
          600: '#1570EF',
          700: '#175CD3',
          800: '#1849A9',
          900: '#194185',
          main: '#2E90FA',
        },
        blueLight: { 50: '#F0F9FF', 200: '#B9E6FE', 700: '#026aa2' },
        indigo: {
          25: '#F5F8FF',
          50: '#EEF4FF',
          100: '#E0EAFF',
          200: '#C7D7FE',
          300: '#A4BCFD',
          400: '#8098F9',
          500: '#6172F3',
          600: '#444CE7',
          700: '#3538CD',
          800: '#2D31A6',
          900: '#2D3282',
          950: '#15F235B',
          main: '#6172F3',
        },
        yellow: { 400: '#FAC515', 500: '#EAAA08', main: '#EAAA08' },
        success: {
          25: '#F6FEF9',
          50: '#ECFDF3',
          100: '#DCFAE6',
          200: '#ABEFC6',
          300: '#75E0A7',
          400: '#47CD89',
          500: '#17B26A',
          600: '#079455',
          700: '#067647',
          800: '#085D3A',
          900: '#074D31',
          950: '#053321',
          main: '#17B26A',
          light: '#75E0A7',
          dark: '#067647',
          contrastText: 'rgba(0, 0, 0, 0.87)',
        },
        warning: {
          25: '#FFFCF5',
          50: '#FFFAEB',
          100: '#FEF0C7',
          200: '#FEDF89',
          300: '#FEC84B',
          400: '#FDB022',
          500: '#F79009',
          600: '#DC6803',
          700: '#B54708',
          800: '#93370D',
          900: '#7A2E0E',
          950: '#4E1D09',
          main: '#F79009',
          light: '#FEC84B',
          dark: '#B54708',
          contrastText: 'rgba(0, 0, 0, 0.87)',
        },
        error: {
          25: '#FFFBFA',
          50: '#FEF3F2',
          100: '#FEE4E2',
          200: '#FECDCA',
          300: '#FDA29B',
          400: '#F97066',
          500: '#F04438',
          600: '#D92D20',
          700: '#B42318',
          800: '#912018',
          900: '#7A271A',
          950: '#55160C',
          main: '#F04438',
          light: '#FDA29B',
          dark: '#B42318',
          contrastText: '#fff',
        },
        common: { black: '#000', white: '#fff' },
        info: {
          main: '#0288d1',
          light: '#03a9f4',
          dark: '#01579b',
          contrastText: '#fff',
        },
        grey: {
          50: '#fafafa',
          100: '#f5f5f5',
          200: '#eeeeee',
          300: '#e0e0e0',
          400: '#bdbdbd',
          500: '#9e9e9e',
          600: '#757575',
          700: '#616161',
          800: '#424242',
          900: '#212121',
          A100: '#f5f5f5',
          A200: '#eeeeee',
          A400: '#bdbdbd',
          A700: '#616161',
        },
        contrastThreshold: 3,
        tonalOffset: 0.2,
        text: {
          primary: 'rgba(0, 0, 0, 0.87)',
          secondary: 'rgba(0, 0, 0, 0.6)',
          disabled: 'rgba(0, 0, 0, 0.38)',
        },
        divider: 'rgba(0, 0, 0, 0.12)',
        action: {
          active: 'rgba(0, 0, 0, 0.54)',
          hover: 'rgba(0, 0, 0, 0.04)',
          hoverOpacity: 0.04,
          selected: 'rgba(0, 0, 0, 0.08)',
          selectedOpacity: 0.08,
          disabled: 'rgba(0, 0, 0, 0.26)',
          disabledBackground: 'rgba(0, 0, 0, 0.12)',
          disabledOpacity: 0.38,
          focus: 'rgba(0, 0, 0, 0.12)',
          focusOpacity: 0.12,
          activatedOpacity: 0.12,
        },
      },
    },
  },
  typography: {
    fontFamily: 'Inter,Roboto,Helvetica,Arial,sans-serif',
    htmlFontSize: 16,
    fontSize: 14,
    fontWeightLight: 300,
    fontWeightRegular: 400,
    fontWeightMedium: 500,
    fontWeightBold: 700,
    h1: {
      fontFamily: 'Inter,Roboto,Helvetica,Arial,sans-serif',
      fontWeight: 300,
      fontSize: '6rem',
      lineHeight: 1.167,
    },
    h2: {
      fontFamily: 'Inter,Roboto,Helvetica,Arial,sans-serif',
      fontWeight: 300,
      fontSize: '3.75rem',
      lineHeight: 1.2,
    },
    h3: {
      fontFamily: 'Inter,Roboto,Helvetica,Arial,sans-serif',
      fontWeight: 400,
      fontSize: '3rem',
      lineHeight: 1.167,
    },
    h4: {
      fontFamily: 'Inter,Roboto,Helvetica,Arial,sans-serif',
      fontWeight: 400,
      fontSize: '2.125rem',
      lineHeight: 1.235,
    },
    h5: {
      fontFamily: 'Inter,Roboto,Helvetica,Arial,sans-serif',
      fontWeight: 400,
      fontSize: '1.5rem',
      lineHeight: 1.334,
    },
    h6: {
      fontFamily: 'Inter,Roboto,Helvetica,Arial,sans-serif',
      fontWeight: 500,
      fontSize: '1.25rem',
      lineHeight: 1.6,
    },
    subtitle1: {
      fontFamily: 'Inter,Roboto,Helvetica,Arial,sans-serif',
      fontWeight: 400,
      fontSize: '1rem',
      lineHeight: 1.75,
    },
    subtitle2: {
      fontFamily: 'Inter,Roboto,Helvetica,Arial,sans-serif',
      fontWeight: 500,
      fontSize: '0.875rem',
      lineHeight: 1.57,
    },
    body1: {
      fontFamily: 'Inter,Roboto,Helvetica,Arial,sans-serif',
      fontWeight: 400,
      fontSize: '1rem',
      lineHeight: 1.5,
    },
    body2: {
      fontFamily: 'Inter,Roboto,Helvetica,Arial,sans-serif',
      fontWeight: 400,
      fontSize: '0.875rem',
      lineHeight: 1.43,
    },
    button: {
      fontFamily: 'Inter,Roboto,Helvetica,Arial,sans-serif',
      fontWeight: 500,
      fontSize: '0.875rem',
      lineHeight: 1.75,
      textTransform: 'uppercase',
    },
    caption: {
      fontFamily: 'Inter,Roboto,Helvetica,Arial,sans-serif',
      fontWeight: 400,
      fontSize: '0.75rem',
      lineHeight: 1.66,
    },
    overline: {
      fontFamily: 'Inter,Roboto,Helvetica,Arial,sans-serif',
      fontWeight: 400,
      fontSize: '0.75rem',
      lineHeight: 2.66,
      textTransform: 'uppercase',
    },
    inherit: {
      fontFamily: 'inherit',
      fontWeight: 'inherit',
      fontSize: 'inherit',
      lineHeight: 'inherit',
      letterSpacing: 'inherit',
    },
  },
  unstable_sxConfig: {
    border: { themeKey: 'borders' },
    borderTop: { themeKey: 'borders' },
    borderRight: { themeKey: 'borders' },
    borderBottom: { themeKey: 'borders' },
    borderLeft: { themeKey: 'borders' },
    borderColor: { themeKey: 'palette' },
    borderTopColor: { themeKey: 'palette' },
    borderRightColor: { themeKey: 'palette' },
    borderBottomColor: { themeKey: 'palette' },
    borderLeftColor: { themeKey: 'palette' },
    outline: { themeKey: 'borders' },
    outlineColor: { themeKey: 'palette' },
    borderRadius: { themeKey: 'shape.borderRadius' },
    color: { themeKey: 'palette' },
    bgcolor: { themeKey: 'palette', cssProperty: 'backgroundColor' },
    backgroundColor: { themeKey: 'palette' },
    p: {},
    pt: {},
    pr: {},
    pb: {},
    pl: {},
    px: {},
    py: {},
    padding: {},
    paddingTop: {},
    paddingRight: {},
    paddingBottom: {},
    paddingLeft: {},
    paddingX: {},
    paddingY: {},
    paddingInline: {},
    paddingInlineStart: {},
    paddingInlineEnd: {},
    paddingBlock: {},
    paddingBlockStart: {},
    paddingBlockEnd: {},
    m: {},
    mt: {},
    mr: {},
    mb: {},
    ml: {},
    mx: {},
    my: {},
    margin: {},
    marginTop: {},
    marginRight: {},
    marginBottom: {},
    marginLeft: {},
    marginX: {},
    marginY: {},
    marginInline: {},
    marginInlineStart: {},
    marginInlineEnd: {},
    marginBlock: {},
    marginBlockStart: {},
    marginBlockEnd: {},
    displayPrint: { cssProperty: false },
    display: {},
    overflow: {},
    textOverflow: {},
    visibility: {},
    whiteSpace: {},
    flexBasis: {},
    flexDirection: {},
    flexWrap: {},
    justifyContent: {},
    alignItems: {},
    alignContent: {},
    order: {},
    flex: {},
    flexGrow: {},
    flexShrink: {},
    alignSelf: {},
    justifyItems: {},
    justifySelf: {},
    gap: {},
    rowGap: {},
    columnGap: {},
    gridColumn: {},
    gridRow: {},
    gridAutoFlow: {},
    gridAutoColumns: {},
    gridAutoRows: {},
    gridTemplateColumns: {},
    gridTemplateRows: {},
    gridTemplateAreas: {},
    gridArea: {},
    position: {},
    zIndex: { themeKey: 'zIndex' },
    top: {},
    right: {},
    bottom: {},
    left: {},
    boxShadow: { themeKey: 'shadows' },
    width: {},
    maxWidth: {},
    minWidth: {},
    height: {},
    maxHeight: {},
    minHeight: {},
    boxSizing: {},
    font: { themeKey: 'font' },
    fontFamily: { themeKey: 'typography' },
    fontSize: { themeKey: 'typography' },
    fontStyle: { themeKey: 'typography' },
    fontWeight: { themeKey: 'typography' },
    letterSpacing: {},
    textTransform: {},
    lineHeight: {},
    textAlign: {},
    typography: { cssProperty: false, themeKey: 'typography' },
  },
  mixins: {
    toolbar: {
      minHeight: 56,
      '@media (min-width:0px)': {
        '@media (orientation: landscape)': { minHeight: 48 },
      },
      '@media (min-width:600px)': { minHeight: 64 },
    },
  },
  shadows: [
    'none',
    '0px 2px 1px -1px rgba(0,0,0,0.2),0px 1px 1px 0px rgba(0,0,0,0.14),0px 1px 3px 0px rgba(0,0,0,0.12)',
    '0px 3px 1px -2px rgba(0,0,0,0.2),0px 2px 2px 0px rgba(0,0,0,0.14),0px 1px 5px 0px rgba(0,0,0,0.12)',
    '0px 3px 3px -2px rgba(0,0,0,0.2),0px 3px 4px 0px rgba(0,0,0,0.14),0px 1px 8px 0px rgba(0,0,0,0.12)',
    '0px 2px 4px -1px rgba(0,0,0,0.2),0px 4px 5px 0px rgba(0,0,0,0.14),0px 1px 10px 0px rgba(0,0,0,0.12)',
    '0px 3px 5px -1px rgba(0,0,0,0.2),0px 5px 8px 0px rgba(0,0,0,0.14),0px 1px 14px 0px rgba(0,0,0,0.12)',
    '0px 3px 5px -1px rgba(0,0,0,0.2),0px 6px 10px 0px rgba(0,0,0,0.14),0px 1px 18px 0px rgba(0,0,0,0.12)',
    '0px 4px 5px -2px rgba(0,0,0,0.2),0px 7px 10px 1px rgba(0,0,0,0.14),0px 2px 16px 1px rgba(0,0,0,0.12)',
    '0px 5px 5px -3px rgba(0,0,0,0.2),0px 8px 10px 1px rgba(0,0,0,0.14),0px 3px 14px 2px rgba(0,0,0,0.12)',
    '0px 5px 6px -3px rgba(0,0,0,0.2),0px 9px 12px 1px rgba(0,0,0,0.14),0px 3px 16px 2px rgba(0,0,0,0.12)',
    '0px 6px 6px -3px rgba(0,0,0,0.2),0px 10px 14px 1px rgba(0,0,0,0.14),0px 4px 18px 3px rgba(0,0,0,0.12)',
    '0px 6px 7px -4px rgba(0,0,0,0.2),0px 11px 15px 1px rgba(0,0,0,0.14),0px 4px 20px 3px rgba(0,0,0,0.12)',
    '0px 7px 8px -4px rgba(0,0,0,0.2),0px 12px 17px 2px rgba(0,0,0,0.14),0px 5px 22px 4px rgba(0,0,0,0.12)',
    '0px 7px 8px -4px rgba(0,0,0,0.2),0px 13px 19px 2px rgba(0,0,0,0.14),0px 5px 24px 4px rgba(0,0,0,0.12)',
    '0px 7px 9px -4px rgba(0,0,0,0.2),0px 14px 21px 2px rgba(0,0,0,0.14),0px 5px 26px 4px rgba(0,0,0,0.12)',
    '0px 8px 9px -5px rgba(0,0,0,0.2),0px 15px 22px 2px rgba(0,0,0,0.14),0px 6px 28px 5px rgba(0,0,0,0.12)',
    '0px 8px 10px -5px rgba(0,0,0,0.2),0px 16px 24px 2px rgba(0,0,0,0.14),0px 6px 30px 5px rgba(0,0,0,0.12)',
    '0px 8px 11px -5px rgba(0,0,0,0.2),0px 17px 26px 2px rgba(0,0,0,0.14),0px 6px 32px 5px rgba(0,0,0,0.12)',
    '0px 9px 11px -5px rgba(0,0,0,0.2),0px 18px 28px 2px rgba(0,0,0,0.14),0px 7px 34px 6px rgba(0,0,0,0.12)',
    '0px 9px 12px -6px rgba(0,0,0,0.2),0px 19px 29px 2px rgba(0,0,0,0.14),0px 7px 36px 6px rgba(0,0,0,0.12)',
    '0px 10px 13px -6px rgba(0,0,0,0.2),0px 20px 31px 3px rgba(0,0,0,0.14),0px 8px 38px 7px rgba(0,0,0,0.12)',
    '0px 10px 13px -6px rgba(0,0,0,0.2),0px 21px 33px 3px rgba(0,0,0,0.14),0px 8px 40px 7px rgba(0,0,0,0.12)',
    '0px 10px 14px -6px rgba(0,0,0,0.2),0px 22px 35px 3px rgba(0,0,0,0.14),0px 8px 42px 7px rgba(0,0,0,0.12)',
    '0px 11px 14px -7px rgba(0,0,0,0.2),0px 23px 36px 3px rgba(0,0,0,0.14),0px 9px 44px 8px rgba(0,0,0,0.12)',
    '0px 11px 15px -7px rgba(0,0,0,0.2),0px 24px 38px 3px rgba(0,0,0,0.14),0px 9px 46px 8px rgba(0,0,0,0.12)',
  ],
  transitions: {
    easing: {
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
      easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
    },
    duration: {
      shortest: 150,
      shorter: 200,
      short: 250,
      standard: 300,
      complex: 375,
      enteringScreen: 225,
      leavingScreen: 195,
    },
  },
  zIndex: {
    mobileStepper: 1000,
    fab: 1050,
    speedDial: 1050,
    appBar: 1100,
    drawer: 1200,
    modal: 1300,
    snackbar: 1400,
    tooltip: 1500,
  },
  defaultColorScheme: 'light',
};
