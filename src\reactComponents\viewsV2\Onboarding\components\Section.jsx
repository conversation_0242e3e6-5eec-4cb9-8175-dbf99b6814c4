import React from 'react';
import PropTypes from 'prop-types';

import { Grid, Text, Collapse, Paper, Box } from '@/reactComponents/library';

import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import PersonOutlineIcon from '@mui/icons-material/PersonOutline';

const styles = {
  paper: {
    display: 'flex',
    flexDirection: 'column',
    p: 2,
    border: '1px solid',
    borderColor: 'gray.300',
    maxWidth: '750px',
    width: '100%',
  },
  divider: {
    margin: '24px 0',
    height: 1,
    width: '100%',
    alignSelf: 'stretch',
    backgroundColor: 'blue',
  },
  icon: {
    display: 'flex',
    flexShrink: 0,
    width: '48px',
    height: '48px',
    borderRadius: '48px',
    backgroundColor: 'gray.100',
    justifyContent: 'center',
    alignItems: 'center',
    mr: 2,
  },
  expandIcon: {
    height: '32px',
    width: '32px',
    borderRadius: '32px',
    backgroundColor: 'gray.100',
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: 'gray.200',
    },
  },
  content: {
    display: 'flex',
    flexDirection: 'column',
    flexWrap: 'wrap',
    gap: 2,
  },
};

const Section = (props) => {
  const { title, children, noCollapse = false } = props;

  const [open, setOpen] = React.useState(true);

  const gridSize = { xs: 12 };

  return (
    <Paper sx={styles.paper}>
      <Grid size={gridSize}>
        <Grid
          container
          spacing={0}
          justifyContent="space-between"
          alignItems="center"
          flexWrap="nowrap"
        >
          <Grid>
            <Grid
              container
              alignItems="center"
              justifyContent="flex-start"
              flexWrap="nowrap"
            >
              <Grid sx={styles.icon}>
                <PersonOutlineIcon />
              </Grid>
              <Grid>
                <Text variant="baseSemi">{title}</Text>
              </Grid>
            </Grid>
          </Grid>
          {!noCollapse && (
            <Grid onClick={() => setOpen(!open)}>
              {open ? (
                <ExpandLessIcon sx={styles.expandIcon} />
              ) : (
                <ExpandMoreIcon sx={styles.expandIcon} />
              )}
            </Grid>
          )}
        </Grid>
      </Grid>
      <Collapse in={open}>
        <Grid sx={{ mt: 3 }} size={gridSize}>
          <Box sx={styles.content}>{children}</Box>
        </Grid>
      </Collapse>
    </Paper>
  );
};

Section.propTypes = {
  title: PropTypes.string.isRequired,
  children: PropTypes.node.isRequired,
  noCollapse: PropTypes.bool,
  doublePaddingFrame: PropTypes.bool,
};

export default Section;
