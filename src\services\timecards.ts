import type TimecardAudit from '@/types/TimecardAudit';
import axios, { type AxiosResponse } from 'axios';
import { coreBaseUrl } from '../utils/constants';

export interface CreateTimecardPayload {
  projectId: number;
  payPeriodId: number;
  timeZone: string;
  batchId?: number;
}

export const createTimecard = async (
  createTimecard: CreateTimecardPayload,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/timecards/`;
  const response = await axios.post(url, createTimecard, {
    withCredentials: true,
  });
  return response;
};

export const createTimecardRevision = async (
  timecardId: number,
  timeZone: string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/timecards/${timecardId}/revision/`;
  const response = await axios.post(
    url,
    { timeZone },
    { withCredentials: true },
  );
  return response;
};

export const getTimecard = async (
  timecardId: number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/timecards/${timecardId}/`;
  const response = await axios.get(url, {
    withCredentials: true,
  });
  return response;
};

export const updateTimecard = async (
  timecardId: number | string,
  payload: any,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/timecards/${timecardId}/`;
  const response = await axios.patch(url, payload, {
    withCredentials: true,
  });
  return response;
};

export const deleteTimecard = async (
  timecardId: number | string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/timecards/${timecardId}/`;
  const response = await axios.delete(url, {
    withCredentials: true,
  });
  return response;
};

export const updateOrCreateMileageForm = async (
  timecardId: number | string,
  mileageForm: any,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/timecards/${timecardId}/mileage-form/`;
  const response = await axios.patch(url, mileageForm, {
    withCredentials: true,
  });
  return response;
};

export const deleteMileageForm = async (
  timecardId: number | string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/timecards/${timecardId}/mileage-form/`;
  const response = await axios.delete(url, {
    withCredentials: true,
  });
  return response;
};

export const getMileageForm = async (
  timecardId: number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/timecards/${timecardId}/mileage-form/`;
  const response = await axios.get(url, {
    withCredentials: true,
  });
  return response;
};

export const getMileageFormPdf = (timecardId: string | number): string => {
  const url = `${coreBaseUrl()}/timecards/${timecardId}/mileage-form/pdf`;
  return url;
};

export const updateOrCreateKitRental = async (
  timecardId: number,
  kitRental: any,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/timecards/${timecardId}/kit-rental/`;
  const response = await axios.patch(url, kitRental, {
    withCredentials: true,
  });
  return response;
};

export const getKitRental = async (
  timecardId: number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/timecards/${timecardId}/kit-rental/`;
  const response = await axios.get(url, {
    withCredentials: true,
  });
  return response;
};

export const getKitRentalPdf = (timecardId: string | number): string => {
  const url = `${coreBaseUrl()}/timecards/${timecardId}/kit-rental/pdf`;
  return url;
};

export const submitTimecard = async (
  timecardId: number | string,
  signature: string,
  timeZone: string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/timecards/${timecardId}/submit/`;
  const response = await axios.patch(
    url,
    {
      signature,
      timeZone,
    },
    { withCredentials: true },
  );
  return response;
};

export const downloadTimecard = (timecardId: string | number): string => {
  const url = `${coreBaseUrl()}/timecards/${timecardId}/pdf`;
  return url;
};

export const approveTimecard = async (
  timecardId: number,
  signature: string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/timecards/${timecardId}/approve/`;
  const response = await axios.patch(
    url,
    { signature },
    { withCredentials: true },
  );
  return response;
};

export const requestChanges = async (
  timecardId: number,
  requestedChanges: string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/timecards/${timecardId}/changes-requested/`;
  const response = await axios.patch(
    url,
    { requestedChanges },
    { withCredentials: true },
  );
  return response;
};

export const updateBatchOnTimecard = async (
  timecardId: number | string,
  batchId: number | null,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/timecards/${timecardId}/batches/${batchId}`;
  const response = await axios.patch(url, {}, { withCredentials: true });
  return response;
};

export const removeBatchFromTimecard = async (
  timecardId: number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/timecards/${timecardId}/batches/`;
  const response = await axios.delete(url, { withCredentials: true });
  return response;
};

export const listTimecardAudit = async (
  timecardId: number,
): Promise<AxiosResponse<TimecardAudit[]>> => {
  const url = `${coreBaseUrl()}/timecards/${timecardId}/audit/`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const changeTimecardPayPeriod = async (
  timecardId: number,
  payPeriodId: number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/timecards/${timecardId}/pay-period/${payPeriodId}`;
  const response = await axios.patch(url, {}, { withCredentials: true });
  return response;
};

export const getTimecardHtgReport = async (
  timecardId: number | string,
  batchId: string | null,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/timecards/${timecardId}/batches/${batchId}/timecard-htg-report`;
  const response = await axios.patch(url, {}, { withCredentials: true });
  return response;
};

export const getHoursToGrossDetails = async (
  timecardId: number | string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/timecards/${timecardId}/hours-to-gross-details`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const calculateGross = async (
  timecardId: number | string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/timecards/${timecardId}/calculate-gross`;
  const response = await axios.post(url, { withCredentials: true });
  return response;
};
