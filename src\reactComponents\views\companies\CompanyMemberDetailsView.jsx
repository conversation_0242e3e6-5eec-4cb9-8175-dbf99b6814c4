import { applyPureVueInReact } from 'veaury';
import CompanyMemberDetailsViewVue from '../../../views/companies/CompanyMemberDetailsView.vue';
import { useAuth, useReactRouter } from '../../AppHooks';
import { useOutletContext } from 'react-router';
const ReactCompanyMemberDetailsView = applyPureVueInReact(
  CompanyMemberDetailsViewVue,
);

const CompanyMemberDetailsView = () => {
  useAuth();
  const { route, navigate } = useReactRouter();
  const context = useOutletContext();
  return (
    <ReactCompanyMemberDetailsView
      company={context.company}
      route={route}
      navigate={navigate}
    />
  );
};

export default CompanyMemberDetailsView;
