import axios, { AxiosError } from 'axios';
import { coreBaseUrl } from '../utils/constants';

const axiosError = (error: AxiosError): string => {
  const data: any = error.response?.data || {};
  if (!data.errors) return error.message;

  const err = ((data.errors || [])[0] || {}).message;
  return err || error.message;
};

const axiosRequest = async (
  method: string,
  path: string,
  data?: any,
  errorHandler?: (error: AxiosError) => Error,
): Promise<any> => {
  try {
    return await axios({
      method,
      url: `${coreBaseUrl()}/${path.replace(/^\/+/, '')}`,
      data,
      withCredentials: true,
    });
  } catch (error) {
    if (error instanceof AxiosError) {
      if (errorHandler) {
        throw errorHandler(error);
      } else {
        throw new Error(axiosError(error));
      }
    }

    throw error;
  }
};

export class UserNotFound extends Error {
  data: any;
  constructor(message: string, data: any) {
    super(message);
    this.name = 'UserNotFound';
    this.data = data;
  }
}

export const getUserInfo = async (phone: string): Promise<any> =>
  await axiosRequest(
    'post',
    '/auth/user',
    { phone },
    (e: AxiosError): Error => {
      const data: any = e.response?.data || {};
      if (e.response?.status === 422 && data.status === 'user_does_not_exist') {
        throw new UserNotFound('User does not exist', data);
      }
      throw e;
    },
  );

export const registerUser = async (data: Object): Promise<any> =>
  await axiosRequest('post', '/auth/register', data);
