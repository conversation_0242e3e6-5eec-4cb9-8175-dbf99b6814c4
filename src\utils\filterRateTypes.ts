export const rateTypeFilter = (
  contracts: any,
  rateType: any[],
  view?: string,
): any => {
  if (!rateType || !contracts) return rateType;
  const isNonUnion = contracts.isNonUnion;
  const isExemptIndicator = contracts.isExemptIndicator;

  //Union OnCall
  if (!isNonUnion && contracts.isOnCallIndicator) {
    return unionOnCall(contracts, rateType);
  }

  //Union Hourly Allowed
  if (
    !isNonUnion &&
    !contracts.isDisAllowPayHourly &&
    contracts.isDisAllowPayWeekly
  ) {
    return unionHourlyAllowed(contracts, rateType, view);
  }

  // Union Hourly not Allowed
  if (
    !isNonUnion &&
    contracts.isDisAllowPayHourly &&
    contracts.isDisAllowPayWeekly
  ) {
    return unionHourlyNotAllowed(contracts, rateType, view);
  }
  // Non Union Exempt
  if (isNonUnion && isExemptIndicator) {
    return getRateTypesForNonUnionExempt(contracts, rateType, view);
  }
  // Non Union Non Exempt
  if (isNonUnion && !isExemptIndicator) {
    return getRateTypesForNonUnionNonExempt(contracts, rateType, view);
  }

  return rateType;
};

const unionOnCall = (contracts: any, rateType: any) => {
  if (
    !contracts.isDisAllowPayDaily &&
    contracts.isDisAllowPayHourly &&
    contracts.isDisAllowPayWeekly &&
    contracts.isOnCallIndicator &&
    contracts.minimumCallHours === 12
  ) {
    return (
      rateType.filter((rate: { key: string }) => rate.key === 'daily') ||
      rateType
    );
  }
  return rateType;
};

const unionHourlyAllowed = (contracts: any, rateType: any, view?: string) => {
  if (
    !contracts.isDisAllowPayDaily &&
    !contracts.isDisAllowPayHourly &&
    contracts.isDisAllowPayWeekly &&
    !contracts.isOnCallIndicator &&
    contracts.minimumCallHours >= 8
  ) {
    if (contracts.minimumCallHours >= 12) {
      return (
        rateType.filter((rate: { key: string }) =>
          view === 'supervisorView'
            ? rate.key === 'hourly'
            : rate.key === 'hourly' || rate.key === 'for_12_hours',
        ) || rateType
      );
    }
    if (contracts.minimumCallHours >= 10) {
      return (
        rateType.filter((rate: { key: string }) =>
          view === 'supervisorView'
            ? rate.key === 'hourly'
            : rate.key === 'hourly' ||
              rate.key === 'for_10_hours' ||
              rate.key === 'for_12_hours',
        ) || rateType
      );
    }
    if (contracts.minimumCallHours >= 8) {
      return (
        rateType.filter((rate: { key: string }) =>
          view === 'supervisorView'
            ? rate.key === 'hourly'
            : rate.key === 'hourly' ||
              rate.key === 'for_8_hours' ||
              rate.key === 'for_10_hours' ||
              rate.key === 'for_12_hours',
        ) || rateType
      );
    }
  }
  return rateType;
};

const unionHourlyNotAllowed = (
  contracts: any,
  rateType: any,
  view?: string,
) => {
  if (
    !contracts.isDisAllowPayDaily &&
    contracts.isDisAllowPayHourly &&
    contracts.isDisAllowPayWeekly &&
    !contracts.isOnCallIndicator &&
    contracts.minimumCallHours >= 8
  ) {
    if (contracts.minimumCallHours >= 12) {
      return (
        rateType.filter((rate: { key: string }) =>
          view === 'supervisorView'
            ? rate.key === 'daily'
            : rate.key === 'for_12_hours',
        ) || rateType
      );
    }
    if (contracts.minimumCallHours >= 10) {
      return (
        rateType.filter((rate: { key: string }) =>
          view === 'supervisorView'
            ? rate.key === 'daily'
            : rate.key === 'for_10_hours' || rate.key === 'for_12_hours',
        ) || rateType
      );
    }
    if (contracts.minimumCallHours >= 8) {
      return (
        rateType.filter((rate: { key: string }) =>
          view === 'supervisorView'
            ? rate.key === 'daily'
            : rate.key === 'for_8_hours' ||
              rate.key === 'for_10_hours' ||
              rate.key === 'for_12_hours',
        ) || rateType
      );
    }
  }
  return rateType;
};
const getRateTypesForNonUnionExempt = (
  contracts: any,
  rateTypes: any[],
  view?: string,
): any => {
  if (
    !contracts.isDisAllowPayDaily &&
    contracts.isDisAllowPayHourly &&
    !contracts.isDisAllowPayWeekly &&
    !contracts.isOnCallIndicator &&
    !contracts.minimumCallHours
  ) {
    const filteredRateTypes = rateTypes.filter((rate) => rate.key === 'daily');
    return filteredRateTypes;
  }
  return rateTypes;
};
const getRateTypesForNonUnionNonExempt = (
  contracts: any,
  rateTypes: any[],
  view?: string,
): any => {
  if (
    contracts.isDisAllowPayDaily &&
    !contracts.isDisAllowPayHourly &&
    contracts.isDisAllowPayWeekly &&
    !contracts.isOnCallIndicator &&
    !contracts.minimumCallHours
  ) {
    const rateKeys =
      view === 'supervisorView'
        ? ['hourly']
        : ['hourly', 'for_8_hours', 'for_10_hours', 'for_12_hours'];
    return rateTypes.filter((rate) => rateKeys.includes(rate.key)) || [];
  }
  return rateTypes;
};

type RateTypeKey =
  | 'for_8_hours'
  | 'for_10_hours'
  | 'for_12_hours'
  | 'hourly'
  | 'daily';
const guaranteedHoursMap: Record<RateTypeKey, number> = {
  for_8_hours: 8,
  for_10_hours: 10,
  for_12_hours: 12,
  hourly: 0,
  daily: 0,
};
export const guaranteedHoursFilter = (
  contracts: any,
  allRateType: any,
  projectMembersRateType?: any,
) => {
  //uinon oncall
  if (
    !contracts.isDisAllowPayDaily &&
    contracts.isDisAllowPayHourly &&
    contracts.isDisAllowPayWeekly &&
    contracts.isOnCallIndicator &&
    contracts.minimumCallHours === 12 &&
    projectMembersRateType.key === 'daily'
  ) {
    return contracts.minimumCallHours;
  }

  //Union Hourly Allowed
  if (
    !contracts.isDisAllowPayDaily &&
    !contracts.isDisAllowPayHourly &&
    contracts.isDisAllowPayWeekly &&
    !contracts.isOnCallIndicator &&
    contracts.minimumCallHours >= 8
  ) {
    return projectMembersRateType.key === 'hourly'
      ? contracts.minimumCallHours
      : projectMembersRateType.key === 'for_8_hours' ||
        projectMembersRateType.key === 'for_10_hours' ||
        projectMembersRateType.key === 'for_12_hours'
      ? guaranteedHoursMap[projectMembersRateType.key as RateTypeKey]
      : 0;
  }
  //Union Hourly not Allowed
  if (
    !contracts.isDisAllowPayDaily &&
    contracts.isDisAllowPayHourly &&
    contracts.isDisAllowPayWeekly &&
    !contracts.isOnCallIndicator &&
    contracts.minimumCallHours >= 8 &&
    (projectMembersRateType.key === 'for_8_hours' ||
      projectMembersRateType.key === 'for_10_hours' ||
      projectMembersRateType.key === 'for_12_hours')
  ) {
    return guaranteedHoursMap[projectMembersRateType.key as RateTypeKey] || 0;
  }
  //Non union Exempt
  if (
    !contracts.isDisAllowPayDaily &&
    contracts.isDisAllowPayHourly &&
    !contracts.isDisAllowPayWeekly &&
    !contracts.isOnCallIndicator &&
    !contracts.minimumCallHours //&&
    //projectMembersRateType.key === 'daily'
  ) {
    return 1;
  }
  //Non union Non Exempt
  if (
    contracts.isDisAllowPayDaily &&
    !contracts.isDisAllowPayHourly &&
    contracts.isDisAllowPayWeekly &&
    !contracts.isOnCallIndicator &&
    !contracts.minimumCallHours
  ) {
    return contracts.isDisAllowPayHourlyGuarantee
      ? 0
      : guaranteedHoursMap[projectMembersRateType.key as RateTypeKey] || 0;
  }

  if (projectMembersRateType.key === 'for_10_hours') {
    return 10;
  } else if (projectMembersRateType.key === 'for_12_hours') {
    return 12;
  } else if (projectMembersRateType.key === 'for_8_hours') {
    return 8;
  } else if (projectMembersRateType.key === 'daily') {
    return contracts.isOnCallIndicator ? 12 : 1;
  }
  return 0;
};

export const supervisorRateTypeFilter = (
  contracts: any,
  rateTypesList: any,
  memberRateType: any,
) => {
  if (!contracts || !rateTypesList || !memberRateType)
    return rateTypesList.filter(
      (rate: any) => rate.key === 'daily' || rate.key === 'hourly',
    );
  //On call
  if (
    !contracts.isDisAllowPayDaily &&
    contracts.isDisAllowPayHourly &&
    contracts.isDisAllowPayWeekly &&
    contracts.isOnCallIndicator &&
    contracts.minimumCallHours === 12
  ) {
    if (memberRateType.key === 'daily') {
      const filteredRateTypes = rateTypesList.filter(
        (rate: any) => rate.key === 'daily',
      );
      return filteredRateTypes;
    }
  }

  //Hourly Allowed
  if (
    !contracts.isDisAllowPayDaily &&
    !contracts.isDisAllowPayHourly &&
    contracts.isDisAllowPayWeekly &&
    !contracts.isOnCallIndicator &&
    contracts.minimumCallHours >= 8
  ) {
    if (
      memberRateType.key === 'for_8_hours' ||
      memberRateType.key === 'for_10_hours' ||
      memberRateType.key === 'for_12_hours'
    ) {
      const filteredRateTypes = rateTypesList.filter(
        (rate: any) => rate.key === 'daily',
      );
      return filteredRateTypes;
    } else if (memberRateType.key === 'hourly') {
      const filteredRateTypes = rateTypesList.filter(
        (rate: any) => rate.key === 'hourly',
      );
      return filteredRateTypes;
    }
  }
  //Hourly Not Allowed
  if (
    !contracts.isDisAllowPayDaily &&
    contracts.isDisAllowPayHourly &&
    contracts.isDisAllowPayWeekly &&
    !contracts.isOnCallIndicator &&
    contracts.minimumCallHours >= 8
  ) {
    if (
      memberRateType.key === 'for_8_hours' ||
      memberRateType.key === 'for_10_hours' ||
      memberRateType.key === 'for_12_hours'
    ) {
      return rateTypesList.filter((rate: any) => rate.key === 'daily');
    }
  }

  //Non union Exempt
  if (
    !contracts.isDisAllowPayDaily &&
    contracts.isDisAllowPayHourly &&
    !contracts.isDisAllowPayWeekly &&
    !contracts.isOnCallIndicator &&
    !contracts.minimumCallHours
  ) {
    //if (memberRateType.key === 'daily') {
    return rateTypesList.filter((rate: any) => rate.key === 'daily');
    //}
  }

  //Non union Non Exempt
  if (
    contracts.isDisAllowPayDaily &&
    !contracts.isDisAllowPayHourly &&
    contracts.isDisAllowPayWeekly &&
    !contracts.isOnCallIndicator &&
    !contracts.minimumCallHours
  ) {
    // if (
    //   memberRateType.key === 'hourly' ||
    //   memberRateType.key === 'for_8_hours' ||
    //   memberRateType.key === 'for_10_hours' ||
    //   memberRateType.key === 'for_12_hours'
    // ) {
    return rateTypesList.filter((rate: any) => rate.key === 'hourly');
    //}
  }

  const filteredRateTypes = rateTypesList.filter(
    (rate: any) => rate.key === 'daily' || rate.key === 'hourly',
  );
  return filteredRateTypes;
};
