<template>
  <div>
    <label for="email-address" class="sr-only">{{ label || type }}</label>
    <label
      v-if="label"
      class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1"
    >
      {{ label }} <span v-if="required" class="text-red-600">*</span>
    </label>
    <input
      :value="computedValue"
      :name="type"
      :placeholder="placeholder"
      :type="inputType"
      :inputmode="inputmode"
      :pattern="pattern"
      :maxlength="maxlength"
      :autocomplete="autoComplete"
      :required="required"
      :disabled="disabled"
      :autofocus="autofocus"
      v-mask="mask"
      v-focus="autofocus"
      :min="min"
      :max="max"
      class="appearance-none bg-white border border-gray-300 dark:border-gray-500 rounded-md shadow-sm text-gray-800 dark:text-gray-200 dark:bg-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-xs block w-full px-3 py-2"
      :class="{ 'bg-gray-100 dark:bg-gray-600 cursor-not-allowed': disabled }"
      @input="update"
      @click.prevent="$emit('click')"
      @keydown="keyDown"
      :data-testid="`${label}-input`"
    />
    <p v-if="errors" class="mt-2 pb-1 text-sm text-red-600" id="email-error">
      Your password must be less than 4 characters.
    </p>
    <p v-else class="pb-4"></p>
  </div>
</template>

<script lang="ts">
import { DateTime } from 'luxon';
import type { DirectiveBinding, VNode } from 'vue';
import { defineComponent } from 'vue';
import { mask } from 'vue-the-mask';

export default defineComponent({
  directives: {
    // this conditionally applies the mask only if the mask value is truthy
    mask: (
      el: HTMLElement,
      binding: DirectiveBinding,
      vnode: VNode,
      oldVnode: VNode,
    ) => {
      if (!binding.value) return;
      mask(el, binding as any, vnode as any, oldVnode as any);
    },
    focus(el: HTMLElement, binding: DirectiveBinding) {
      if (binding.value) {
        el.focus();
      }
    },
  },
  emits: ['update:modelValue', 'update:rawValue', 'click'],
  props: [
    'modelValue',
    'label',
    'type',
    'errors',
    'disabled',
    'inputmode',
    'pattern',
    'autoComplete',
    'maxlength',
    'min',
    'max',
    'required',
    'autofocus',
    'placeholder',
  ],
  data() {
    return {
      masks: {
        phone: ['###-###-####', '+###########'],
        ssn: '#########',
        ein: '##-#######',
        otc: '######',
        postal: ['#####', '#####-####'],
      } as Record<string, string | string[]>,
    };
  },
  name: 'TextInput',
  computed: {
    mask(): string | string[] {
      return this.masks[this.type] || '';
    },
    computedValue(): any {
      if (this.type === 'date' && this.modelValue) {
        return this.modelValue.toFormat('yyyy-MM-dd');
      }
      return this.modelValue;
    },
    isSafari(): boolean {
      return (
        window.navigator.userAgent.includes('Safari') &&
        window.navigator.userAgent.includes('Chrome') === false &&
        window?.navigator?.vendor.includes('Apple')
      );
    },
    inputType(): string | undefined {
      const isSafari = this.isSafari;
      if (isSafari) {
        if (this.type === 'number') {
          return 'text';
        } else {
          return this.type;
        }
      } else {
        return this.type;
      }
    },
  },
  watch: {
    modelValue(value: string) {
      if (this.type === 'phone') {
        this.setPhoneMask(value);
      }
    },
  },
  methods: {
    setPhoneMask(value: string) {
      if (value.startsWith('+')) {
        this.masks.phone = '+############';
      } else {
        this.masks.phone = '###-###-####';
      }
    },
    update(event: any): void {
      let value = event.target!.value!;
      if (this.type === 'number') {
        value = this.parseNumber(value);
      }
      if (this.type === 'date') {
        value = DateTime.fromFormat(value, 'yyyy-MM-dd');
      }
      if (this.type === 'name') {
        value = value.trim();
      }
      this.$emit('update:modelValue', value);
      this.$emit('update:rawValue', this.getRawValue(value));
    },
    getRawValue(value: string): string {
      const rawFunctionMap: Record<string, (v: string) => string> = {
        phone: (v: string) => v.replace(/-/g, ''),
        ssn: (v: string) => v.replace(/-/g, ''),
        ein: (v: string) => v.replace(/-/g, ''),
        otc: (v: string) => v,
      };
      const rawFunction = rawFunctionMap[this.type];
      return rawFunction ? rawFunction(value) : value;
    },
    parseNumber(str: string): number | null {
      if (!str) {
        return null; // or throw an error
      }
      if (this.isSafari) {
        str = str.replace(/[^0-9.]/g, '');
      }
      const num = parseFloat(str);

      if (isNaN(num)) {
        return null; // or throw an error
      }

      if (str.indexOf('.') !== -1) {
        return num; // return as decimal
      }

      return parseInt(str, 10); // return as integer
    },
    keyDown(event: any) {
      if (this.type === 'name') {
        const regex = /^[a-zA-Z._'\-\s]+$/;
        const key = event.key;
        if (!regex.test(key) && !this.isControlKey(event)) {
          event.preventDefault();
        }
      }
    },
    isControlKey(event: any) {
      const controlKeys = [
        'Backspace',
        'ArrowLeft',
        'ArrowRight',
        'Tab',
        'Delete',
      ];
      return controlKeys.includes(event.key);
    },
  },
});
</script>
