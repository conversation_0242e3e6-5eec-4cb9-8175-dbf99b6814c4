import React from 'react';
import { XMarkIcon } from '@heroicons/react/20/solid';
import { LinkIcon } from '@heroicons/react/24/solid';
// import { DateTime } from 'luxon';
// import { exportProjectToCSV } from '../services/export';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import { Icon } from '@/reactComponents/library';
import { Modal, Menu } from '@/reactComponents/library';
import { ProjectHeaderContext } from './ProjectHeader';
import PropTypes from 'prop-types';

const ProjectHeaderAdminActions = (props) => {
  const { project } = props;
  const ProjectContext = React.useContext(ProjectHeaderContext);
  const { route, navigate } = ProjectContext;
  const [settingOpen, setSettingOpen] = React.useState(null);
  const [projectLinks, openProjectLinks] = React.useState(false);
  const settings = React.useMemo(
    () => [
      {
        label: 'Edit project',
        action: () => {
          if (navigate) {
            navigate({
              pathname: `/projects/${project.hashId}/edit`,
            });
          }
        },
        active: route?.location?.pathname?.includes('project'),
      },
      {
        label: 'Day defaults',
        action: () => {
          if (navigate) {
            navigate({
              pathname: `/projects/${project.hashId}/admin/defaultDays`,
            });
          }
        },
        active: route?.location?.pathname?.includes('admin'),
      },
      {
        label: 'Custom paperwork',
        action: () => {
          if (navigate) {
            navigate({
              pathname: `/projects/${project.hashId}/admin/document-templates`,
            });
          }
        },
        active: route?.location?.pathname?.includes('templates'),
      },
    ],

    [navigate, project, route],
  );

  function copyProjectLinkToClipboard(departmentId, departmentTypeName) {
    let url = `${window.location.origin}/v2/projects/${project.hashId}?onboard=true`;
    if (departmentId) {
      url += `&departmentId=${departmentId}`;
    }
    navigator.clipboard.writeText(url);
    SnackbarStore.triggerSnackbar(
      `${departmentTypeName} project link copied to clipboard`,
      2500,
      'success',
    );
  }

  return (
    <div className="flex flex-row items-center justify-between">
      <div className="hidden sm:flex w-full items-center justify-end">
        <button
          type="button"
          onClick={() => openProjectLinks(true)}
          className="button-link bg-white pr-3 pl-3 pt-2 pb-3"
        >
          <Icon name="link" className="w-5 h-4" />
        </button>
        <Modal
          open={projectLinks}
          setOpen={openProjectLinks}
          disableTitle
          useClassic
        >
          <div className="flex justify-between items-center mb-2">
            <h1 className="text-lg font-semibold">Project Links</h1>
            <XMarkIcon
              className="w-6 h-6 text-gray-400 cursor-pointer hover:text-gray-500"
              onClick={() => openProjectLinks(false)}
            />
          </div>
          {project?.departments?.map((department) => {
            return (
              <div
                key={department.id}
                className="flex justify-between items-center mb-2"
              >
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {department.type.name}
                </div>
                <div
                  onClick={() =>
                    copyProjectLinkToClipboard(
                      department.id,
                      department.type.name,
                    )
                  }
                  className="flex p-2 cursor-pointer text-sm font-semibold text-indigo-600 rounded-md border-indigo-400 hover:text-indigo-700 dark:text-indigo-400 dark:hover:text-indigo-300"
                >
                  <LinkIcon className="w-4 h-4 mt-0.5 mr-1" />
                </div>
              </div>
            );
          })}
        </Modal>
      </div>
      <div className="flex justify-end xl:mt-0 xl:ml-4">
        <div className="hidden sm:flex justify-end">
          <button
            className="button-link bg-white flex items-center pr-4 pl-4 pt-2 pb-2"
            onClick={(e) => {
              setSettingOpen(e.currentTarget);
            }}
            data-testid="project-setting-btn"
          >
            <Icon name="settings" className="w-5 h-5 mr-2" />
            <div className="flex justify-between text-sm font-semibold text-gray-900">
              Settings
            </div>
          </button>
          <Menu
            anchorEl={settingOpen}
            open={Boolean(settingOpen)}
            onClose={() => setSettingOpen(null)}
          >
            {settings.map((setting, index) => {
              return (
                <div key={`itemGroup-${setting.label}`}>
                  <div className="px-1 py-1">
                    <button
                      className="group flex w-full items-center rounded-md px-2 py-2 text-sm text-gray-900 dark:text-gray-200 active:bg-gray-100 active:dark:bg-gray-600 hover:bg-gray-100 hover:dark:bg-gray-600"
                      onClick={() => {
                        setting.action();
                        setSettingOpen(null);
                      }}
                      data-testid={`${setting.label}-btn`}
                    >
                      <span
                        className="mr-2 text-blue-400"
                        aria-hidden="true"
                      ></span>
                      {setting.label}
                    </button>
                  </div>
                </div>
              );
            })}
          </Menu>
        </div>

        {/* // <DropdownMenu class="sm:hidden" :item-groups="moreItems">
      //   <template #buttonContent>
      //     <MenuButton
      //       class="inline-flex items-center gap-x-1.5 rounded-md bg-white dark:bg-gray-800 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-200 shadow-sm ring-1 ring-inset ring-gray-300 hover:ring-gray-400"
      //     >
      //       More
      //       <ChevronDownIcon
      //         class="-mr-1 h-5 w-5 text-gray-400"
      //         aria-hidden="true"
      //       />
      //     </MenuButton>
      //   </template>
      // </DropdownMenu> */}
      </div>
    </div>
  );
};

ProjectHeaderAdminActions.propTypes = {
  project: PropTypes.object.isRequired,
};

export default ProjectHeaderAdminActions;
