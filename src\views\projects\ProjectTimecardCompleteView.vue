<template>
  <div class="flex justify-center mx-auto w-full py-2 px-5">
    <div class="max-w-7xl">
      <h2 class="text-xl font-bold mb-3">Complete</h2>
      <p class="pb-2">You completed the timecard!</p>
      <p>If you want a copy of the pdf, you can click download below.</p>
      <p class="text-sm pt-2">
        (<span class="font-semibold">Note:</span> The document is stored on our
        platform so you don't have to download it.)
      </p>
      <div class="flex justify-center mt-5 pb-5">
        <Button
          v-if="timecard.fileId"
          color="gray"
          size="sm"
          @click="downloadTimecard"
        >
          <div class="flex justify items-center space-x-1">
            <ArrowDownTrayIcon class="h-4 w-4" />
            <div>Download</div>
          </div>
        </Button>
      </div>
      <p>
        Otherwise, click the exit button below to return to the project home!
      </p>
    </div>
  </div>
</template>

<script lang="ts">
import Button from '@/components/library/Button.vue';
import { downloadTimecard } from '@/services/timecards';
import type Project from '@/types/Project';
import type Timecard from '@/types/Timecard';
import { ArrowDownTrayIcon } from '@heroicons/vue/20/solid';
import { defineComponent, type PropType } from 'vue';

export default defineComponent({
  props: {
    project: {
      type: Object as PropType<Project>,
      required: true,
    },
    timecard: {
      type: Object as PropType<Timecard>,
      required: true,
    },
    navigate: {
      type: Function,
      required: true,
    },
  },
  components: {
    Button,
    ArrowDownTrayIcon,
  },
  data() {
    return {};
  },
  methods: {
    async submit() {
      this.navigate({
        pathname: `/projects/${this.project.hashId}/timecards`,
      });
    },
    downloadTimecard() {
      if (!this.timecard.fileId) return;
      const link = document.createElement('a');
      link.href = downloadTimecard(this.timecard.id);

      link.setAttribute(
        'download',
        `${this.timecard.payPeriod?.startsAt?.toFormat('MM_dd')}_timecard.pdf`,
      );
      document.body.appendChild(link);
      link.click();
    },
  },
});
</script>

<style></style>
