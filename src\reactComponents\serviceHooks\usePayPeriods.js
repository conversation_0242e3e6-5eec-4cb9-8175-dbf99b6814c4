import { useState, useEffect } from 'react';
import { DateTime } from 'luxon';
import { getProjectPayPeriods } from '@/services/project';

const usePayPeriods = (projectId) => {
  const [payPeriods, setPayPeriods] = useState([]);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchPayPeriods = async () => {
      try {
        setLoading(true);
        const { data } = await getProjectPayPeriods(projectId);
        const formattedPayPeriods = data.map((pp) => {
          const startsAt = DateTime.fromISO(pp.startsAt);
          const endsAt = DateTime.fromISO(pp.endsAt);
          return {
            id: pp.id,
            name: `${startsAt.toFormat('ccc MM/dd')} - ${endsAt.toFormat(
              'ccc MM/dd',
            )}`,
            value: pp.id,
            startsAt,
            endsAt,
          };
        });
        setPayPeriods(formattedPayPeriods);
      } catch (err) {
        setError('Failed to fetch pay periods. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (projectId) {
      fetchPayPeriods();
    }
  }, [projectId]);

  const getPayPeriodsDropdownItems = () => {
    return payPeriods.map((pp) => ({
      value: pp.id,
      label: pp.name,
    }));
  };

  return { payPeriods, error, loading, getPayPeriodsDropdownItems };
};

export default usePayPeriods;
