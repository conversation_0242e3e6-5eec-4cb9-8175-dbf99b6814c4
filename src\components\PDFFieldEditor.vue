<template>
  <div>
    <span class="font-bold">{{ fieldName }}</span> <br />
    Type: {{ value.type }} <br />
    <TextInput
      label="Width"
      type="number"
      :model-value="value.width"
      @update:modelValue="handleInput('width', $event)"
    />
    <TextInput
      label="Height"
      type="number"
      :model-value="value.height"
      @update:modelValue="handleInput('height', $event)"
    />
    <TextInput
      label="X"
      type="number"
      :model-value="value?.position?.x"
      @update:modelValue="handleInput('position.x', $event)"
    />
    <TextInput
      label="Y"
      type="number"
      :model-value="value?.position?.y"
      @update:modelValue="handleInput('position.y', $event)"
    />
    <template v-if="isTextField">
      <!-- Font Name: {{ value.fontName }} <br /> Roboto is only option for now -->
      <TextInput
        label="Font Size"
        type="number"
        :model-value="value.fontSize"
        @update:modelValue="handleInput('fontSize', $event)"
      />
      <ColorPicker
        label="Font Color"
        :model-value="value.fontColor"
        @update:modelValue="handleInput('fontColor', $event)"
      />
      <ColorPicker
        label="Background Color"
        :model-value="value.backgroundColor"
        @update:modelValue="handleInput('backgroundColor', $event)"
      />
      <!-- Horizontal Align: {{ value.alignment }} <br /> Dropdown list with three options -->
      <!-- Vertical Align: {{ value.verticalAlignment }} <br /> Dropdown list with three options -->
      <TextInput
        label="Line Height"
        type="number"
        :model-value="value.lineHeight"
        @update:modelValue="handleInput('lineHeight', Number($event))"
      />
    </template>
  </div>
</template>

<script lang="ts">
import ColorPicker from '@/components/library/ColorPicker.vue';
import TextInput from '@/components/library/TextInput.vue';
import type { StyledPDFFieldOption } from '@/types/PDFFieldOption';
import type { PropType } from 'vue';
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'PDFFieldEditor',
  components: {
    TextInput,
    ColorPicker,
  },
  props: {
    fieldName: {
      type: String as PropType<string>,
      required: true,
    },
    value: {
      type: Object as PropType<StyledPDFFieldOption>,
      required: true,
    },
  },
  data() {
    return {};
  },
  mounted(): void {},
  computed: {
    isTextField(): boolean {
      return this.value.type === 'text';
    },
  },
  methods: {
    handleInput(field: string, value: any): void {
      const fields = field.split('.');
      const newValue = JSON.parse(JSON.stringify(this.value));
      let currentObj = newValue;

      for (let i = 0; i < fields.length - 1; i++) {
        const fieldName = fields[i];
        if (!currentObj[fieldName]) {
          currentObj[fieldName] = {};
        }
        currentObj = currentObj[fieldName];
      }

      currentObj[fields[fields.length - 1]] = value;
      this.$emit('input', newValue);
    },
  },
});
</script>

<style></style>
