<template>
  <div class="rounded-lg p-4" x-data="app()">
    <div class="text-center px-2 pb-4 text-lg">Enter your pin code</div>
    <div class="flex justify-center">
      <input
        v-for="(l, i) in pinlength"
        :key="i"
        ref="codefield"
        :autofocus="i == 0"
        class="h-10 w-10 border bg-white border-gray-300 dark:border-gray-500 text-gray-800 dark:text-gray-200 dark:bg-gray-700 mx-2 rounded-lg flex items-center text-center font-thin text-3xl"
        v-model="cells[i].value"
        maxlength="1"
        inputmode="text"
        @keyup="stepForward(i)"
        @keydown.backspace="stepBack(i)"
        @focus="resetValue(i)"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  props: {
    pinlength: {
      type: Number,
      default: 4,
    },
    'update:modelValue': {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      cells: [{ value: '' }, { value: '' }, { value: '' }, { value: '' }],
    };
  },
  methods: {
    resetValue(i: number) {
      for (let x = 0; x < this.pinlength; x++) {
        if (x >= i) this.cells[x].value = '';
      }
    },
    stepForward(i: number) {
      if (this.cells[i].value && i != this.pinlength - 1) {
        const codefield: any = this.$refs.codefield as any;
        codefield[i + 1].focus();
      }
      this.checkPin();
    },
    stepBack(i: any) {
      if (i == 0) return;
      if (this.cells[i].value) {
        const codefield: any = this.$refs.codefield as any;
        codefield[i - 1].focus();
        this.cells[i].value = '';
      }
    },
    checkPin() {
      let code = '';
      for (let i = 0; i < this.pinlength; i++) {
        code = code + this.cells[i].value;
      }
      if (code.length == this.pinlength) {
        this.$emit('checkPin', code);
      }
    },
  },
});
</script>
