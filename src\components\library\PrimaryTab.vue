<template>
  <span
    :class="[
      tab.key === currentTab?.key
        ? 'border-pink-700 dark:border-pink-500 text-pink-700 dark:text-pink-500'
        : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-500',
      'whitespace-nowrap border-b-2 py-3 px-4 text-base leading-6 font-semibold cursor-pointer select-none',
    ]"
    @click="handleClick"
  >
    {{ tab.label }}
  </span>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
export default defineComponent({
  props: {
    tab: {
      type: Object,
      required: true,
    },
    currentTab: {
      type: Object,
      default: () => ({}),
    },
  },
  name: 'PrimaryTab',
  methods: {
    handleClick() {
      if ('routePath' in this.tab) {
        this.$emit('tabClick', this.tab);
      } else {
        // todo: remove
        this.$emit('tabClick', this.tab.routeName);
      }
    },
  },
});
</script>

<style></style>
