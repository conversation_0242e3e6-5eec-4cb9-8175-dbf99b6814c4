import type MinuteIncrement from '@/types/MinuteIncrement';
import axios, { type AxiosResponse } from 'axios';
import { coreBaseUrl } from '../utils/constants';

export const listMinuteIncrements = async (): Promise<
  AxiosResponse<MinuteIncrement[]>
> => {
  const url = `${coreBaseUrl()}/minute-increments`;
  const response = await axios.get(url, {
    withCredentials: true,
  });
  return response;
};
