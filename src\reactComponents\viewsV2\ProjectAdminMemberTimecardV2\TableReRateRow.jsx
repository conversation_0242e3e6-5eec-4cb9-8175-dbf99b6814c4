import { Box } from '@mui/material';
import { forwardRef, useContext } from 'react';
import PropTypes from 'prop-types';
import { IconButton } from '@/reactComponents/library';
import DeleteIcon from '@mui/icons-material/Delete';

import { RERATE_COLUMNS } from './timecardTableUtils';
import TableReRateCells from './TableReRateCells';
import { TimecardContext } from './timecardUtils';
import { useGridApiContext } from '@mui/x-data-grid-pro';

const TableReRateRow = forwardRef((props, ref) => {
  const { rowId, index, rowHeight, row } = props;

  return (
    <Box
      ref={ref}
      data-rowindex={index}
      data-id={rowId}
      sx={[
        {
          height: `${rowHeight}px`,
          borderTop: '1px solid',
          borderColor: 'background.border',
          animation: 'all 1s ease-in-out',
          backgroundColor: 'background.paper',
          display: 'flex',
          padding: '0 24px',
          position: 'relative',
          alignItems: 'center',
        },
        (theme) =>
          theme.applyStyles('dark', {
            backgroundColor: 'transparent',
          }),
      ]}
    >
      {RERATE_COLUMNS.map((colDef, index) => {
        const value = row[colDef.field];
        return (
          <TableReRateCells
            key={`${rowId}-${colDef.field}`}
            colDef={colDef}
            index={index}
            value={value}
            id={rowId}
            row={row}
          />
        );
      })}
      <DeleteButtonCell row={row} />
    </Box>
  );
});

const DeleteButtonCell = ({ row }) => {
  const context = useContext(TimecardContext);
  const apiRef = useGridApiContext();
  const { removeReRate } = context;

  const handleDelete = () => {
    const parentRowId = row.parentRowId;
    removeReRate({ parentRowId: parentRowId, api: apiRef.current });
  };
  return (
    <Box
      sx={{
        position: 'sticky',
        right: 40,
        marginLeft: 'auto',
      }}
    >
      <IconButton onClick={handleDelete}>
        <DeleteIcon />
      </IconButton>
    </Box>
  );
};

DeleteButtonCell.propTypes = {
  row: PropTypes.object.isRequired,
};

TableReRateRow.propTypes = {
  rowId: PropTypes.string.isRequired,
  index: PropTypes.number.isRequired,
  rowHeight: PropTypes.number.isRequired,
  row: PropTypes.object.isRequired,
};

export default TableReRateRow;
