import PropTypes from 'prop-types';
import { hasFlag } from 'country-flag-icons';
import * as FLAGS from 'country-flag-icons/react/3x2';

const Flag = ({ code }) => {
  let flagIcon = code.toUpperCase();
  if (!hasFlag(flagIcon)) return flagIcon;

  const Component = FLAGS[code];
  return <Component style={{ width: 20 }} />;
};
Flag.propTypes = {
  code: PropTypes.string.isRequired,
};

export default Flag;
