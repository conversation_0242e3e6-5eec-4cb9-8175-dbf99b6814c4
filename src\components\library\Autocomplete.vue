<template>
  <div>
    <label
      v-if="label"
      class="block text-xs font-xs text-gray-700 dark:text-gray-400 pb-1"
    >
      {{ label }} <span v-if="required" class="text-red-600">*</span>
    </label>

    <div
      class="relative inline-block text-left w-full"
      @focusout="handleFocusOut"
    >
      <Icon name="search" class="absolute z-10 top-1.5 left-1.5 w-4 h-4" />
      <div>
        <input
          type="text"
          v-model="searchQuery"
          @focus="checkFocus"
          @input="filterMenuItems"
          :disabled="disabled"
          class="h-8 pl-7 w-full rounded-md border border-gray-300 dark:border-gray-500 bg-white dark:bg-gray-700 px-3 py-[0.6rem] text-sm font-sm text-gray-800 dark:text-gray-200 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          :placeholder="placeholder"
        />
      </div>

      <transition
        enter-active-class="transition ease-out duration-100"
        enter-from-class="transform opacity-0 scale-95"
        enter-to-class="transform opacity-100 scale-100"
        leave-active-class="transition ease-in duration-75"
        leave-from-class="transform opacity-100 scale-100"
        leave-to-class="transform opacity-0 scale-95"
      >
        <div
          v-if="isOpen"
          class="absolute left-0 z-10 mt-2 w-full max-h-64 rounded-md bg-white dark:bg-gray-700 shadow-lg ring-1 ring-black ring-opacity-5 overflow-auto"
        >
          <ul>
            <li
              v-for="(item, index) in filteredMenuItems"
              :key="index"
              @mousedown="selectItem(item)"
              class="cursor-pointer px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600"
            >
              {{ (item as any)?.[displayName] }}
            </li>
            <li
              v-if="filteredMenuItems.length === 0"
              class="px-4 py-2 text-sm text-gray-700 dark:text-gray-400"
            >
              No items found
            </li>
          </ul>
        </div>
      </transition>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import Icon from '@/components/Icon.vue';
export default defineComponent({
  components: {
    Icon,
  },
  props: {
    menuItems: {
      type: Array,
      default: () => [],
    },
    label: String,
    errors: Array,
    placeholder: {
      type: String,
      default: 'Search',
    },
    displayName: {
      type: String,
      default: 'label',
    },
    required: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    initialValue: {
      type: [Object, String],
      default: null,
    },
  },
  data() {
    return {
      isOpen: false,
      searchQuery: '',
      filteredMenuItems: this.menuItems,
    };
  },
  watch: {
    menuItems: {
      handler(newItems) {
        this.filteredMenuItems = newItems;
      },
      immediate: true,
    },
  },
  methods: {
    filterMenuItems() {
      const query = this.searchQuery.toLowerCase();
      this.filteredMenuItems = this.menuItems.filter((item: any) =>
        item[this.displayName].toLowerCase().includes(query),
      );
    },
    selectItem(item: any) {
      this.searchQuery = item[this.displayName];
      this.$emit('update:modelValue', item);
      this.isOpen = false;
    },
    checkFocus() {
      this.isOpen = true;
    },
    handleFocusOut(event: FocusEvent) {
      const relatedTarget = event.relatedTarget as HTMLElement;
      if (!relatedTarget || !this.$el.contains(relatedTarget)) {
        this.isOpen = false;
      }
    },
  },
  mounted() {
    if (this.initialValue) {
      this.selectItem(this.initialValue);
    }
  },
});
</script>
