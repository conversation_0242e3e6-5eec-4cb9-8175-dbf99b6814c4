import React from 'react';
import PropTypes from 'prop-types';
import { useGridApiContext } from '@mui/x-data-grid-pro';
import { Box } from '@mui/material';
import { GridTextField } from '../../library';

import { RERATE_COLUMNS } from './timecardTableUtils';

const widths = RERATE_COLUMNS.map((col) => col.width);
const Cell_Single_Margin = 24;
const Cell_double_Margin = Cell_Single_Margin * 2;

const ReRateCell = ({ colDef, index, value, row, rowId }) => {
  const apiRef = useGridApiContext();
  const Cell = colDef.renderCell;

  const leftPosition = React.useMemo(() => {
    return index === 0
      ? Cell_double_Margin
      : widths.slice(0, index).reduce((a, b, i) => {
          return a + b + Cell_double_Margin;
        }, 0) + Cell_double_Margin;
  }, [index]);

  return (
    <Box
      sx={{
        width: colDef.width,
        margin: `0px ${Cell_Single_Margin}px`,
        left: leftPosition,
        position: 'sticky',
      }}
      key={colDef.field}
    >
      <Box
        sx={{
          marginBottom: '2px',
          fontSize: '15px',
          fontWeight: 600,
          whiteSpace: 'nowrap',
          alignSelf: 'center',
          padding: '0 4px',
        }}
      >
        {`${colDef.headerName}${colDef.isRequired ? ' *' : ''}`}
      </Box>
      <Box>
        {Cell && (
          <Cell
            colDef={colDef}
            value={value}
            row={row}
            rowId={rowId}
            api={apiRef.current}
          />
        )}
        {!Cell && (
          <GridTextField
            sx={{ width: '100%', padding: '0px 4px' }}
            value={value?.key || value?.name}
            inputProps={{
              readOnly: true,
            }}
          />
        )}
      </Box>
    </Box>
  );
};

ReRateCell.propTypes = {
  colDef: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  value: PropTypes.any,
  row: PropTypes.object.isRequired,
  rowId: PropTypes.string,
};

export default ReRateCell;
