import React from 'react';
import PropTypes from 'prop-types';

import {
  Autocomplete,
  GridTextField,
  Box,
  Tooltip,
} from '@/reactComponents/library';

const AutocompleteCell = (props) => {
  const {
    column,
    workLocations,
    occupations,
    rateTypes,
    reimbursement,
    updateCell,
    loading,
    fetchOptions,
    useDisplayName,
    infiniteScrolling = false,
    hasNextPage,
    error,
    disabled,
  } = props;
  const { columnId } = column;

  let value = reimbursement[columnId];
  let options = [];
  let getOptionLabel = (o) => o?.name || '';
  let onChange = (event, newValue) => {
    updateCell(newValue);
  };
  let title = '';

  if (columnId === 'workLocation') {
    options = workLocations;
    getOptionLabel = (option) => {
      const locationName = option?.shootLocation?.locationName || '';
      const zip = option?.shootLocation?.zip || '';
      return `${locationName} ${zip}`;
    };
    title = getOptionLabel(value).trim();
  } else if (columnId === 'occupation') {
    options = occupations;
    title = getOptionLabel(value).trim();
  } else if (columnId === 'rateType') {
    options = rateTypes;
    getOptionLabel = (option) =>
      (useDisplayName ? option?.crew_display_name : option?.name) || '';
  }

  return (
    <>
      <Tooltip title={title} placement="top" arrow enterDelay={500}>
        <Box sx={{ width: '100%' }}>
          <Autocomplete
            value={value || null}
            options={options}
            getOptionLabel={getOptionLabel}
            infiniteScrolling={infiniteScrolling}
            hasNextPage={hasNextPage}
            fetchOptions={fetchOptions}
            onChange={onChange}
            loading={loading}
            TextFieldComp={GridTextField}
            error={error}
            disabled={disabled}
          />
        </Box>
      </Tooltip>
    </>
  );
};

AutocompleteCell.propTypes = {
  column: PropTypes.object.isRequired,
  workLocations: PropTypes.array,
  occupations: PropTypes.array,
  rateTypes: PropTypes.array,
  reimbursement: PropTypes.object.isRequired,
  updateCell: PropTypes.func.isRequired,
  fetchOptions: PropTypes.func,
  loading: PropTypes.bool,
  useDisplayName: PropTypes.bool,
  infiniteScrolling: PropTypes.bool,
  hasNextPage: PropTypes.bool,
  error: PropTypes.bool,
  disabled: PropTypes.bool,
};

export default AutocompleteCell;
