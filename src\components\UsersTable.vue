<template>
  <div>
    <TableFilters :filters="filters" @update:filters="filters = $event" />
    <div class="bg-white dark:bg-gray-900 shadow sm:rounded-md mb-4">
      <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-500">
        <li v-for="(user, userIndex) in users" :key="`${user.id}-${userIndex}`">
          <a
            class="block hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
            :class="{
              'rounded-t-md': userIndex === 0,
              'rounded-b-md': userIndex === users.length - 1,
            }"
            @click="goToUser(user.id!)"
          >
            <div class="px-4 py-4 sm:px-6">
              <div class="flex items-center justify-between">
                <p
                  class="truncate text-sm font-medium text-indigo-600 dark:text-indigo-300"
                >
                  {{
                    `${user.firstName}
                                    ${
                                      user.middleName
                                        ? user.middleName + ' '
                                        : ''
                                    }${user.lastName}`
                  }}
                </p>
              </div>
              <div class="mt-2 flex flex-wrap justify-between items-start">
                <div
                  class="sm:flex sm:flex-wrap justify-start sm:space-x-2 items-center"
                >
                  <p
                    class="flex items-center space-x-2 justify-start text-sm text-gray-500 dark:text-gray-400"
                  >
                    <Icon name="phone" class="w-4 h-4" />
                    {{ user.phone }}
                  </p>
                  <div
                    class="flex items-center space-x-2 justify-start text-sm text-gray-500 dark:text-gray-400"
                  >
                    <Icon name="envelope" class="w-4 h-4" />
                    <div>
                      {{ user.email }}
                    </div>
                  </div>
                  <p
                    class="flex items-center text-sm space-x-2"
                    :class="
                      user.role.key === 'admin'
                        ? 'text-red-400'
                        : 'text-gray-500 dark:text-gray-400'
                    "
                  >
                    <Icon name="user" class="mr-2 w-4 h-4" />
                    {{ user.role.name }}
                  </p>
                </div>
                <div
                  class="flex items-center text-sm text-gray-500 dark:text-gray-400"
                >
                  <Icon name="calendar" class="mr-1.5 w-4 h-4" />
                  <p>
                    Created on
                    {{ ' ' }}
                    <time :datetime="(user.createdAt.toISO() as string)">{{
                      user.createdAt.toFormat('MM/dd')
                    }}</time>
                  </p>
                </div>
              </div>
            </div>
          </a>
        </li>
      </ul>
    </div>
    <Pagination v-model="pagination" />
  </div>
</template>

<script lang="ts" setup>
import Icon from '@/components/Icon.vue';
import TableFilters from '@/components/TableFilters.vue';
import Pagination from '@/components/library/Pagination.vue';
import { listUsers } from '@/services/users';
import { FilterOperator, FilterUIType, type Filter } from '@/types/Filter';
import type { Pagination as PaginationType } from '@/types/Pagination';
import type User from '@/types/User';
import { convertFiltersToQuery } from '@/utils/filter';
import { convertSortsToQuery } from '@/utils/sort';
import {
  copyMetaToPagination,
  updateReactRouteWithPagination,
} from '@/utils/tableview';
import { computed, inject, onMounted, ref, watch, type Ref } from 'vue';
const route: any = inject('route');
const navigate: any = inject('navigate');

const users = ref([] as User[]);
//TODO - need ot test pagination as  I only had 1 user to work with in dev
const pagination = ref({ page: 1, limit: 10, total: 0 } as PaginationType);
const skipFetch = ref(true);
const filters: Ref<Filter[]> = ref([
  {
    id: 'first_name',
    field: 'firstName',
    label: 'First Name',
    value: '',
    type: FilterUIType.Text,
    operator: FilterOperator.ILike,
    active: false,
    sortable: true,
    sortDirection: '',
  },
  {
    id: 'last_name',
    field: 'lastName',
    label: 'Last Name',
    value: '',
    type: FilterUIType.Text,
    operator: FilterOperator.ILike,
    active: false,
    sortable: true,
    sortDirection: '',
  },
  {
    id: 'email',
    field: 'email',
    label: 'Email',
    value: '',
    type: FilterUIType.Text,
    operator: FilterOperator.ILike,
    active: false,
    sortable: true,
    sortDirection: '',
  },
]);

watch(route.value.query, (newVal, oldVal) => {
  if (newVal.page === oldVal.page && newVal.limit === oldVal.limit) {
    return;
  }
  copyQueryParamsToPagination();
  skipFetch.value = true;
  getUsers();
});

watch(pagination, (newVal, oldVal) => {
  if (newVal.page === oldVal.page && newVal.limit === oldVal.limit) {
    return;
  }
  updateRoute(newVal);
  if (!skipFetch.value) {
    getUsers();
  }
  skipFetch.value = false;
});

onMounted(() => {
  establishQueryParams();
  getUsers();
});

const goToUser = (id: number) => {
  navigate(`/users/${id}`);
};

const getUsers = async () => {
  const filtersString = convertFiltersToQuery(activeFilters.value);
  const sortsString = convertSortsToQuery(activeFilters.value);
  const {
    data: { meta, data },
  } = await listUsers(pagination.value, filtersString, sortsString);

  const { current_page, last_page } = meta;
  if (current_page > last_page) {
    pagination.value.page = 1;
    updateRoute(pagination.value);
    await getUsers();
    return;
  }

  users.value = data;
  pagination.value = copyMetaToPagination(meta);
};

const establishQueryParams = () => {
  copyQueryParamsToPagination();
  updateRoute(pagination.value);
};

const copyQueryParamsToPagination = () => {
  const { query } = route.value;
  pagination.value.page = Number(query.page) || 1;
  pagination.value.limit = Number(query.limit) || 10;
};

const updateRoute = (pagination: PaginationType) => {
  if (navigate) {
    updateReactRouteWithPagination(navigate, pagination, route.value.query);
  }
};

const activeFilters = computed(() => {
  return filters.value.filter(({ active }) => active);
});

watch(activeFilters, async () => {
  getUsers();
});
</script>
