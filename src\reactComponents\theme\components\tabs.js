import { tabsClasses } from '@mui/material/Tabs';
import { tabClasses } from '@mui/material/Tab';

const tabs = {
  styleOverrides: {
    root: (props) => {
      const { theme } = props;
      const { palette } = theme;
      return {
        [`.${tabsClasses.indicator}`]: {
          backgroundColor: `${palette.primary[700]}`,
        },
        variants: [
          {
            props: { variant: 'secondary' },
            style: (buttonProps) => {
              const { theme } = buttonProps;
              const { palette } = theme;
              const newStyles = {
                [`.${tabClasses.root}`]: {
                  borderRadius: theme.shape.borderRadius,
                  [`&.${tabClasses.selected}`]: {
                    backgroundColor: palette.primary[50],
                  },
                },
                [`.${tabsClasses.indicator}`]: {
                  display: 'none',
                },
              };

              return newStyles;
            },
          },
        ],
      };
    },
  },
};

export const tab = {
  styleOverrides: {
    root: (props) => {
      const { theme } = props;
      const { palette } = theme;
      return {
        fontWeight: 'bold',
        textTransform: 'none',
        fontSize: '16px',
        [`&.${tabClasses.selected}`]: {
          color: `${palette.primary[700]} !important`,
          // backgroundColor: 'blue !important',
        },
      };
    },
  },
};

export default tabs;
