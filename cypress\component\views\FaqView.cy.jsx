import { createMemoryRouter, RouterProvider } from 'react-router-dom';
import FaqView from '@/reactComponents/views/FaqView';

describe('FaqView component testing', () => {
  beforeEach(() => {
    const router = createMemoryRouter([
      {
        path: '/',
        element: <FaqView />,
      },
    ]);
    cy.mount(<RouterProvider router={router} />);
  });
  it('should render the FaqView component correctly', () => {
    cy.contains('Frequently asked questions').should('be.visible');
    cy.contains(
      'Can’t find the answer you’re looking for? Reach out to our customer support <NAME_EMAIL>',
    ).should('be.visible');
    cy.contains('What is FPS').should('be.visible');
    cy.contains(
      'FPS is a production management software company. We interface with the existing payroll providers to create a seamless experience for crew.',
    ).should('be.visible');
    cy.contains('How is my data secured?').should('be.visible');
    cy.contains(
      'We use the latest security protocols to ensure your data is safe. We also use the latest encryption protocols to ensure your data is safe. Finally, we work with a company called Drata (drata.com) to continuously monitor our security and ensure we are up to date with the latest trends and protocols.',
    ).should('be.visible');
  });
});
