import type { DateTime } from 'luxon';
import type TimecardDayMeal from './TimecardDayMeal';

export enum TrackingKeysEnum {
  KEY_TRACKKEY = 'Tk1Value',
}

export default interface TimecardDay {
  generalCrewCall: DateTime | null | undefined;
  id: number;
  isActive: boolean;
  timecardId: number;
  lineNumber: string;
  date: DateTime;
  startsAt: DateTime;
  endsAt: DateTime;
  updatedAt: DateTime;
  createdAt: DateTime;
  isRentalDay: boolean;
  meals: TimecardDayMeal[];
  projectShootLocation: any;
  projectLocationId: number;
  rate: number;
  zipCode: number;
  workStatusId: number;
  workStatus: any;
  workZoneId: number;
  workZone: any;
  mealPenalties: number;
  hasNdb: boolean;
  comments: string;
  hoursWorked: number;
}
