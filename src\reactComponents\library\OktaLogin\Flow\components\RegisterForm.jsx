import PropTypes from 'prop-types';

import { Box, TextInput } from '@/reactComponents/library';

const styles = {
  fields: {
    display: 'flex',
    flexDirection: 'column',
    gap: 1,
    alignItems: 'center',
    p: 2,

    '& >.MuiBox-root': {
      width: '100%',

      '& .MuiFormControl-root': {
        width: '100%',
      },
    },
  },
  row: {
    display: 'flex',
    flexDirection: 'row',
    gap: 1,
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
};

const RegisterForm = ({ data, onChange, disable = [] }) => (
  <Box sx={styles.fields}>
    <Box sx={styles.row}>
      <TextInput
        label="First Name *"
        value={data.firstName}
        type="name"
        onChange={onChange('firstName')}
        disabled={disable.includes('firstName')}
        slotProps={{
          htmlInput: {
            'data-testid': 'register-firstName-input',
          },
        }}
      />
      <TextInput
        label="Middle Name"
        value={data.middleName}
        type="name"
        onChange={onChange('middleName')}
        disabled={disable.includes('middleName')}
        slotProps={{
          htmlInput: {
            'data-testid': 'register-middleName-input',
          },
        }}
      />
    </Box>
    <TextInput
      label="Last Name *"
      value={data.lastName}
      type="name"
      onChange={onChange('lastName')}
      disabled={disable.includes('lastName')}
      slotProps={{
        htmlInput: {
          'data-testid': 'register-lastName-input',
        },
      }}
    />
    <TextInput
      label="Email *"
      value={data.email}
      onChange={onChange('email')}
      disabled={disable.includes('email')}
      slotProps={{
        htmlInput: {
          'data-testid': 'register-email-input',
        },
      }}
    />
  </Box>
);
RegisterForm.propTypes = {
  data: PropTypes.object.isRequired,
  onChange: PropTypes.func.isRequired,
  disable: PropTypes.array,
};

export default RegisterForm;
