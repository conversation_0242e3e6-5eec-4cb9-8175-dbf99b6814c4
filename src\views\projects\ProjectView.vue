<template>
  <div class="flex flex-col h-screen">
    <ProjectHeader :project="project" :isAdmin="isAdmin" />
    <div class="w-full overflow-y-auto flex-grow bg-gray-100 dark:bg-gray-800">
      <RouterView v-if="doneLoading" :project="project" :is-admin="isAdmin" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import ProjectHeader from '@/components/ProjectHeader.vue';
import { getProjectByHashId, isProjectAdmin } from '@/services/project';
import type Project from '@/types/Project';
import type ProjectMember from '@/types/ProjectMember';
import { onMounted, ref, type Ref } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();

const project: Ref<Project> = ref({} as Project);

const projectMember: Ref<ProjectMember | null> = ref({} as ProjectMember);

const isAdmin: Ref<boolean> = ref(false);

const doneLoading: Ref<boolean> = ref(false);

onMounted(async () => {
  const { data: projectData } = await getProjectByHashId(
    route.params.hashId as string,
  );
  if (!projectData) {
    doneLoading.value = true;
    return;
  }
  project.value = projectData;

  const { data: isAdminData } = await isProjectAdmin(project.value.id!);
  isAdmin.value = isAdminData;
  doneLoading.value = true;
});
</script>
