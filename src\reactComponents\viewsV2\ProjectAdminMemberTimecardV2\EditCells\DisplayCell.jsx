import React from 'react';
import PropTypes from 'prop-types';

import { Box } from '@/reactComponents/library';
import { formatDecimal } from '@/reactComponents/utils/stringNum';

const DisplayCell = (props) => {
  const { column, reimbursement } = props;
  const { columnId, decimalPlaces } = column;

  let value;
  if (columnId === 'mileageExpense') {
    value = 'MILEAGE - NON -TAXABLE';
  } else if (columnId === 'kitExpense') {
    value = 'KIT RENTAL - NON-TAXABLE';
  } else if (columnId === 'date') {
    value = reimbursement[columnId];
    value = value?.toFormat('MM/dd/yyyy') || '';
  } else {
    value = reimbursement[columnId];

    if (decimalPlaces) {
      value = formatDecimal(value, decimalPlaces);
    }
  }

  return <Box sx={{ pl: 1 }}>{value || ''}</Box>;
};

DisplayCell.propTypes = {
  column: PropTypes.object.isRequired,
  reimbursement: PropTypes.object.isRequired,
};

export default DisplayCell;
