import React from 'react';
import PropTypes from 'prop-types';

import { Box, TableCell, TableRow, TableBody } from '@/reactComponents/library';

import {
  GridTable,
  GridTableHead,
  GridTableRow,
  GridTableCell,
} from '@/reactComponents/library/styledComponents';

import CellWrapper from './CellWrapper';

import { COLUMNS, reimbursementFileName } from '../timecardUtils';

const styles = {
  title: {
    pl: 9,
    height: '68px',
    display: 'flex',
    fontWeight: 'bold',
    fontSize: '18px',
    alignItems: 'center',
  },
};

const Group = (props) => {
  const {
    data = [],
    isMileageForm,
    isKitRental,
    updateReimbursements,
    updateTimecard,
    workLocations = [],
    expenseTypes = [],
    timecardDays = [],
    trackingDetails,
    member,
    timecard = null,
    displayErrors,
    readOnlyMode = false,
  } = props;

  const columns = React.useMemo(() => {
    return COLUMNS.filter((column) => {
      if (column.columnId === 'lineNumber' && !trackingDetails?.allowed) {
        return false;
      }
      return (
        (isMileageForm && column.mileageForm) ||
        (isKitRental && column.kitRental) ||
        (!isMileageForm && !isKitRental && column.otherExpense)
      );
    });
  }, [isKitRental, isMileageForm, trackingDetails?.allowed]);

  const reimbursementType = isMileageForm
    ? 'mileageForm'
    : isKitRental
    ? 'kitRental'
    : 'otherExpense';

  const title = isMileageForm
    ? 'Mileage'
    : isKitRental
    ? 'Kit Rental'
    : 'Other';

  const buildFileName = (reimbursement) => {
    let date = reimbursement.date;
    if (isKitRental) {
      const firstDay = timecardDays.find(
        (day) => day.isRentalDay && day.isActive,
      );
      date = firstDay?.date;
    }

    return reimbursementFileName({
      member,
      reimbursement,
      title: title === 'Other' ? reimbursement?.type?.name : title,
      date,
    });
  };

  const typeErrors = []; //errors for this group type
  let errorKeys = [];
  if (isMileageForm) {
    errorKeys = Object.keys(displayErrors).filter((key) =>
      key.startsWith('mileageForm'),
    );
  } else if (isKitRental) {
    errorKeys = Object.keys(displayErrors).filter((key) =>
      key.startsWith('kitRental'),
    );
  } else {
    errorKeys = Object.keys(displayErrors).filter((key) =>
      key.startsWith('re'),
    );
  }

  errorKeys.forEach((key) => {
    const error = displayErrors[key];
    if (error) {
      typeErrors.push({
        [key]: error,
      });
    }
  });

  return (
    <Box>
      <Box sx={styles.title}>{title}</Box>
      <GridTable>
        <GridTableHead sx={{ backgroundColor: 'background.default' }}>
          <TableRow>
            {columns.map((column) => (
              <TableCell
                key={column.columnId}
                data-testid={`header-${column.columnId}`}
              >
                {column.label}
              </TableCell>
            ))}
          </TableRow>
        </GridTableHead>
        <TableBody>
          {data.map((reimbursement, index) => {
            const updateRow = isMileageForm
              ? (updated) => {
                  updateTimecard({ mileageForm: updated });
                }
              : isKitRental
              ? (updated) => {
                  updateTimecard({ kitRental: updated });
                }
              : (updatedReimbursement) => {
                  updateReimbursements(updatedReimbursement, index);
                };

            const deleteRow = isMileageForm
              ? () => {
                  updateTimecard({ mileageForm: null });
                }
              : isKitRental
              ? () => {
                  updateTimecard({ kitRental: null });
                }
              : () => {
                  updateReimbursements(null, index);
                };
            const fileName = buildFileName(reimbursement);

            const rowErrors = {};
            typeErrors.forEach((error) => {
              const errorKey = Object.keys(error)[0];
              const fieldName = errorKey.split('.')[1];
              if (
                errorKey.startsWith(`re${reimbursement.id}`) ||
                isMileageForm ||
                isKitRental
              ) {
                rowErrors[fieldName] = error[errorKey];
              }
            });

            return (
              <GridTableRow key={reimbursement.id || index}>
                {columns.map((column) => {
                  const updateCell = (newValue) => {
                    const updated = {
                      ...reimbursement,
                      [column.columnId]: newValue,
                    };
                    updateRow(updated);
                  };

                  return (
                    <GridTableCell
                      key={column.columnId}
                      data-testid={`body-${column.columnId}`}
                    >
                      <CellWrapper
                        column={column}
                        reimbursement={reimbursement}
                        updateCell={updateCell}
                        workLocations={workLocations}
                        expenseTypes={expenseTypes}
                        deleteRow={deleteRow}
                        updateRow={updateRow} //          required for expense update
                        timecardDays={timecardDays} //    required for rentalDays
                        updateTimecard={updateTimecard} //required for rentalDays update
                        reimbursementType={reimbursementType}
                        fileName={fileName}
                        timecard={timecard}
                        cellError={rowErrors[column.columnId]}
                        disabled={readOnlyMode}
                      />
                    </GridTableCell>
                  );
                })}
              </GridTableRow>
            );
          })}
        </TableBody>
      </GridTable>
    </Box>
  );
};

Group.propTypes = {
  data: PropTypes.array.isRequired,
  isMileageForm: PropTypes.bool,
  isKitRental: PropTypes.bool,
  updateReimbursements: PropTypes.func,
  updateTimecard: PropTypes.func,
  workLocations: PropTypes.array.isRequired,
  expenseTypes: PropTypes.array,
  timecardDays: PropTypes.array,
  trackingDetails: PropTypes.object.isRequired,
  member: PropTypes.object.isRequired,
  timecard: PropTypes.object,
  displayErrors: PropTypes.object.isRequired,
  readOnlyMode: PropTypes.bool.isRequired,
};

export default Group;
