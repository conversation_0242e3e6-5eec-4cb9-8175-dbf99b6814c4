import { createMemoryRouter, RouterProvider } from 'react-router-dom';
import VerifyPhoneView from '@/reactComponents/views/VerifyPhoneView';

describe('VerifyPhoneView component testing', () => {
  beforeEach(() => {
    const router = createMemoryRouter(
      [
        {
          path: '/v2/api/user/verify-phone/:phone',
          element: <VerifyPhoneView />,
        },
      ],
      {
        initialEntries: ['/v2/api/user/verify-phone/+573146725678'],
      },
    );
    cy.mount(<RouterProvider router={router} />);
  });

  it('should correctly render the VerifyPhoneView component, including the Verify message, OTC input, and the enter option', () => {
    cy.contains('Verify').should('be.visible');
    cy.contains('Code').should('be.visible');
    cy.contains('Enter code for +573146725678').should('be.visible');
    cy.contains('Enter').should('be.visible');
  });

  it('should allow the user to enter a code', () => {
    cy.get('[data-testid="verify-phone-input"]')
      .should('be.visible')
      .type('+452311');
  });

  it('should submit the Verify code with enter option', () => {
    cy.get('[data-testid="verify-phone-btn"]').click();
  });
});
