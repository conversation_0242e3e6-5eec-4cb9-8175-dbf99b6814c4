import React from 'react';
import PropTypes from 'prop-types';
import { InputAdornment } from '@mui/material';
import { PickersDay } from '@mui/x-date-pickers';
import WarningIcon from '@mui/icons-material/WarningAmber';

import { DatePicker, GridTextField, Tooltip } from '@/reactComponents/library';
import { setTimeZone } from '@/reactComponents/utils/dateTime';

const styles = {
  payPeriodDay: {
    backgroundColor: 'grey.100',
  },
  warningIcon: {
    color: 'warning.main',
  },
  outsideCurrentMonth: {
    color: 'grey.400',
  },
};

const CalendarDay = ({
  day,
  period = null,
  sx = {},
  outsideCurrentMonth = false,
  ...props
}) => {
  const { startsAt, endsAt } = period || {};

  if (day >= startsAt && day <= endsAt) {
    sx = { ...sx, ...styles.payPeriodDay };
  }
  if (outsideCurrentMonth) {
    sx = { ...sx, ...styles.outsideCurrentMonth };
  }

  return (
    <PickersDay
      day={day}
      sx={sx}
      outsideCurrentMonth={outsideCurrentMonth}
      {...props}
    />
  );
};
CalendarDay.propTypes = {
  day: PropTypes.object.isRequired,
  period: PropTypes.object,
  sx: PropTypes.object,
  outsideCurrentMonth: PropTypes.bool,
};

const DateCell = (props) => {
  const {
    updateCell,
    reimbursement,
    column,
    timecard = null,
    localTimezone = false,
    disabled,
  } = props;
  const { columnId } = column;
  const value = reimbursement[columnId];
  const { payPeriod: { startsAt, endsAt } = {} } = timecard || {};
  const outOfWeek = value && (value < startsAt || value > endsAt);
  const inputRef = React.useRef(null);

  const [open, setOpen] = React.useState(false);

  let endAdornment = null;
  if (outOfWeek) {
    endAdornment = (
      <InputAdornment position="end">
        <Tooltip title="This date is outside the work week.">
          <WarningIcon sx={styles.warningIcon} />
        </Tooltip>
      </InputAdornment>
    );
  }

  const slotProps = {
    day: { period: timecard?.payPeriod },
    textField: {
      inputRef,
      onClick: () => {
        if (disabled) return;
        setOpen((prev) => !prev);
        inputRef?.current?.blur();
      },
      InputProps: { endAdornment },
    },
  };

  const onChange = (val) =>
    updateCell(localTimezone ? val : setTimeZone(val, 'UTC'));

  return (
    <DatePicker
      open={open}
      value={value || null}
      onChange={onChange}
      onClose={() => setOpen(false)}
      slots={{
        textField: GridTextField,
        day: CalendarDay,
      }}
      disabled={disabled}
      slotProps={slotProps}
      showDaysOutsideCurrentMonth
    />
  );
};

DateCell.propTypes = {
  column: PropTypes.object.isRequired,
  reimbursement: PropTypes.object.isRequired,
  updateCell: PropTypes.func.isRequired,
  timecard: PropTypes.object,
  localTimezone: PropTypes.bool,
  disabled: PropTypes.bool,
};

export default DateCell;
