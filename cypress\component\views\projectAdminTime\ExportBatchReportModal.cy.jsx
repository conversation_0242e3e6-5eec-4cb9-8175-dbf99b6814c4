import { createMemoryRouter, RouterProvider } from 'react-router-dom';
import ExportBatchReportModal from '@/reactComponents/viewsV2/ProjectAdminTime/ExportBatchReportModal';
import { SnackbarProvider } from 'notistack';
import PropTypes from 'prop-types';

const mockBatch = {
  id: 456,
};
const TestWrapper = ({ open, setOpen }) => {
  const router = createMemoryRouter(
    [
      {
        path: '/',
        element: (
          <SnackbarProvider>
            <ExportBatchReportModal
              open={open}
              setOpen={setOpen}
              batch={mockBatch}
            />
          </SnackbarProvider>
        ),
      },
    ],
    { initialEntries: ['/'] },
  );

  return <RouterProvider router={router} />;
};

TestWrapper.propTypes = {
  open: PropTypes.bool.isRequired,
  setOpen: PropTypes.func.isRequired,
};

describe('<ExportBatchReportModal />', () => {
  let mockSetOpen;

  beforeEach(() => {
    mockSetOpen = cy.stub().as('setOpen');
    cy.mount(<TestWrapper open={true} setOpen={mockSetOpen} />);
  });

  it('should mount the component Export Batch Report Modal', () => {
    cy.contains('Export PDFs').should('be.visible');
    cy.contains('One PDF vs Multiple PDFs').should('be.visible');
    cy.contains('Export order').should('be.visible');
    cy.contains('Paperwork scope').should('be.visible');
    cy.contains('Include Kit Rental').should('exist');
    cy.contains('Include Mileage Form').should('exist');
    cy.contains('Include Reimbursement Form').should('exist');
    cy.get('[data-testid="Export-btn"]').should('be.visible');
    cy.get('[data-testid="CloseIcon"]').should('be.visible');
    cy.get(
      '[data-testid="switch-include-kit-rental"] input[type="checkbox"]',
    ).should('be.checked');
    cy.get(
      '[data-testid="switch-include-mileage-form"] input[type="checkbox"]',
    ).should('be.checked');
    cy.get(
      '[data-testid="switch-include-reimbursement-form"] input[type="checkbox"]',
    ).should('be.checked');
  });

  it('should export PDF successfully and show snackbar', () => {
    cy.intercept(
      'GET',
      '**/v2/api/core/batches/456/pdf?scope=all&format=pdf&includeKitRental=true&includeMileageForm=true&includeReimbursementForms=true&exportOrder=startPaperworkFirst',
      {
        statusCode: 200,
        body: { success: true },
      },
    ).as('getDocuments');
    cy.get('[data-testid="Export-btn"]').click();
    cy.contains('Batch exported successfully.').should('be.visible');
    cy.get('@setOpen').should('have.been.calledWith', false);
  });

  it('should show an error message when exporting PDF fails', () => {
    cy.intercept(
      'GET',
      '**/v2/api/core/batches/456/pdf?scope=all&format=pdf&includeKitRental=true&includeMileageForm=true&includeReimbursementForms=true&exportOrder=startPaperworkFirst',
      {
        statusCode: 400,
        body: { success: false, message: 'Error exporting batch' },
      },
    ).as('getDocuments');
    cy.get('[data-testid="Export-btn"]').click();
    cy.contains('Error exporting batch').should('be.visible');
  });

  it('should close the modal when the "Cancel" button is clicked', () => {
    cy.get('[data-testid="Cancel-btn"]').click();
    cy.get('@setOpen').should('have.been.calledWith', false);
  });

  it('should close the modal when the "X" button is clicked', () => {
    cy.get('[data-testid="CloseIcon"]').click();
    cy.get('@setOpen').should('have.been.calledWith', false);
  });
});
