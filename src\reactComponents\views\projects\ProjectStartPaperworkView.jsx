import { applyPureVueInReact } from 'veaury';
import ProjectStartPaperworkViewVue from '../../../views/projects/ProjectStartPaperworkView.vue';
import { useAuth, useReactRouter } from '../../AppHooks';
import { useOutletContext } from 'react-router';
const ReactProjectStartPaperworkView = applyPureVueInReact(
  ProjectStartPaperworkViewVue,
);

const ProjectStartPaperworkView = () => {
  useAuth();
  const { navigate, route } = useReactRouter();
  const context = useOutletContext();
  return (
    <ReactProjectStartPaperworkView
      route={route}
      navigate={navigate}
      project={context.project}
    />
  );
};

export default ProjectStartPaperworkView;
