export class Cookie {
  static set(
    name: string,
    value: any,
    lifetime: number | null = null,
    path: string = '/',
  ) {
    let exp = '';
    if (lifetime) {
      const date = new Date();
      date.setTime(date.getTime() + lifetime * 1000);
      exp = `; expires=${date.toUTCString()}`;
    }
    document.cookie = `${name}=${value || ''}${exp}; path=${path}`;
  }

  static get(name: string) {
    const nameEQ = `${name}=`;
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
  }

  static remove(name: string) {
    document.cookie = `${name}=; Max-Age=-99999999;`;
  }
}
