import { getUserPermissions, getUser } from '@/services/users';
import type { Permission } from '@/types/Permission';
import { makeAutoObservable, runInAction } from 'mobx';
import type User from '@/types/User';
// import { defineStore } from 'pinia';

// export const usePermissionStore = defineStore({
//   id: 'permission',
//   state: () => ({
//     permissions: [] as Permission[],
//   }),
//   getters: {
//     getPermissions: (state) => state.permissions,
//   },
//   actions: {
//     async setPermissions() {
//       const { data } = await getUserPermissions();
//       this.permissions = data;
//     },
//     hasPermission(key: string | undefined): boolean {
//       if (!key) return true;
//       const permission: Permission | undefined = this.permissions.find(
//         (p) => p.key === key,
//       );
//       return permission !== undefined;
//     },
//     clearPermissions() {
//       this.permissions = [];
//     },
//   },
// });

class PermissionStore {
  constructor() {
    makeAutoObservable(this);
  }

  _permissions: Permission[] = [];
  _fetchedAdmin: boolean = false;
  isSiteAdmin: boolean = false;
  isCompanyAdmin: boolean = false;

  async fetchPermissions() {
    if (this._permissions.length > 0) return;
    const { data } = await getUserPermissions();
    this.permissions = data as Permission[];
  }

  set permissions(permissions: Permission[]) {
    this._permissions = permissions;
  }
  get permissions(): Permission[] {
    return this._permissions;
  }

  async fetchAdmin() {
    if (this._fetchedAdmin) return;
    this._fetchedAdmin = true;

    const { data }: { data: User | undefined } = await getUser('me');

    runInAction(() => {
      this.isSiteAdmin = data?.isSiteAdmin || false;
      this.isCompanyAdmin = data?.isCompanyAdmin || false;
    });
  }

  hasPermission(key: string | undefined): boolean {
    if (!key) return true;
    const permission: Permission | undefined = this.permissions.find(
      (p) => p.key === key,
    );
    return permission !== undefined;
  }

  clearPermissions() {
    this.permissions = [];
  }

  clearAdmin() {
    this.isSiteAdmin = false;
    this.isCompanyAdmin = false;
    this._fetchedAdmin = false;
  }

  getAdmin() {
    return {
      isSiteAdmin: this.isSiteAdmin || false,
      isCompanyAdmin: this.isCompanyAdmin || false,
    };
  }
}

const PermissionStoreInstance = new PermissionStore();
export default PermissionStoreInstance;
