import React from 'react';
import MuiAutocomplete from '@mui/material/Autocomplete';
import PropTypes from 'prop-types';
import _throttle from 'lodash/throttle';

import {
  TextField,
  Popper,
  Box,
  InputLabel,
  Text,
  CircularProgress,
} from '@/reactComponents/library';

import SearchIcon from '@mui/icons-material/Search';
import CheckIcon from '@mui/icons-material/Check';

import { tailwindInputFix } from '../theme/components/utils';

const AutoPopper = (props) => {
  // eslint-disable-next-line react/destructuring-assignment
  if (props.style?.width) {
    props.style.minWidth = props.style.width;
    //Allow options popper to be wider than Autocomplete comp
    //eslint-disable-next-line react/destructuring-assignment
    delete props.style.width;
  }
  return <Popper {...props} />;
};

AutoPopper.propTypes = {
  style: PropTypes.object,
};

/**
 *
 * @param {func} fetchOptions (search:string,isInfiniteScrolling:bool)  -
 * include if you want to fetch options based on typed value
 * will be throttled to 500ms
 * @param {bool} loading - show loading spinner when fetching options
 * @param {bool} infiniteScrolling - set to true if you want to fetch more options on scroll to bottom of list
 * @param {bool} hasNextPage - set to false when no more options to fetch, must be true/false to use infiniteScrolling
 * @returns
 */
const Autocomplete = (props) => {
  const {
    placeholder,
    autoFocus = false,
    label,
    fetchOptions,
    loading,
    infiniteScrolling,
    hasNextPage,
    slotProps = {},
    TextFieldComp = TextField,
    sx = {},
    error,
    ...restProps
  } = props;

  //handle search
  const searchRef = React.useRef('');
  let searchOptions;
  if (fetchOptions) {
    searchOptions = _throttle(fetchOptions, 500, {
      trailing: true,
      leading: true,
    });
    //show search icon when focused
    sx['&.Mui-focused'] = { '.MuiSvgIcon-root': { display: 'inline-block' } };
  }

  //trigger infinite scroll search more
  if (
    searchOptions &&
    infiniteScrolling &&
    (hasNextPage === true || hasNextPage === false)
  ) {
    if (!slotProps.listbox) slotProps.listbox = {};
    slotProps.listbox.onScroll = (e) => {
      if (infiniteScrolling && hasNextPage) {
        const { scrollTop, scrollHeight, clientHeight } = e.target;

        if (scrollTop + clientHeight >= scrollHeight - 240) {
          if (!loading) {
            searchOptions(searchRef.current, infiniteScrolling);
          }
        }
      }
    };
  }

  const getOptionLabel = restProps.getOptionLabel
    ? restProps.getOptionLabel
    : (option) => option.label ?? option;

  const Component = (
    <MuiAutocomplete
      slots={{ popper: AutoPopper }}
      sx={sx}
      slotProps={{
        popper: {
          placement: 'bottom',
        },
        ...slotProps,
      }}
      openOnFocus={autoFocus}
      renderInput={(params) => {
        const inputPropClass = params.inputProps.className || '';
        if (inputPropClass.includes(tailwindInputFix.trim()) === false) {
          params.inputProps.className += //remove tailwind styling on input
            tailwindInputFix;
        }

        if (searchOptions) {
          if (params.onBlur) {
            console.warn('incoming onBlur event will be overridden');
          }
          if (params.onChange) {
            console.warn('incoming onChange event will be overridden');
          }
          params.onChange = (e) => {
            const v = e.target.value;
            searchRef.current = v;
            searchOptions(v);
          };
          params.onBlur = (e) => {
            searchRef.current = '';
            searchOptions('');
          };
          params.InputProps.startAdornment = (
            <>
              <SearchIcon sx={{ display: 'none', color: 'text.secondary' }} />
              {params.InputProps.startAdornment}
            </>
          );
          if (loading) {
            params.InputProps.endAdornment = (
              <>
                <CircularProgress size={15} />
                {params.InputProps.endAdornment}
              </>
            );
          }
        }

        return (
          <TextFieldComp
            placeholder={placeholder}
            autoFocus={autoFocus}
            error={error}
            {...params}
          />
        );
      }}
      renderOption={(liProps, option, state, ownerState) => {
        const { selected } = state;
        const { key, ...rest } = liProps;
        const { multiple } = ownerState;
        const showCheck = !multiple && selected;

        return (
          <li
            key={key}
            style={{
              width: '100%',
              justifyContent: 'space-between',
            }}
            {...rest}
          >
            <span> {getOptionLabel(option)}</span>
            {showCheck && <CheckIcon color="primary" />}
          </li>
        );
      }}
      {...restProps}
    />
  );

  // If no label, return the component without extra div
  if (!label) return Component;

  return (
    <Box sx={{ width: '100%' }}>
      <InputLabel>
        <Text variant="smMed">{label}</Text>
      </InputLabel>
      {Component}
    </Box>
  );
};

Autocomplete.propTypes = {
  placeholder: PropTypes.string,
  inputProps: PropTypes.object,
  autoFocus: PropTypes.bool,
  label: PropTypes.string,
  fetchOptions: PropTypes.func,
  loading: PropTypes.bool,
  infiniteScrolling: PropTypes.bool,
  hasNextPage: PropTypes.bool,
  slotProps: PropTypes.object,
  TextFieldComp: PropTypes.elementType,
  sx: PropTypes.object,
  error: PropTypes.bool,
};

export default Autocomplete;
