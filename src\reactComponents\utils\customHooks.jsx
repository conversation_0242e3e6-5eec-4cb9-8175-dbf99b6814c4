import React from 'react';
import { useTheme, useMediaQuery } from '@mui/material';

export function useDidMount(func) {
  const didMountRef = React.useRef(true);
  React.useEffect(() => {
    if (didMountRef.current) {
      func();
      didMountRef.current = false;
    }
  }, [func]);
}

export function useWillUnmount(func) {
  const willUnmountRef = React.useRef(false);
  React.useEffect(() => {
    return () => {
      willUnmountRef.current = true;
    };
  }, []);

  React.useEffect(() => {
    return () => {
      if (willUnmountRef.current === true) {
        func();
        willUnmountRef.current = false;
      }
    };
  }, [func]);
}

export const useIsMobile = () => {
  const theme = useTheme();
  return useMediaQuery(theme.breakpoints.down('md'));
};
/**
 * Was Was hoping to make a function that would prevent us from having things render multiple times. that shouldn't
 *  didn't realize that that doesn't work because vite doesn't redo the entire thing so the counts don't reset.
 * Leaving this here as a reminder to maybe look into it but as of now this is unused
 *  outside of way too many useEffects to watch the other deps I don't have a good solution right now.
 */
export const useShouldRenderOnce = (dep, depName) => {
  const renderCount = React.useRef(0);
  React.useEffect(() => {
    renderCount.current += 1;

    if (renderCount.current > 1) {
      console.error(`${depName} rendered more than once`);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dep]);
};
