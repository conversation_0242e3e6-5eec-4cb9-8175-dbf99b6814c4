import { applyPureVueInReact } from 'veaury';
import CompanyFormViewVue from '../../../views/companies/CompanyFormView.vue';
import { useAuth, useReactRouter } from '../../AppHooks';

const ReactCompanyFormView = applyPureVueInReact(CompanyFormViewVue);

const CompanyFormView = () => {
  useAuth();
  const { navigate, route } = useReactRouter();
  return <ReactCompanyFormView route={route} navigate={navigate} />;
};

export default CompanyFormView;
