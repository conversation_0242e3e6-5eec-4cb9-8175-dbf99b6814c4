<template>
  <div class="flex justify-center mx-auto w-full py-2 px-5">
    <div class="max-w-xl mb-32">
      <h2 class="text-xl text-center font-bold mb-3">Project Onboarding</h2>
      <p v-if="isAnyFieldPrefilled" class="text-sm mb-4">
        Your production supervisor has prefilled information. You may not make
        edits to prefilled fields. If anything does not look correct, please
        reach out to your production supervisor.
      </p>
      <div v-if="integrationLive">
        <h3
          class="text-lg font-medium leading-6 text-gray-900 dark:text-gray-400"
        >
          Work Location
        </h3>
        <Dropdown
          class="grow mt-1"
          v-model="projectOnboarding.workLocation"
          :loading="loadingProjectOnboarding"
          display-name="shootLocation.locationName"
          :menu-items="projectShootLocations"
          @change="
            () => {
              updateUnionList();
              projectOnboarding.unions = [];
              projectOnboarding.occupation = undefined;
              selectedUnion = null;
            }
          "
        >
          <template #label>
            {{
              `${projectOnboarding.workLocation?.shootLocation.locationName} (${projectOnboarding.workLocation?.zip})`
            }}
          </template>
          <template #item="{ value: projectWorkLocation }">
            {{
              `${projectWorkLocation?.shootLocation?.locationName} (${projectWorkLocation?.zip})`
            }}
          </template>
        </Dropdown>
      </div>
      <div
        v-if="!integrationLive || projectOnboarding.workLocation?.shootLocation"
        class="py-2"
      >
        <div>
          <h3
            class="text-lg font-medium leading-6 text-gray-900 dark:text-gray-400"
          >
            Unions
          </h3>
          <div class="flex gap-1 columns-2">
            <div class="w-full p-2">
              <RadioButton
                v-model="selectedUnionOption"
                label="Union"
                value="Union"
                name="selectUnion"
                @update:modelValue="onUnionChange"
                :disabled="loadingUnions || unions.length == 0"
              />
              <Dropdown
                v-model="selectedUnion"
                :loading="loadingUnions"
                :menu-items="unions"
                display-name="name"
                v-if="selectedUnionOption == 'Union'"
                @change="addUnion"
                class="mt-1.5"
                data-testid="union-dropdown"
              />
            </div>
            <div class="w-full p-2">
              <RadioButton
                v-model="selectedUnionOption"
                label="Non-Union"
                value="NonUnion"
                name="selectUnion"
                @update:modelValue="onUnionChange"
                :disabled="
                  loadingUnions ||
                  nonUnion?.name?.toUpperCase() !== NON_UNION_NAME
                "
              />
            </div>
          </div>
        </div>
      </div>

      <div
        v-if="
          !integrationLive ||
          (projectOnboarding.workLocation &&
            (projectOnboarding.unions?.length || 0) > 0)
        "
        class="flex grow mb-1"
      >
        <Combobox
          label="Occupation"
          v-model="projectOnboarding.occupation"
          :items="occupations"
          :search="occupationSearch"
          :loading="loadingOccupations"
          :infiniteScroll="true"
          @change="handleOccupationChange"
          :pagination="paginationOccupation"
          @update:pagination="(pagination: Pagination) => (updatePagination(pagination))"
          @update:search="(x: any) => (occupationSearch = x)"
          class="w-full"
          display-name="name"
          data-testid="occupation-dropdown"
        >
          <template #label="{ value: modelValue }">
            {{ modelValue?.name || 'Select' }}
          </template>
          <template #item="{ value: item }">
            {{ item?.name }}
          </template>
        </Combobox>
      </div>
      <div
        v-if="
          projectOnboarding.requiresHireLocation &&
          projectOnboarding.occupation?.name
        "
      >
        <Dropdown
          label="Hire Location"
          v-model="projectOnboarding.hireLocation"
          :loading="loadingHireLocation"
          :menu-items="hireLocations"
          display-name="name"
          @change="handleHireLocationChange"
        >
          <template #label>
            {{ projectOnboarding.hireLocation?.name }}
          </template>
          <template #item="{ value: item }">
            {{ item.name }}
          </template>
        </Dropdown>
      </div>
      <div
        v-if="
          !projectOnboarding.requiresHireLocation &&
          loadingHireLocation &&
          projectOnboarding.occupation?.name
        "
        class="flex justify-center"
      >
        <Spinner class="w-4 h-4" />
      </div>
      <div class="py-1 border-b-2 dark:border-gray-500" />
      <div class="flex space-between space-x-2 pt-4">
        <TextInput
          v-model="projectOnboarding.rate"
          :disabled="isFieldPrefilled.rate"
          label="Rate ($)"
          type="number"
          data-testid="rate-input"
        />
        <Dropdown
          data-testid="rateType-dropdown"
          v-model="projectOnboarding.rateType"
          :disabled="isFieldPrefilled.rateType"
          label="Rate Type"
          :loading="loadingRateTypes"
          :menu-items="rateTypes"
          display-name="crew_display_name"
        />
        <DatePicker
          v-model="projectOnboarding.startDate as DateTime"
          label="Start Date"
          :disabled="isFieldPrefilled.startDate"
        />
      </div>
      <div class="flex space-x-2 columns-2">
        <TextInput
          v-if="supervisorView"
          class="mt-2 w-full"
          label="AICP/Line#"
          v-model="projectOnboarding.lineNumber"
          :disabled="!lineNumberFieldsAllowed"
        />
        <TextInput
          v-if="supervisorView"
          class="mt-2 w-full"
          label="AICP/Line# - SHOOT"
          v-model="projectOnboarding.shootLineNumber"
          :disabled="!lineNumberFieldsAllowed"
        />
      </div>
      <Dropdown
        v-if="supervisorView"
        label="Department"
        v-model="projectOnboarding.department"
        :menu-items="project.departments"
        display-name="typeId"
      >
        <template #label>
          {{ projectOnboarding.department?.type?.name }}
        </template>
        <template #item="{ value: item }">
          {{ item?.type?.name }}
        </template>
      </Dropdown>
      <div v-else>
        <h2 class="dark:text-gray-400 text-sm font-semibold">Department</h2>
        <div>{{ projectOnboarding.department?.type?.name || 'None' }}</div>
      </div>
      <div class="flex justify-center">
        <Button class="mt-4" @click="save" color="primary" :loading="loading"
          >Save</Button
        >
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Button from '@/components/library/Button.vue';
import Combobox from '@/components/library/Combobox.vue';
import DatePicker from '@/components/library/DatePicker.vue';
import Dropdown from '@/components/library/Dropdown.vue';
import RadioButton from '@/components/library/RadioButton.vue';
import Spinner from '@/components/library/Spinner.vue';
import TextInput from '@/components/library/TextInput.vue';
import { getStates } from '@/services/address';
import {
  getContractSettingsAndScheduleInfo,
  getCurrentMember,
  getTrackingDetails,
  listProjectHireLocations,
  listProjectOccupations,
  listProjectShootLocations,
  listProjectUnions,
  updateMember,
} from '@/services/project';
import {
  getProjectMemberById,
  updateProjectMember,
} from '@/services/project-members';
import { listRateTypes } from '@/services/rate-types';
import { getInvite } from '@/services/users';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import type { ContractSettingsAndScheduleInfo } from '@/types/ContractSettingsAndScheduleInfo';
import type Occupation from '@/types/Occupation';
import type { Pagination } from '@/types/Pagination';
import type Project from '@/types/Project';
import type ProjectMember from '@/types/ProjectMember';
import type ProjectShootLocation from '@/types/ProjectShootLocation';
import type { RateType } from '@/types/RateType';
import type State from '@/types/State';
import type { Union } from '@/types/Union';
import axios from 'axios';
import { DateTime } from 'luxon';
import {
  computed,
  onMounted,
  ref,
  watch,
  type ComputedRef,
  type PropType,
  type Ref,
} from 'vue';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';
import type { SnackType } from '@/types/Snackbar';
import { TrackingKeysEnum } from '@/types/TimecardDay';
import { rateTypeFilter } from '@/utils/filterRateTypes';

// Props
const props = defineProps({
  refresh: {
    type: Function,
    required: true,
  },
  project: {
    type: Object as PropType<Project>,
    required: true,
  },
  supervisorView: {
    type: Boolean,
    required: false,
    default: false,
  },
  navigate: {
    type: Function,
    required: true,
  },
  route: {
    type: Object as PropType<ParsedRoute>,
    required: true,
  },
});

const emit = defineEmits(['refresh']);
const NON_UNION_NAME = 'NON';
const projectOnboarding: Ref<Partial<ProjectMember>> = ref({
  rate: 0,
  rateType: undefined,
  occupation: undefined,
  startDate: DateTime.now().toUTC(),
  hireLocationCity: undefined,
  hireLocationState: undefined,
  workLocation: undefined,
  lineNumber: '',
  shootLineNumber: '',
  department: undefined,
  requiresHireLocation: undefined,
  unions: [],
});
const loadingProjectOnboarding: Ref<boolean> = ref(true);

// not used anymore coz invitation onboarding isn't used
const isFieldPrefilled = ref({
  rate: false,
  rateType: false,
  occupationId: false,
  startDate: false,
  hireLocationCity: false,
  hireLocationState: false,
});

const occupations: Ref<Occupation[]> = ref([]);
const loadingOccupations: Ref<boolean> = ref(true);
const rateTypes: Ref<RateType[]> = ref([]);
const states: Ref<State[]> = ref<State[]>([]);
const occupationSearch = ref('');
const paginationOccupation = ref<Pagination>({
  page: 1,
  limit: 50,
  total: 0,
});
const loading = ref(false);

const hireLocations: Ref<any[]> = ref([]);

const triggerSnackbar = (msg: string, duration: number, type: SnackType) => {
  SnackbarStore.triggerSnackbar(msg, duration, type);
};

const unions = ref([]);
const loadingUnions: Ref<boolean> = ref(false);
const addUnionModal = ref(false);
const selectedUnion: Ref<Union | null> = ref(null as Union | null);
const lineNumberFieldsAllowed: Ref<boolean> = ref(false);

const contractSettingsAndScheduleInfo: Ref<ContractSettingsAndScheduleInfo | null> =
  ref(null);
const nonUnion = ref({} as any);
const selectedUnionOption = ref('');

watch(occupationSearch, async (val) => {
  paginationOccupation.value = resetPagination(paginationOccupation.value);
  occupations.value = [];
  await getOccupations();
});

const projectMemberId: ComputedRef<number | null> = computed(() => {
  if (!props.supervisorView) {
    return null;
  }
  const projectMemberId = props.route.params?.projectMemberId;
  return projectMemberId ? parseInt(projectMemberId as string) : null;
});

const updateUnionList = async () => {
  unions.value = [];
  loadingUnions.value = true;
  try {
    const { data: unionData } = await listProjectUnions(
      props.project.id!,
      projectOnboarding.value?.workLocation?.payrollProjectLocationId || null,
    );
    if (!projectOnboarding.value?.unions || !unionData) return;
    nonUnion.value = unionData.find(
      (union: any) => union.name?.toUpperCase() === NON_UNION_NAME,
    );
    unions.value = unionData.filter(
      (union: any) => union.name?.toUpperCase() !== NON_UNION_NAME,
    );
  } catch (err) {
    triggerSnackbar('Failed to load unions.', 3500, 'error');
  }
  loadingUnions.value = false;
};

const onUnionChange = async () => {
  if (selectedUnionOption.value === 'NonUnion') {
    selectedUnion.value = nonUnion.value;
    addUnion();
  } else {
    selectedUnion.value = null;
    projectOnboarding.value.unions = [];
  }
};

const addUnion = async () => {
  if (!selectedUnion.value?.id || !projectOnboarding.value.unions) {
    triggerSnackbar('Must select union.', 2500, 'error');
    return;
  }
  projectOnboarding.value.unions = [selectedUnion.value];
  addUnionModal.value = false;
  occupations.value = [];
  projectOnboarding.value.occupation = undefined;
  paginationOccupation.value = resetPagination(paginationOccupation.value);
  getOccupations();
};

const removeUnion = async (union: Union) => {
  if (!union?.id || !projectOnboarding.value.unions) {
    return;
  }
  projectOnboarding.value.unions = projectOnboarding.value.unions.filter(
    (u) => u.id !== union?.id,
  );
  await updateUnionList();
  occupations.value = [];
  projectOnboarding.value.rateType = undefined;
  projectOnboarding.value.occupation = undefined;
  paginationOccupation.value = resetPagination(paginationOccupation.value);
  await getOccupations();
};

const isAnyFieldPrefilled = computed(() => {
  return Object.values(isFieldPrefilled.value).some((x) => x);
});

const populateOptions = async () => {
  await Promise.all([getRateTypes(), fetchStates(), updateUnionList()]);
};

const fetchStates = async () => {
  const {
    data: { data: statesData },
  } = await getStates();
  states.value = statesData;
};

const getOccupations = async () => {
  loadingOccupations.value = true;
  try {
    const { data: occupationsData } = await listProjectOccupations(
      props.project.id!,
      projectOnboarding.value.workLocation?.payrollProjectLocationId || null,
      projectOnboarding.value.unions || null,
      occupationSearch.value,
      paginationOccupation.value,
    );
    if (paginationOccupation.value.page === 1) {
      occupations.value = [];
    }
    occupations.value.push(...occupationsData.jobTitleList);
    paginationOccupation.value.total = occupationsData.totalCount;
  } catch (err) {
    console.warn('Failed to list occupations', err);
  }
  loadingOccupations.value = false;
};

const projectShootLocations: Ref<ProjectShootLocation[]> = ref(
  [] as ProjectShootLocation[],
);
const loadingShootLocations: Ref<Boolean> = ref(false);

const loadShootLocations = async () => {
  loadingShootLocations.value = true;
  projectShootLocations.value = [];
  try {
    const { data } = await listProjectShootLocations(props.project.id!);
    projectShootLocations.value = data;
  } catch (err) {
    console.warn('Failed to list shoot locations', err);
  }
  loadingShootLocations.value = false;
};

const loadingRateTypes: Ref<boolean> = ref(false);

const getRateTypes = async () => {
  loadingRateTypes.value = true;
  rateTypes.value = [];
  const { data: rateTypesData } = await listRateTypes();

  if (contractSettingsAndScheduleInfo.value && rateTypesData.length > 0) {
    rateTypes.value = rateTypeFilter(
      contractSettingsAndScheduleInfo.value,
      rateTypesData,
    );
  }
  // if (contractSettingsAndScheduleInfo.value?.isOnCallIndicator) {
  //   rateTypes.value = rateTypesData.filter((rt: any) => rt.key === 'daily');
  // } else {
  //   rateTypes.value = rateTypesData;
  // }
  loadingRateTypes.value = false;
};

const setDepartment = (departmentId: number) => {
  let department = null;
  if (departmentId) {
    department = props.project.departments.find((d) => d.id === departmentId);
  }
  if (!department) {
    department = props.project.departments.find(
      (d) => d.type.key === 'general',
    );
  }
  projectOnboarding.value.department = department;
};

const getProjectMember = async () => {
  loadingProjectOnboarding.value = true;
  let projectOnboardingData;

  if (props.supervisorView && projectMemberId.value) {
    const { data } = await getProjectMemberById(projectMemberId.value!);
    projectOnboardingData = data;
  } else {
    const { data } = await getCurrentMember(props.project.id!);
    projectOnboardingData = data;
  }
  if (projectOnboardingData) {
    projectOnboarding.value = {
      ...projectOnboardingData,
      startDate: projectOnboardingData.startDate
        ? projectOnboardingData.startDate.toUTC()
        : DateTime.now().toUTC(),
    };
  }
  if (
    projectOnboardingData?.unions &&
    projectOnboardingData.unions.length > 0
  ) {
    projectOnboardingData.unions[0]?.name?.toUpperCase() === NON_UNION_NAME
      ? (selectedUnionOption.value = 'NonUnion')
      : (selectedUnionOption.value = 'Union');
    selectedUnion.value = projectOnboardingData.unions[0];
  }
  loadingProjectOnboarding.value = false;
};

const save = async () => {
  if (loading.value || loadingHireLocation.value) return;
  loading.value = true;
  if (!projectOnboarding.value.workLocation) {
    triggerSnackbar('Please select a work location.', 2500, 'error');
    loading.value = false;
    return;
  }

  if (
    projectOnboarding.value.requiresHireLocation &&
    !projectOnboarding.value.hireLocation
  ) {
    triggerSnackbar('Please select a hire location.', 2500, 'error');
    loading.value = false;
    return;
  }
  try {
    if (props.supervisorView && projectMemberId.value) {
      await updateProjectMember(projectMemberId.value, projectOnboarding.value);
      props.refresh();
      props.navigate(-1);
    } else {
      await updateMember(props.project.id!, projectOnboarding.value);
      props.navigate({
        pathname: `/projects/${props.project.hashId}`,
      });
    }
  } catch (err) {
    if (axios.isAxiosError(err)) {
      const msg = err.response?.data?.['errors']?.[0]?.message;
      triggerSnackbar(msg, 2500, 'error');
    } else {
      triggerSnackbar(err as string, 2500, (err as any).message || 'error');
    }
  } finally {
    loading.value = false;
  }
};

const parseInviteData = async () => {
  const inviteCode = props.route.query?.code as string;
  if (!inviteCode) {
    return;
  }
  const inviteData = (await getInvite(inviteCode)).data;
  projectOnboarding.value.rate = inviteData.rate || 0;
  isFieldPrefilled.value.rate = !!inviteData.rate;

  projectOnboarding.value.hireLocationCity = inviteData.hireLocationCity || '';
  isFieldPrefilled.value.hireLocationCity = !!inviteData.hireLocationCity;

  projectOnboarding.value.startDate =
    inviteData.startDate?.toUTC() || DateTime.now().toUTC();
  isFieldPrefilled.value.startDate = !!inviteData.startDate;

  projectOnboarding.value.rateType =
    rateTypes.value.find((rt) => rt.id === inviteData.rateTypeId) || undefined;
  isFieldPrefilled.value.rateType = !!inviteData.rateTypeId;

  projectOnboarding.value.occupationId = inviteData.occupationId || undefined;
  isFieldPrefilled.value.occupationId = !!inviteData.occupationId;

  projectOnboarding.value.hireLocationState =
    states.value.find((s) => s.id === inviteData.hireLocationStateId) ||
    undefined;
  isFieldPrefilled.value.hireLocationState = !!inviteData.hireLocationStateId;

  setDepartment(inviteData.departmentId || 0);
};

let loadingHireLocation = ref(false);

const handleOccupationChange = async (onMountedCall: boolean = false) => {
  const union: any = projectOnboarding.value.unions?.[0];
  if (!union) {
    return;
  }
  if (!onMountedCall) {
    projectOnboarding.value.rateType = undefined;
    projectOnboarding.value.hireLocation = null;
    projectOnboarding.value.hireLocationId = null;
  }
  await loadContractSettingsAndScheduleInfo();
  if (contractSettingsAndScheduleInfo.value!.isHireLocationRequired) {
    loadingHireLocation.value = true;
    const { data } = await listProjectHireLocations(
      props.project.id!,
      projectOnboarding.value.workLocation?.payrollProjectLocationId!,
      union?.castAndCrewId || union.id!,
      projectOnboarding.value.occupation?.payrollOccupationCode!,
    );
    loadingHireLocation.value = false;
    hireLocations.value = data.hireLocationList;
  } else {
    projectOnboarding.value.hireLocation = null;
    projectOnboarding.value.hireLocationId = null;
  }
};

const handleHireLocationChange = async () => {
  if (contractSettingsAndScheduleInfo.value!.isHireLocationRequired) {
    await loadContractSettingsAndScheduleInfo();
  }
};

const integrationLive = computed(() => {
  return props.project.productionCompany?.castAndCrewId;
});

const updatePagination = (pagination: Pagination): void => {
  paginationOccupation.value = pagination;
  getOccupations();
};
const resetPagination = (pagination: Pagination) => {
  pagination.page = 1;
  return pagination;
};

const loadContractSettingsAndScheduleInfo = async () => {
  try {
    const { data } = await getContractSettingsAndScheduleInfo(
      props.project.id!,
      projectOnboarding.value.workLocation?.payrollProjectLocationId!,
      projectOnboarding.value.occupation?.payrollOccupationCode!,
      projectOnboarding.value.startDate?.toISODate()!,
      projectOnboarding.value.unions?.[0]?.name!,
      projectOnboarding.value.unions?.[0]?.castAndCrewId! ||
        projectOnboarding.value.unions?.[0].id!,
      projectOnboarding.value.hireLocation?.payrollHireLocationId!,
    );
    contractSettingsAndScheduleInfo.value = {
      ...data.item,
      isHireLocationRequired: data.isHireLocationRequired,
    };
    projectOnboarding.value.requiresHireLocation = data.isHireLocationRequired;
    getRateTypes();
  } catch (err) {
    triggerSnackbar(err as string, 2500, 'error');
  }
};

const getTrackingHeaderDetails = async () => {
  try {
    const { data: trackingKeys } = await getTrackingDetails(props.project.id!);
    const trackingKeyHeaderDetails = trackingKeys.trackingKeyHeaderDetails;
    if (
      trackingKeyHeaderDetails !== null &&
      trackingKeyHeaderDetails.length > 0
    ) {
      trackingKeyHeaderDetails.forEach((eachTrackingKey: any) => {
        if (
          eachTrackingKey?.name === TrackingKeysEnum.KEY_TRACKKEY &&
          eachTrackingKey?.allowed
        ) {
          lineNumberFieldsAllowed.value = true;
        }
      });
    }
  } catch (err) {
    console.warn('Failed to get tracking details', err);
  }
};

// Lifecycle hooks
onMounted(async () => {
  await getTrackingHeaderDetails(); // needed in new onboarding
  await getProjectMember(); // needed
  await populateOptions(); // needed
  await loadShootLocations(); // needed, working location
  await parseInviteData(); // not sure?
  await setDepartment(
    //  needed
    props.route.query.departmentId
      ? parseInt(props.route.query.departmentId.toString())
      : projectOnboarding.value.department?.id
      ? projectOnboarding.value.department.id
      : 0,
  );
  getOccupations(); // needed
  handleOccupationChange(true); // initial mount occupation setup, needed
});
</script>
