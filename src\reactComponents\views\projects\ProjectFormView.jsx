import { applyPureVueInReact } from 'veaury';
import ProjectFormViewVue from '../../../views/projects/ProjectFormView.vue';
import { useAuth, useReactRouter } from '../../AppHooks';
const ReactProjectFormView = applyPureVueInReact(ProjectFormViewVue);

const ProjectFormView = () => {
  useAuth();
  const { navigate, route } = useReactRouter();
  const editMode = !!route?.match?.handle?.editMode;
  return (
    <ReactProjectFormView
      editMode={editMode}
      route={route}
      navigate={navigate}
    />
  );
};

export default ProjectFormView;
