import {
  ClockIcon,
  DocumentTextIcon,
  MagnifyingGlassIcon,
  UserIcon,
} from '@heroicons/react/24/outline';

export const actions = [
  {
    title: 'User Manual',
    url: 'https://full-workshop-49c.notion.site/Customer-How-To-Guides-97feada33e944846857bb71c71d1a31b?pvs=74',
    icon: DocumentTextIcon,
    iconForeground: 'text-indigo-700',
    iconBackground: 'bg-indigo-50 dark:bg-indigo-100',
    body: 'Confused where to start? Click here to take a look at our user manual.',
  },
  {
    title: 'Projects',
    path: 'projects',
    icon: ClockIcon,
    iconForeground: 'text-teal-700',
    iconBackground: 'bg-teal-50 dark:bg-teal-100',
    body: 'Click here to navigate to your projects.',
  },
  {
    title: 'Profile',
    path: 'profile',
    icon: UserIcon,
    iconForeground: 'text-purple-700',
    iconBackground: 'bg-purple-50 dark:bg-purple-100',
    body: 'Manage your personal info.',
  },
  {
    title: 'FAQ',
    path: 'faq',
    icon: MagnifyingGlassIcon,
    iconForeground: 'text-sky-700',
    iconBackground: 'bg-sky-50 dark:bg-sky-100',
    body: 'View frequently asked questions.',
  },
];
