{"id": 1910, "productionCompanyId": 141, "typeId": 1, "documentTemplateId": 26548, "loanOutSettingId": 1, "isEnabled": true, "isOptional": false, "locationSettingId": 1, "locationInclusionStatusId": 1, "unionInclusionStatusId": 1, "locationSetting": {"id": 1, "name": "All", "key": "all", "description": "Should be generated for all locations."}, "unionInclusionStatus": {"id": 1, "name": "Including", "key": "including", "description": "Including the selections"}, "type": {"id": 1, "key": "non_union", "name": "Non Union", "description": "Only for non union project members"}, "loanOutSetting": {"id": 1, "name": "<PERSON><PERSON> Out and <PERSON> Loan Out", "key": "loan_out_and_non_loan_out", "description": "Should be generated regardless of if the user is using a loan out or not."}, "locationInclusionStatus": {"id": 1, "name": "Including", "key": "including", "description": "Including the selections"}, "locations": [], "unions": [], "documentTemplate": {"id": 26548, "name": "documentTemplateName", "documentId": "875f897d-ec52-4bdc-8529-ee31cd5f8eaa", "schema": {"schemas": [{}]}, "sendToPayroll": true, "isCustom": true, "numberOfPages": 1, "countersignSchema": {"schemas": [{}]}}}