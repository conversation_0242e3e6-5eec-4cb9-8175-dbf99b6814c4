import React, { forwardRef, useEffect, useRef, useState, useMemo } from 'react';

import { Clear } from '@mui/icons-material';
import { Paper, IconButton, Tooltip } from '@mui/material';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import { renderTimeViewClock } from '@mui/x-date-pickers/timeViewRenderers';
// move this get to lib utils
import { get } from '../../viewsV2/ProjectAdminMemberTimecardV2/timecardUtils';
import TextFieldRaw from '../TextFieldRaw';
import KeyboardControlBox, { findNextTabCell } from './CellKeyboardControl';
import PropTypes from 'prop-types';

const TimePickerViewRenderers = (params) => {
  const { timeMinuteIncrement, colDef, row, api, value, update } = params;
  const inputRef = useRef();
  const [open, setOpen] = useState(false);
  const field = colDef?.field;

  const inArray = colDef?.inArray;
  const headerName = colDef?.headerName;
  const showClearBtn = colDef?.showClearBtn;
  const isHidden = colDef?.isHidden;
  const timeValue = inArray ? get(row, field) : value;

  const initialLoad = useRef(true);
  const localValueRef = useRef(timeValue);
  const updating = useRef(false);

  const hidden = useMemo(() => {
    if (isHidden) {
      return isHidden(row);
    }
    return false;
  }, [row, isHidden]);

  let errMsg = '';
  const displayErrors = params.api?.state?.displayErrors || {};
  const dayId = `day${params.row.id}`;
  if (displayErrors?.[dayId]?.[field]) {
    errMsg = displayErrors[dayId][field]?.message || '';
  }

  useEffect(() => {
    if (updating.current) {
      updating.current = false;
      // work around, will need to change to custom input with time clock picker later or otherwise
      setTimeout(() => {
        inputRef?.current?.focus();
      }, 400);
    }
    localValueRef.current = timeValue;
  }, [timeValue]);

  useEffect(() => {
    if (params.hasFocus) {
      inputRef?.current?.focus();
      if (initialLoad.current) {
        setOpen(true);
        initialLoad.current = false;
      }
    }
    initialLoad.current = false;
  }, [params.hasFocus, hidden]);

  const updateTime = () => {
    let newValue = localValueRef.current;
    if (newValue === undefined) return;

    const isWorkDaysGrid = !!api;
    if (newValue) {
      // Ensure date(mm/dd/yy) matches timecard
      // DateTime's set method returns a new DateTime object, so it's not changing the original DateTime object
      newValue = row.date.set({
        hour: newValue.hour,
        minute: newValue.minute,
      });
    }

    api.setEditCellValue({
      id: row.id,
      field,
      value: newValue,
    });
    update({
      field: field,
      value: newValue,
      rowId: row.id,
      isWorkDaysGrid: isWorkDaysGrid,
      inArray: inArray,
      api: api,
    });
  };

  return (
    <Tooltip
      arrow
      variant="html"
      enterDelay={500}
      placement="top"
      title={errMsg ? errMsg : ''}
    >
      <KeyboardControlBox
        sx={(theme) => ({
          display: 'flex',
          alignItems: 'center',
          padding: '0 4px',
          width: '100%',
          ...(errMsg
            ? {
                border: `1px solid ${theme.palette.error[500]}`,
                borderRadius: '4px',
              }
            : {}),
        })}
        tabIndex={0}
        api={api}
        rowId={row.id}
        colDef={colDef}
      >
        {!hidden && (
          <TimePicker
            timezone="UTC"
            placeholder={headerName}
            minutesStep={timeMinuteIncrement}
            inputRef={inputRef}
            open={open}
            onClose={() => {
              setOpen(false);
              updateTime();
            }}
            closeOnSelect={true}
            onOpen={() => {
              setOpen(true);
            }}
            disableOpenPicker
            onChange={(newValue, ctx) => {
              // ctx may have validation error
              updating.current = true;
              localValueRef.current = newValue;
            }}
            value={localValueRef.current || null}
            viewRenderers={{
              hours: renderTimeViewClock,
              minutes: renderTimeViewClock,
            }}
            slotProps={{
              desktopTrapFocus: {
                disableEnforceFocus: true,
                disableRestoreFocus: true,
              },
              popper: {
                sx: {
                  zIndex: 1501, // higher than tooltip zIndex
                },
              },
              textField: {
                onKeyDown: (e) => {
                  if (e.key !== 'Tab') {
                    e.preventDefault();
                    // e.stopPropagation() prevent user from use keyboard to type invalid time
                  }
                },
                inputProps: {
                  style: { padding: '4px' },
                },
                onClick: (e) => {
                  setOpen(true);
                },
              },
              actionBar: { sx: { display: 'none' } },
              toolbar: {
                hidden: false,
                sx: {
                  '& .Mui-selected': {
                    fontWeight: 600,
                  },
                },
              },
            }}
            slots={{
              textField: TextFieldRaw,
              desktopPaper: WithRefAndProps(CustomPaper, {
                setOpen,
                api,
                rowId: row.id,
                field,
              }),
            }}
          />
        )}
        {showClearBtn && !hidden && (
          <IconButton
            onClick={() => {
              localValueRef.current = null;
              updateTime();
            }}
            tabIndex={-1} // prevent tabbing to the clear button
          >
            <Clear />
          </IconButton>
        )}
      </KeyboardControlBox>
    </Tooltip>
  );
};

const WithRefAndProps = (Component, props) => {
  return forwardRef((innerProps, ref) => (
    <Component {...props} {...innerProps} ref={ref} />
  ));
};

const CustomPaper = forwardRef((props, ref) => {
  const { setOpen, rowId, api, field, ...otherProps } = props;
  return (
    <Paper
      {...otherProps}
      ref={ref}
      style={{
        // copy from mui in inspect for now
        boxShadow: `3px 5px 5px -3px rgba(0,0,0,0.2),0px 8px 10px 1px rgba(0,0,0,0.14),0px 3px 14px 2px rgba(0,0,0,0.12)`,
      }}
      onKeyDown={(e) => {
        if (e.key === 'Tab') {
          e.preventDefault();
          e.stopPropagation();
          setOpen(false);
          setTimeout(() => {
            findNextTabCell(api, rowId, field);
          }, 400);
        }
      }}
    />
  );
});

CustomPaper.propTypes = {
  setOpen: PropTypes.func,
  rowId: PropTypes.number,
  api: PropTypes.object,
  field: PropTypes.string,
};

const TimepickerCell = (params) => <TimePickerViewRenderers {...params} />;

export default TimepickerCell;
