export enum WorkStatusKeys {
  KEY_WORK = 'WORK',
  KEY_IDLE = 'IDLE',
  KEY_TRAVEL = 'TRAVEL',
  KEY_HNW = 'HNW',
  KEY_DOWN = 'DOWN',
  KEY_PAID_IDLE = 'PAID IDLE',
  KEY_CANCELPAY_REQ_WITHPW = 'CANCELPAY-REQ WITHPW',
  KEY_CANCELPAY_REQ_NOPW = 'CANCELPAY-REQ NOPW',
  KEY_CANCELPAY_NOPW = 'CANCELPAY-NOPW',
  KEY_CANCELPAY_NOREQ_NOPW = 'CANCELPAY-NOREQ NOPW',
  KEY_DGA_IDLE_PEN_ONLY = 'DGA IDLE PEN ONLY',
  KEY_HOL = 'HOL',
  KEY_HOLNOPAY = 'HOL NO PAY',
  KEY_SHOOT = 'SHOOT',
  KEY_PREP = 'PREP',
  KEY_TECH_SCOUT = 'TECH SCOUT',
  KEY_WRAP = 'WRAP',
  KEY_7TH_AFTER_IDLE = '7TH AFTER IDLE',
}

//TODO - create a new enum for the work status keys that do have time entry
//     - WorkStatusKeys should be a combination of those two.
export enum WorkStatusKeysNoTimeEntry {
  KEY_IDLE = 'IDLE',
  KEY_PAID_IDLE = 'PAID IDLE',
  KEY_TRAVEL = 'TRAVEL',
  KEY_HNW = 'HNW',
  KEY_DOWN = 'DOWN',
  KEY_CANCELPAY_NOREQ_NOPW = 'CANCELPAY-NOREQ NOPW',
  KEY_CANCELPAY_REQ_WITHPW = 'CANCELPAY-REQ WITHPW',
  KEY_DGA_IDLE_PEN_ONLY = 'DGA IDLE PEN ONLY',
  KEY_HOL = 'HOL',
  KEY_HOLNOPAY = 'HOL NO PAY',
}

export interface WorkStatus {
  id: number;
  key: string;
  name: string;
  description: string;
}
