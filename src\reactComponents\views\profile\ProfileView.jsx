// import React from 'react';
import { useNavigate } from 'react-router';
import { applyPureVueInReact } from 'veaury';
import ProfileVue from '@/views/ProfileView.vue';
import { useAuth } from '@/reactComponents/AppHooks';
const ProfileViewReact = applyPureVueInReact(ProfileVue);

const ProfileView = () => {
  useAuth();

  const navigateTo = useNavigate();

  const navigate = (path) => {
    navigateTo(path);
  };

  return <ProfileViewReact navigate={navigate} />;
};

export default ProfileView;
