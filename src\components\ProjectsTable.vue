<template>
  <div>
    <TableFilters :filters="filters" @update:filters="filters = $event" />
    <div class="bg-white dark:bg-gray-900 shadow sm:rounded-md">
      <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-500">
        <li
          v-for="(project, projectIndex) in projects"
          :key="project.id"
          data-testid="projects-list"
        >
          <a
            class="block hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
            :class="{
              'rounded-t-md': projectIndex === 0,
              'rounded-b-md': projectIndex === projects.length - 1,
            }"
            @click="goToProject(project.hashId!)"
          >
            <div class="px-4 py-4 sm:px-6">
              <div class="flex items-center justify-between">
                <p
                  class="truncate text-sm font-medium text-indigo-600 dark:text-indigo-300"
                  :data-testid="`project-name-${project.id}`"
                >
                  {{ project.name }}
                </p>
                <div
                  class="ml-2 flex flex-shrink-0"
                  :data-testid="`project-number-${project.id}`"
                >
                  <Badge :text="project.number" type="success" />
                </div>
              </div>
              <div class="mt-2 sm:flex sm:justify-between">
                <div class="flex justify-start space-x-2">
                  <div class="sm:flex">
                    <p
                      class="flex items-center text-sm text-gray-500 dark:text-gray-400"
                      :data-testid="`project-company-${project.id}`"
                    >
                      <Icon name="briefcase" class="mr-1.5 w-4 h-4" />
                      {{ project?.productionCompany?.name }}
                    </p>
                  </div>
                  <div class="sm:flex">
                    <p
                      class="flex items-center text-sm text-gray-500 dark:text-gray-400"
                      :data-testid="`project-type-${project.id}`"
                    >
                      <Icon name="film" class="mr-1.5 w-4 h-4" />
                      {{ project?.type?.name }}
                    </p>
                  </div>
                </div>
                <div
                  class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400 sm:mt-0"
                  :data-testid="`project-endsOn-${project.id}`"
                >
                  <Icon name="calendar" class="mr-1.5 w-4 h-4" />
                  <p>
                    Ends On
                    {{ ' ' }}
                    <time :datetime="(project.endsAt.toISOTime() as string)">{{
                      project.endsAt.toFormat('MM/dd')
                    }}</time>
                  </p>
                </div>
              </div>
            </div>
          </a>
        </li>
      </ul>
    </div>
    <Pagination
      v-if="pagination.total > pagination.limit"
      v-model="pagination"
    />
  </div>
</template>

<script lang="ts" setup>
import Icon from '@/components/Icon.vue';
import Badge from '@/components/library/Badge.vue';
import Pagination from '@/components/library/Pagination.vue';
import { listProjects } from '@/services/project';
import { FilterOperator, FilterUIType, type Filter } from '@/types/Filter';
import type { Pagination as PaginationType } from '@/types/Pagination';
import type Project from '@/types/Project';
import { convertFiltersToQuery } from '@/utils/filter';
import {
  copyMetaToPagination,
  updateReactRouteWithPagination,
} from '@/utils/tableview';
import { computed, inject, onMounted, ref, watch, type Ref } from 'vue';
import TableFilters from './TableFilters.vue';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';

const navigate: Function | undefined = inject('navigate');
const route: any = inject('route') as { value: ParsedRoute };

const projects = ref([] as Project[]);
const pagination = ref<PaginationType>({
  page: 1,
  limit: 10,
  total: 0,
});
const skipFetch = ref(false);

const filters: Ref<Filter[]> = ref([
  {
    id: 'project_name',
    field: 'name',
    label: 'Name',
    value: '',
    type: FilterUIType.Text,
    operator: FilterOperator.ILike,
    active: false,
  },
  {
    id: 'production_company_name',
    field: 'productionCompany.name',
    label: 'Production Company',
    value: '',
    type: FilterUIType.Text,
    operator: FilterOperator.ILike,
    active: false,
  },
]);

const activeFilters = computed(() => {
  return filters.value.filter(({ active }) => active);
});

watch(activeFilters, async () => {
  getProjects();
});

watch(route.value.query, (newVal, oldVal) => {
  if (newVal.page === oldVal.page && newVal.limit === oldVal.limit) {
    return;
  }
  copyQueryParamsToPagination();
  skipFetch.value = true;
  getProjects();
});

watch(
  pagination,
  (newVal, oldVal) => {
    if (newVal.page === oldVal.page && newVal.limit === oldVal.limit) {
      return;
    }
    // updateRouteWithPagination(router, newVal, route.query)
    if (navigate) {
      updateReactRouteWithPagination(navigate, newVal, route.value.query);
    }
    if (!skipFetch.value) {
      getProjects();
    }
    skipFetch.value = false;
  },
  { deep: true },
);

const getProjects = async () => {
  const controller = new AbortController();
  const { signal } = controller;

  const filtersString = convertFiltersToQuery(activeFilters.value);
  const {
    data: { meta, data },
  } = await listProjects(signal, pagination.value, filtersString);

  const { current_page, last_page } = meta;
  if (current_page > last_page) {
    pagination.value.page = 1;
    updateRoute(pagination.value);
    await getProjects();
    return;
  }

  projects.value = data;
  pagination.value = copyMetaToPagination(meta);
};

const establishQueryParams = (): void => {
  copyQueryParamsToPagination();
  updateRoute(pagination.value);
};

const copyQueryParamsToPagination = (): void => {
  const query = route?.value?.query;
  pagination.value.page = Number(query.page) || 1;
  pagination.value.limit = Number(query.limit) || 10;
};

const updateRoute = (pagination: PaginationType): void => {
  if (navigate) {
    updateReactRouteWithPagination(navigate, pagination, route.value.query);
    return;
  }
  // updateRouteWithPagination(router, pagination, route.query)
};

const goToProject = (hashId: string) => {
  if (navigate) {
    navigate({ pathname: `/projects/${hashId}` });
  }
};

onMounted(async () => {
  establishQueryParams();
  await getProjects();
});
</script>
