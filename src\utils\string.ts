export const toTitle = (str: string): string => {
  if (!str) {
    return '';
  }
  return str
    .replace(/([A-Z])/g, ' $1')
    .replace(/\w\S*/g, (word) => {
      return word.charAt(0).toUpperCase() + word.substr(1).toLowerCase();
    })
    .trim();
};

export const toCamel = (str: string): string => {
  if (!str) {
    return '';
  }
  return str
    .replace(/[-\s]+(.)?/g, (match, chr) => (chr ? chr.toUpperCase() : ''))
    .replace(/(?:^\w|[A-Z])/g, (word, index) =>
      index === 0 ? word.toLowerCase() : word,
    );
};

export const centsToDollars = (cents: number): string => {
  return `$${(cents / 100).toFixed(2)}`;
};
