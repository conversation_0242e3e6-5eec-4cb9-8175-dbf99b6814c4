import React from 'react';

import PropTypes from 'prop-types';

const PrimaryTab = (props) => {
  const {
    tab,
    currentTab,
    isSelected = (tab, currentTab) => {
      return tab?.key === currentTab?.key;
    },
    onClick,
  } = props;
  const handleClick = () => {
    if ('routePath' in tab) {
      onClick(tab);
    } else {
      // todo: remove
      onClick(tab.routeName);
    }
  };
  return (
    <span
      className={`${
        typeof isSelected === 'function' && isSelected(tab, currentTab)
          ? 'border-pink-700 dark:border-pink-500 text-pink-700 dark:text-pink-500'
          : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-500'
      } whitespace-nowrap border-b-2 py-3 px-4 text-base leading-6 font-semibold cursor-pointer select-none`}
      onClick={handleClick}
    >
      {tab.label}
    </span>
  );
};

PrimaryTab.propTypes = {
  tab: PropTypes.shape({
    label: PropTypes.string.isRequired,
    key: PropTypes.string.isRequired,
    routeName: PropTypes.string,
    routePath: PropTypes.string,
  }).isRequired,
  currentTab: PropTypes.shape({
    key: PropTypes.string.isRequired,
  }),
  isSelected: PropTypes.func,
  onClick: PropTypes.func.isRequired,
};

export default PrimaryTab;
