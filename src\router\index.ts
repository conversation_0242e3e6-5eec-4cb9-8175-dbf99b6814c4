import { useAuthStore } from '@/stores/auth';
import { usePermissionStore } from '@/stores/permission';
import { PermissionKeys } from '@/types/Permission';
import axios from 'axios';
import { createRouter, createWebHistory } from 'vue-router';
import routes from './routes';

import ProjectStore from '@/reactComponents/stores/project';

const router = createRouter({
  history: createWebHistory((import.meta as any).env.BASE_URL),
  routes,
});

router.beforeEach(async (to, from, next) => {
  if (to.meta.authRequired) {
    const authStore = useAuthStore();
    if (!authStore.isLoggedIn) {
      next({ name: 'login', query: { redirect: to.fullPath } });
      return;
    }
  }
  if (to.meta.onlyLoggedOut) {
    const authStore = useAuthStore();
    if (authStore.isLoggedIn) {
      next({ name: 'landing' });
      return;
    }
  }
  if (to.meta.isProjectAdmin && to.params?.hashId) {
    const permissionStore = usePermissionStore();
    await permissionStore.setPermissions();
    if (
      permissionStore.hasPermission(PermissionKeys.ADMIN_VIEW_ON_ALL_PROJECTS)
    ) {
      next();
      return;
    }

    const projectData = await ProjectStore.fetchProject(
      to.params.hashId as string,
    );
    const isAdmin = await ProjectStore.fetchIsAdmin(projectData?.id!);
    if (!isAdmin) {
      next({ name: 'project-home', params: { hashId: to.params?.hashId } });
      return;
    }
  }
  next();
  return;
});

axios.interceptors.response.use(
  function (response) {
    return response;
  },
  function (error) {
    if (error.response.status === 401) {
      const authStore = useAuthStore();
      if (router.currentRoute.value.path !== '/login') {
        authStore.logout();
        router.push({
          name: 'login',
          query: {
            redirect: router.currentRoute.value.fullPath,
          },
        });
      }
    }
    return Promise.reject(error);
  },
);

export default router;
