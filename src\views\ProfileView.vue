<template>
  <div class="min-h-screen pt-16">
    <main class="py-10">
      <!-- Page header -->
      <div
        class="mx-auto max-w-3xl items-center px-4 sm:px-6 md:flex md:justify-between space-y-2 md:space-x-5 lg:max-w-7xl lg:px-8"
      >
        <div class="flex items-center space-x-5">
          <div class="flex-shrink-0">
            <div class="relative">
              <Avatar
                :firstName="AuthStore.getUser.firstName"
                :lastName="AuthStore.getUser.lastName"
                bgColor="white"
                size="2xl"
              />
              <span
                class="absolute inset-0 rounded-full shadow-inner"
                aria-hidden="true"
              />
            </div>
          </div>
          <div>
            <h1
              class="text-2xl font-bold flex justify-start items-center space-x-2"
            >
              <div>
                {{
                  `${AuthStore.getUser.firstName} ${AuthStore.getUser.lastName}`
                }}
              </div>
              <Icon
                name="pencil"
                class="h-5 w-5 stroke-gray-500 hover:stroke-gray-800 dark:stroke-gray-400 dark:hover:stroke-gray-200 cursor-pointer"
                @click="openEditUserModal"
              />
              <Modal v-model="editUserModal">
                <h2 class="font-semibold mb-2">Edit User</h2>
                <div class="flex-wrap">
                  <TextInput
                    v-model="user.firstName"
                    label="First Name"
                    type="name"
                    auto-complete="off"
                  />
                  <TextInput
                    v-model="user.middleName"
                    label="Middle Name"
                    type="name"
                    auto-complete="off"
                  />
                  <TextInput
                    v-model="user.lastName"
                    label="Last Name"
                    type="name"
                    auto-complete="off"
                  />
                  <TextInput v-model="user.email" label="Email" />
                </div>
                <div class="flex justify-start space-x-2">
                  <Button
                    class="mt-2"
                    @click="updateUser"
                    :loading="loading"
                    size="sm"
                    color="primary"
                    >Save</Button
                  >
                  <Button
                    class="mt-2"
                    @click="editUserModal = false"
                    size="sm"
                    color="gray"
                    >Cancel</Button
                  >
                </div>
              </Modal>
            </h1>
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">
              {{ AuthStore.getUser.email }}
            </p>
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">
              {{ AuthStore.getUser.phone }}
            </p>
          </div>
        </div>
      </div>

      <div
        class="mx-auto mt-8 grid max-w-3xl grid-cols-1 gap-6 sm:px-6 lg:max-w-7xl lg:grid-flow-col-dense lg:grid-cols-3"
      >
        <div class="space-y-6 lg:col-span-2 lg:col-start-1">
          <section aria-labelledby="applicant-information-title">
            <div class="bg-white dark:bg-gray-900 shadow sm:rounded-lg">
              <div class="flex justify-between px-4 py-5 sm:px-6">
                <h2
                  id="applicant-information-title"
                  class="text-lg font-medium leading-6"
                >
                  Personal Information
                </h2>
                <Icon
                  name="pencil"
                  class="h-6 w-6 stroke-gray-500 hover:stroke-gray-800 dark:stroke-gray-400 dark:hover:stroke-gray-200 cursor-pointer"
                  @click="editCrewInfo()"
                />
              </div>
              <div
                v-if="userCrew.address"
                class="border-t border-gray-200 dark:border-gray-500 px-4 py-5 sm:px-6"
              >
                <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
                  <div class="sm:col-span-1">
                    <dt
                      class="text-sm font-medium text-gray-500 dark:text-gray-400"
                    >
                      Date of Birth
                    </dt>
                    <dd class="mt-1 text-sm">
                      {{ userCrew.dateOfBirth?.toFormat('MM/dd/yy') }}
                    </dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt
                      class="text-sm font-medium text-gray-500 dark:text-gray-400"
                    >
                      SSN
                    </dt>
                    <div class="flex">
                      <dd class="mt-1 mr-2 text-sm">
                        {{
                          hideSSN
                            ? '***-**-****'
                            : formatSSN(userCrew.socialSecurityNumber)
                        }}
                      </dd>
                      <EyeIcon
                        v-if="hideSSN"
                        class="w-5 h-5 cursor-pointer text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300"
                        @click="hideSSN = false"
                      />
                      <EyeSlashIcon
                        v-else
                        class="w-5 h-5 cursor-pointer text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300"
                        @click="hideSSN = true"
                      />
                    </div>
                  </div>
                  <!-- <div class="sm:col-span-1">
                    <dt
                      class="text-sm font-medium text-gray-500 dark:text-gray-400"
                    >
                      Unions
                    </dt>
                    <dd class="mt-1 text-sm">
                      <div class="flex flex-wrap items-center space-x-2 py-3">
                        <div
                          v-for="(union, unionIndex) in userCrew.unions"
                          :key="`union-${unionIndex}`"
                          class="rounded-full bg-green-100 hover:bg-green-200 px-2 text-xs font-semibold leading-5 text-green-800 cursor-pointer"
                        >
                          {{ union.name }}
                        </div>
                      </div>
                    </dd>
                  </div> -->
                  <div class="sm:col-span-1">
                    <dt
                      class="text-sm font-medium text-gray-500 dark:text-gray-400"
                    >
                      Personal Address
                    </dt>
                    <dd class="mt-1 text-sm">
                      {{
                        `${userCrew.address.street}${
                          userCrew.address.street2
                            ? ` ${userCrew.address.street2}`
                            : ''
                        },
                                            ${userCrew.address.city},
                                            ${userCrew.address.state.name}
                                            ${userCrew.address.zip}`
                      }}
                    </dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt
                      class="text-sm font-medium text-gray-500 dark:text-gray-400"
                    >
                      Ethnicity
                    </dt>
                    <dd class="mt-1 text-sm">{{ userCrew.ethnicity?.name }}</dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt
                      class="text-sm font-medium text-gray-500 dark:text-gray-400"
                    >
                      Citizenship Status
                    </dt>
                    <dd class="mt-1 text-sm">
                      {{ userCrew.citizenshipStatus?.name }}
                    </dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt
                      class="text-sm font-medium text-gray-500 dark:text-gray-400"
                    >
                      Gender
                    </dt>
                    <dd class="mt-1 text-sm">{{ userCrew.gender?.name }}</dd>
                  </div>
                </dl>
              </div>
              <div
                v-else
                class="border-t border-gray-200 dark:border-gray-500 px-4 py-5 sm:px-6"
              >
                No user crew data has been entered. Click the pencil icon to add
                personal information.
              </div>
            </div>
          </section>
          <section aria-labelledby="applicant-information-title">
            <div class="bg-white dark:bg-gray-900 shadow sm:rounded-lg">
              <div class="flex justify-between px-4 py-5 sm:px-6">
                <h2
                  id="applicant-information-title"
                  class="text-lg font-medium leading-6"
                >
                  Loan Out Information
                </h2>
                <Icon
                  name="pencil"
                  class="h-6 w-6 stroke-gray-500 hover:stroke-gray-800 dark:stroke-gray-400 dark:hover:stroke-gray-200 cursor-pointer"
                  @click="navigate('/loan-outs')"
                />
              </div>
              <div
                class="border-t border-gray-200 dark:border-gray-500 px-4 py-5 sm:px-6"
              >
                <dl
                  v-if="loanOut"
                  class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"
                >
                  <div class="sm:col-span-1">
                    <dt
                      class="text-sm font-medium text-gray-500 dark:text-gray-400"
                    >
                      Business Name
                    </dt>
                    <dd class="mt-1 text-sm">{{ loanOut.businessName }}</dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt
                      class="text-sm font-medium text-gray-500 dark:text-gray-400"
                    >
                      Tax Classification
                    </dt>
                    <dd class="mt-1 text-sm">
                      {{ loanOut.taxClassification.name }}
                    </dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt
                      class="text-sm font-medium text-gray-500 dark:text-gray-400"
                    >
                      EIN
                    </dt>
                    <dd class="mt-1 text-sm">
                      {{ loanOut.employerIdentificationNumber }}
                    </dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt
                      class="text-sm font-medium text-gray-500 dark:text-gray-400"
                    >
                      State Incorporated
                    </dt>
                    <dd class="mt-1 text-sm">
                      {{ loanOut.stateIncorporated?.key || 'N/A' }}
                    </dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt
                      class="text-sm font-medium text-gray-500 dark:text-gray-400"
                    >
                      State Identification Number
                    </dt>
                    <dd class="mt-1 text-sm">
                      {{ loanOut.stateIdentificationNumber || 'N/A' }}
                    </dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt
                      class="text-sm font-medium text-gray-500 dark:text-gray-400"
                    >
                      Address
                    </dt>
                    <dd class="mt-1 text-sm">
                      {{
                        `${loanOut.address.street}${
                          loanOut.address.street2
                            ? ` ${loanOut.address.street2}`
                            : ''
                        },
                                            ${loanOut.address.city},
                                            ${loanOut.address.state.name}
                                            ${loanOut.address.zip}`
                      }}
                    </dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt
                      class="text-sm font-medium text-gray-500 dark:text-gray-400"
                    >
                      Date Incorporated
                    </dt>
                    <dd class="mt-1 text-sm">
                      {{ loanOut.dateIncorporated?.toFormat('MM/dd/yy') }}
                    </dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt
                      class="text-sm font-medium text-gray-500 dark:text-gray-400"
                    >
                      Article of Incorporation
                    </dt>
                    <dd
                      v-if="loanOut.articleOfIncorporationDocumentId"
                      class="mt-1 text-sm"
                    >
                      <div v-if="loanOut?.articleOfIncorporationDocumentId">
                        Click
                        <span
                          class="underline cursor-pointer"
                          @click="openArticleOfIncorporation"
                          >here</span
                        >
                        to download the form.
                      </div>
                    </dd>
                    <dd v-else class="mt-1 text-sm">
                      No article of incorporation has been uploaded.
                    </dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt
                      class="text-sm font-medium text-gray-500 dark:text-gray-400"
                    >
                      Agent Authorization
                    </dt>
                    <dd
                      v-if="loanOut.articleOfIncorporationDocumentId"
                      class="mt-1 text-sm"
                    >
                      <div v-if="loanOut?.articleOfIncorporationDocumentId">
                        Click
                        <span
                          class="underline cursor-pointer"
                          @click="openAgentAuthorization"
                          >here</span
                        >
                        to download the form.
                      </div>
                    </dd>
                    <dd v-else class="mt-1 text-sm">
                      No agent authorization has been uploaded.
                    </dd>
                  </div>
                </dl>
                <div v-else>
                  No loan out information has been added. Click the pencil icon
                  to add a loan out information.
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </main>
  </div>
</template>

<script lang="ts">
import Icon from '@/components/Icon.vue';
import Avatar from '@/components/library/Avatar.vue';
import Button from '@/components/library/Button.vue';
import Modal from '@/components/library/Modal.vue';
import TextInput from '@/components/library/TextInput.vue';
import {
  getLoanOut,
  getLoanOutAgentAuthorization,
  getLoanOutArticleOfIncorporation,
} from '@/services/loan-out';
import { getUserCrewById } from '@/services/user-crew';
import { updateUser } from '@/services/users';
import type { SnackType } from '@/types/Snackbar';
import type User from '@/types/User';
import type UserCrew from '@/types/UserCrew';
import { formatSSN } from '@/utils/ssn';
import { EyeIcon, EyeSlashIcon } from '@heroicons/vue/24/solid';
import axios from 'axios';
// import { mapActions, mapState } from 'pinia'
import { defineComponent } from 'vue';
import AuthStore from '@/reactComponents/stores/auth';
import SnackbarStore from '@/reactComponents/stores/snackbar';

export default defineComponent({
  components: { EyeIcon, EyeSlashIcon, Modal, Button, TextInput, Icon, Avatar },
  props: {
    navigate: {
      type: Function,
      default: null,
    },
  },
  setup() {
    return {
      AuthStore,
      SnackbarStore,
    };
  },
  data() {
    return {
      userCrew: {} as UserCrew,
      user: {} as User,
      loanOut: null as any,
      hideSSN: true as boolean,
      editUserModal: false as boolean,
      loading: false as boolean,
      formatSSN,
    };
  },
  methods: {
    triggerSnackbar(msg: string, duration = 2500, type: SnackType = 'success') {
      this.SnackbarStore.triggerSnackbar(msg, duration, type);
    },
    async reloadUser() {
      this.AuthStore.reloadUser();
    },
    openArticleOfIncorporation() {
      const link = document.createElement('a');
      link.href = getLoanOutArticleOfIncorporation(this.loanOut.id);
      link.setAttribute('download', 'article_of_incorporation.pdf');
      document.body.appendChild(link);
      link.click();
    },
    openAgentAuthorization() {
      const link = document.createElement('a');
      link.href = getLoanOutAgentAuthorization(this.loanOut.id);
      link.setAttribute('download', 'agent_authorization.pdf');
      document.body.appendChild(link);
      link.click();
    },
    editCrewInfo() {
      const destination = this.userCrew?.id ? 'edit' : 'create';
      this.navigate(destination);
    },
    openEditUserModal() {
      this.editUserModal = true;
      this.user = {
        ...this.AuthStore.getUser,
      };
    },
    async updateUser() {
      if (this.loading) return;
      this.loading = true;
      try {
        await updateUser('me', this.user);
        await this.reloadUser();
        await this.load();
        this.editUserModal = false;
        this.triggerSnackbar('User updated successfully.');
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          this.triggerSnackbar(msg, 2500, 'error');
        } else {
          this.triggerSnackbar(err as string, 2500, 'error');
        }
      }
      this.loading = false;
    },
    async load() {
      const { data: userCrew } = await getUserCrewById(
        this.AuthStore.getUser.id!,
      );
      this.userCrew = userCrew;
      const { data: LoanOut } = await getLoanOut();
      this.loanOut = LoanOut;
    },
  },
  async mounted() {
    await this.load();
  },
});
</script>
