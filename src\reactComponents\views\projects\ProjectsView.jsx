import { useAuth } from '@/reactComponents/AppHooks';
import React from 'react';
import PermissionStore from '@/reactComponents/stores/permission';
import { observer } from 'mobx-react-lite';
import { Button } from '@/reactComponents/library';
import ProjectsTable from '@/reactComponents/library/ProjectsTable/ProjectsTable';
import { useNavigate } from 'react-router';

const ProjectsViewHOC = observer(() => {
  useAuth();

  const navigate = useNavigate();

  return (
    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pt-20">
      <div className="flex justify-between items-center space-x-1 my-4">
        <h3 className="text-lg font-semibold leading-6 dark:text-gray-200">
          Projects
        </h3>

        {(PermissionStore.isCompanyAdmin || PermissionStore.isSiteAdmin) && (
          <div className="flex space-x-2">
            <Button
              size="small"
              variant="outlined"
              onClick={() => navigate('/projects/new')}
              data-testid="create-project-btn"
            >
              Create
            </Button>
          </div>
        )}
      </div>
      <ProjectsTable />
    </div>
  );
});

export default ProjectsViewHOC;
