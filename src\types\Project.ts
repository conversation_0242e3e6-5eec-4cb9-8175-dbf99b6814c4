import type { DateTime } from 'luxon';
import type Address from './Address';
import type MinuteIncrement from './MinuteIncrement';
import type PayPeriod from './PayPeriod';
import type ProductionCompany from './ProductionCompany';
import type { ProjectDepartment } from './ProjectDepartment';
import type ProjectShootLocation from './ProjectShootLocation';
import type ShootLocation from './ShootLocation';

export default interface Project {
  id?: number;
  hashId?: string;
  name?: string;
  number?: string;
  description?: string;
  code?: string;
  startsAt: DateTime;
  endsAt: DateTime;
  productionCompany?: ProductionCompany;
  payPeriods: PayPeriod[];
  projectShootLocations: ProjectShootLocation[];
  shootLocations?: ShootLocation[];
  productionCompanyAddress?: Address;
  type: any;
  minuteIncrement?: MinuteIncrement;
  isApplyingForTaxIncentives: boolean;
  departments: ProjectDepartment[];
  mileageAicpNumber?: string;
  castAndCrewId?: number;
}

export interface CreateProject {
  id?: number;
  name?: string;
  number?: string;
  code?: string;
  startsAt: DateTime;
  endsAt: DateTime;
  productionCompanyId?: number;
  productionCompany?: ProductionCompany;
  projectShootLocations: ProjectShootLocation[];
  shootLocations: ShootLocation[];
  productionCompanyAddress: Address;
  minuteIncrement?: MinuteIncrement;
  isApplyingForTaxIncentives: Address;
  mileageAicpNumber?: string;
}
