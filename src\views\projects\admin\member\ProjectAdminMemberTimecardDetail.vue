<template>
  <div>
    <main class="pb-8 flex justify-center">
      <div class="max-w-screen-3xl min-w-screen-3xl overflow-scroll">
        <Button size="sm" color="primary" @click="navigateToTimecards">
          <div class="flex font-semibold">
            <ArrowLongLeftIcon class="mr-1 h-5 w-5" />
            Back to Employee Timecards
          </div>
        </Button>
        <section class="mt-5 text-zinc-600 dark:text-zinc-400">
          <div class="flex items-center space-x-4">
            <h2 class="text-xl font-bold">
              Timecard ({{
                `${payPeriod.startsAt?.toFormat(
                  'ccc. MM/dd',
                )} - ${payPeriod.endsAt?.toFormat('ccc. MM/dd')}`
              }})
            </h2>
            <div class="flex grow space-x-2" />
            <div>
              <Button
                v-if="
                  timecard.status?.id === TimecardStatusId.Approved && batchId
                "
                size="sm"
                color="primary"
                @click="htgDetails"
                :loading="loadHtgTcReport"
                :disabled="loadHtgTcReport"
              >
                HTG Details
              </Button>
            </div>
            <div v-if="unsavedChanges" class="flex space-x-1">
              <ExclamationCircleIcon class="h-5 w-5 text-gray-500" />
              <div>Unsaved Changes</div>
            </div>
            <div
              v-if="timecard.status"
              class="flex text-xl text-zinc-500 dark:text-zinc-400"
            >
              <TimecardStatusIcon :timecard="timecard" />
            </div>
          </div>
          <div class="flex justify-start items-center space-x-3">
            <Button
              size="xs"
              color="primary-outlined"
              @click="changePayPeriodModal"
            >
              <div class="flex items-center space-x-1 text-pink-600">
                <CalendarIcon class="h-4 w-4" />
                <div class="font-semibold">Change Week</div>
              </div>
            </Button>
            <Modal v-model="changePayPeriod.modal">
              <div class="pb-48">
                <h2 class="text-xl font-bold pb-2">Change Week</h2>
                <p>
                  If the crew member submitted this timecard for the wrong week,
                  you can change it here.
                </p>
                <div class="flex space-x-2 pt-2">
                  <Dropdown
                    v-model="changePayPeriod.newPayPeriod"
                    display-name="id"
                    :menu-items="changePayPeriod.payPeriods"
                  >
                    <template #label="{ value: item }">
                      <div v-if="!item">Select a new pay period</div>
                      <div v-else>
                        {{ item?.startsAt?.toFormat('ccc. MM/dd') }} -
                        {{ item?.endsAt?.toFormat('ccc. MM/dd') }}
                      </div>
                    </template>
                    <template #item="{ value: item }">
                      {{ item?.startsAt?.toFormat('ccc. MM/dd') }} -
                      {{ item?.endsAt?.toFormat('ccc. MM/dd') }}
                    </template>
                  </Dropdown>
                </div>
                <div class="flex justify-center space-x-2">
                  <Button @click="changePayPeriod.modal = false" color="gray">
                    Cancel
                  </Button>
                  <Button
                    :loading="changePayPeriod.loading"
                    @click="changePayPeriodHandler"
                  >
                    Change
                  </Button>
                </div>
              </div>
            </Modal>
          </div>
          <div class="flex items-top space-x-4 mt-5">
            <div>
              <div class="text-xs font-bold mb-4">Union/Local</div>
              <div
                class="rounded-full bg-green-200 px-2 text-xs font-semibold leading-5 text-green-800"
                style="width: fit-content"
              >
                <div class="flex justify-center">
                  {{ timecard.union?.name }}
                </div>
              </div>
            </div>
            <div>
              <div class="text-xs font-bold mb-2">Job title / Occupation</div>
              <div>
                <Combobox
                  v-model="timecard.occupation"
                  :items="occupations"
                  :search="occupationSearch"
                  :loading="loadingOccupations"
                  :infiniteScroll="true"
                  @change="handleOccupationChange"
                  :pagination="paginationOccupation"
                  @update:pagination="(pagination: Pagination) => (updatePagination(pagination))"
                  @update:search="(x: any) => (occupationSearch = x)"
                  class="w-full"
                  display-name="name"
                >
                  <template #label="{ value: modelValue }">
                    {{ modelValue?.name || 'Select' }}
                  </template>
                  <template #item="{ value: item }">
                    {{ item?.name }}
                  </template>
                </Combobox>
              </div>
            </div>
            <div
              v-if="timecard.requiresHireLocation && timecard.occupation?.name"
            >
              <div class="text-xs font-bold mb-2">Hire location</div>
              <div>
                <Dropdown
                  v-model="timecard.hireLocation"
                  :loading="loadingHireLocation"
                  :menu-items="hireLocations"
                  display-name="name"
                >
                  <template #label>
                    {{ timecard.hireLocation?.name }}
                  </template>
                  <template #item="{ value: item }">
                    {{ item.name }}
                  </template>
                </Dropdown>
              </div>
            </div>
            <div>
              <div class="text-xs font-bold mb-2">Rate ($)</div>
              <div>
                <TextInput
                  v-model="timecard.hourlyRate"
                  @input="resetRateForTimecardDays"
                  :disabled="isFieldPrefilled.rate"
                  type="number"
                />
              </div>
            </div>
            <div>
              <div class="text-xs font-bold mb-2">Rate type</div>
              <div>
                <Dropdown
                  v-model="timecard.rateType"
                  :disabled="isFieldPrefilled.rateType"
                  :loading="loadingRateTypes"
                  :menu-items="rateTypes"
                  @change="calculateGuarHoursAndRate"
                  display-name="name"
                />
              </div>
            </div>
            <div>
              <div class="text-xs font-bold mb-2">Guar. hours</div>
              <div>
                <TextInput
                  v-model="timecard.guarHours"
                  @input="calculateRate"
                  type="number"
                />
              </div>
            </div>
            <!-- <div>
                  <div class="text-xs font-bold mb-3">Guar. rate ($)</div>
                  <div>{{ timecard.guarRate }}</div>
                </div> -->
          </div>
          <div class="border-b-2 pb-2" />
          <div class="overflow-x-scroll">
            <table>
              <colgroup>
                <col width="2%" />
                <col width="1%" />
                <col width="1%" />
                <col width="3%" />
                <col width="3%" />
                <col width="2%" />
                <col width="15%" />
                <col width="5%" />
                <col width="15%" />
                <col width="5%" />
                <col width="1%" />
                <col width="10%" />
              </colgroup>
              <tr>
                <th class="font-bold text-l">Day</th>
                <th class="font-bold text-m">Active</th>
                <th class="font-bold text-l">AICP/Line#</th>
                <th class="font-bold text-l">Rate</th>
                <th class="font-bold text-l">Work Status</th>
                <th class="font-bold text-l">Work Zone</th>
                <th v-if="integrationLive" class="font-bold text-l">
                  Work Location
                </th>
                <th class="font-bold text-l w-24">Starts At</th>
                <th class="font-bold text-l w-48">Meal</th>
                <th class="font-bold text-l w-24">Ends At</th>
                <th class="font-bold text-l w-24">NDB</th>
                <th class="font-bold text-l w-24">Crew Notes</th>
              </tr>
              <tr
                v-for="(timecardDay, timecardDayIndex) in timecard.timecardDays"
                :key="`timecard-day-${timecardDayIndex}`"
              >
                <td class="text-l align-top">
                  <div class="text-sm font-semibold">
                    {{ timecardDay.date.toFormat('ccc') }}
                  </div>
                  {{ timecardDay?.startsAt?.toFormat('MM/dd') }}
                </td>
                <td class="px-1 py-2 align-top">
                  <Toggle v-model="timecardDay.isActive" :disabled="!isAdmin" />
                </td>
                <td class="align-top">
                  <TextInput
                    v-if="timecardDay.isActive"
                    v-model="timecardDay.lineNumber"
                    :disabled="!isAdmin || !trackingHeaderDetails.allowed"
                    @input="unsavedChanges = true"
                  />
                </td>
                <td class="align-top">
                  <TextInput
                    v-if="timecardDay.isActive"
                    class="w-24"
                    v-model="timecardDay.rate"
                    type="number"
                    :disabled="!isAdmin"
                    @input="unsavedChanges = true"
                  />
                </td>
                <td class="align-top">
                  <Dropdown
                    v-if="timecardDay.isActive"
                    :loading="loadingWorkStatuses"
                    v-model="timecardDay.workStatus"
                    :disabled="!isAdmin"
                    :menu-items="workStatuses"
                    display-name="name"
                    @change="workStatusSelectMethod(timecardDay)"
                  />
                </td>
                <td class="align-top">
                  <Dropdown
                    v-if="timecardDay.isActive"
                    :loading="loadingWorkZones"
                    v-model="timecardDay.workZone"
                    :disabled="!isAdmin"
                    :menu-items="workZones"
                    display-name="name"
                  />
                </td>
                <td v-if="integrationLive" class="align-top">
                  <Dropdown
                    v-if="timecardDay.isActive"
                    v-model="timecardDay.projectShootLocation"
                    :menu-items="projectShootLocations"
                    :disabled="!isAdmin"
                    display-name="shootLocation.locationName"
                  >
                    <template #label>
                      {{
                        `${timecardDay.projectShootLocation?.shootLocation.locationName}
                      (${timecardDay.projectShootLocation?.zip})`
                      }}
                    </template>
                    <template #item="{ value: projectShootLocation }">
                      {{
                        `${projectShootLocation?.shootLocation.locationName} (${projectShootLocation?.zip})`
                      }}
                    </template>
                  </Dropdown>
                </td>
                <td class="align-top">
                  <TimePicker
                    v-if="
                      timecardDay.isActive &&
                      !timecard.isExempt &&
                      workStatusKeyCheck(timecardDay.workStatus?.key)
                    "
                    class="w-24"
                    v-model="timecardDay.startsAt"
                    :disabled="!isAdmin"
                    :increment="timePickerIncrement"
                  />
                </td>
                <td class="align-top">
                  <div
                    v-if="
                      !timecard.isExempt &&
                      workStatusKeyCheck(timecardDay.workStatus?.key)
                    "
                  >
                    <div class="flex space-x-1">
                      <TimePicker
                        v-if="
                          timecardDay.isActive &&
                          timecardDay?.meals?.[0]?.startsAt
                        "
                        class="w-24"
                        v-model="timecardDay.meals[0].startsAt"
                        :disabled="!isAdmin"
                        :increment="timePickerIncrement"
                      />
                      <TimePicker
                        class="w-24"
                        v-if="
                          timecardDay.isActive &&
                          timecardDay?.meals?.[0]?.endsAt
                        "
                        v-model="timecardDay.meals[0].endsAt"
                        :disabled="!isAdmin"
                        :increment="timePickerIncrement"
                      />
                    </div>
                    <div class="flex space-x-1">
                      <TimePicker
                        v-if="
                          timecardDay.isActive &&
                          timecardDay?.meals?.[1]?.startsAt
                        "
                        class="w-24"
                        v-model="timecardDay.meals[1].startsAt"
                        :disabled="!isAdmin"
                        :increment="timePickerIncrement"
                      />
                      <TimePicker
                        v-if="
                          timecardDay.isActive &&
                          timecardDay?.meals?.[1]?.endsAt
                        "
                        class="w-24"
                        v-model="timecardDay.meals[1].endsAt"
                        :disabled="!isAdmin"
                        :increment="timePickerIncrement"
                      />
                    </div>
                    <div
                      v-if="timecardDay.isActive && isAdmin"
                      class="flex justify-center space-x-1 items-center"
                    >
                      <Button color="gray" size="xs">
                        <MinusIcon
                          @click="removeMeal(timecardDay)"
                          class="h-3 w-3"
                        />
                      </Button>
                      <Button color="gray" size="xs">
                        <PlusIcon
                          @click="addMeal(timecardDay)"
                          class="h-3 w-3"
                        />
                      </Button>
                    </div>
                  </div>
                </td>
                <td class="align-top">
                  <TimePicker
                    v-if="
                      timecardDay.isActive &&
                      !timecard.isExempt &&
                      workStatusKeyCheck(timecardDay.workStatus?.key)
                    "
                    class="w-24"
                    v-model="timecardDay.endsAt"
                    :disabled="!isAdmin"
                    :increment="timePickerIncrement"
                  />
                </td>
                <td class="px-1 py-2 align-top">
                  <Toggle
                    v-if="
                      timecardDay.isActive &&
                      workStatusKeyCheck(timecardDay.workStatus?.key)
                    "
                    v-model="timecardDay.hasNdb"
                    :disabled="!isAdmin"
                  />
                </td>
                <td width="10%" class="align-top">
                  <TextArea
                    v-if="timecardDay.isActive"
                    class="w-52"
                    rows="2"
                    v-model="timecardDay.comments"
                    :disabled="true"
                  />
                </td>
              </tr>
            </table>
          </div>
          <!-- TODO calculate gross -->
          <!-- <div class="flex justify-center space-x-2">
            <TextInput v-model="timecard.gross" type="number" label="Gross"
              :disabled="!hasPermission('payroll_manager_view')" />
            <TextInput v-model="timecard.grossWithBoxRentalAndMileage" type="number"
              label="Gross w/ Box Rental and Mileage" :disabled="!hasPermission('payroll_manager_view')" />
          </div> -->
          <div class="flex justify-center space-x-2">
            <Button
              :disabled="unsavedChanges"
              @click="isRequestChangesModalOpen = true"
              color="secondary"
              size="sm"
            >
              <div class="font-semibold">Request Changes</div>
            </Button>
            <Modal v-model="isRequestChangesModalOpen">
              <h2 class="text-xl font-bold pb-2">Request Changes</h2>
              <TextArea v-model="timecard.requestedChanges" />
              <div class="flex justify-center space-x-2 pt-3">
                <Button @click="isRequestChangesModalOpen = false" color="gray">
                  Cancel
                </Button>
                <Button
                  @click="requestChanges()"
                  :loading="loading"
                  color="error"
                >
                  Request
                </Button>
              </div>
            </Modal>
            <Button
              v-if="timecard.fileId"
              :disabled="unsavedChanges"
              color="secondary"
              size="sm"
              @click="downloadTimecard()"
            >
              <div class="flex items-center space-x-1">
                <!-- <DocumentTextIcon class="h-4 w-4" /> -->
                <div class="font-semibold">Download</div>
              </div>
            </Button>

            <Button
              color="secondary"
              size="sm"
              @click="deleteTimecardModal = true"
            >
              <div
                class="flex items-center space-x-1 stroke-gray-700 dark:stroke-gray-200"
              >
                <Icon name="trash" class="w-5 h-5" />
                <!-- Remove -->
              </div>
            </Button>
            <Modal v-model="deleteTimecardModal">
              <!-- deleteTimecard() -->
              <h2 class="text-xl font-bold pb-2">Delete Timecard</h2>
              <p>Are you sure you would like to delete this timecard?</p>
              <div class="flex justify-start space-x-2 pt-3">
                <Button @click="deleteTimecardModal = false" color="gray">
                  Cancel
                </Button>
                <Button
                  @click="deleteTimecard()"
                  :loading="loading"
                  color="error"
                >
                  Delete
                </Button>
              </div>
            </Modal>
            <Button
              @click="updateTimecardDialog = true"
              :disabled="!unsavedChanges"
              size="sm"
              color="secondary"
            >
              <div class="flex items-center space-x-1">
                <!-- <PencilIcon class="h-4 w-4" /> -->
                <div class="font-semibold">Save</div>
              </div>
            </Button>
            <Modal v-model="updateTimecardDialog">
              <h2 class="text-xl font-bold pb-2">Save Changes</h2>
              <p class="text-sm dark:text-gray-400 mb-2">
                Specify the changes and why you made them (optional).
              </p>
              <TextArea v-model="updateTimecardAuditNotes" />
              <div class="flex justify-center space-x-2 pt-3">
                <Button @click="updateTimecardDialog = false" color="gray">
                  Cancel
                </Button>
                <Button :loading="loading" @click="updateTimecard()">
                  Save
                </Button>
              </div>
            </Modal>
            <Button
              @click="checkTimecardStatus"
              :disabled="
                unsavedChanges ||
                timecard.status?.id === TimecardStatusId.Approved ||
                timecard.status?.id === TimecardStatusId.RequestedChanges
              "
              size="sm"
              color="primary"
            >
              <div class="flex items-center space-x-1">
                <!-- <CheckIcon class="h-4 w-4" /> -->
                <div class="font-semibold">Approve</div>
              </div>
            </Button>
          </div>
        </section>
        <ProjectAdminMemberTimecardDetailMileageLog
          v-if="timecard?.id"
          class="mt-5"
          :timecard="timecard"
          :project="project"
          :edit-disabled="!isAdmin"
          :trackingHeaderDetails="trackingHeaderDetails"
          @refresh="load"
        />
        <ProjectAdminMemberTimecardDetailKitRental
          v-if="timecard?.id"
          class="mt-5"
          :timecard="timecard"
          :project="project"
          :trackingHeaderDetails="trackingHeaderDetails"
          @refresh="load"
        />
        <section class="mt-5">
          <div class="border-t-2 pb-2"></div>
          <h2 class="text-xl font-bold">Reimbursements</h2>
          <div class="flex justify-center">
            <table class="mt-3">
              <colgroup>
                <col width="40%" />
                <col width="10%" />
                <col v-if="integrationLive" width="15%" />
                <col width="10%" />
                <col width="10%" />
                <col width="10%" />
                <col width="10%" />
              </colgroup>
              <tr>
                <th class="font-bold text-l">Name</th>
                <th class="font-bold text-l">AICP/Line#</th>
                <th v-if="integrationLive" class="font-bold text-l">Date</th>
                <th v-if="integrationLive" class="font-bold text-l">Type</th>
                <th v-if="integrationLive" class="font-bold text-l">
                  Work Location
                </th>
                <th class="font-bold text-l">Rate</th>
                <th class="font-bold text-l">Quantity</th>
                <th class="font-bold text-l">Receipt</th>
                <th class="font-bold text-l">Total</th>
              </tr>
              <tr
                v-for="reimbursement in reimbursements"
                :key="reimbursement.id"
              >
                <td class="px-1">
                  <TextInput
                    v-model="reimbursement.name"
                    :disabled="!isAdmin"
                  />
                </td>
                <td class="px-1">
                  <TextInput
                    v-model="reimbursement.lineNumber"
                    :disabled="!isAdmin || !trackingHeaderDetails.allowed"
                  />
                </td>
                <td v-if="integrationLive" class="px-1">
                  <DatePicker
                    v-model="reimbursement.date"
                    label=""
                    :disabled="!isAdmin"
                    @change="getTimecardDayWorkLocation(reimbursement)"
                  />
                </td>
                <td v-if="integrationLive" class="px-1">
                  <Dropdown
                    v-model="reimbursement.type"
                    :menu-items="reimbursementTypes"
                    :disabled="!isAdmin"
                    display-name="key"
                  >
                    <template #label="{ value: item }">
                      <div v-if="!item">Select a type</div>
                      <div v-else>
                        {{ item.key }}
                      </div>
                    </template>
                    <template #item="{ value: item }">
                      {{ item.key }}
                    </template>
                  </Dropdown>
                </td>
                <td v-if="integrationLive" class="px-1">
                  <Dropdown
                    v-model="reimbursement.workLocation"
                    :menu-items="project.projectShootLocations"
                    display-name="shootLocation.locationName"
                    :disabled="!isAdmin"
                  >
                    <template #label>
                      {{
                        `${reimbursement.workLocation?.shootLocation.locationName} (${reimbursement.workLocation?.zip})`
                      }}
                    </template>
                    <template #item="{ value: projectShootLocation }">
                      {{
                        `${projectShootLocation?.shootLocation.locationName} (${projectShootLocation?.zip})`
                      }}
                    </template>
                  </Dropdown>
                </td>
                <td class="px-1">
                  <TextInput
                    v-model="reimbursement.rate"
                    :disabled="!isAdmin"
                  />
                </td>
                <td class="px-1">
                  <TextInput
                    v-model="reimbursement.quantity"
                    :disabled="!isAdmin"
                  />
                </td>
                <td class="px-1">
                  <FileUpload
                    v-model="reimbursement.documentId"
                    :disabled="!isAdmin"
                  />
                </td>
                <td class="px-1">
                  <h2 class="font-semibold text-l mb-3 text-center">
                    ${{ reimbursement.rate * reimbursement.quantity }}
                  </h2>
                </td>
                <td class="px-1">
                  <ArrowDownTrayIcon
                    v-if="reimbursement.id"
                    @click="getReimbursementReceipt(reimbursement)"
                    class="h-5 w-5 hover:text-gray-400 cursor-pointer"
                  />
                </td>
              </tr>
            </table>
          </div>
          <div
            v-if="isAdmin"
            class="flex justify-center items-center space-x-5 mb-5"
          >
            <Button color="gray" size="xs">
              <MinusIcon @click="removeReimbursement" class="h-3 w-3" />
            </Button>
            <Button color="gray" size="xs">
              <PlusIcon @click="addReimbursement" class="h-3 w-3" />
            </Button>
          </div>
          <div v-if="isAdmin" class="flex justify-center">
            <Button
              @click="saveReimbursements"
              color="primary"
              :loading="loadingReimbursements"
            >
              Save
            </Button>
          </div>
        </section>
        <ProjectAdminMemberTimecardDetailAudits :audits="audits" />
      </div>
    </main>
    <Modal v-model="isSubmitModalOpen">
      <div class="flex justify-between items-center">
        <h2 class="text-xl font-bold pb-2">Approve Timecard</h2>
      </div>
      <div>
        This timecard is in progress. Approving it will submit the timecard on
        behalf of the employee. Do you want to continue?
      </div>
      <div class="flex justify-center space-x-2 pt-3">
        <Button @click="isSubmitModalOpen = false" color="gray">
          Cancel
        </Button>
        <Button @click="continueApproveTimecard" color="primary">
          Continue
        </Button>
      </div>
    </Modal>
    <Modal v-model="isSignatureModalOpen">
      <h2 class="font-semibold mb-2">Sign here:</h2>
      <SignatureArea ref="signaturePad" v-model="signature" />
      <div class="flex justify-center space-x-2">
        <Button class="mt-3" color="gray" @click="isSignatureModalOpen = false">
          Cancel
        </Button>
        <Button class="mt-3" :loading="loadingSignature" @click="approve">
          Submit
        </Button>
      </div>
    </Modal>
  </div>
</template>

<script lang="ts">
import Icon from '@/components/Icon.vue';
import Button from '@/components/library/Button.vue';
import Combobox from '@/components/library/Combobox.vue';
import DatePicker from '@/components/library/DatePicker.vue';
import Dropdown from '@/components/library/Dropdown.vue';
import FileUpload from '@/components/library/FileUpload.vue';
import Modal from '@/components/library/Modal.vue';
import SignatureArea from '@/components/library/SignatureArea.vue';
import TextArea from '@/components/library/TextArea.vue';
import TextInput from '@/components/library/TextInput.vue';
import TimePicker from '@/components/library/TimePicker.vue';
import Toggle from '@/components/library/Toggle.vue';
import ProjectAdminMemberTimecardDetailAudits from '@/components/ProjectAdminMemberTimecardDetailAudits.vue';
import ProjectAdminMemberTimecardDetailKitRental from '@/components/ProjectAdminMemberTimecardDetailKitRental.vue';
import ProjectAdminMemberTimecardDetailMileageLog from '@/components/ProjectAdminMemberTimecardDetailMileageLog.vue';
import TimecardStatusIcon from '@/components/TimecardStatusIcon.vue';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';
import {
  getContractSettingsAndScheduleInfo,
  listProjectHireLocations,
  listProjectOccupations,
  listProjectReimbursementTypes,
  listProjectShootLocations,
  getTrackingDetails,
} from '@/services/project';
import {
  getAvailablePayPeriods,
  getProjectMemberById,
  getWorkStatuses,
} from '@/services/project-members';
import { listRateTypes } from '@/services/rate-types';
import {
  getReimbursementReceipt,
  listTimecardReimbursements,
  updateTimecardReimbursements,
} from '@/services/reimbursements';
import {
  approveTimecard,
  changeTimecardPayPeriod,
  deleteTimecard,
  downloadTimecard,
  getTimecard,
  listTimecardAudit,
  requestChanges,
  submitTimecard,
  updateTimecard,
  getTimecardHtgReport,
} from '@/services/timecards';
import { getBatchDetails } from '@/services/batch';
import { getWorkZones } from '@/services/project-members';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import type { ContractSettingsAndScheduleInfo } from '@/types/ContractSettingsAndScheduleInfo';
import type { Occupation } from '@/types/Occupation';
import type { Pagination } from '@/types/Pagination';
import type PayPeriod from '@/types/PayPeriod';
import type Project from '@/types/Project';
import type ProjectMember from '@/types/ProjectMember';
import type { RateType } from '@/types/RateType';
import type Reimbursement from '@/types/Reimbursements';
import type Timecard from '@/types/Timecard';
import type TimecardAudit from '@/types/TimecardAudit';
import type TimecardDay from '@/types/TimecardDay';
import { TrackingKeysEnum } from '@/types/TimecardDay';
import type UserCrew from '@/types/UserCrew';
import { WorkStatusKeys } from '@/types/WorkStatus';
import { BatchStatusEnum, BatchStatusCapsPayEnum } from '@/types/Batch';
import { WorkZoneKeys } from '@/types/WorkZone';
import { TimecardStatusId } from '@/utils/enum';
import {
  ArrowLongLeftIcon,
  CalendarIcon,
  ExclamationCircleIcon,
} from '@heroicons/vue/20/solid';
import {
  ArrowDownTrayIcon,
  MinusIcon,
  PlusIcon,
} from '@heroicons/vue/24/outline';
import axios from 'axios';
import { DateTime } from 'luxon';
import { defineComponent, type PropType } from 'vue';

export default defineComponent({
  props: {
    project: {
      type: Object as PropType<Project>,
      required: true,
    },
    isAdmin: {
      type: Boolean,
      default: false,
    },
    projectMember: {
      type: Object as PropType<ProjectMember>,
      required: true,
    },
    route: {
      type: Object as PropType<ParsedRoute>,
      required: true,
    },
    navigate: {
      type: Function,
      required: true,
    },
    refresh: {
      type: Function,
      required: true,
    },
  },
  components: {
    TextArea,
    TimePicker,
    Button,
    MinusIcon,
    Modal,
    TextInput,
    PlusIcon,
    DatePicker,
    Combobox,
    Dropdown,
    TimecardStatusIcon,
    Toggle,
    ArrowLongLeftIcon,
    CalendarIcon,
    ExclamationCircleIcon,
    ProjectAdminMemberTimecardDetailAudits,
    ProjectAdminMemberTimecardDetailKitRental,
    ProjectAdminMemberTimecardDetailMileageLog,
    SignatureArea,
    FileUpload,
    ArrowDownTrayIcon,
    Icon,
  },
  data() {
    return {
      loading: true,
      TimecardStatusId,
      isRequestChangesModalOpen: false,
      timecard: {} as Timecard,
      batchId: '' as string,
      occupations: [] as Occupation[],
      occupationSearch: '' as string,
      loadingOccupations: true as boolean,
      paginationOccupation: {
        page: 1,
        limit: 50,
        total: 0,
      } as Pagination,
      contractSettingsAndScheduleInfo: {} as ContractSettingsAndScheduleInfo,
      loadingHireLocation: false as boolean,
      hireLocations: [] as any[],
      loadingRateTypes: false as boolean,
      rateTypes: [] as RateType[],
      isFieldPrefilled: {
        rate: false,
        rateType: false,
        occupationId: false,
        startDate: false,
        hireLocationCity: false,
        hireLocationState: false,
      } as any,
      payPeriod: {} as PayPeriod,
      userCrew: {} as UserCrew,
      selectedTimecardDay: undefined as TimecardDay | undefined,
      workStatuses: [] as any[],
      loadingWorkStatuses: true as boolean,
      reimbursements: [] as Reimbursement[],
      loadingReimbursements: false as boolean,
      workZones: [] as any[],
      loadingWorkZones: true as boolean,
      audits: [] as TimecardAudit[],
      updateTimecardDialog: false as boolean,
      updateTimecardAuditNotes: '' as string,
      unsavedChanges: false,
      deleteTimecardModal: false,
      isSignatureModalOpen: false,
      isSubmitModalOpen: false,
      signature: null,
      loadingSignature: false as boolean,
      changePayPeriod: {
        modal: false,
        payPeriods: [] as PayPeriod[],
        newPayPeriod: null as any,
        loading: false,
      },
      projectShootLocations: [] as any[],
      projectMemberId: null as any,
      reimbursementTypes: [] as any[],
      WorkStatusKeys,
      workZonechangeUpdated: false,
      lineNumberModified: false,
      trackingHeaderDetails: {
        allowed: true,
        required: true,
      },
      restrictChangeWeek: true,
      BatchStatusEnum,
      loadHtgTcReport: false,
    };
  },
  watch: {
    timecard: {
      handler: async function (val: Timecard) {
        if (!val) return;
        if (this.loading) return;
        this.unsavedChanges = true;
      },
      deep: true,
    },
    occupationSearch: {
      handler: function () {
        this.paginationOccupation = this.resetPagination(
          this.paginationOccupation,
        );
        this.occupations = [];
        this.getOccupations();
      },
    },
    workZones() {
      this.checkWorkZonesLoaded();
    },
  },
  computed: {
    // ...mapState(useAuthStore, ['isLoggedIn']),
    lastDownloaded(): string {
      if (!this.timecard || !this.timecard.lastDownloadedAt) {
        return '';
      }
      const timestamp = this.timecard.lastDownloadedAt.toString();
      const dateTimeInUserTimezone =
        DateTime.fromISO(timestamp).setZone('local');
      return dateTimeInUserTimezone.toFormat('dd/MM/yy');
    },
    timePickerIncrement(): number {
      return this.project?.minuteIncrement?.key || 15;
    },
    timecardId(): number {
      return parseInt(this.route.params.timecardId as string);
    },
    integrationLive(): boolean {
      return !!this.project?.productionCompany?.castAndCrewId!;
    },
  },
  methods: {
    async updateTimecard() {
      this.loading = true;
      let payload: Timecard = JSON.parse(
        JSON.stringify(this.timecard),
      ) as Timecard;
      payload.timecardDays.forEach((timecardDay: TimecardDay) => {
        timecardDay.mealPenalties = parseInt(
          (timecardDay.mealPenalties * 100).toFixed(0),
        );
      });
      try {
        await updateTimecard(this.timecardId.toString(), {
          timecard: payload,
          auditNotes: this.updateTimecardAuditNotes,
        });
        await this.load();
        SnackbarStore.triggerSnackbar(
          'Timecard updated successfully.',
          2500,
          'success',
        );
        this.unsavedChanges = false;
        this.updateTimecardAuditNotes = '';
        this.lineNumberModified = true;
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
      }
      this.loading = false;
      this.updateTimecardDialog = false;
    },
    formatUSD(cents: number) {
      const formatter = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      });
      return formatter.format(cents);
    },
    moveRight() {
      let index = this.timecard.timecardDays.indexOf(this.selectedTimecardDay!);
      while (
        this.timecard.timecardDays[index + 1] &&
        !this.timecard.timecardDays[index + 1].isActive
      ) {
        index++;
      }
      if (index >= this.timecard.timecardDays.length) {
        return;
      }
      const nextTimecardDay = this.timecard.timecardDays[index + 1];
      if (nextTimecardDay) {
        this.selectedTimecardDay = nextTimecardDay;
      }
    },
    moveLeft() {
      let index = this.timecard.timecardDays.indexOf(this.selectedTimecardDay!);
      while (
        this.timecard.timecardDays[index - 1] &&
        !this.timecard.timecardDays[index - 1].isActive
      ) {
        index--;
      }
      if (index < 0) {
        return;
      }
      const previousTimecardDay = this.timecard.timecardDays[index - 1];
      if (previousTimecardDay) {
        this.selectedTimecardDay = previousTimecardDay;
      }
    },
    async requestChanges() {
      this.loading = true;
      try {
        await requestChanges(
          this.timecard.id,
          this.timecard.requestedChanges.trim(),
        );
        await this.load();
        this?.refresh?.();
        SnackbarStore.triggerSnackbar('Changes requested.', 2500, 'success');
        this.isRequestChangesModalOpen = false;
        this.loading = false;
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
        this.loading = false;
        throw err;
      }
    },
    addMeal(timecardDay: any) {
      if (timecardDay!.meals.length >= 2) return;
      timecardDay!.meals.push({
        startsAt: timecardDay?.date.toUTC().set({ hour: 13 })!,
        endsAt: timecardDay?.date.toUTC().set({ hour: 14 })!,
      });
    },
    removeMeal(timecardDay: any) {
      timecardDay!.meals.pop();
    },
    addReimbursement() {
      this.reimbursements.push({
        name: '',
        lineNumber: '',
        date: DateTime.now(),
        type: null,
        rate: 0,
        quantity: 0,
      });
    },
    downloadTimecard() {
      if (!this.timecard.fileId) return;
      const link = document.createElement('a');
      link.href = downloadTimecard(this.timecard.id);

      link.setAttribute(
        'download',
        `${this.projectMember.user.lastName}_${
          this.projectMember.user.firstName
        }_${this.project.name}_${this.timecard.payPeriod?.startsAt?.toFormat(
          'MM_dd',
        )}_timecard.pdf`,
      );
      document.body.appendChild(link);
      link.click();
    },
    removeReimbursement() {
      this.reimbursements.pop();
    },
    async loadReimbursementTypes() {
      if (this.project.productionCompany?.castAndCrewId) {
        try {
          const { data } = await listProjectReimbursementTypes(
            this.project.id!,
          );
          this.reimbursementTypes = data;
        } catch (err) {
          console.warn('Failed to load reimbursement types', err);
        }
      }
    },
    async saveReimbursements() {
      this.loadingReimbursements = true;
      try {
        let payload: any[] = JSON.parse(JSON.stringify(this.reimbursements));
        payload.forEach((reimbursement: any) => {
          if (reimbursement.rate) {
            reimbursement.rate *= 100;
          }
          reimbursement.totalAmount =
            reimbursement.rate * reimbursement.quantity;
        });
        await updateTimecardReimbursements(this.timecard.id, payload);
        this.getReimbursements();
        SnackbarStore.triggerSnackbar(
          'Reimbursements updated successfully.',
          2500,
          'success',
        );
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
      }
      this.loadingReimbursements = false;
    },
    async getWorkStatuses(): Promise<void> {
      this.loadingWorkStatuses = true;
      try {
        const { data: workStatuses } = await getWorkStatuses(
          this.projectMemberId!,
        );
        this.workStatuses = workStatuses;
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
      }
      this.loadingWorkStatuses = false;
    },
    async getWorkZones(): Promise<void> {
      this.loadingWorkZones = true;
      try {
        const { data: workZones } = await getWorkZones(this.projectMemberId!);
        this.workZones = workZones;
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
      }
      this.loadingWorkZones = false;
    },
    async getProjectMember(): Promise<void> {
      const projectMemberId =
        this?.route?.params?.projectMemberId?.toString() || '';
      const { data: projectMember } = await getProjectMemberById(
        projectMemberId,
      );
      this.projectMemberId = projectMember.id;
      this.userCrew = projectMember.user.userCrew;
    },
    async getBatchDetails(): Promise<void> {
      this.restrictChangeWeek = false;
      if (!this.batchId) return;
      const { data }: any = await getBatchDetails(this.batchId);
      this.restrictChangeWeek =
        data?.batchStatus !== BatchStatusCapsPayEnum.Open;
    },

    async getReimbursements(): Promise<void> {
      const { data: reimbursements } = await listTimecardReimbursements(
        this.timecardId,
      );
      reimbursements.forEach((reimbursement: any) => {
        reimbursement.rate = reimbursement.rate / 100;
        reimbursement.totalAmount = reimbursement.totalAmount / 100;
      });
      this.reimbursements = reimbursements;
    },
    async getTimecard(): Promise<void> {
      const { data } = await getTimecard(this.timecardId);
      this.batchId = data.batchId;
      if (data.kitRental?.id) {
        data.kitRental.rentalRate = data.kitRental.rentalRate / 100;
        data.kitRental.inventoryItems.forEach((inventoryItem: any) => {
          inventoryItem.amount = inventoryItem.amount / 100;
        });
      }
      this.timecard = data as Timecard;
      this.timecard.timecardDays.forEach((day: TimecardDay) => {
        day.startsAt = day.startsAt.toUTC();
        day.endsAt = day.endsAt.toUTC();
        day.meals.forEach((meal) => {
          meal.startsAt = meal.startsAt.toUTC();
          meal.endsAt = meal.endsAt.toUTC();
        });
        day.date = day.date.toUTC();
        if (
          day.isActive &&
          this.trackingHeaderDetails.allowed &&
          (day.lineNumber == '' || day.lineNumber == null)
        ) {
          day.lineNumber =
            day.workStatus?.key?.toUpperCase() === WorkStatusKeys.KEY_SHOOT
              ? this.projectMember?.shootLineNumber
              : this.projectMember?.lineNumber;
        }
      });
      if (data.mileageForm?.date) {
        this.timecard.mileageForm.date = DateTime.fromISO(
          data.mileageForm.date,
        );
      }
      this.payPeriod = data.payPeriod;
      this.selectedTimecardDay = this.timecard.timecardDays.find(
        (timecardDay: TimecardDay) => timecardDay.isActive,
      );
    },
    async getTimecardAudits(): Promise<void> {
      const { data: audits } = await listTimecardAudit(this.timecardId);
      this.audits = audits;
    },
    async load() {
      this.loading = true;
      await Promise.all([
        await this.getProjectMember(),
        await this.getTrackingDetails(),
        this.getWorkStatuses(),
        this.getWorkZones(),
        this.getReimbursements(),
        this.getTimecard(),
        this.getTimecardAudits(),
        this.loadShootLocations(),
        this.loadReimbursementTypes(),
        await this.getOccupations(),
        await this.getBatchDetails(),
        this.handleOccupationChange(true),
      ]);
      this.loading = false;
      this.unsavedChanges = this.workZonechangeUpdated ? true : false;
    },
    navigateToTimecards() {
      this.navigate({
        pathname: `/projects/${this.project.hashId}/admin/members/${this.projectMember.id}/member/timecards`,
      });
    },
    async deleteTimecard() {
      try {
        await deleteTimecard(this.timecardId);
        this.navigateToTimecards();
        SnackbarStore.triggerSnackbar(
          'Timecard deleted successfully.',
          2500,
          'success',
        );
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
        throw err;
      }
    },
    checkTimecardStatus() {
      if (this.timecard.status?.id === TimecardStatusId.InProgress) {
        this.isSubmitModalOpen = true;
      } else {
        this.isSignatureModalOpen = true;
      }
    },
    continueApproveTimecard() {
      this.isSubmitModalOpen = false;
      this.isSignatureModalOpen = true;
    },
    async approve() {
      if (this.loadingSignature) return;
      this.loadingSignature = true;
      const signatureRef = this.$refs.signaturePad as any;
      if (signatureRef.isEmpty()) {
        SnackbarStore.triggerSnackbar(
          'You must sign to approve.',
          2500,
          'error',
        );
        this.loadingSignature = false;
        return;
      }
      const signature = signatureRef.getSignatureImage();
      try {
        if (this.timecard.status?.id === TimecardStatusId.InProgress) {
          const EMPTY_SIGNATURE_BASE64 =
            'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAGQCAQAAAC+fJXwAAACzElEQVR42u3SQQ0AAAgDMeZf9DBBwqeVcLm0A+diLIyFsTAWGAtjYSwwFsbCWGAsjIWxwFgYC2OBsTAWxgJjYSyMBcbCWBgLjIWxMBYYC2NhLDAWxsJYYCyMhbHAWBgLY4GxMBbGAmNhLIwFxsJYGAuMhbEwFhgLY2EsMBbGwlhgLIyFscBYGAtjgbEwFsYCY2EsjAXGwlgYC4yFsTAWGAtjYSwwFsbCWGAsjIWxwFgYC2NhLBEwFsbCWGAsjIWxwFgYC2OBsTAWxgJjYSyMBcbCWBgLjIWxMBYYC2NhLDAWxsJYYCyMhbHAWBgLY4GxMBbGAmNhLIwFxsJYGAuMhbEwFhgLY2EsMBbGwlhgLIyFscBYGAtjgbEwFsYCY2EsjAXGwlgYC4yFsTAWGAtjYSwwFsbCWGAsjIWxwFgYC2OBsTAWxsJYxsJYGAtjgbEwFsYCY2EsjAXGwlgYC4yFsTAWGAtjYSwwFsbCWGAsjIWxwFgYC2OBsTAWxgJjYSyMBcbCWBgLjIWxMBYYC2NhLDAWxsJYYCyMhbHAWBgLY4GxMBbGAmNhLIwFxsJYGAuMhbEwFhgLY2EsMBbGwlhgLIyFscBYGAtjgbEwFsYCY2EsjAXGwlgYC2MZC2NhLIwFxsJYGAuMhbEwFhgLY2EsMBbGwlhgLIyFscBYGAtjgbEwFsYCY2EsjAXGwlgYC4yFsTAWGAtjYSwwFsbCWGAsjIWxwFgYC2OBsTAWxgJjYSyMBcbCWBgLjIWxMBYYC2NhLDAWxsJYYCyMhbHAWBgLY4GxMBbGAmNhLIwFxsJYGAuMhbEwFhgLY2EsMBbGwlgYSwSMhbEwFhgLY2EsMBbGwlhgLIyFscBYGAtjgbEwFsYCY2EsjAXGwlgYC4yFsTAWGAtjYSwwFsbCWGAsjIWxwFgYC2OBsTAWxgJjYSyMBcbCWBgLjIWxMBYYiw8Ln98enhovrYAAAAAASUVORK5CYII=';
          const timeZone = DateTime.now().zoneName;
          await submitTimecard(
            this.timecard.id,
            EMPTY_SIGNATURE_BASE64,
            timeZone,
          );
          await this.load();
        }
        await approveTimecard(this.timecard.id, signature);
        await this.load();
        this?.refresh?.();
        SnackbarStore.triggerSnackbar('Timecard approved.', 2500, 'success');
        this.navigate(-1);
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg =
            err.response?.data?.['errors']?.[0]?.message ||
            'Failed to approve timecard.';
          SnackbarStore.triggerSnackbar(msg, 3000, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
      }
      this.loadingSignature = false;
      this.isSignatureModalOpen = false;
    },
    async changePayPeriodModal() {
      if (!this.timecard.isActive) {
        SnackbarStore.triggerSnackbar(
          'You cannot change the pay period for deleted timecard.',
          2500,
          'error',
        );
        return;
      }
      if (this.restrictChangeWeek) {
        SnackbarStore.triggerSnackbar(
          'You cannot change the pay period for this timecard as it has been Submitted to payroll.',
          2500,
          'error',
        );
        return;
      }

      this.changePayPeriod.modal = true;
      const { data: payPeriods } = await getAvailablePayPeriods(
        this.projectMember.id,
      );
      this.changePayPeriod.payPeriods = payPeriods;
    },
    async changePayPeriodSubmit() {
      this.changePayPeriod.loading = true;
      try {
        await this.load();
        this.changePayPeriod.modal = false;
        SnackbarStore.triggerSnackbar(
          'Pay period changed successfully.',
          2500,
          'success',
        );
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
        throw err;
      }
      this.changePayPeriod.loading = false;
    },
    async changePayPeriodHandler() {
      if (!this.changePayPeriod.newPayPeriod) {
        SnackbarStore.triggerSnackbar(
          'Please select a new pay period.',
          2500,
          'error',
        );
        return;
      }
      this.changePayPeriod.loading = true;
      try {
        await changeTimecardPayPeriod(
          this.timecard.id,
          this.changePayPeriod.newPayPeriod.id,
        );
        await this.load();
        this.changePayPeriod.modal = false;
        SnackbarStore.triggerSnackbar(
          'Pay period changed successfully.',
          2500,
          'success',
        );
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(
            'Failed to update pay period.' as string,
            2500,
            'error',
          );
        }
      }
      this.changePayPeriod.loading = false;
    },
    async loadShootLocations() {
      const { data } = await listProjectShootLocations(this.project.id!);
      this.projectShootLocations = data;
    },
    getTimecardDayWorkLocation(reimbursement: Reimbursement) {
      const workLocation = this.timecard.timecardDays.find(
        (timecardDay: TimecardDay) => {
          return (
            timecardDay.date.toISODate() === reimbursement.date.toISODate()
          );
        },
      )?.projectShootLocation;
      if (workLocation) {
        reimbursement.workLocation = workLocation;
      }
    },
    getReimbursementReceipt(reimbursement: Reimbursement) {
      const link = document.createElement('a');
      link.href = getReimbursementReceipt(reimbursement.id!);
      link.setAttribute('download', 'reimbursement_receipt.pdf');
      document.body.appendChild(link);
      link.click();
    },
    checkWorkZonesLoaded() {
      if (this.workZones.length > 0) {
        this.timecard.timecardDays.forEach((day: TimecardDay) => {
          if (
            day.workStatus?.key?.toUpperCase() === WorkStatusKeys.KEY_IDLE &&
            !day.workZone &&
            this.workZones.length > 0
          ) {
            day.workZone = this.workZones.find(
              (getWorkzone: any) =>
                getWorkzone?.key?.toUpperCase() === WorkZoneKeys.KEY_DISTANT,
            );
            this.workZonechangeUpdated = true;
          }
        });
      }
    },
    workStatusSelectMethod(timecardDay: TimecardDay) {
      if (!timecardDay.workZone) {
        const convertKeyToUppercase =
          timecardDay.workStatus?.key?.toUpperCase();
        if (
          convertKeyToUppercase === WorkStatusKeys.KEY_IDLE ||
          convertKeyToUppercase === WorkStatusKeys.KEY_PAID_IDLE ||
          convertKeyToUppercase === WorkStatusKeys.KEY_TRAVEL ||
          convertKeyToUppercase === WorkStatusKeys.KEY_HNW ||
          convertKeyToUppercase === WorkStatusKeys.KEY_DOWN ||
          convertKeyToUppercase === WorkStatusKeys.KEY_CANCELPAY_NOREQ_NOPW ||
          convertKeyToUppercase === WorkStatusKeys.KEY_CANCELPAY_REQ_WITHPW ||
          convertKeyToUppercase === WorkStatusKeys.KEY_DGA_IDLE_PEN_ONLY ||
          convertKeyToUppercase === WorkStatusKeys.KEY_HOL ||
          convertKeyToUppercase === WorkStatusKeys.KEY_HOLNOPAY
        ) {
          timecardDay.hasNdb = false;
          if (
            convertKeyToUppercase === WorkStatusKeys.KEY_IDLE &&
            this.workZones.length > 0
          ) {
            const distantWorkZone = this.workZones.find(
              (workZone: any) =>
                workZone?.key?.toUpperCase() === WorkZoneKeys.KEY_DISTANT,
            );
            timecardDay.workZone = distantWorkZone;
          }
        }
      }
      if (
        timecardDay.isActive &&
        (this.projectMember?.lineNumber || this.projectMember?.shootLineNumber)
      ) {
        timecardDay.workStatus?.key?.toUpperCase() ===
          WorkStatusKeys.KEY_SHOOT && this.trackingHeaderDetails.allowed
          ? (timecardDay.lineNumber = this.projectMember?.shootLineNumber)
          : (timecardDay.lineNumber = this.projectMember?.lineNumber);
        this.unsavedChanges = true;
      }
    },
    workStatusKeyCheck(workStatusKey: string): boolean {
      return (
        workStatusKey?.toUpperCase() !== WorkStatusKeys.KEY_IDLE &&
        workStatusKey?.toUpperCase() !== WorkStatusKeys.KEY_PAID_IDLE &&
        workStatusKey?.toUpperCase() !== WorkStatusKeys.KEY_TRAVEL &&
        workStatusKey?.toUpperCase() !== WorkStatusKeys.KEY_HNW &&
        workStatusKey?.toUpperCase() !== WorkStatusKeys.KEY_DOWN &&
        workStatusKey?.toUpperCase() !==
          WorkStatusKeys.KEY_CANCELPAY_NOREQ_NOPW &&
        workStatusKey?.toUpperCase() !==
          WorkStatusKeys.KEY_CANCELPAY_REQ_WITHPW &&
        workStatusKey?.toUpperCase() !== WorkStatusKeys.KEY_DGA_IDLE_PEN_ONLY &&
        workStatusKey?.toUpperCase() !== WorkStatusKeys.KEY_HOL &&
        workStatusKey?.toUpperCase() !== WorkStatusKeys.KEY_HOLNOPAY
      );
    },
    updatePagination(pagination: Pagination): void {
      this.paginationOccupation = pagination;
      this.getOccupations();
    },
    async getOccupations() {
      this.loadingOccupations = true;
      try {
        const { data: occupationsData } = await listProjectOccupations(
          this.projectMember.projectId!,
          this.projectMember.workLocation?.payrollProjectLocationId || null,
          this.projectMember.unions || null,
          this.occupationSearch,
          this.paginationOccupation,
        );
        if (this.paginationOccupation.page === 1) {
          this.occupations = [];
        }
        this.occupations.push(...occupationsData.jobTitleList);
        this.paginationOccupation.total = occupationsData.totalCount;
        this.unsavedChanges = false;
      } catch (err) {
        console.warn('Failed to list occupations', err);
      }
      this.loadingOccupations = false;
    },
    async getTrackingDetails() {
      try {
        const { data: trackingKeys } = await getTrackingDetails(
          this.projectMember.projectId!,
        );
        const trackingKeyHeaderDetails = trackingKeys.trackingKeyHeaderDetails;
        if (
          trackingKeyHeaderDetails === null ||
          trackingKeyHeaderDetails.length === 0
        ) {
          this.trackingHeaderDetails.allowed = false;
          this.trackingHeaderDetails.required = false;
        }
        if (
          trackingKeyHeaderDetails !== null &&
          trackingKeyHeaderDetails.length > 0
        ) {
          trackingKeyHeaderDetails.forEach((eachTrackingKey: any) => {
            if (eachTrackingKey?.name === TrackingKeysEnum.KEY_TRACKKEY) {
              this.trackingHeaderDetails.allowed = eachTrackingKey?.allowed;
              this.trackingHeaderDetails.required = eachTrackingKey?.required;
            }
          });
        }
      } catch (err) {
        console.warn('Failed to get tracking details', err);
      }
    },
    async handleOccupationChange(onMountedCall: boolean = false) {
      const union: any = this.timecard.union;
      if (!union) {
        return;
      }
      if (!onMountedCall) {
        this.timecard.rateType = undefined;
      }
      await this.loadContractSettingsAndScheduleInfo();
      if (this.contractSettingsAndScheduleInfo.isHireLocationRequired) {
        this.loadingHireLocation = true;
        const { data } = await listProjectHireLocations(
          this.projectMember.projectId!,
          this.projectMember.workLocation?.payrollProjectLocationId!,
          union?.castAndCrewId || union.id!,
          this.timecard.occupation?.payrollOccupationCode!,
        );
        this.timecard.requiresHireLocation = data.isHireLocationRequired;
        if (!this.timecard.requiresHireLocation) {
          this.timecard.hireLocation = null;
          this.timecard.hireLocationId = null;
        }
        this.loadingHireLocation = false;
        this.hireLocations = data.hireLocationList;
      } else {
        this.timecard.requiresHireLocation = false;
        this.timecard.hireLocation = null;
        this.timecard.hireLocationId = null;
        this.timecard.hireLocation = null;
        this.timecard.hireLocationId = null;
      }
      this.getRateTypes();
    },
    async loadContractSettingsAndScheduleInfo() {
      try {
        const { data } = await getContractSettingsAndScheduleInfo(
          this.projectMember.projectId!,
          this.projectMember.workLocation?.payrollProjectLocationId!,
          this.timecard.occupation?.payrollOccupationCode!,
          this.projectMember.startDate?.toISODate()!,
          this.timecard.union?.name!,
          this.timecard.union?.castAndCrewId! || this.timecard.union.id!,
          this.timecard.hireLocation?.id!,
        );
        this.contractSettingsAndScheduleInfo = data;
        this.timecard.isOnCall =
          this.contractSettingsAndScheduleInfo.isOnCallIndicator;
        this.timecard.isExempt =
          this.contractSettingsAndScheduleInfo.isExemptIndicator;
        this.timecard.isHalfDayAllowed =
          this.contractSettingsAndScheduleInfo.isHalfDayAllowed;
        this.timecard.isNdbAllowed =
          this.contractSettingsAndScheduleInfo.isNdbAllowed;
      } catch (err) {
        SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
      }
    },
    async getRateTypes() {
      this.loadingRateTypes = true;
      this.rateTypes = [];
      const { data: rateTypesData } = await listRateTypes();
      const filteredRateTypes = this.filterRateTypes(rateTypesData);
      if (this.contractSettingsAndScheduleInfo?.isOnCallIndicator) {
        this.rateTypes = filteredRateTypes.filter(
          (rt: any) => rt.key === 'daily',
        );
      } else {
        this.rateTypes = filteredRateTypes;
      }
      this.loadingRateTypes = false;
      if (!this.timecard.guarHours) {
        this.calculateGuarHoursAndRate();
      }
    },
    filterRateTypes(rateTypesData: RateType[]) {
      const allowedRateTypes: string[] = ['daily', 'hourly'];
      return rateTypesData.filter((rt: RateType) =>
        allowedRateTypes.includes(rt.key),
      );
    },
    calculateGuarHoursAndRate() {
      let guarHours = 0;
      if (this.timecard.rateType.key === 'for_10_hours') {
        guarHours = 10;
      } else if (this.timecard.rateType.key === 'for_12_hours') {
        guarHours = 12;
      } else if (this.timecard.rateType.key === 'for_8_hours') {
        guarHours = 8;
      } else if (this.timecard.rateType.key === 'daily') {
        guarHours = this.timecard.isOnCall ? 12 : 1;
      }
      this.timecard.guarHours = guarHours;
      this.timecard.guarRate = Number(
        (this.timecard.guarHours * this.timecard.hourlyRate).toFixed(2),
      );
      this.unsavedChanges = false;
    },
    calculateRate() {
      if (
        this.timecard.guarHours &&
        this.timecard.guarHours != 0 &&
        this.timecard.rateType
      ) {
        this.timecard.guarRate = Number(
          (this.timecard.guarHours * this.timecard.hourlyRate).toFixed(2),
        );
      } else {
        this.timecard.guarRate = 0;
      }
      this.unsavedChanges = false;
    },
    resetRateForTimecardDays() {
      this.timecard.timecardDays.forEach((day: TimecardDay) => {
        day.rate = this.timecard.hourlyRate;
      });
      this.unsavedChanges = false;
    },
    resetPagination(pagination: Pagination) {
      pagination.page = 1;
      return pagination;
    },
    async htgDetails() {
      this.loadHtgTcReport = true;
      try {
        const { data } = await getTimecardHtgReport(
          this.timecardId,
          this.batchId,
        );
        if (data.preSignedUrl) {
          window.open(data.preSignedUrl);
          SnackbarStore.triggerSnackbar(
            'Timecard HTG report generated successfully.',
          );
        }
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(
            'Error on generating timecard HTG report.',
            2500,
            'error',
          );
        }
      }
      this.loadHtgTcReport = false;
    },
    fieldDirtyChange() {
      this.unsavedChanges = true;
    },
  },
  async mounted() {
    await this.load();
  },
});
</script>
