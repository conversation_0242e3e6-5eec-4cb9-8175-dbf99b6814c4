import { applyPureVueInReact } from 'veaury';
import ProjectAdminSignPaperworkFormVue from '../../../../views/projects/admin/ProjectAdminSignPaperworkForm.vue';
import { useAuth, useReactRouter } from '../../../AppHooks';
import { useOutletContext } from 'react-router';

const ReactProjectAdminSignPaperworkForm = applyPureVueInReact(
  ProjectAdminSignPaperworkFormVue,
);

const ProjectAdminSignPaperworkForm = () => {
  useAuth();
  const { navigate, route } = useReactRouter();
  const context = useOutletContext();
  return (
    <ReactProjectAdminSignPaperworkForm
      project={context.project}
      navigate={navigate}
      route={route}
    />
  );
};

export default ProjectAdminSignPaperworkForm;
