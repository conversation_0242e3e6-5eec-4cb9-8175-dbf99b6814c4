import React from 'react';
import PropTypes from 'prop-types';

import { PillBadge } from '@/reactComponents/library';
import { TimecardStatusId, TimecardStatusLabel } from '@/utils/enum';
import { BatchStatusEnum } from '@/types/Batch';

const tcVariantMap = {
  [TimecardStatusId.InProgress]: 'warning',
  [TimecardStatusId.Submitted]: 'info',
  [TimecardStatusId.Approved]: 'success',
  [TimecardStatusId.Rejected]: 'error',
};

const batchVariantMap = {
  [BatchStatusEnum.OPEN]: 'tertiaryAttention',
  [BatchStatusEnum.PENDING_ADMIN_APPROVAL]: 'primaryInfo',
  [BatchStatusEnum.SUBMITTED_TO_PAYROLL]: 'primaryInfo',
  [BatchStatusEnum.REOPENED]: 'tertiaryAttention',
  [BatchStatusEnum.READY_FOR_CLIENT_REVIEW]: 'primaryInfo',
  [BatchStatusEnum.RESUBMITTED]: 'primaryInfo',
  [BatchStatusEnum.APPROVED]: 'tertiaryPositive',
  [BatchStatusEnum.PAID]: 'primaryInteractive',
};

const StatusBadge = (props) => {
  const { tcStatusId, batchStatusId, ...rest } = props;

  let otherProps = { ...rest };
  let label = '';
  let variant = 'gray';
  if (tcStatusId) {
    label = TimecardStatusLabel[tcStatusId];
    variant = tcVariantMap[tcStatusId] || 'gray';
  } else if (batchStatusId) {
    label = BatchStatusEnum[batchStatusId];
    label = label?.replace(/_/g, ' ').toLowerCase();
    label = label?.charAt(0).toUpperCase() + label?.slice(1);

    variant = batchVariantMap[batchStatusId] || 'gray';
    otherProps.squared = true;
    otherProps.statusIcon = true;
  }

  return (
    <PillBadge variant={variant} {...otherProps}>
      {label}
    </PillBadge>
  );
};

StatusBadge.propTypes = {
  batchStatusId: PropTypes.number,
  tcStatusId: PropTypes.number,
};

export default StatusBadge;
