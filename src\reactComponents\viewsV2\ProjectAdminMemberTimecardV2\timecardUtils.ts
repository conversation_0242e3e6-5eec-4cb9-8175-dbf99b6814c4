import { WorkStatusKeys } from '@/types/WorkStatus';
import type { default as Timecard } from '@/types/Timecard';
import type { TemplateTimecard } from '@/types/TemplateDay';
import type TimecardDay from '@/types/TimecardDay';
import type TimecardDayMeal from '@/types/TimecardDayMeal';
import type Reimbursement from '@/types/Reimbursements';
import type { ContractSettingsAndScheduleInfo } from '@/types/ContractSettingsAndScheduleInfo';
import { TrackingKeysEnum } from '@/types/TimecardDay';
import _cloneDeep from 'lodash/cloneDeep';
import { DateTime } from 'luxon';
import { getTrackingDetails } from '@/services/project';
import { createContext } from 'react';
import { setTimeZone } from '@/reactComponents/utils/dateTime';

import debug from 'debug';

type TimecardWithContractSettings = Timecard & ContractSettingsAndScheduleInfo;

export const db = debug('timecardV2');

export const INITIAL_TC_ERRORS = { isValid: true };

export const MILEAGE_RATE = 0.7; //TODO https://castandcrew.atlassian.net/browse/MTG-867

export const TimecardContext = createContext<any>({});

export const timeFieldLabel = {
  startsAt: 'Time In',
  generalCrewCall: 'General Crew Call',
  'meals.0.startsAt': 'Meal 1 Out',
  'meals.0.endsAt': 'Meal 1 In',
  'meals.1.startsAt': 'Meal 2 Out',
  'meals.1.endsAt': 'Meal 2 In',
  endsAt: 'Time Out',
};

export const timeFieldOrder = Object.keys(timeFieldLabel);

const addValError = ({
  errors,
  field,
  msg,
  day = {},
}: {
  errors: any;
  field: string;
  msg: string;
  day?: any;
}) => {
  const dayId = day?.id ? `day${day.id}` : '';

  if (dayId) {
    if (!errors[dayId]) {
      errors[dayId] = {};
      if (day.date.isValid) {
        //Add date to make debugging easier when dumping validation messages
        const date = day.date;
        const dateStr = date.toFormat('MM/dd/yyyy');
        errors[dayId].dateStr = dateStr;
      }
    }
    if (!errors[dayId][field]) errors[dayId][field] = {};

    errors[dayId][field].message = msg;
  } else {
    if (!errors[field]) errors[field] = {};

    errors[field].message = msg;
  }

  errors.isValid = false;
};

export const validateTimecard = (
  timecard: TimecardWithContractSettings | TemplateTimecard,
  isTemplate: boolean = false,
  reimbursements = [] as Reimbursement[],
  trackingDetails = {} as any,
) => {
  const errors = { ...INITIAL_TC_ERRORS };
  if (!timecard) return errors;

  // This Validation hasn't been requested specifically
  // commenting out to preserve the method which TimecardDetails is ready for (textCells at least)
  // const { hourlyRate } = timecard;
  // const numRate = Number(hourlyRate);
  // if (isNaN(numRate) && !isTemplate) {
  //   addValError({
  //     errors,
  //     field: 'hourlyRate',
  //     msg: 'Hourly Rate must be a number',
  //   });
  // }
  // if (
  //   (hourlyRate === undefined || hourlyRate === null || hourlyRate === '') &&
  //   !isTemplate
  // ) {
  //   addValError({
  //     errors,
  //     field: 'hourlyRate',
  //     msg: 'Hourly Rate is required',
  //   });
  // }

  // validate any timecard specific fields
  if (!isTemplate) {
    const isLineNumberRequired = !!trackingDetails?.required;
    validateTimecardWithContracts(
      timecard as TimecardWithContractSettings,
      reimbursements,
      isLineNumberRequired,
      errors,
    );
  }

  // validate timecard and template day shared fields

  timecard.timecardDays.forEach((day) => {
    const addDayError = (field: string, msg: string) =>
      addValError({ errors, day, msg, field });

    if (day.isActive) {
      //Removing time field validation for now as it interferes with the timecards that are crossing midnight border
      // timeFieldOrder.forEach((field, idx) => {
      //   const currentValue = get(day, field);
      //   if (isTemplate && field === 'startsAt') return;

      //   for (let i = idx + 1; i < timeFieldOrder.length; i++) {
      //     const nextField = timeFieldOrder[i];
      //     const nextValue = get(day, nextField);
      //     if (isTemplate && nextField === 'startsAt') continue;
      //     if (!nextValue || !currentValue) continue;
      //     if (
      //       nextField === 'generalCrewCall' &&
      //       field === 'startsAt' &&
      //       currentValue.equals(nextValue)
      //     )
      //       //crew call can have same value as startsAt
      //       continue;
      //     if (currentValue >= nextValue) {
      //       let generalSpecial = '';
      //       if (nextField === 'generalCrewCall' && field === 'startsAt') {
      //         generalSpecial = '/equal to';
      //       }
      //       const msgAfter = `${timeFieldLabel[nextField]} must be after${generalSpecial} ${timeFieldLabel[field]}`;
      //       addDayError(nextField, msgAfter);
      //       break;
      //     }
      //   }
      // });

      if (day.hasNdb && !day.generalCrewCall) {
        addDayError(
          'generalCrewCall',
          'General Crew Call is required when NDB is enabled',
        );
      }
    }
  });

  db(errors);
  return errors;
};

const validateTimecardWithContracts = (
  timecard: TimecardWithContractSettings,
  reimbursements: Reimbursement[],
  isLineNumberRequired: boolean,
  errors: any,
) => {
  const isApproved =
    false &&
    // timecard.project.castAndCrewId && //BE code verifies against timecard.project.productionCompany.castAndCrewId
    // setting to always false for now until we can get the correct data here.
    // TODO for FPS-1487
    timecard.capsPayId;

  if (timecard.isHireLocationRequired && !timecard.hireLocation) {
    addValError({
      errors,
      field: 'hireLocation',
      msg: 'Hire Location is required',
    });
  }

  if (isApproved && isLineNumberRequired) {
    timecard.timecardDays.forEach((day) => {
      const addDayError = (field: string, msg: string) =>
        addValError({ errors, day, msg, field });

      if (day.isActive) {
        if (!day.lineNumber || day.lineNumber.trim() === '') {
          addDayError('lineNumber', 'Line# is required');
        }
      }
    });

    // BE fills in mileage form line number if its missing
    // not validating it here

    if (timecard.kitRental) {
      const kitRental = timecard.kitRental;
      if (!kitRental.lineNumber || kitRental.lineNumber.trim() === '') {
        addValError({
          errors,
          field: 'kitRental.lineNumber',
          msg: 'Kit Rental Line# is required',
        });
      }
    }

    reimbursements.forEach((r: Reimbursement) => {
      const reId = r.id;
      const forText = r.name
        ? ` for reimbursement "${r.name}"`
        : 'for reimbursements';
      if (!r.lineNumber || r.lineNumber.trim() === '') {
        addValError({
          errors,
          field: `re${reId}.lineNumber`,
          msg: `Line# is required ${forText}`,
        });
      }
    });
  }
};

export const timecardOnLoad = (
  freshTimecard: Timecard,
  projectMember: any,
  trackingHeaderDetails: any,
  contractSettings: ContractSettingsAndScheduleInfo,
) => {
  const timecard: TimecardWithContractSettings = {
    ...freshTimecard,
    ...contractSettings,
  };

  if (!timecard.isHireLocationRequired) {
    timecard.hireLocation = null;
    timecard.hireLocationId = null;
  }

  timecard.timecardDays.forEach((day: TimecardDay) => {
    day.startsAt = day.startsAt.toUTC();
    day.endsAt = day.endsAt.toUTC();
    day.generalCrewCall = day.generalCrewCall?.toUTC();
    day.meals.forEach((meal: TimecardDayMeal) => {
      meal.startsAt = meal.startsAt.toUTC();
      meal.endsAt = meal.endsAt.toUTC();
    });
    day.date = day.date.toUTC();
    if (
      day.isActive &&
      trackingHeaderDetails?.allowed &&
      (day.lineNumber === '' || day.lineNumber == null)
    ) {
      day.lineNumber =
        day.workStatus?.key?.toUpperCase() === WorkStatusKeys.KEY_SHOOT
          ? projectMember?.shootLineNumber
          : projectMember?.lineNumber;
    }
  });
  if (timecard.mileageForm) {
    const mileageForm = timecard.mileageForm;

    if (mileageForm && !mileageForm.rate) {
      mileageForm.rate = MILEAGE_RATE;

      mileageForm.totalAmount =
        Math.floor(timecard.mileageForm.totalMileage * MILEAGE_RATE * 100) /
        100;
    }
  }

  if (timecard.kitRental) {
    const kitRental = timecard.kitRental;
    const { rateType } = kitRental;
    let { rentalRate } = kitRental;

    if (rentalRate) {
      rentalRate = kitRental.rentalRate / 100;
      kitRental.rentalRate = rentalRate;
      if (rateType?.key === 'weekly') {
        kitRental.totalAmount = Math.floor(rentalRate * 100) / 100;
      } else {
        const rentalDaysCount =
          timecard.timecardDays?.filter(isRentalDay).length || 0;
        kitRental.totalAmount =
          Math.floor(rentalRate * rentalDaysCount * 100) / 100;
      }
    }

    if (rateType) {
      // remove when we're saving date properly MTG-1088

      kitRental.date = calcKitDate(rateType.key, timecard.timecardDays);
    }
  }

  return timecard;
};

export const calcKitDate = (
  rateTypeKey: string,
  timecardDays: TimecardDay[],
) => {
  const activeDays = timecardDays.filter((day: TimecardDay) => day.isActive);
  const rentalDays = activeDays.filter((day: TimecardDay) => day.isRentalDay);
  const firstActiveDayDate = activeDays[0]?.date || null;
  const firstRentalDayDate = rentalDays[0]?.date || null;

  return rateTypeKey === 'weekly'
    ? firstActiveDayDate
    : rateTypeKey === 'daily'
    ? firstRentalDayDate
    : null;
};

export const changeWorkWeek = (
  prev: Timecard,
  newWorkWeek: any,
  setReimbursements: (args: any) => any,
) => {
  const timecard = _cloneDeep(prev);
  const { timecardDays } = timecard;

  const oldStartsAt = setTimeZone(timecard?.payPeriod?.startsAt, 'UTC');
  const oldEndsAt = setTimeZone(timecard?.payPeriod?.endsAt, 'UTC');
  const newEndsAt = setTimeZone(newWorkWeek.endsAt, 'UTC');
  const shouldChange = (date: DateTime) =>
    date >= oldStartsAt && date < oldEndsAt;

  const currWeekEnding = timecardDays[6].date;
  // Calculate the offset between the current Sunday and the new one
  // Luxon diff is Float, therefore we need to ceil it
  const offset = Math.ceil(newEndsAt.diff(currWeekEnding, 'days').days);

  if (Number.isNaN(offset)) {
    throw new Error('Error changing week');
  }

  // timecard update
  timecard.payPeriod = newWorkWeek;
  timecard.payPeriodId = newWorkWeek.id;

  // timecard days update
  timecardDays.forEach((day: TimecardDay) => {
    for (const k in day) {
      const key = k as keyof TimecardDay;

      if (key === 'updatedAt' || key === 'createdAt') continue;
      if (Object.prototype.hasOwnProperty.call(day, key)) {
        const value = day[key];
        if (value instanceof DateTime) {
          // maybe better solution: https://github.com/microsoft/TypeScript/issues/31663#issuecomment-518858179
          (day[key] as TimecardDay[typeof key]) = value.plus({ days: offset });
        }
      }
    }

    if (day.meals && Array.isArray(day.meals)) {
      day.meals.forEach((meal: TimecardDayMeal) => {
        for (const k in meal) {
          const key = k as keyof TimecardDayMeal;
          if (key === 'updatedAt' || key === 'createdAt') continue;
          if (Object.prototype.hasOwnProperty.call(meal, key)) {
            const value = meal[key];
            if (value instanceof DateTime) {
              (meal[key] as TimecardDayMeal[typeof key]) = value.plus({
                days: offset,
              });
            }
          }
        }
      });
    }
  });

  // mileage form update
  const mileageDate: any = timecard.mileageForm?.date;
  if (mileageDate instanceof DateTime && shouldChange(mileageDate)) {
    timecard.mileageForm.date = timecard.mileageForm.date?.plus({
      days: offset,
    });
  }

  // kit rental update
  if (timecard.kitRental?.date instanceof DateTime) {
    timecard.kitRental.date = timecard.kitRental.date.plus({ days: offset });
  }

  setReimbursements((prev: Reimbursement[]) => {
    const reimbursements = _cloneDeep(prev);
    reimbursements.forEach((reimbursement: Reimbursement) => {
      if (reimbursement.date instanceof DateTime) {
        if (shouldChange(reimbursement.date)) {
          reimbursement.date = reimbursement.date.plus({ days: offset });
        }
      }
    });
    return reimbursements;
  });

  return timecard;
};

export const prepTimecardForSave = (timecard: Timecard) => {
  if (timecard.kitRental?.rentalRate) {
    timecard.kitRental.rentalRate *= 100;
  }

  if (Array.isArray(timecard.reimbursements)) {
    timecard.reimbursements.forEach((reimbursement: Reimbursement) => {
      if (reimbursement.rate) {
        reimbursement.rate *= 100;
      }
      if (reimbursement.totalAmount) {
        reimbursement.totalAmount *= 100;
      }
    });
  }

  timecard.timecardDays.forEach((timecardDay: TimecardDay) => {
    timecardDay.mealPenalties = parseInt(
      (timecardDay.mealPenalties * 100).toFixed(0),
    );

    //remove meals with null start/end times
    timecardDay.meals = timecardDay.meals.filter((meal: TimecardDayMeal) => {
      return meal.startsAt !== null && meal.endsAt !== null;
    });
  });

  return timecard;
};

export const reimbursementsOnLoad = (reimbursements: Reimbursement[]) => {
  reimbursements.forEach((reimbursement: Reimbursement) => {
    reimbursement.rate = reimbursement.rate / 100;
    reimbursement.totalAmount = (reimbursement.totalAmount || 0) / 100;
  });
  return reimbursements;
};

export const reimbursementFileName = ({
  member = {},
  title = '',
  date = null,
}: {
  member?: any;
  title?: string;
  date?: DateTime | null;
}) => {
  const { user: { firstName = '', lastName = '' } = {} } = member || {};
  const dateStr = date ? '_' + date.toFormat('MM.dd.yyyy') : '';

  return `${lastName}, ${firstName}_${title}${dateStr}.pdf`;
};

export const fetchTrackingDetails = async (projectId: any) => {
  try {
    const { data: trackingKeys } = await getTrackingDetails(projectId);
    const trackingDetails = { allowed: true, required: true };
    const trackingKeyHeaderDetails = trackingKeys.trackingKeyHeaderDetails;
    if (
      trackingKeyHeaderDetails === null ||
      trackingKeyHeaderDetails.length === 0
    ) {
      trackingDetails.allowed = false;
      trackingDetails.required = false;
    }
    if (
      trackingKeyHeaderDetails !== null &&
      trackingKeyHeaderDetails.length > 0
    ) {
      trackingKeyHeaderDetails.forEach((eachTrackingKey: any) => {
        if (eachTrackingKey?.name === TrackingKeysEnum.KEY_TRACKKEY) {
          trackingDetails.allowed = eachTrackingKey?.allowed;
          trackingDetails.required = eachTrackingKey?.required;
        }
      });
    }

    return trackingDetails;
  } catch (err) {
    console.warn('Failed to get tracking details', err);
  }
};

export function parseTime(value: string | DateTime | null) {
  if (value instanceof DateTime) {
    return value.toFormat('hh:mm a');
  }
  const timeVal = value ? DateTime.fromISO(value).toFormat('h:mm a') : '';
  if (timeVal === 'Invalid DateTime') return '';
  return timeVal;
}

// lodash's get method has some problem in timepicker cell and valueFormatter
// use case: get(row, 'meals.1.startsAt') => undefined or its value
export const get = (object: any, path: string) => {
  try {
    const pathArr = path.split('.');
    let target = object;
    if (!object) return undefined;
    for (let i = 0; i < pathArr.length; i++) {
      target = target[pathArr[i]];
      if (target === undefined) return undefined;
    }
    return target;
  } catch (e) {
    console.error('get function error', e);
    return undefined;
  }
};

// use case: set(row, 'meals.1.startsAt', '2023-10-01T00:00:00.000Z'):
// set row[meals][1][startsAt] = '2023-10-01T00:00:00.000Z', if there's no object/array in the path, create a new one
export const set = (object: any, path: string, value: any) => {
  if (!object) {
    console.error('please use valid object');
    return;
  }
  const pathArr = path.split('.');

  let target = object;
  for (let i = 0; i < pathArr.length; i++) {
    const prop = pathArr[i];
    const nextProp = pathArr[i + 1];
    const isLastProps = nextProp === undefined;

    const isNumber = /^\d+$/.test(nextProp);
    const nextTarget = target[prop];

    // console.log(
    //   `---loop ${i}---:`,
    //   prop,
    //   nextProp,
    //   isLastProps,
    //   nextTarget,
    //   isNumber,
    // );

    if (!isLastProps) {
      if (nextTarget === undefined) {
        target[prop] = isNumber ? [] : {};
      }
      target = target[prop];
    } else if (isLastProps) {
      target[prop] = value;
    }
  }
};

export const isRentalDay = (day: any) => day.isActive && day.isRentalDay;

//kit rental types
export const rentalRateTypes = [{ id: 1, key: 'daily', name: 'Daily' }];

//reimbursement columns
export const COLUMNS = [
  {
    place: 0,
    columnId: 'lineNumber',
    label: 'Line#',
    type: 'text',
    width: '80px',
    otherExpense: true,
    mileageForm: true,
    kitRental: true,
  },
  {
    place: 1,
    columnId: 'expense',
    label: 'Expense',
    type: 'autocomplete',
    width: '150px',
    otherExpense: true,
    mileageForm: false,
    kitRental: false,
  },
  {
    place: 1,
    columnId: 'mileageExpense',
    label: 'Expense',
    type: 'display',
    otherExpense: false,
    mileageForm: true,
    kitRental: false,
    width: '150px',
  },
  {
    place: 1,
    columnId: 'kitExpense',
    label: 'Expense',
    type: 'display',
    otherExpense: false,
    mileageForm: false,
    kitRental: true,
    width: '150px',
  },
  {
    place: 2,
    columnId: 'rate',
    label: 'Rate $',
    type: 'display',
    isNum: true,
    otherExpense: false,
    mileageForm: true,
    kitRental: false,
    width: '70px',
    decimalPlaces: [2, 2],
  },
  {
    place: 2,
    columnId: 'rate',
    label: 'Rate $',
    type: 'text',
    isNum: true,
    otherExpense: true,
    mileageForm: false,
    kitRental: false,
    width: '70px',
    decimalPlaces: [2, 2],
  },
  {
    place: 2,
    columnId: 'rentalRate',
    label: 'Rate $',
    type: 'text',
    isNum: true,
    otherExpense: false,
    mileageForm: false,
    kitRental: true,
    width: '70px',
    decimalPlaces: [2, 2],
  },
  {
    place: 3,
    columnId: 'rentalDays',
    label: 'Rental Days',
    type: 'rentalDays',
    otherExpense: false,
    mileageForm: false,
    kitRental: true,
    width: '100px',
  },
  {
    place: 3,
    columnId: 'totalMileage',
    label: 'Mileage',
    type: 'text',
    isNum: true,
    otherExpense: false,
    mileageForm: true,
    kitRental: false,
    width: '100px',
    decimalPlaces: [2, 4],
  },
  {
    place: 3,
    columnId: 'quantity',
    label: 'Quantity',
    type: 'text',
    otherExpense: true,
    mileageForm: false,
    kitRental: false,
    isIntOnly: true,
    width: '100px',
  },
  {
    place: 4,
    columnId: 'date',
    label: 'Date',
    type: 'date',
    width: '130px',
    otherExpense: true,
    mileageForm: true,
    kitRental: false,
  },
  {
    place: 4,
    columnId: 'date',
    label: 'Date',
    type: 'display',
    width: '130px',
    otherExpense: false,
    mileageForm: false,
    kitRental: true,
  },
  {
    place: 5,
    columnId: 'workLocation',
    label: 'Work Location',
    type: 'autocomplete',
    width: '200px',
    otherExpense: true,
    mileageForm: true,
    kitRental: true,
  },
  {
    place: 6,
    columnId: 'totalAmount',
    label: 'Total Amount',
    type: 'display',
    isNum: true,
    otherExpense: true,
    mileageForm: true,
    kitRental: true,
    width: '75px',
  },

  {
    place: 7,
    columnId: 'documentId',
    label: 'Attachment',
    type: 'fileUpload',
    otherExpense: true,
    mileageForm: true,
    kitRental: true,
    width: '100px',
  },
  {
    place: 8,
    columnId: 'deleteBtn',
    label: '',
    type: 'deleteBtn',
    otherExpense: true,
    mileageForm: true,
    kitRental: true,
    width: '75px',
  },
];

if (process.env.NODE_ENV === 'development') {
  COLUMNS.forEach((colA) => {
    COLUMNS.forEach((colB) => {
      if (colA.place === colB.place && colA.width !== colB.width) {
        console.error(
          'Column width mismatch. Columns in Same place must have same width',
          colA,
          colB,
        );
      }
    });
  });
}

export const getCalcIssues = (
  isSuccess: boolean,
  warning: string,
  message: string,
) => {
  const newCalcIssues: any[] = [];
  if (warning || isSuccess === false) {
    const warnings = warning?.split('\n') || [];
    warnings.forEach((w) => newCalcIssues.push({ message: w, isError: false }));

    if (isSuccess === false) {
      const errors = message?.split('\n') || [];
      errors.forEach((e) => newCalcIssues.push({ message: e, isError: true }));
    }
  }
  return newCalcIssues;
};

/**
 * get the last active day on the timecard OR the last day on the timecard if no active days
 */
export const calcMileageDate = (timecard: Timecard) => {
  const days = timecard.timecardDays;
  let mileageDate = null;
  days.forEach((day: any) => {
    if (day.isActive) {
      mileageDate = day.date;
    }
  });

  if (mileageDate === null) {
    mileageDate = days[days.length - 1].date;
  }

  return mileageDate;
};

export const makeSaveTooltip = (tcErrors: any) => {
  const { isValid, ...errors } = tcErrors;

  if (isValid) return '';
  const errorKeys = Object.keys(errors);
  const errorDayIds = errorKeys.filter((key) => key.startsWith('day'));
  const errorFields = errorKeys.filter((key) => !key.startsWith('day'));
  const errorMsgs = [];

  let hiddenCount = 0;

  errorDayIds.forEach((key) => {
    const dayError = errors[key];

    const { dateStr, ...fieldErrors } = dayError;
    if (!hiddenCount) errorMsgs.push(dateStr);

    Object.keys(fieldErrors).forEach((field) => {
      const fieldError = fieldErrors[field];
      if (errorMsgs.length > 4) {
        hiddenCount += 1;
      } else {
        errorMsgs.push(fieldError.message);
      }
    });
  });

  errorFields.forEach((key) => {
    const fieldError = errors[key];
    if (errorMsgs.length > 4) {
      hiddenCount += 1;
    } else {
      errorMsgs.push(fieldError.message);
    }
  });

  if (hiddenCount) {
    errorMsgs.push(`and ${hiddenCount} more...`);
  }

  return errorMsgs;
};
