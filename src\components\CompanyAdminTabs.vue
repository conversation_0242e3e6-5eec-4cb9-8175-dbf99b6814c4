<template>
  <div class="bg-gray-50 dark:bg-gray-700 w-full hidden sm:block">
    <div class="flex items-center justify-start">
      <PrimaryTab
        v-for="tab in tabs"
        :key="tab.label"
        :tab="tab"
        :currentTab="currentTab"
        @tabClick="goTo"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject } from 'vue';
import PrimaryTab from './library/PrimaryTab.vue';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';

const navigate = inject('navigate') as Function;
const route = inject('route') as { value: ParsedRoute };

type Tab = { label: string; key: string; routeName: string; routePath: string };

const props = defineProps<{
  tabs: Tab[];
}>();

const currentTab = computed(() => {
  const currentPath = route.value?.location.pathname;
  const lastRoutePostion = 3;
  const routeSplits = currentPath.split('/');
  const currentRoute = routeSplits[lastRoutePostion] || '';
  return props.tabs.find((tab) => tab.routePath === currentRoute);
});

const companyId = computed((): number => {
  return parseInt(route.value.params.id as string);
});

const goTo = (routeName: Tab): void => {
  navigate({
    pathname: `/companies/${companyId.value}/${routeName.routePath || ''}`,
  });
};
</script>
