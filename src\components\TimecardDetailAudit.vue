<template>
  <li class="pb-4">
    <span
      v-if="!lastItem"
      class="absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-500"
      aria-hidden="true"
    ></span>
    <div class="relative flex space-x-3">
      <div>
        <span
          :class="[
            iconBackground,
            'h-8 w-8 rounded-full flex items-center justify-center',
          ]"
        >
          <component
            :is="icon"
            class="h-4 w-4 text-white dark:text-gray-800"
            aria-hidden="true"
          />
        </span>
      </div>
      <div class="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
        <div>
          <p class="text-sm text-gray-500 dark:text-gray-300">
            {{ actionDisplay }}
            <span
              class="font-semibold"
              v-if="
                computedAction === 'batch_update' ||
                computedAction === 'batch_remove'
              "
            >
              {{ batchDetail }}
            </span>
            by
            <span class="font-semibold">{{ actorName }}</span>
          </p>
          <p
            v-if="notes"
            class="text-sm text-gray-500 dark:text-gray-300 italic"
          >
            {{ notes }}
          </p>
        </div>
        <div
          class="whitespace-nowrap text-right text-sm text-gray-500 dark:text-gray-300"
        >
          <p>{{ formattedDate }}</p>
          <p>{{ formattedTimestamp }}</p>
        </div>
      </div>
    </div>
  </li>
</template>

<script setup lang="ts">
import type TimecardAudit from '@/types/TimecardAudit';
import {
  ArrowUpTrayIcon,
  BoltIcon,
  BoltSlashIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  HandThumbUpIcon,
} from '@heroicons/vue/20/solid';

import {
  computed,
  type FunctionalComponent,
  type HTMLAttributes,
  type VNodeProps,
} from 'vue';
const props = defineProps<{
  audit: TimecardAudit;
  lastItem: boolean;
}>();

const formattedDate = computed(() => {
  return props.audit.createdAt.toFormat('LLL dd');
});

const formattedTimestamp = computed(() => {
  return props.audit.createdAt.toFormat('h:mm a');
});

const actorName = computed(() => {
  return `${props.audit.user.firstName} ${props.audit.user.lastName}`;
});

const batchDetail = computed(() => {
  return `${props.audit.newValues.status.key}`;
});

const notes = computed(() => {
  return props.audit.notes;
});

const computedAction = computed(() => {
  const { action, newValues } = props.audit;
  const key = newValues.status.key;

  if (
    action === 'edit' ||
    action === 'batch_update' ||
    action === 'batch_remove'
  ) {
    return action;
  }

  return key;
});

const actionDisplay = computed(() => {
  const { action, newValues } = props.audit;
  const status = newValues.status;
  const key = status.key;

  if (action === 'edit') {
    return `Edited`;
  }
  if (
    key === 'payroll_manager_requested_changes' ||
    key === 'production_supervisor_requested_changes'
  ) {
    return `Changes Requested`;
  }
  if (key === 'production_supervisor_approved') {
    return `Approved`;
  }

  return status.name;
});

const iconBackground = computed(() => {
  const iconBackgroundMap: Record<string, string> = {
    created: 'bg-gray-500 dark:bg-gray-400',
    in_progress: 'bg-gray-500 dark:bg-gray-400',
    edit: 'bg-gray-500 dark:bg-gray-400',
    submitted: 'bg-blue-500 dark:bg-blue-400',
    production_supervisor_requested_changes: 'bg-yellow-500 dark:bg-yellow-400',
    payroll_manager_requested_changes: 'bg-yellow-500 dark:bg-yellow-400',
    production_supervisor_approved: 'bg-green-500 dark:bg-green-400',
    approved: 'bg-green-500 dark:bg-green-400',
    batch_update: 'bg-gray-500 dark:bg-gray-400',
    batch_remove: 'bg-gray-500 dark:bg-gray-400',
  };
  return iconBackgroundMap[computedAction.value];
});

const icon = computed(() => {
  const iconMap: Record<
    string,
    FunctionalComponent<HTMLAttributes & VNodeProps, {}, any>
  > = {
    created: DocumentTextIcon,
    in_progress: DocumentTextIcon,
    edit: DocumentTextIcon,
    submitted: ArrowUpTrayIcon,
    production_supervisor_requested_changes: ExclamationTriangleIcon,
    payroll_manager_requested_changes: ExclamationTriangleIcon,
    production_supervisor_approved: HandThumbUpIcon,
    approved: HandThumbUpIcon,
    batch_update: BoltIcon,
    batch_remove: BoltSlashIcon,
  };
  return iconMap[computedAction.value];
});
</script>
