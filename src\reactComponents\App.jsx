import { observer } from 'mobx-react-lite';
import React, { useEffect } from 'react';
import { RouterProvider } from 'react-router-dom';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterLuxon } from '@mui/x-date-pickers/AdapterLuxon';

import { Provider as SnackbarProvider } from '@/reactComponents/library/Snackbar';
import { Provider as OktaAuthProvider } from '@/reactComponents/library/OktaLogin';

import { appRoutes } from './routes';

import { isLoggedIn } from '../services/auth';
import VueSnackbar from './Snackbar';
import AppStore from './stores/app';
import PermissionStore from './stores/permission';
import DarkModeWrapper from './DarkModeWrapper';

import '../assets/base.css';
import { ThemeProvider } from '@mui/material/styles';
import theme from './theme/theme';
import { tabTitle } from './utils/tabTitle';

//TODO - remove these after spotlight integrates them in to their dependencies properly.
import { library } from '@fortawesome/fontawesome-svg-core';
import { fas } from '@fortawesome/free-solid-svg-icons';
import { far } from '@fortawesome/free-regular-svg-icons';
import { faTwitter, faFontAwesome } from '@fortawesome/free-brands-svg-icons';

library.add(fas, far, faTwitter, faFontAwesome);

const App = () => {
  const isDarkModeEnabled = AppStore.getIsDarkModeEnabled;
  useEffect(() => {
    if (isDarkModeEnabled) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [isDarkModeEnabled]);

  const handleDarkModeChange = () => {
    AppStore.setColorModeSettings();
  };
  const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

  const onHandleSignIn = async () => {
    const loggedIn = await isLoggedIn();
    if (loggedIn) {
      await PermissionStore.fetchPermissions();
      await PermissionStore.fetchAdmin();
    }
    handleDarkModeChange();
    darkModeMediaQuery.addEventListener('change', handleDarkModeChange);
  };

  useEffect(() => {
    window.document.title = tabTitle;
    onHandleSignIn();
    return () => {
      darkModeMediaQuery.removeEventListener('change', handleDarkModeChange);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <React.Fragment>
      <LocalizationProvider dateAdapter={AdapterLuxon}>
        <ThemeProvider theme={theme}>
          <DarkModeWrapper isDarkModeEnabled={AppStore.getIsDarkModeEnabled}>
            <SnackbarProvider>
              <VueSnackbar />
              <OktaAuthProvider>
                <RouterProvider router={appRoutes} />
              </OktaAuthProvider>
            </SnackbarProvider>
          </DarkModeWrapper>
        </ThemeProvider>
      </LocalizationProvider>
    </React.Fragment>
  );
};

export default observer(App);
