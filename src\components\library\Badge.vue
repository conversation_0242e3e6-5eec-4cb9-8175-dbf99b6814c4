<template>
  <div
    class="flex justify-center items-center"
    :class="[badgeTypeClass, sizeClasses]"
  >
    <span v-if="$slots.icon" class="inline-flex items-center">
      <slot name="icon"></slot>
    </span>
    <span class="badge-text font-semibold">{{ text }}</span>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  props: {
    text: {
      type: String,
      required: true,
      default: '',
    },
    type: {
      type: String,
      default: 'default',
    },
    size: {
      type: String,
      default: 'default',
    },
  },
  data() {
    return {
      badgeTypeClass: this.getBadgeTypeClass(),
      sizeClasses: this.getsizeClasses(),
    };
  },
  methods: {
    getBadgeTypeClass(): string {
      switch (this.type) {
        case 'success':
          return 'border-success-200 border border-1px inline-flex rounded-full bg-success-50 text-xs px-2 font-semibold leading-5 text-success-700';
        case 'error':
          return 'border-error-200 border border-1px inline-flex rounded-full bg-error-50 text-xs px-2 font-semibold leading-5 text-error-700';
        case 'warning':
          return 'border-warning-200 border border-1px inline-flex rounded-full bg-warning-50 text-xs px-2 font-semibold leading-5 text-warning-700';
        case 'blue':
          return 'border-blue-200 border border-1px inline-flex rounded-full bg-blue-50 text-xs px-2 font-semibold leading-5 text-blue-700';
        case 'gray':
          return 'border-gray-400 border border-1px inline-flex rounded-full bg-gray-200 text-xs px-2 font-semibold leading-5 text-gray-700';
        default:
          return 'border-gray-200 border border-1px inline-flex rounded-full bg-gray-50 text-xs px-2 font-semibold leading-5 text-gray-700';
      }
    },
    getsizeClasses(): string {
      switch (this.size) {
        case 'md':
          return 'text-md px-btn-[14px] py-btn-[10px] h-10';
        case 'sm':
          return 'text-sm px-2 py-1 h-8';
        case 'xs':
          return 'text-xs px-1';
        case 'icon':
          return 'text-xs px-1 py-2';
        default:
          return 'text-xs px-1 h-6';
      }
    },
  },
});
</script>
