<template>
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pt-3">
    <div
      class="bg-white rounded-md mb-10 dark:bg-gray-900 shadow sm:rounded-lg"
    >
      <div
        class="flex justify-between items-center border-b-2 p-4 border-gray-200"
      >
        <div class="flex justify-start items-center mx-2 space-x-2">
          <div class="text-lg font-semibold">Document Templates</div>
          <div class="rounded-md border-2 border-gray-400 px-1 text-sm">
            {{ numberOfDocuments }}
          </div>
        </div>
        <Observer>
          <Button
            v-if="canCreateCustomStartPaperwork()"
            class="max-w-xs"
            color="gray"
            size="sm"
            @click="createCustomPaperwork"
            data-testid="project-paperwork-add-btn"
          >
            <div class="flex justify-center items-center space-x-1">
              <Icon
                name="plus-circle"
                class="stroke-gray-500 dark:stroke-white"
              />
              <div>Add Paperwork</div>
            </div>
          </Button>
        </Observer>
      </div>
      <div class="mx-auto max-w-xl p-4">
        <section>
          <div class="flex justify-between items-center space-x-1 mt-4">
            <h3 class="text-lg font-bold leading-6">Custom documents</h3>
          </div>
          <p class="mb-10">
            Activate or inactivate custom documents, while also editing
            assignment rules.
          </p>
          <List v-model="customDocuments">
            <template #item="{ value: projectDocumentTemplate }">
              <div
                class="flex justify-between items-center my-4"
                :data-testid="`custom-doc-${projectDocumentTemplate.documentTemplate.name}`"
              >
                <div>
                  <div class="flex justify-start items-center space-x-1">
                    <div class="font-semibold">
                      <Toggle
                        v-model="projectDocumentTemplate.isEnabled"
                        @update:modelValue="
                          updatedocumentTemplate(
                            $event,
                            projectDocumentTemplate,
                          )
                        "
                        label="Default"
                        data-testid="project-paperwork-list-toggle"
                      >
                        <span class="font-semibold">{{
                          projectDocumentTemplate.documentTemplate.name
                        }}</span>
                        <!-- <div class="text-gray-600 text-sm font-normal">
                          {{
                            supportDocumentText(
                              projectDocumentTemplate.documentTemplate
                                .documentId,
                            )
                          }}
                        </div> -->
                      </Toggle>
                    </div>
                  </div>
                </div>
                <div class="flex justify-end items-center space-x-2">
                  <Button
                    color="gray"
                    @click.stop="
                      gotoCustomPaperwork(projectDocumentTemplate.id, 0)
                    "
                    :data-testid="`custom-doc-edit-${projectDocumentTemplate.documentTemplate.name}`"
                  >
                    Edit
                  </Button>
                  <Button
                    color="gray"
                    @click.stop="
                      deleteCustomStartPaperwork(projectDocumentTemplate)
                    "
                    :data-testid="`custom-doc-delete-${projectDocumentTemplate.documentTemplate.name}`"
                  >
                    Delete
                  </Button>
                </div>
              </div>
            </template>
          </List>
        </section>
        <section>
          <div class="flex justify-between items-center space-x-1 mt-4">
            <h3 class="text-lg font-bold leading-6">Default documents</h3>
          </div>
          <p class="mb-10">
            Activate or inactivate default company documents, while also editing
            assignment rules. Note: I-9 and Federal W4 remain active,
            non-editable.
          </p>
          <List v-model="defaultDocuments">
            <template #item="{ value: projectDocumentTemplate }">
              <div class="flex justify-between items-center my-4">
                <div>
                  <div class="flex justify-start items-center space-x-1">
                    <div
                      v-if="
                        projectDocumentTemplate.documentTemplate.documentId !==
                          ProductionCompanyDocumentTemplateKeys.KEY_I9 &&
                        projectDocumentTemplate.documentTemplate.documentId !==
                          ProductionCompanyDocumentTemplateKeys.KEY_W4
                      "
                    >
                      <Toggle
                        :modelValue="projectDocumentTemplate.isEnabled"
                        @update:modelValue="
                          updatedocumentTemplate(
                            $event,
                            projectDocumentTemplate,
                          )
                        "
                        label="Default"
                      >
                        <span class="font-semibold">{{
                          projectDocumentTemplate.documentTemplate.name
                        }}</span>
                        <!-- <div class="text-gray-600 text-sm font-normal">
                          {{
                            supportDocumentText(
                              projectDocumentTemplate.documentTemplate
                                .documentId,
                            )
                          }}
                        </div> -->
                      </Toggle>
                    </div>
                    <div v-else class="font-semibold">
                      {{ projectDocumentTemplate.documentTemplate.name }}
                    </div>
                    <!-- <Icon name="pencil" color="gray" @click="gotoCustomPaperwork(productionCompanyDocumentTemplate.documentTemplate.id!)"/> -->
                  </div>
                </div>
                <div
                  class="flex justify-end items-center space-x-2"
                  v-if="
                    projectDocumentTemplate.documentTemplate.documentId !==
                      ProductionCompanyDocumentTemplateKeys.KEY_I9 &&
                    projectDocumentTemplate.documentTemplate.documentId !==
                      ProductionCompanyDocumentTemplateKeys.KEY_W4
                  "
                >
                  <!-- <Button
                    color="gray"
                    @click.stop="
                      gotoCustomPaperwork(projectDocumentTemplate.id, 1)
                    "
                  >
                    Edit
                  </Button> -->
                </div>
              </div>
            </template>
          </List>
        </section>
        <Modal v-model="isDocumentPreviewOpen">
          <div class="flex w-full justify-end mb-2">
            <XMarkIcon
              class="h-6 w-6 cursor-pointer"
              @click="isDocumentPreviewOpen = false"
            />
          </div>
          <PDFViewer
            :url="pdfPreviewUrl"
            class="h-3/4 max-h-[500px] overflow-y-scroll"
          />
        </Modal>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
//todo: need to test re-render coz of mobx Observer
import Icon from '@/components/Icon.vue';
import Button from '@/components/library/Button.vue';
import List from '@/components/library/List.vue';
import Modal from '@/components/library/Modal.vue';
import PDFViewer from '@/components/library/PDFViewer.vue';
import PermissionStore from '@/reactComponents/stores/permission';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import { formatPhoneNumber } from '@/utils/phone';
// import { CalendarIcon, TrashIcon, XMarkIcon } from '@heroicons/vue/24/outline';
import { Observer } from 'mobx-vue-lite';

import Toggle from '@/components/library/Toggle.vue';
import { getPdfUrl } from '@/services/pdf';
import { listProjectDocumentTemplates } from '@/services/project';
import {
  deleteProjectDocumentTemplate,
  disableDocumentTemplate,
  enableDocumentTemplate,
} from '@/services/project-document-templates';
import { ProductionCompanyDocumentTemplateKeys } from '@/types/ProductionCompanyDocumentTemplates';
import { PermissionKeys } from '@/types/Permission';
import type Project from '@/types/Project';
import { XMarkIcon } from '@heroicons/vue/24/outline';
import axios from 'axios';
import { defineComponent, type PropType } from 'vue';

export default defineComponent({
  props: {
    project: {
      type: Object as PropType<Project>,
      required: true,
    },
    navigate: {
      type: Function as PropType<Function>,
      required: true,
    },
  },
  components: {
    Button,
    Modal,
    PDFViewer,
    XMarkIcon,
    Icon,
    Observer,
    List,
    Toggle,
  },
  computed: {
    canDeleteDocuments(): boolean {
      return PermissionStore.hasPermission(
        PermissionKeys.EDIT_DOCUMENT_TEMPLATE,
      );
    },
    customDocuments(): any[] {
      return this.documentTemplates.filter(
        (doc: any) => doc.documentTemplate.isCustom,
      );
    },
    defaultDocuments(): any[] {
      return this.documentTemplates.filter(
        (doc: any) => !doc.documentTemplate.isCustom,
      );
    },
    numberOfDocuments(): number {
      return this.documentTemplates?.length;
    },
  },
  // setup(props, ctx) {
  //   return {
  //     PermissionStore
  //   }
  // },
  data() {
    return {
      PermissionKeys,
      documentTemplates: [] as any[],
      isDocumentPreviewOpen: false,
      pdfPreviewUrl: '',
      ProductionCompanyDocumentTemplateKeys,
    };
  },
  methods: {
    formatPhoneNumber,
    async load() {
      const {
        data: { data },
      } = await listProjectDocumentTemplates(this.project.id!);
      this.documentTemplates = data;
    },
    gotoCustomPaperwork(projectDocumentTemplateId: number, isDefault: number) {
      if (
        PermissionStore.hasPermission(PermissionKeys.EDIT_DOCUMENT_TEMPLATE)
      ) {
        this.navigate({
          pathname: `/projects/${this.project.hashId}/admin/document-templates/${projectDocumentTemplateId}/is-Default/${isDefault}`,
        });
      } else {
        SnackbarStore.triggerSnackbar(
          'You do not have permission to update this document template',
          1000,
          'error',
        );
      }
    },
    canCreateCustomStartPaperwork() {
      return PermissionStore.hasPermission(
        PermissionKeys.EDIT_DOCUMENT_TEMPLATE,
      );
    },
    createCustomPaperwork() {
      this.navigate({
        pathname: `/projects/${this.project.hashId}/admin/document-templates/create`,
      });
    },
    previewDocument(documentTemplate: any) {
      const { documentId } = documentTemplate;
      if (!documentId) {
        SnackbarStore.triggerSnackbar('No document found', 2500, 'error');
        return;
      }
      if (documentId) {
        this.pdfPreviewUrl = getPdfUrl(documentId);
        this.isDocumentPreviewOpen = true;
        return;
      }
    },
    // supportDocumentText(documentTemplateId: string) {
    //   if (
    //     documentTemplateId ===
    //     ProductionCompanyDocumentTemplateKeys.KEY_NY_WAGE_THEFT_PROTECTION
    //   ) {
    //     return 'Assignment rules here, separated by commas';
    //   }
    //   return 'Specific Union IATSE, Loan out setting';
    // },
    async updatedocumentTemplate(
      checked: boolean,
      projectDocumentTemplate: any,
    ) {
      if (
        PermissionStore.hasPermission(PermissionKeys.EDIT_DOCUMENT_TEMPLATE) ||
        !projectDocumentTemplate.isCompanyDoc
      ) {
        try {
          const { data } = checked
            ? await enableDocumentTemplate(
                projectDocumentTemplate.documentTemplateId,
                projectDocumentTemplate.projectId,
              )
            : await disableDocumentTemplate(
                projectDocumentTemplate.documentTemplateId,
                projectDocumentTemplate.projectId,
              );
          projectDocumentTemplate.isEnabled = data.isEnabled;
          SnackbarStore.triggerSnackbar(
            'Document template updated',
            1000,
            'success',
          );
        } catch (err) {
          if (axios.isAxiosError(err)) {
            const msg = err.response?.data?.['errors']?.[0]?.message;
            SnackbarStore.triggerSnackbar(msg, 2500, 'error');
          } else {
            SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
          }
        }
      } else {
        SnackbarStore.triggerSnackbar(
          'You do not have permission to update this document template',
          1000,
          'error',
        );
      }
    },
    async deleteCustomStartPaperwork(projectDocumentTemplate: any) {
      if (
        PermissionStore.hasPermission(PermissionKeys.EDIT_DOCUMENT_TEMPLATE) ||
        !projectDocumentTemplate.isCompanyDoc
      ) {
        try {
          await deleteProjectDocumentTemplate(projectDocumentTemplate.id);
          SnackbarStore.triggerSnackbar(
            'Document template deleted',
            1000,
            'success',
          );
          await this.load();
        } catch (err) {
          console.warn('Error deleting document template', err);
        }
      } else {
        SnackbarStore.triggerSnackbar(
          'You do not have permission to delete this document template',
          1000,
          'error',
        );
      }
    },
    getDocumentTemplateNameAndKey(documentTemplate: any): any {
      const getunions =
        documentTemplate.type?.key === 'specific_unions'
          ? documentTemplate.unions.map((union: any) => union.name).join(', ')
          : '';
      return getunions == ''
        ? documentTemplate.type?.name
        : documentTemplate.type?.name + '-' + getunions;
    },
  },
  async mounted() {
    await this.load();
  },
});
</script>
