<template>
  <div class="space-y-8 divide-y divide-gray-200 pt-16">
    <div
      class="relative flex min-h-screen flex-col justify-center overflow-hidden"
    >
      <div
        class="m-auto max-w-screen-lg space-y-8 divide-y divide-gray-200 dark:divide-gray-400 mt-5"
      >
        <div>
          <div>
            <h3 class="text-3xl mb-3 mt-3 font-medium leading-6">Loan Out</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Fill out this form if you have a business you operate through.
            </p>
          </div>
          <div class="mt-6 grid grid-cols-2 gap-x-4">
            <div class="col-span-2">
              <TextInput
                label="Business Name*"
                v-model="loanOut.businessName"
              />
            </div>
            <div class="col-span-2">
              <TextInput label="DBA (Opt.)" v-model="loanOut.dba" />
            </div>
            <div class="col-span-2">
              <TextInput
                label="Employer Identification Number (EIN)*"
                type="ein"
                :model-value="loanOut.employerIdentificationNumber"
                @update:rawValue="loanOut.employerIdentificationNumber = $event"
              />
            </div>
            <div class="col-span-2">
              <TextInput
                label="State Identification Number (Opt.)"
                :model-value="loanOut.stateIdentificationNumber"
                @update:rawValue="loanOut.stateIdentificationNumber = $event"
              />
            </div>
            <div class="col-span-2">
              <Dropdown
                label="Tax Classification*"
                :menu-items="taxClassifications"
                display-name="name"
                v-model="loanOut.taxClassification"
              />
            </div>
            <div class="sm:col-span-2">
              <Dropdown
                label="State Incorporated*"
                v-model="loanOut.stateIncorporated"
                :menu-items="states"
                display-name="name"
              />
            </div>
            <div v-if="loanOut.dateIncorporated" class="sm:col-span-2">
              <DatePicker
                label="Date Incorporated*"
                v-model="loanOut.dateIncorporated"
              />
            </div>
          </div>
        </div>
        <div class="pt-8">
          <div>
            <h3 class="text-lg font-medium leading-6">Address</h3>
          </div>
          <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
            <div class="sm:col-span-6">
              <div class="mt-1">
                <TextInput
                  label="Street Address*"
                  v-model="loanOut.address.street"
                />
              </div>
            </div>

            <div class="sm:col-span-2">
              <TextInput label="City*" v-model="loanOut.address.city" />
            </div>

            <div class="sm:col-span-2">
              <Dropdown
                label="State*"
                v-model="loanOut.address.state"
                :menu-items="states"
                display-name="name"
              />
            </div>

            <div class="sm:col-span-2">
              <TextInput
                label="ZIP / Postal code*"
                v-model="loanOut.address.zip"
                type="postal"
              />
            </div>
          </div>
        </div>
        <div class="pt-8">
          <div>
            <h3 class="text-lg font-medium leading-6">
              Article of Incorporation
            </h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
              If you have an article of incorporation, please upload it here.
            </p>
            <FileUpload
              v-model="loanOut.articleOfIncorporationDocumentId"
              class="my-1"
            />
          </div>
          <div>
            <h3 class="text-lg font-medium leading-6">Agent Authorization</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
              If you have an agent authorization, please upload it here.
            </p>
            <FileUpload
              v-model="loanOut.agentAuthorizationDocumentId"
              class="my-1"
            />
          </div>
        </div>
        <div class="flex justify-center space-x-2 py-5">
          <Button @click="navigate(-1)" color="gray"> Cancel </Button>
          <Button @click="submit" class="justify-center">{{
            editMode ? 'Update' : 'Create'
          }}</Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Button from '@/components/library/Button.vue';
import DatePicker from '@/components/library/DatePicker.vue';
import Dropdown from '@/components/library/Dropdown.vue';
import FileUpload from '@/components/library/FileUpload.vue';
import TextInput from '@/components/library/TextInput.vue';
import { getProductionCompanyAddresses, getStates } from '@/services/address';
import { createLoanOut, getLoanOut, updateLoanOut } from '@/services/loan-out';
import { getTaxClassifications } from '@/services/tax-classifications';
import SnackbarStore from '@/reactComponents/stores/snackbar';

import type Address from '@/types/Address';
import type State from '@/types/State';
import type { SnackType } from '@/types/Snackbar';
import axios from 'axios';
import { DateTime } from 'luxon';
import { defineComponent } from 'vue';

export default defineComponent({
  props: {
    navigate: {
      type: Function,
      required: true,
    },
  },
  setup() {
    return { SnackbarStore };
  },
  components: {
    TextInput,
    Button,
    Dropdown,
    DatePicker,
    FileUpload,
  },
  data() {
    return {
      loanOut: {
        id: undefined,
        businessName: undefined,
        employerIdentificationNumber: undefined,
        agentAuthorizationDocumentId: undefined,
        address: {
          street: '',
          city: '',
          state: {} as State,
          zip: '',
        } as Address,
      } as any,
      state: {} as State,
      states: [] as State[],
      taxClassifications: [],
      editMode: false,
    };
  },
  watch: {
    'project.productionCompany': {
      handler: function () {
        this.updateAddresses();
      },
      immediate: true,
    },
  },
  methods: {
    triggerSnackbar(msg: string, duration: number, type: SnackType) {
      this.SnackbarStore.triggerSnackbar(msg, duration, type);
    },
    async submit() {
      try {
        if (this.editMode) {
          await updateLoanOut(this.loanOut);
        } else {
          await createLoanOut(this.loanOut);
        }
        this.navigate(-1);
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          this.triggerSnackbar(msg, 2500, 'error');
        } else {
          this.triggerSnackbar(err as string, 2500, 'error');
        }
      }
    },
    addShootLocation() {
      this.loanOut.shootLocations.push({
        street: '',
        city: '',
        state: {
          id: -1,
          name: '',
          key: '',
        },
        zip: '',
      });
    },
    removeShootLocation() {
      this.loanOut.shootLocations.pop();
    },
    async updateAddresses() {
      if (!this.loanOut.productionCompany) return;
      const { data: productionCompanyAddresses } =
        await getProductionCompanyAddresses(
          this.loanOut.productionCompany!.id!,
        );
      this.loanOut.addresses = productionCompanyAddresses;
    },
  },
  async mounted() {
    const { data: LoanOut } = await getLoanOut();
    if (LoanOut) {
      this.loanOut = LoanOut;
      this.editMode = true;
    }
    const {
      data: { data: taxClassification },
    } = await getTaxClassifications();
    this.taxClassifications = taxClassification;
    if (!this.loanOut.dateIncorporated) {
      this.loanOut.dateIncorporated = DateTime.now();
    }
    const {
      data: { data: states },
    } = await getStates();
    this.states = states;
  },
});
</script>
