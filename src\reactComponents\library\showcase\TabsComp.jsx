import React from 'react';

import { Box, Tab, Tabs } from '@/reactComponents/library';

const TabsComp = (props) => {
  const [value, setValue] = React.useState(0);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  //https://mui.com/material-ui/react-tabs

  return (
    <Box sx={{ p: 10, backgroundColor: 'background.default' }}>
      {' '}
      <Box sx={{ width: '100%' }}>
        <Box sx={{}}>
          <Tabs value={value} onChange={handleChange}>
            <Tab label="Item One" />
            <Tab label="Item Two two two two" />
            <Tab label="Item Three" />
          </Tabs>
        </Box>
        <Box>Tab Value: {value}</Box>
      </Box>
      <Box sx={{ width: '100%' }}>
        <Box sx={{}}>
          <Tabs variant={'secondary'} value={value} onChange={handleChange}>
            <Tab label="Item One" />
            <Tab label="Item Two two two two" />
            <Tab label="Item Three" />
          </Tabs>
        </Box>
        <Box>Tab Value: {value}</Box>
      </Box>
    </Box>
  );
};

TabsComp.propTypes = {};

export default TabsComp;
