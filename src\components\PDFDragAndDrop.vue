<template>
  <div class="flex flex-row h-full">
    <div class="container mx-auto py-4">
      <ButtonListGroup class="mr-4">
        <ListGroupItem
          v-for="(fieldOption, fieldOptionIndex) in fieldOptions"
          :key="`field-option-${fieldOption.value}-${fieldOptionIndex}`"
          @click.stop="addFieldToSchema($event, fieldOption)"
        >
          + {{ fieldOption.label }}
        </ListGroupItem>
      </ButtonListGroup>
    </div>
    <div id="pdf-container" class="max-w-2xl h-full" />
  </div>
</template>

<script lang="ts">
import ButtonListGroup from '@/components/library/ButtonListGroup.vue';
import ListGroupItem from '@/components/library/ButtonListGroupItem.vue';
import { getPdfUrl } from '@/services/pdf';
import type { StyledPDFFieldOption } from '@/types/PDFFieldOption';
import { deepClone } from '@/utils/deepClone';
import type { Schema, Template } from '@pdfme/ui';
import { Designer } from '@pdfme/ui';
import type { PropType } from 'vue';
import { defineComponent } from 'vue';
import type { SchemaItem } from '../types/Schema';

export default defineComponent({
  components: {
    ButtonListGroup,
    ListGroupItem,
  },
  props: {
    documentId: {
      type: String as PropType<string>,
      required: true,
    },
    schemas: {
      type: Array as PropType<SchemaItem[]>,
      required: true,
    },
    fieldOptions: {
      type: Array as PropType<StyledPDFFieldOption[]>,
      required: true,
    },
  },
  data() {
    return {
      pdfUrl: '' as string,
      designer: null as Designer | null,
      observer: null as MutationObserver | null,
      currentPage: null as unknown as number,
      totalPages: null as unknown as number,
      selectedFieldName: null as string | null,
      validateSchemaDebounce: null as any,
    };
  },
  watch: {
    documentId(): void {
      this.getPdfURL();
    },
    pdfUrl(): void {
      this.generateDesigner();
    },
    schemas(value): void {
      if (!this.pdfUrl) return;
      if (!this.designer) {
        this.generateDesigner();
      }
      this.updateDesignerSchemas(value);
    },
    totalPages(): void {
      if (!this.totalPages) return;
      if (this.validateSchemaDebounce) {
        window.clearTimeout(this.validateSchemaDebounce);
      }
      this.validateSchemaDebounce = window.setTimeout(async () => {
        this.validateSchemasSize();
      }, 1000);
    },
  },
  mounted(): void {
    this.getPdfURL();
    this.setupObserver();
  },
  beforeUnmount(): void {
    if (this.observer) {
      this.observer.disconnect();
    }
  },
  computed: {
    selectedField(): any {
      if (!this.selectedFieldName) {
        return null;
      }
      const currentPageIndex = this.currentPage - 1;
      const currentPageSchema = this.schemas[currentPageIndex];
      return currentPageSchema[this.selectedFieldName];
    },
  },
  methods: {
    getPdfURL(): void {
      if (!this.documentId) return;
      this.pdfUrl = getPdfUrl(this.documentId);
    },
    generateTemplate(): Template {
      const template: Template = {
        basePdf: this.pdfUrl,
        schemas: this.schemas,
      };
      return template;
    },
    generateDesigner(): void {
      const domContainer = document.getElementById('pdf-container');
      if (!domContainer) {
        return;
      }
      const template: Template = this.generateTemplate();
      const designer: Designer = new Designer({ domContainer, template });
      this.designer = designer;
      this.designer.onChangeTemplate((template: Template) => {
        this.emitSchemaUpdate(template.schemas);
      });
    },
    setupObserver(): void {
      // This exists to watch for DOM changes and take action on certain elements

      const container = document.getElementById('pdf-container');
      if (!container) return;

      const config = { childList: true, subtree: true };
      this.observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.addedNodes.length > 0) {
            this.readPageValues(container);
            this.getSelectedFieldName(container);
            // EVERYTHING BELOW CAN BE REMOVED ONCE WE START HIDING THE PARENT DIV ALTOGETHER
            this.disableInputWithNameLabel(container);
            this.disableSelectWithTypeLabel(container);
            this.hideBulkUpdateFieldNames(container);
            this.hideAddNewFieldButton(container);
          }
        });
      });

      this.observer.observe(container, config);
    },
    disableInputWithNameLabel(container: HTMLElement): void {
      if (!container) {
        return;
      }
      container.querySelectorAll('div').forEach((div) => {
        const label = div.querySelector('label');
        const input = div.querySelector('input');

        if (label && input) {
          if (label.textContent?.includes('Name')) {
            input.disabled = true;
          }
        }
      });
    },
    getSelectedFieldName(container: HTMLElement): void {
      if (!container) {
        return;
      }

      const divs = Array.from(container.querySelectorAll('div'));

      const targetDiv = divs.find((div) => {
        const label = div.querySelector('label');
        const input = div.querySelector('input');
        return label?.textContent?.includes('Name') && input;
      });

      this.selectedFieldName = targetDiv?.querySelector('input')?.value || null;
    },
    disableSelectWithTypeLabel(container: HTMLElement): void {
      if (!container) {
        return;
      }
      container.querySelectorAll('div').forEach((div) => {
        const label = div.querySelector('label');

        if (label && label.textContent?.includes('Type')) {
          const select = div.querySelector('select');
          if (select) {
            select.disabled = true;
          }
        }
      });
    },
    hideBulkUpdateFieldNames(container: HTMLElement): void {
      if (!container) {
        return;
      }
      container.querySelectorAll('u').forEach((u) => {
        if (u?.textContent?.includes('Bulk update field names')) {
          u.style.display = 'none';
        }
      });
    },
    hideAddNewFieldButton(container: HTMLElement): void {
      if (!container) {
        return;
      }
      container.querySelectorAll('button').forEach((button) => {
        if (button?.textContent?.includes('Add new field')) {
          button.style.display = 'none';
        }
      });
    },
    readPageValues(container: HTMLElement): void {
      if (!container) {
        return;
      }
      container.querySelectorAll('div').forEach((div) => {
        const isFlexDisplay = getComputedStyle(div).display === 'flex';
        if (!isFlexDisplay) {
          return;
        }
        const strong = div.querySelector('strong');
        if (!strong) {
          return;
        }
        const content = strong.textContent || '';
        const regex = /^\d+\/\d+$/; // Regular expression to match the #/# format
        if (!regex.test(content)) {
          return;
        }
        const split = content.split('/');
        this.currentPage = parseInt(split[0], 10);
        this.totalPages = parseInt(split[1], 10);
      });
      this.currentPage = this.currentPage || 1;
      this.totalPages = this.totalPages || 1;
    },
    addFieldToSchema(event: Event, fieldOption: StyledPDFFieldOption): void {
      const designer = this.designer;
      if (!designer) {
        return;
      }
      const schemas: SchemaItem[] = deepClone(designer.getTemplate().schemas);
      const field = {
        type: fieldOption.type,
        position: fieldOption.position || { x: 0, y: 0 },
        width: fieldOption.width || 30,
        height: fieldOption.height || 7,
      };

      const currentPageIndex = this.currentPage - 1;
      const currentPageSchema = schemas[currentPageIndex];

      const fieldName = this.calculateFieldName(fieldOption, schemas);
      currentPageSchema[fieldName] = field;

      schemas[currentPageIndex] = currentPageSchema;

      this.emitSchemaUpdate(schemas);
    },
    calculateFieldName(
      fieldOption: StyledPDFFieldOption,
      schemas: SchemaItem[],
    ): string {
      const currentPageSchema = schemas[this.currentPage - 1];

      const baseFieldName = fieldOption.value;
      let fieldName = `${baseFieldName}`;
      let fieldNumber = 1;
      let doesFieldExist = true;

      while (doesFieldExist) {
        doesFieldExist = !!currentPageSchema[fieldName];
        if (!doesFieldExist) {
          break;
        }

        fieldNumber++;
        fieldName = `${baseFieldName}_${fieldNumber}`;
      }

      return fieldName;
    },
    emitSchemaUpdate(schemas: SchemaItem[]): void {
      schemas = this.removeFontNameFromSchemas(schemas);
      this.$emit('update:schemas', schemas);
    },
    removeFontNameFromSchemas(schemas: SchemaItem[]): SchemaItem[] {
      return schemas.map((schema) => {
        const newSchema = { ...schema };
        // itirate through each object in the schema
        Object.keys(newSchema).forEach((key) => {
          // iterate through each key in the object
          const value: any = newSchema[key];
          if (typeof value === 'object') {
            // if the value is an object, itirate through it's keys
            Object.keys(value).forEach((valueKey) => {
              // if the key is fontName, remove it
              if (valueKey === 'fontName') {
                delete value[valueKey];
              }
            });
          }
        });
        return newSchema;
      });
    },
    updateDesignerSchemas(schemas: Schema): void {
      const designer = this.designer;
      if (!designer) {
        return;
      }

      const didSchemaChange =
        JSON.stringify(schemas) !==
        JSON.stringify(designer.getTemplate().schemas);
      if (!didSchemaChange) {
        return;
      }

      const template = designer.getTemplate();
      template.schemas = schemas as any;
      designer.updateTemplate(template);
    },
    validateSchemasSize(): void {
      let schemas = deepClone(this.schemas);
      if (!schemas || !schemas.length) {
        schemas = [];
      }

      const pageDiff = this.totalPages - schemas.length;
      if (pageDiff === 0) {
        return;
      }

      if (pageDiff > 0) {
        // ADD EXTRA SCHEMA TO BACK
        for (let i = 0; i < pageDiff; i++) {
          schemas.push({});
        }
      } else {
        // REMOVE EXTRA SCHEMA OFF THE BACK
        for (let i = 0; i < Math.abs(pageDiff); i++) {
          schemas.pop();
        }
      }

      this.emitSchemaUpdate(schemas as SchemaItem[]);
    },
    updateSelectedField(newValue: any): void {
      const currentPageIndex = this.currentPage - 1;
      const currentPageSchema = this.schemas[currentPageIndex];
      currentPageSchema[this.selectedFieldName!] = newValue;
      const schemas = [...this.schemas];
      schemas[currentPageIndex] = currentPageSchema;

      this.emitSchemaUpdate(schemas);
    },
  },
});
</script>

<style>
.scena-horizontal,
.scena-vertical,
.ruler-container {
  display: none !important;
}
</style>
