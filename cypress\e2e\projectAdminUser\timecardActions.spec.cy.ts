/// <reference types="cypress" />

import TimecardDetails from '../../pageObjects/timecards/timecardDetails';
import TimecardModal from '../../pageObjects/timecards/timecardModal';
import {
  deleteTimecard,
  fetchProjectIdentifiersByName,
  fetchProjectMemberByName,
} from '../../support/apiHelpers';
import {
  interceptEditTimecard,
  interceptDeleteTimecard,
  interceptCreateTimecard,
} from '../../support/apiTimecardInterceptors';

describe('User Project Admin - Timecard Actions', () => {
  const timecardDetails = new TimecardDetails();
  const timecardModal = new TimecardModal();
  const rateTypes = 'Hourly';
  const hourlyRate = '80';

  beforeEach((): void => {
    cy.loginWithSession(
      Cypress.env('PHONE_NUMBER_PROJECT_ADMIN'),
      Cypress.env('OTC_CODE'),
      Cypress.env('BASE_URL'),
      'project-admin',
    );

    // Get Details by Api before test
    cy.then(() => {
      // Step 1: Get the project Id by project Name
      return fetchProjectIdentifiersByName(Cypress.env('projectName')).then(
        (project) => {
          if (!project || !project.id) {
            throw new Error('Project not found or invalid Project ID');
          }
          Cypress.env('projectId', project.id);
          cy.log(`Project ID set: ${project.id}`);
          Cypress.env('projectHashId', project.hashId);

          // Step 2: Get the member by first/last name
          return fetchProjectMemberByName('PA-test', 'PA-test').then(
            (member) => {
              if (!member || !member.id) {
                throw new Error(
                  'Project member not found or invalid Member ID',
                );
              }
              Cypress.env('projectMemberId', member.id);
              cy.log(`Project Member ID set: ${member.id}`);
            },
          );
        },
      );
    });
  });

  afterEach(() => {
    // ----------- Cleanup: Delete the created timecard -----------
    cy.log('Cleanup: Delete the created timecard');
    deleteTimecard(Cypress.env('timecardId'));
  });

  // https://castandcrew.atlassian.net/browse/FPS-1525
  // https://castandcrew.atlassian.net/browse/FPS-1527
  it('Project Admin is able to create, edit, and delete a timecard', () => {
    // ----------- Arrange: Navigate to the project timecards section -----------
    cy.log('Arrange: Navigate to the project timecards section');
    cy.visit(
      `${Cypress.env('BASE_URL')}projects/${Cypress.env(
        'projectHashId',
      )}/admin/time`,
    );

    interceptCreateTimecard();
    timecardModal.createTimecard(Cypress.env('employeeName'));
    cy.wait('@createTimecard').then((interception): void => {
      const response = interception.response?.body;
      Cypress.env('timecardId', response.id);

      // ----------- Action: Edit the timecard -----------
      cy.log('Action: Edit the timecard');
      interceptEditTimecard();
      timecardDetails.fillTimecardDetails(
        undefined,
        hourlyRate,
        rateTypes,
        '10',
        true,
      );
      timecardDetails.save();
    });

    // ----------- Assert: Validate the changes in the timecard details -----------
    cy.log('Assert: Validate the changes in the timecard details');
    cy.wait('@editedTimecard').then((response): void => {
      expect(response.response?.statusCode).to.equal(200);
      const rate: string = response.response?.body.hourlyRate;
      expect(String(rate)).to.eq(String(hourlyRate));

      // ----------- Action: Delete the timecard -----------
      cy.log('Action: Delete the timecard');
      interceptDeleteTimecard();
      timecardModal.deleteTimecard();

      // ----------- Assert: Verify the timecard was deleted -----------
      cy.log('Assert: Verify the timecard was deleted');
      cy.wait('@deletedTimecard').then((interception) => {
        const responseBody = interception.response?.body;
        expect(interception.response?.statusCode).to.equal(200);
        expect(responseBody.id).to.eq(Cypress.env('timecardId'));
        expect(responseBody.projectId).to.eq(Cypress.env('projectId'));
        expect(responseBody.projectMemberId).to.eq(
          Cypress.env('projectMemberId'),
        );
        cy.log(
          'Success: Project Admin is able to create, move to batch, edit, and delete a timecard',
        );
      });
    });
  });
});
