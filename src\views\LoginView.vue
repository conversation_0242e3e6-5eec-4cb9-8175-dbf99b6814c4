<template>
  <div
    class="relative flex min-h-screen flex-col justify-center overflow-hidden"
  >
    <div class="m-auto bg-white dark:bg-gray-900 p-10 rounded-lg shadow-sm">
      <div class="flex justify-center"></div>
      <h2 class="text-center text-3xl font-extrabold leading-9 pt-4 pb-1">
        Welcome
      </h2>
      <div>
        <TextInput
          label="Mobile Phone"
          :model-value="phone"
          inputmode="tel"
          type="phone"
          autofocus
          data-testid="login-phone-input"
          @keyup.enter="login"
          @update:rawValue="phone = $event"
        />
        <div class="flex justify-center">
          <Button
            color="primary"
            @click="login"
            :loading="loading"
            data-testid="login-enter-btn"
          >
            Enter
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, type Ref } from 'vue';
import Button from '../components/library/Button.vue';
import TextInput from '../components/library/TextInput.vue';
import { passwordlessStart } from '../services/auth';

const loading: Ref<boolean> = ref(false);

const phone: Ref<string> = ref('');
const props = defineProps<{
  navigate: Function;
  route: { query: any; params: any };
}>();

const reactLogin = async () => {
  const redirect = props?.route?.query?.redirect;
  const code = props?.route?.query?.code as string;

  const urlSearchParams = new URLSearchParams();
  urlSearchParams.append('redirect', redirect || '');
  if (code) {
    urlSearchParams.append('code', code || '');
  }
  await passwordlessStart(phone.value);
  props.navigate({
    pathname: `/login/${phone.value}/verify`,
    search: urlSearchParams.toString(),
  });
};

const login = async () => {
  if (props.navigate) {
    loading.value = true;
    await reactLogin();
    loading.value = false;
    return;
  }
};
</script>

<style></style>
