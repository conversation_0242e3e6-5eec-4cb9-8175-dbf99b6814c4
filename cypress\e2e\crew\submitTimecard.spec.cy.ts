import { faker } from '@faker-js/faker';
import ProjectViewPageObject from '../../pageObjects/project/projectView';
import TimecardListPageObject from '../../pageObjects/timecardCrew/timecardList';
import {
  TimecardFormPageObject,
  TimeInformation,
} from '../../pageObjects/timecardCrew/timecardForm';
import {
  addPersonalInfo,
  fetchPayPeriodsRemainingByProject,
  fetchProjectInformation,
  fetchShootLocationsByProject,
  fetchTimecardById,
  logout,
} from '../../support/apiHelpers';
import { DateFormatter } from '../../utils/DateFormatter';
import { startPaperWorkFlow } from '../../support/apiFlows/startPaperWorkFlow';
import { createProjectFlow } from '../../support/apiFlows/createProjectFlow';
import { projectOnboardingFlow } from '../../support/apiFlows/projectOnboardingFlow';
import {
  interceptAddTimecard,
  interceptSubmitTimecard,
} from '../../support/apiTimecardInterceptors';
import { TimecardViewPageObject } from '../../pageObjects/timecardCrew/timecardView';

describe('User Crew - Submit Timecard', () => {
  const projectViewPO = new ProjectViewPageObject();
  const timecardListPO = new TimecardListPageObject();
  const timecardFormPO = new TimecardFormPageObject();
  const timecardViewPO = new TimecardViewPageObject();
  const payPeriod = { start: '0', end: '0' };
  const timeInformation: TimeInformation = {
    day: 'Monday',
    workStatus: 'WORK',
    workLocation: '',
    startsAt: '8:00 AM',
    mealStart: ['12:00 PM'],
    mealEnd: ['1:00 PM'],
    endsAt: '5:00 PM',
    notes: faker.lorem.sentence(),
    ndb: false,
    copyLastDay: false,
  };

  beforeEach(() => {
    cy.loginWithSession(
      Cypress.env('PHONE_NUMBER'),
      Cypress.env('OTC_CODE'),
      Cypress.env('BASE_URL'),
      'supervisor',
    );
    //Create project by API
    cy.then(() => {
      return createProjectFlow();
    })
      .then((res) => {
        cy.log(res.body);
        Cypress.env('projectId', res.body.id);
        const hashId = res.body.hashId;
        Cypress.env('projectHashId', hashId);

        //Get project info to add user
        return fetchProjectInformation().then((res) => {
          const departmentId = res.departments[0].id;
          return logout().then(() => {
            //login with crew into the new project
            const url = `${Cypress.env(
              'BASE_URL',
            )}/projects/${hashId}?onboard=true&departmentId=${departmentId}`;

            cy.loginWithoutSession(
              Cypress.env('PHONE_NUMBER_CREW'),
              Cypress.env('OTC_CODE'),
              url,
            );
          });
        });
      })
      .then(() => {
        //filling out information needed for add time cards using API
        return projectOnboardingFlow();
      })
      .then(() => {
        return addPersonalInfo();
      })
      .then(() => {
        return startPaperWorkFlow().then(() => {
          //reload page to see changes
          cy.reload();
        });
      })
      .then(() => {
        // Selecting period for timecard
        return fetchPayPeriodsRemainingByProject();
      })
      .then((periods) => {
        payPeriod.start = periods[0].startsAt;
        payPeriod.end = periods[0].endsAt;
        // Selection shoot location
        return fetchShootLocationsByProject();
      })
      .then((locations) => {
        cy.log(JSON.stringify(locations));
        timeInformation.workLocation = locations[0].shootLocation.locationName;
      });
  });

  it('Verify Crew is able to submit timecard', () => {
    interceptAddTimecard();
    const period = `${DateFormatter.formatDateDayNameMonthDay(
      payPeriod.start,
    )} - ${DateFormatter.formatDateDayNameMonthDay(payPeriod.end)}`;

    // Navigate to add timecard
    projectViewPO.goToTimecards();
    timecardListPO.goToAddTimecard();
    timecardFormPO.startTimecard(period);

    cy.wait('@addTimecard').then((interception): void => {
      const timecardId = interception.response?.body.id;
      interceptSubmitTimecard(timecardId);
      cy.log(`Timecard created with ID: ${interception.response?.body}`);

      // Fillout timecard information
      timecardFormPO.fillOutTimecardForm([timeInformation]);
      const today = `${DateFormatter.formatDateMonthNameDayYear(
        new Date().toISOString().split('T')[0],
      )}`;
      timecardListPO.goToTimecard(timecardId);

      // Validate timecard information
      timecardViewPO.validateTimecardDetails(
        `${DateFormatter.formatDateMonthNameDayYear(
          payPeriod.start,
        )} - ${DateFormatter.formatDateMonthNameDayYear(payPeriod.end)}`,
        'Submitted',
        today,
        today,
      );
      fetchTimecardById(timecardId).then((timecard) => {
        // get date for monday time card
        const timeDate = timecard.timecardDays[1].startsAt;
        timecardViewPO.validateTimecardTime(
          DateFormatter.formatDateMMDDYYYY(timeDate.split('T')[0]),
          timeInformation.startsAt,
          timeInformation.endsAt,
          '8.00',
          `${timeInformation.mealStart[0]} - ${timeInformation.mealEnd[0]}`,
        );
      });
    });
  });
});
