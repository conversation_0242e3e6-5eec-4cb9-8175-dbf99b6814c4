import type { StyledPDFFieldOption } from '../types/PDFFieldOption';
import { BatchStatusEnum } from '@/types/Batch';

// Since we're exporting objects, we need to use a class, but then do some things so we can treat it like an enum
export class PDFField {
  static readonly FullName = new PDFField('FullName', {
    label: 'Full Name',
    value: 'user.fullName',
    type: 'text',
    height: 9.7,
    width: 86.5,
    fontSize: 16,
    lineHeight: 1,
  });
  static readonly TodaysDate = new PDFField('TodaysDate', {
    label: "Today's Date",
    value: 'todaysDate',
    type: 'text',
    height: 10,
    width: 40,
    alignment: 'left',
    fontSize: 10,
    lineHeight: 1,
  });
  static readonly Signature = new PDFField('Signature', {
    label: 'Signature',
    value: 'signature',
    type: 'image',
    height: 20,
    width: 40,
  });
  static readonly ProjectName = new PDFField('ProjectName', {
    label: 'Project Name',
    value: 'project.name',
    type: 'text',
    height: 7,
    width: 92,
    alignment: 'left',
    fontSize: 16,
    lineHeight: 1,
  });

  static readonly Phone = new PDFField('Phone', {
    label: 'Phone',
    value: 'user.phone',
    type: 'text',
    height: 7,
    width: 92,
    alignment: 'left',
    fontSize: 16,
    lineHeight: 1,
  });

  static readonly Email = new PDFField('Email', {
    label: 'Email',
    value: 'user.email',
    type: 'text',
    height: 7,
    width: 92,
    alignment: 'left',
    fontSize: 16,
    lineHeight: 1,
  });

  static readonly ProductionCompanyAddress = new PDFField(
    'ProductionCompanyAddress',
    {
      label: 'Production Company Address',
      value: 'productionCompany.addressOneLine',
      type: 'text',
      height: 7,
      width: 92,
      alignment: 'left',
      fontSize: 16,
      lineHeight: 1,
    },
  );

  static readonly occupation = new PDFField('occupation', {
    label: 'Occupation',
    value: 'projectMember.occupation.name',
    type: 'text',
    height: 7,
    width: 92,
    alignment: 'left',
    fontSize: 16,
    lineHeight: 1,
  });

  public readonly value: StyledPDFFieldOption;
  private readonly key: string;

  // private to disallow creating other instances of this type
  private constructor(key: string, value: StyledPDFFieldOption) {
    this.key = key;
    this.value = value;
  }

  toString() {
    return this.key;
  }
}

export enum ProjectMemberTypeId {
  Admin = 1,
  Crew = 2,
}

export enum SortDirection {
  Ascending = 'asc',
  Descending = 'desc',
  None = '',
}

export enum ProjectMemberLoanOutStatusId {
  Pending = 1,
  Approved = 2,
  Rejected = 3,
  Requested = 4,
  Used = 5,
}

export enum TimecardStatusId {
  InProgress = 1,
  Submitted = 2,
  RequestedChanges = 3,
  Approved = 4,
  Processing = 5,
  Paid = 6,
}
export const TimecardStatusLabel = {
  [TimecardStatusId.InProgress]: 'In Progress',
  [TimecardStatusId.Submitted]: 'Submitted',
  [TimecardStatusId.RequestedChanges]: 'Requested Changes',
  [TimecardStatusId.Approved]: 'Approved',
  [TimecardStatusId.Processing]: 'Processing',
  [TimecardStatusId.Paid]: 'Paid',
};

export const BatchStatusLabel = {
  [BatchStatusEnum.OPEN]: 'Open',
  [BatchStatusEnum.PENDING_ADMIN_APPROVAL]: 'Pending Admin Approval',
  [BatchStatusEnum.SUBMITTED_TO_PAYROLL]: 'Submitted to Payroll',
  // [BatchStatusEnum.REOPENED]: 'Reopened', // not available yet
  [BatchStatusEnum.READY_FOR_CLIENT_REVIEW]: 'Ready for Client Review',
  // [BatchStatusEnum.RESUBMITTED]: 'Resubmitted', // not available yet
  [BatchStatusEnum.APPROVED]: 'Approved',
  [BatchStatusEnum.PAID]: 'Paid',
};
