import { applyPureVueInReact } from 'veaury';
import VerifyPhoneViewVue from '../../views/VerifyPhoneView.vue';
import { useReactRouter } from '../AppHooks';
const ReactVerifyPhoneView = applyPureVueInReact(VerifyPhoneViewVue);

const VerifyPhoneView = () => {
  const { navigate, route } = useReactRouter();
  return <ReactVerifyPhoneView route={route} navigate={navigate} />;
};

export default VerifyPhoneView;
