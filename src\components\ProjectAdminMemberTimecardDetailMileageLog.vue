<template>
  <section class="mt-5">
    <div class="border-t-2 pb-2"></div>
    <div class="flex space-x-3 items-center">
      <h2 class="text-xl font-bold">Mileage Log</h2>
      <Button size="xs" color="secondary" @click="openMileageEditModal">
        <div class="flex items-center space-x-1 stroke-gray-400">
          <Icon
            v-if="timecard.mileageForm"
            name="pencil"
            class="h-4 w-4 stroke-gray-500 dark:stroke-gray-400"
          />
          <Icon v-else name="plus" class="h-4 w-4" />
        </div>
      </Button>
      <Button
        v-if="timecard.mileageForm"
        size="xs"
        color="error"
        @click="openDeleteMileageForm"
      >
        <div class="flex items-center space-x-1 stroke-gray-400">
          <Icon name="trash" class="h-4 w-4" />
        </div>
      </Button>
    </div>
    <Modal v-model="addMileageModal">
      <div class="flex justify-between items-center">
        <h2 class="text-xl font-bold pb-2">Add Mileage</h2>
        <XMarkIcon
          @click="addMileageModal = false"
          class="h-6 w-6 hover:text-gray-300 cursor-pointer"
        />
      </div>
      <TextInput
        v-model="lineNumber"
        class="mt-2"
        label="Line Number"
        :disabled="!trackingHeaderDetails.allowed"
      />
      <TextInput label="Total Mileage" v-model="totalMileage" />
      <DatePicker
        v-model="mileageForm.date"
        class="mt-2"
        label="Date"
        :disabled="editDisabled"
        @change="getTimecardDayWorkLocation"
      />
      <Dropdown
        class="grow"
        v-model="mileageForm.workLocation"
        label="Work Location"
        display-name="shootLocation.locationName"
        :menu-items="project.projectShootLocations"
      >
        <template #label>
          {{
            `${mileageForm.workLocation?.shootLocation.locationName} (${mileageForm.workLocation?.zip})`
          }}
        </template>
        <template #item="{ value: projectShootLocation }">
          {{
            `${projectShootLocation?.shootLocation.locationName} (${projectShootLocation?.zip})`
          }}
        </template>
      </Dropdown>
      <FileUpload v-model="documentId" class="my-1" />
      <div class="flex justify-center space-x-2 pt-3">
        <Button @click="addMileageModal = false" color="gray"> Cancel </Button>
        <Button @click="saveMileageHandler" :loading="loading" color="primary">
          Save
        </Button>
      </div>
    </Modal>
    <div v-if="timecard.mileageForm" class="mt-5">
      <div class="flex items-center space-x-4">
        <div>
          <div class="text-xs font-bold">Line Number</div>
          <div>{{ timecard.mileageForm.lineNumber }}</div>
        </div>
        <div>
          <div class="text-xs font-bold">Total Mileage</div>
          <div>{{ timecard.mileageForm.totalMileage }}</div>
        </div>
        <div>
          <div class="text-xs font-bold">Date</div>
          <div>{{ timecard.mileageForm.date?.toISODate() }}</div>
        </div>
        <div>
          <div class="text-xs font-bold">Work Location</div>
          <div>
            {{
              `${timecard.mileageForm.workLocation?.shootLocation.locationName}
            (${timecard.mileageForm.workLocation?.zip})`
            }}
          </div>
        </div>
      </div>
      <Modal v-model="editModal">
        <div class="flex justify-between items-center">
          <h2 class="text-xl font-bold pb-2">Edit Mileage</h2>
          <XMarkIcon
            @click="editModal = false"
            class="h-6 w-6 hover:text-gray-300 cursor-pointer"
          />
        </div>
        <TextInput
          v-model="lineNumber"
          label="Line Number"
          :disabled="!trackingHeaderDetails.allowed"
        />
        <TextInput v-model="totalMileage" label="Total Mileage" />
        <DatePicker
          v-model="mileageForm.date"
          class="mt-2"
          label="Date"
          :disabled="editDisabled"
          @change="getTimecardDayWorkLocation"
        />
        <Dropdown
          class="grow"
          v-model="mileageForm.workLocation"
          label="Work Location"
          display-name="shootLocation.locationName"
          :menu-items="project.projectShootLocations"
        >
          <template #label>
            {{
              `${mileageForm.workLocation?.shootLocation.locationName} (${mileageForm.workLocation?.zip})`
            }}
          </template>
          <template #item="{ value: projectShootLocation }">
            {{
              `${projectShootLocation?.shootLocation.locationName} (${projectShootLocation?.zip})`
            }}
          </template>
        </Dropdown>
        <FileUpload v-model="documentId" class="my-1" />
        <div class="flex justify-center space-x-2 pt-3">
          <Button @click="editModal = false" color="gray"> Cancel </Button>
          <Button
            @click="updateMileageHandler"
            :loading="loading"
            color="primary"
          >
            Save
          </Button>
        </div>
      </Modal>
      <Modal v-model="deleteModal">
        <div class="flex justify-between items-center">
          <h2 class="text-xl font-bold pb-2">Delete Mileage</h2>
        </div>
        <div>Are you sure you want to delete this mileage log?</div>
        <div class="flex justify-center space-x-2 pt-3">
          <Button @click="deleteModal = false" color="gray"> Cancel </Button>
          <Button
            @click="deleteMileageFormHandler"
            :loading="loading"
            color="error"
          >
            Delete
          </Button>
        </div>
      </Modal>
      <Button
        class="mt-3"
        size="sm"
        color="primaryOutline"
        @click="openMileageForm()"
      >
        <div class="flex items-center">
          <ArrowUpTrayIcon class="h-4 w-4 mr-1" />
          <div>Download Mileage Form</div>
        </div>
      </Button>
    </div>
    <div v-else class="text-l pt-3">No mileage log uploaded.</div>
  </section>
</template>

<script setup lang="ts">
import Icon from '@/components/Icon.vue';
import Button from '@/components/library/Button.vue';
import DatePicker from '@/components/library/DatePicker.vue';
import Dropdown from '@/components/library/Dropdown.vue';
import FileUpload from '@/components/library/FileUpload.vue';
import Modal from '@/components/library/Modal.vue';
import TextInput from '@/components/library/TextInput.vue';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import { deleteMileageForm, updateMileageForm } from '@/services/mileage-forms';
import {
  getMileageFormPdf,
  updateOrCreateMileageForm,
} from '@/services/timecards';
import type { MileageForm } from '@/types/MileageForm';
import type Project from '@/types/Project';
import type Timecard from '@/types/Timecard';
import type TimecardDay from '@/types/TimecardDay';
import { ArrowUpTrayIcon, XMarkIcon } from '@heroicons/vue/24/outline';
import axios from 'axios';
import { DateTime } from 'luxon';
import { ref, watch, type Ref } from 'vue';

const props = defineProps<{
  timecard: Timecard;
  project: Project;
  editDisabled: boolean;
  trackingHeaderDetails: any;
}>();

const emits = defineEmits(['refresh']);

const editModal: Ref<boolean> = ref(false);
const addMileageModal: Ref<boolean> = ref(false);
const deleteModal: Ref<boolean> = ref(false);
const totalMileage: Ref<number> = ref(0);
const loading: Ref<boolean> = ref(false);
const documentId: Ref<string | null> = ref(null);
const lineNumber: Ref<string> = ref('');

const mileageForm: Ref<MileageForm> = ref({
  ...props.timecard.mileageForm,
  date: props.timecard.mileageForm?.date || DateTime.now(),
});

watch(
  () => props.timecard.mileageForm,
  (newVal) => {
    mileageForm.value = {
      ...newVal,
      date: newVal?.date || DateTime.now(),
    };
  },
);

const openMileageEditModal = async () => {
  if (props.timecard.mileageForm) {
    editModal.value = true;
    lineNumber.value = mileageForm.value.lineNumber;
    totalMileage.value = mileageForm.value.totalMileage;
    documentId.value = mileageForm.value.documentId;
  } else {
    addMileageModal.value = true;
  }
  if (
    props.project.mileageAicpNumber &&
    !lineNumber.value &&
    props.trackingHeaderDetails.allowed
  ) {
    lineNumber.value = props.project.mileageAicpNumber;
  }
};

const saveMileageHandler = async () => {
  loading.value = true;
  try {
    if (!lineNumber.value && props.trackingHeaderDetails.required) {
      throw Error('Line number is required.');
    }
    if (totalMileage.value === 0) {
      throw Error('Total Mileage cannot be 0.');
    }
    if (!documentId.value) {
      throw Error('Please upload a document.');
    }
    await updateOrCreateMileageForm(props.timecard.id, {
      lineNumber: lineNumber.value,
      totalMileage: totalMileage.value,
      timecardId: props.timecard.id,
      date: mileageForm.value.date?.toISODate(),
      workLocation: mileageForm.value.workLocation,
      documentId: documentId.value,
    });
    SnackbarStore.triggerSnackbar('Mileage form saved', 2500, 'success');
    emits('refresh');
    addMileageModal.value = false;
  } catch (err) {
    if (axios.isAxiosError(err)) {
      const msg = err.response?.data?.['errors']?.[0]?.message;
      SnackbarStore.triggerSnackbar(msg, 2500, 'error');
    } else {
      SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
    }
  }
  loading.value = false;
};

const updateMileageHandler = async () => {
  if (!mileageForm.value.id) return;
  loading.value = true;
  try {
    if (!lineNumber.value && props.trackingHeaderDetails.required) {
      throw Error('Line number is required.');
    }
    await updateMileageForm(mileageForm.value.id, {
      lineNumber: lineNumber.value,
      totalMileage: totalMileage.value,
      documentId: documentId.value!,
      workLocation: mileageForm.value.workLocation || undefined,
      date: mileageForm.value.date.toISODate()!,
    });
    SnackbarStore.triggerSnackbar(
      'Mileage updated successfully.',
      2500,
      'success',
    );
    emits('refresh');
    editModal.value = false;
  } catch (err) {
    if (axios.isAxiosError(err)) {
      const msg = err.response?.data?.['errors']?.[0]?.message;
      SnackbarStore.triggerSnackbar(msg, 2500, 'error');
    } else {
      SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
    }
  }
  loading.value = false;
};

const deleteMileageFormHandler = async () => {
  if (!mileageForm.value.id) return;
  loading.value = true;
  try {
    await deleteMileageForm(mileageForm.value.id);
    documentId.value = null;
    totalMileage.value = 0;
    lineNumber.value = '';
    SnackbarStore.triggerSnackbar(
      'Mileage deleted successfully.',
      2500,
      'success',
    );
    emits('refresh');
    deleteModal.value = false;
  } catch (err) {
    if (axios.isAxiosError(err)) {
      const msg = err.response?.data?.['errors']?.[0]?.message;
      SnackbarStore.triggerSnackbar(msg, 2500, 'error');
    } else {
      SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
    }
  }
  loading.value = false;
};

const openMileageForm = async () => {
  const link = document.createElement('a');
  link.href = getMileageFormPdf(props.timecard.id);
  link.setAttribute('download', 'mileage_form.pdf');
  document.body.appendChild(link);
  link.click();
};

const openDeleteMileageForm = () => {
  deleteModal.value = true;
};

const getTimecardDayWorkLocation = async () => {
  if (mileageForm.value.date) {
    const workLocation = props.timecard.timecardDays.find(
      (timecardDay: TimecardDay) => {
        return (
          timecardDay.date.toISODate() === mileageForm.value.date.toISODate()
        );
      },
    )?.projectShootLocation;
    if (workLocation) {
      mileageForm.value.workLocation = workLocation;
    }
  }
};
</script>
