import MuiAvatar from '@mui/material/Avatar';
import PropTypes from 'prop-types';

// // Removing dynamic color until we can get Design to give us some boundaries on it
// MTG-1186
// function stringToColor(string) {
//   let hash = 0;
//   let i;

//   /* eslint-disable no-bitwise */
//   for (i = 0; i < string.length; i += 1) {
//     hash = string.charCodeAt(i) + ((hash << 5) - hash);
//   }

//   let color = '#';

//   for (i = 0; i < 3; i += 1) {
//     const value = (hash >> (i * 8)) & 0xff;
//     color += `00${value.toString(16)}`.slice(-2);
//   }
//   /* eslint-enable no-bitwise */

//   return color;
// }

const sizes = {
  sm: { height: 30, width: 30, fontSize: '12px', fontWeight: 600 },
  md: { height: 40, width: 40, fontSize: '16px', fontWeight: 600 },
  lg: { height: 60, width: 60, fontSize: '25px', fontWeight: 600 },
};

function Avatar(props) {
  const { name = '', sx = {}, size = 'lg' } = props;

  const initials = `${name.split(' ')[0][0]}${
    name.split(' ')[1][0]
  }`.toUpperCase();

  const styleSX = [
    () => ({
      bgcolor: 'gray.200',
      // bgcolor: stringToColor(name),
      border: '1px solid',
      borderColor: 'gray.300',
      color: 'text.primary',
      ...sizes[size],
      ...sx,
    }),
    (theme) =>
      theme.applyStyles('dark', {
        bgcolor: 'gray.700',
        textColor: 'white',
      }),
  ];

  return <MuiAvatar sx={styleSX}>{initials}</MuiAvatar>;
}

Avatar.propTypes = {
  name: PropTypes.string,
  sx: PropTypes.object,
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
};

export default Avatar;
