<template>
  <div class="space-y-8 divide-y divide-gray-200 px-4 sm:px-0 pt-16">
    <div class="relative flex flex-col overflow-hidden md:py-3">
      <div
        class="sm:m-auto max-w-screen-lg space-y-8 divide-y divide-gray-200 dark:divide-gray-400 mt-5"
      >
        <div>
          <div>
            <h3 class="text-3xl mb-3 mt-3 font-medium leading-6">Project</h3>
            <p class="mt-1 text-sm text-gray-500">
              {{
                editMode
                  ? `Update project details.`
                  : `Let's start a new project!`
              }}
            </p>
          </div>

          <div class="mt-6 grid grid-cols-2 gap-x-4 sm:grid-cols-6">
            <div class="sm:col-span-4 col-span-2">
              <TextInput
                label="Project Name"
                v-model="project.name"
                data-testid="project-name-input"
              />
            </div>
            <div class="sm:col-span-2 col-span-2">
              <TextInput
                label="Project Number"
                v-model="project.number"
                data-testid="project-number-input"
              />
            </div>
            <div class="sm:col-span-6 col-span-2">
              <TextArea
                label="Project Description"
                v-model="project.description"
                data-testid="project-description-input"
              />
            </div>
            <div
              class="sm:col-span-3"
              data-testid="project-startsOn-datePicker"
            >
              <DatePicker label="Starts On" v-model="project.startsAt" />
            </div>
            <div class="sm:col-span-3" data-testid="project-endsOn-datePicker">
              <DatePicker label="Ends On" v-model="project.endsAt" />
            </div>
            <div class="sm:col-span-6">
              <Dropdown
                label="Minute Increment"
                v-model="project.minuteIncrement"
                display-name="name"
                :menu-items="minuteIncrements"
                data-testid="project-minuteIncrements-dropdown"
              />
            </div>
            <div class="sm:col-span-6">
              <TextInput
                label="Mileage AICP Line Number"
                v-model="project.mileageAicpNumber"
                :disabled="!lineNumberFieldsAllowed"
                data-testid="project-mileageAicpNumber-input"
              />
            </div>
            <div class="sm:col-span-6">
              <Toggle v-model="project.isApplyingForTaxIncentives">
                <div
                  class="text-sm"
                  style="margin-top: -2px"
                  data-testid="project-tax-toggle"
                >
                  Applying for Tax Incentives
                </div>
              </Toggle>
            </div>
          </div>
        </div>
        <div>
          <h3 class="text-xl font-bold pt-4 dark:text-gray-200">
            Production Company
          </h3>
          <Combobox
            class="mt-2"
            v-model="project.productionCompany"
            :items="companies"
            @update:search="(x: any) => (companySearch = x)"
            :search="companySearch"
            display-name="name"
            data-testid="production-company-combobox"
          >
            <template #item="{ value: item }">
              {{ item?.name }}
            </template>
          </Combobox>
          <Dropdown
            v-if="addresses.length > 0"
            v-model="project.productionCompanyAddress"
            :menu-items="addresses"
            label="Production Company Address"
            display-name="street"
            data-testid="production-address-dropdown"
          />
          <div class="pt-4">
            <Dropdown
              v-if="project.productionCompany?.castAndCrewId"
              label="Project Type"
              v-model="project.type"
              display-name="name"
              :loading="loadingProjectTypes"
              :menu-items="projectTypes"
              @change="loadShootLocations"
              data-testid="project-type-dropdown"
            />
          </div>
          <div v-if="project.productionCompany?.castAndCrewId">
            <h3 class="text-xl font-bold pt-4 dark:text-gray-200">
              Work Locations
            </h3>
            <div
              v-for="(
                projectShootLocation, shootLocationIndex
              ) in project.projectShootLocations as any
              "
              :key="`shoot-location-${shootLocationIndex}`"
              class="mt-6 mb-3 grid grid-cols-1 gap-x-4 sm:grid-cols-6"
            >
              <div>
                <div class="font-semibold">Name</div>
                {{ projectShootLocation.shootLocation.locationName }}
              </div>
              <div>
                <div class="font-semibold">City</div>
                {{ projectShootLocation.shootLocation.city }}
              </div>
              <div>
                <div class="font-semibold">State</div>
                {{ projectShootLocation.shootLocation.state }}
              </div>
              <div v-if="projectShootLocation.createdAt">
                <div class="font-semibold">Zip Code</div>
                {{ projectShootLocation.zip }}
              </div>
              <div v-else>
                <TextInput
                  label="Zip Code"
                  type="postal"
                  v-model="projectShootLocation.zip"
                  data-testid="locationCard-zipCode-input"
                />
              </div>
              <div class="grow" />
              <div class="flex items-center">
                <Button
                  size="xs"
                  color="error"
                  @click="
                    removeShootLocation(projectShootLocation.shootLocationId)
                  "
                  data-testid="locationCard-remove-btn"
                >
                  Remove
                </Button>
              </div>
            </div>
            <Button
              size="sm"
              class="mt-2"
              @click="shootLocationModal = true"
              data-testid="location-add-btn"
            >
              Add Work Location
            </Button>
            <Modal v-model="shootLocationModal" class="mb-16">
              <h2 class="mb-2 text-lg">Add Work Location</h2>
              <input focus hidden />
              <Autocomplete
                v-model="selectedShootLocation"
                :menu-items="shootLocations"
                label="Work Locations"
                display-name="locationName"
                data-testid="location-search-input"
              />
              <p class="mt-2">
                The above dropdown lists out all work locations valid for your
                production company.
              </p>
              <div class="flex justify-start space-x-2 mt-32">
                <Button
                  class="mt-2"
                  @click="addShootLocation"
                  size="sm"
                  color="primary"
                  data-testid="location-confirm-btn"
                  >Add</Button
                >
                <Button
                  class="mt-2"
                  @click="shootLocationModal = false"
                  size="sm"
                  color="gray"
                  data-testid="location-cancel-btn"
                  >Cancel</Button
                >
              </div>
            </Modal>
          </div>
        </div>

        <div>
          <h3 class="text-xl font-bold pt-4 dark:text-gray-200">Departments</h3>
          <p class="text-sm text-gray-500 pb-2">
            Departments allow you to subdivide the work and visibility of a
            project.
          </p>
          <div>
            <Button
              v-if="project.departments?.length <= 1"
              size="sm"
              @click="openCreateDepartmentModal"
              data-testid="department-add-btn"
            >
              Add
            </Button>
          </div>
          <Modal v-model="createDepartment.modal" title="Create Department">
            <h2 class="font-semibold mb-2">Add Department</h2>
            <Dropdown
              label="Department"
              v-model="createDepartment.type"
              :menu-items="availableDepartments"
              display-name="name"
              data-testid="department-select-dropdown"
            />
            <div class="flex justify-start space-x-2">
              <Button
                class="mt-2"
                @click="addDepartment"
                size="sm"
                color="primary"
                data-testid="department-confirm-btn"
                >Add</Button
              >
              <Button
                class="mt-2"
                @click="createDepartment.modal = false"
                size="sm"
                color="gray"
                data-testid="department-cancel-btn"
                >Cancel</Button
              >
            </div>
          </Modal>
          <div>
            <div v-for="department in project.departments" :key="department.id">
              <div class="flex justify-start py-3 px-1">
                <p
                  class="truncate text-sm font-medium text-indigo-600 dark:text-indigo-400"
                >
                  {{ department.type.name }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div class="py-5 mx-2">
          <div class="flex justify-center">
            <Button
              class="w-48 mr-1"
              @click="cancel"
              color="secondary"
              data-testid="project-cancel-btn"
              >Cancel</Button
            >
            <Button
              class="w-48 ml-1"
              color="primary"
              :loading="loading"
              @click="submit"
              data-testid="project-create-btn"
              >{{ editMode ? 'Update' : 'Create' }}
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Button from '@/components/library/Button.vue';
import Combobox from '@/components/library/Combobox.vue';
import DatePicker from '@/components/library/DatePicker.vue';
import Dropdown from '@/components/library/Dropdown.vue';
import Modal from '@/components/library/Modal.vue';
import TextArea from '@/components/library/TextArea.vue';
import TextInput from '@/components/library/TextInput.vue';
import Autocomplete from '@/components/library/Autocomplete.vue';
import Toggle from '@/components/library/Toggle.vue';
import { getProductionCompanyAddresses, getStates } from '@/services/address';
import { listMinuteIncrements } from '@/services/minute-increment';
import {
  getProductionCompanyWorkLocations,
  listProductionCompanies,
  listProjectTypes,
} from '@/services/production-company';
import {
  createProject,
  getProjectByHashId,
  updateProject,
  getTrackingDetails,
} from '@/services/project';
import { listProjectDepartmentTypes } from '@/services/project-departments';
import { getTaxClassifications } from '@/services/tax-classifications';
import { FilterOperator, FilterUIType, type Filter } from '@/types/Filter';
import type MinuteIncrement from '@/types/MinuteIncrement';
import type { Pagination } from '@/types/Pagination';
import type ProductionCompany from '@/types/ProductionCompany';
import type Project from '@/types/Project';
import type { ProjectDepartment } from '@/types/ProjectDepartment';
import type ProjectShootLocation from '@/types/ProjectShootLocation';
import type State from '@/types/State';
import type { SnackType } from '@/types/Snackbar';
import { convertFiltersToQuery } from '@/utils/filter';
import { TrackingKeysEnum } from '@/types/TimecardDay';
import axios from 'axios';
import { debounce } from 'lodash';
import { DateTime } from 'luxon';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import { defineComponent } from 'vue';
import ProjectStore from '@/reactComponents/stores/project';

export default defineComponent({
  props: {
    editMode: {
      type: Boolean,
      default: false,
    },
    navigate: {
      type: Function,
      required: true,
    },
    route: {
      type: Object,
      required: true,
    },
  },
  setup() {
    return { SnackbarStore };
  },
  components: {
    TextInput,
    Button,
    Dropdown,
    DatePicker,
    Combobox,
    Toggle,
    TextArea,
    Modal,
    Autocomplete,
  },
  data() {
    return {
      loading: false,
      project: {
        id: undefined,
        name: undefined,
        number: undefined,
        description: undefined,
        startsAt: DateTime.now().startOf('day'),
        endsAt: DateTime.now().startOf('day'),
        projectShootLocations: [] as ProjectShootLocation[],
        payPeriods: [],
        productionCompanyAddress: undefined,
        productionCompanyId: undefined,
        productionCompany: undefined,
        type: null,
        minuteIncrement: undefined,
        isApplyingForTaxIncentives: false,
        departments: [] as ProjectDepartment[],
      } as Project,
      minuteIncrements: [] as MinuteIncrement[],
      state: {} as State,
      states: [] as State[],
      addresses: [] as any[],
      companies: [
        {
          name: 'Test Company',
        },
      ] as ProductionCompany[],
      companySearch: '',
      taxClassifications: [],
      loadingProjectTypes: true,
      projectTypes: [] as any[],
      shootLocations: [] as any[],
      shootLocationModal: false,
      selectedShootLocation: null as any,
      lineNumberFieldsAllowed: true,
      trackingKeyLineNo: null as any,
      createDepartment: {
        modal: false,
        type: null as any,
        types: [] as any[],
      },
    };
  },
  watch: {
    'project.productionCompany': {
      handler: async function (val) {
        if (!val) return;
        await this.updateAddresses();
        await this.getProjectTypes(val.id, this.project?.id);
      },
      immediate: true,
    },
    companySearch: {
      handler: async function () {
        debounce(async () => {
          await this.getProductionCompanies();
        }, 500)();
      },
      immediate: true,
    },
  },
  async mounted() {
    if (this.editMode) {
      await this.getProject();
      await this.getTrackingHeaderDetails();
      await this.updateAddresses();
    }
    await this.getTaxClassifications();
    await this.getStates();
    await this.getProductionCompanies();
    await this.getMinuteIncrements();
    if (this.project.departments.length === 0) {
      const projectDepartment: ProjectDepartment = {
        type: {
          id: 2,
          name: 'General',
          key: 'general',
          description: '',
        },
        projectId: this.project.id!,
      };
      this.project.departments.push(projectDepartment);
    }
  },
  computed: {
    availableDepartments() {
      return this.createDepartment.types.filter((type) => {
        return !this.project.departments.some(
          (department) => department.type.id === type.id,
        );
      });
    },
  },
  methods: {
    triggerSnackbar(msg: string, duration: number, type: SnackType) {
      this.SnackbarStore.triggerSnackbar(msg, duration, type);
    },
    async getProject(): Promise<void> {
      const { data: project } = await getProjectByHashId(
        this.route.params.hashId as string,
      );
      if (project) {
        this.project = project;
        await this.getProjectTypes(
          this.project?.productionCompany?.id!,
          this.project?.id!,
        );
      }
    },
    async getTrackingHeaderDetails(): Promise<void> {
      const { data: trackingKeys } = await getTrackingDetails(this.project.id!);
      const trackingKeyHeaderDetails = trackingKeys.trackingKeyHeaderDetails;
      if (
        trackingKeyHeaderDetails !== null &&
        trackingKeyHeaderDetails.length > 0
      ) {
        trackingKeyHeaderDetails.forEach((eachTrackingKey: any) => {
          if (
            eachTrackingKey?.name === TrackingKeysEnum.KEY_TRACKKEY &&
            eachTrackingKey?.allowed
          ) {
            this.trackingKeyLineNo = eachTrackingKey;
          }
        });
      }
      if (this.trackingKeyLineNo === null) {
        this.lineNumberFieldsAllowed = false;
        this.project.mileageAicpNumber = undefined;
      }
    },
    async getProjectTypes(
      productionCompanyId: number,
      projectId?: number,
    ): Promise<void> {
      this.loadingProjectTypes = true;
      this.projectTypes = [];
      try {
        const { data: projectTypes } = await listProjectTypes(
          productionCompanyId,
          projectId,
        );
        this.projectTypes = projectTypes;
        if (this.project.type?.key) {
          const projectType = projectTypes.find(
            (type: any) => type.key === this.project.type.key,
          );
          this.project.type = projectType;
          this.loadShootLocations();
        }
      } catch (err) {
        console.warn('Error loading project types', err);
      }
      this.loadingProjectTypes = false;
    },
    async getTaxClassifications(): Promise<void> {
      const { data: taxClassifications } = await getTaxClassifications();
      this.taxClassifications = taxClassifications;
    },
    async getStates(): Promise<void> {
      const {
        data: { data: states },
      } = await getStates();
      this.states = states;
    },
    async getProductionCompanies(): Promise<void> {
      const pagination: Pagination = {
        page: 1,
        limit: 100,
        total: 0,
      };
      const filters: Filter[] = [
        {
          id: 'company_name',
          field: 'name',
          label: 'Company Name',
          value: this.companySearch,
          type: FilterUIType.Text,
          operator: FilterOperator.ILike,
          active: true,
          sortable: true,
          sortDirection: '',
        },
      ];
      const filtersString = convertFiltersToQuery(filters);
      const {
        data: { data: companies },
      } = await listProductionCompanies(pagination, filtersString);
      this.companies = companies;
    },
    async getMinuteIncrements(): Promise<void> {
      const { data } = await listMinuteIncrements();
      this.minuteIncrements = data;
    },
    async submit() {
      if (this.loading) return;
      this.loading = true;
      try {
        if (this.editMode) {
          await updateProject(this.project);
          SnackbarStore.triggerSnackbar(
            'Project updated successfully.',
            2500,
            'success',
          );
          ProjectStore.fetchProject(this.route.params.hashId, true);
        } else {
          await createProject(this.project);
          SnackbarStore.triggerSnackbar(
            'Project created successfully.',
            2500,
            'success',
          );
        }
        this.navigate(-1);
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
      }
      this.loading = false;
    },
    cancel() {
      this.navigate(-1);
    },
    addShootLocation() {
      const newProjectShootLocation: ProjectShootLocation = {
        shootLocation: this.selectedShootLocation,
        shootLocationId: this.selectedShootLocation.locationId,
        projectId: this.project.id!,
        zip: this.selectedShootLocation.zipCode,
        payrollProjectLocationId:
          this.selectedShootLocation.payrollProjectLocationId,
      };
      this.project.projectShootLocations.push(newProjectShootLocation);
      this.shootLocationModal = false;
      this.selectedShootLocation = null;
      this.loadShootLocations();
    },
    removeShootLocation(shootLocationId: number) {
      // remove the first instance with the provided shootLocationId
      const index = this.project.projectShootLocations.findIndex(
        (x) => x.shootLocationId === shootLocationId,
      );
      if (index > -1) {
        this.project.projectShootLocations.splice(index, 1);
      }
      this.loadShootLocations();
    },
    async updateAddresses() {
      if (
        !this.project.productionCompany &&
        !this.project.productionCompany!.castAndCrewId
      )
        return;
      const { data: productionCompanyAddresses } =
        await getProductionCompanyAddresses(
          this.project.productionCompany!.id!,
        );
      this.addresses = productionCompanyAddresses;
    },
    async openCreateDepartmentModal() {
      this.createDepartment.modal = true;
      const { data: departmentTypes } = await listProjectDepartmentTypes();
      this.createDepartment.types = departmentTypes;
    },
    async addDepartment() {
      const projectDepartment: ProjectDepartment = {
        type: this.createDepartment.type,
        projectId: this.project.id!,
      };
      this.project.departments.push(projectDepartment);
      this.createDepartment.modal = false;
      this.createDepartment.type = null;
    },
    async loadShootLocations(id?: number) {
      const productionCompanyId = id || this.project.productionCompany?.id;
      if (!productionCompanyId) return;
      this.shootLocations = [];
      const payrollProjectTypeId = this.project.type?.castAndCrewId;
      if (!payrollProjectTypeId) return;
      try {
        const {
          data: {
            result: { items },
          },
        } = await getProductionCompanyWorkLocations(
          productionCompanyId,
          payrollProjectTypeId,
        );
        this.shootLocations = items;
      } catch (err) {
        console.warn('Error loading shoot locations', err);
      }
    },
  },
});
</script>
