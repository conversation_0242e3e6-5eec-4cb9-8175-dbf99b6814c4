<template>
  <div class="min-h-full">
    <main class="pt-8 pb-16">
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <Observer>
          <ProjectAdminTimecardsTable :project="project" :is-admin="isAdmin" />
        </Observer>
      </div>
    </main>
  </div>
</template>

<script lang="ts" setup>
// todo: need to test Observer for re-render
import ProjectAdminTimecardsTable from '@/components/ProjectAdminTimecardsTable.vue';
import type Project from '@/types/Project';
import { computed, provide, ref, watchEffect } from 'vue';
import { Observer } from 'mobx-vue-lite';
import PermissionStore from '@/reactComponents/stores/permission';
import { PermissionKeys } from '../../../types/Permission';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';

const props = defineProps<{
  project: Project;
  isAdmin: boolean;
  navigate?: Function;
  route?: ParsedRoute;
}>();

const route = ref(props.route);

watchEffect(() => {
  route.value = props.route;
});

provide('route', route);
provide('navigate', props.navigate);

const isAdmin = computed(
  () =>
    props.isAdmin ||
    PermissionStore.hasPermission(PermissionKeys.ADMIN_VIEW_ON_ALL_PROJECTS),
);
</script>
