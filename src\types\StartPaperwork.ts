import type { DateTime } from 'luxon';
import type { DocumentTemplate } from './Document';
import type ProjectMember from './ProjectMember';
import type User from './User';

export interface StartPaperwork {
  id: number;
  documentId: string;
  documentTemplateId: string;
  documentTemplate: DocumentTemplate;
  projectMemberId: number;
  supervisorSignedById: number;
  supervisorSignedBy: User;
  supervisorSignedAt: DateTime;
  supervisorIpAddress: string;
  crewIpAddress: string;
  crewSignedAt: DateTime;
  crewSigned: boolean;
  projectMember: ProjectMember;
  numPages: number;
  createdAt: DateTime;
  updatedAt: DateTime;
}
