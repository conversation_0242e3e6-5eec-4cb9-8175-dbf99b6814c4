export class ProjectOnboardingPageObject {
  workLocationDropdown = 'button[id="headlessui-menu-button-10"]';
  unionCheckbox = 'input[value="Union"]';
  unionDropdown = '[data-testid="union-dropdown"]';
  nonUnionCheckbox = 'input[value="NonUnion"]';
  occupationDropdown = '[data-testid="occupation-dropdown"]';
  rateInput = '[data-testid="rate-input"]';
  rateTypeDropdown = '[data-testid="rateType-dropdown"]';
  saveButton = 'button[type="submit"]';

  fillOutForm(
    workLocation: string,
    unionName: string,
    occupation: string,
    rate: string,
    rateType: string,
  ) {
    cy.pickDropdownOption(this.workLocationDropdown, workLocation);
    if (unionName === 'NON') {
      cy.get(this.nonUnionCheckbox).should('be.visible').click();
    } else {
      cy.get(this.unionCheckbox).should('be.visible').click();
      cy.pickDropdownOption(this.unionDropdown, unionName);
    }
    cy.get('div.animate-pulse').should('not.exist');
    cy.pickDropdownOption(this.occupationDropdown, occupation, true);
    cy.get(this.rateInput).should('be.visible').type(rate);
    cy.pickDropdownOption(this.rateTypeDropdown, rateType);
    cy.get(this.saveButton).should('be.visible').click();
  }
}
