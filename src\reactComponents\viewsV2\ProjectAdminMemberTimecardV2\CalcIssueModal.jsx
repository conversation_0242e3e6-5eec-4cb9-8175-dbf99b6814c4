import React from 'react';
import PropTypes from 'prop-types';

import { Box, Modal, List, ListItem } from '@/reactComponents/library';

const styles = {
  items: { display: 'flex', flexDirection: 'column', gap: 1, width: '100%' },
  itemTitle: {
    fontWeight: 'bold',
    fontSize: 18,
  },
};

const ErrorModal = (props) => {
  const { open, setOpen, calcIssues } = props;

  const errors = calcIssues.filter((issue) => issue.isError);
  const warnings = calcIssues.filter((issue) => !issue.isError);

  return (
    <Modal
      title={'Calculation Issues'}
      open={open}
      setOpen={setOpen}
      isSidebar={true}
      submitText="Ok"
      onSubmit={() => setOpen(false)}
    >
      <Box sx={styles.items}>
        {errors.length > 0 && (
          <Box>
            <Box sx={styles.itemTitle}>Errors</Box>
            <List>
              {errors.map((issue) => (
                <ListItem key={issue.message}>{issue.message}</ListItem>
              ))}
            </List>
          </Box>
        )}
        {warnings.length > 0 && (
          <Box>
            <Box sx={styles.itemTitle}>Warnings</Box>
            <List>
              {warnings.map((issue) => (
                <ListItem key={issue.message}>{issue.message}</ListItem>
              ))}
            </List>
          </Box>
        )}
      </Box>
    </Modal>
  );
};

ErrorModal.propTypes = {
  open: PropTypes.bool,
  setOpen: PropTypes.func,
  calcIssues: PropTypes.array,
};

export default ErrorModal;
