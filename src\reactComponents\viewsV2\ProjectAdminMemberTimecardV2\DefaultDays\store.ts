import { makeAutoObservable, toJS } from 'mobx';
import type { GridApiPro } from '@mui/x-data-grid-pro';
import type {
  TemplateDay,
  TemplateDayMeal,
  TemplateTimecard,
  TemplateDayDTO,
} from '@/types/TemplateDay';
import _cloneDeep from 'lodash/cloneDeep';
import {
  snackbarErr,
  snackbarSuccess,
} from '@/reactComponents/library/Snackbar';

import {
  listDayTemplates,
  updateDayTemplates,
  deleteDayTemplateDay,
} from '@/services/project';
import { getAllWorkStatuses, getAllWorkZones } from '@/services/app';

import {
  set,
  validateTimecard,
} from '@/reactComponents/viewsV2/ProjectAdminMemberTimecardV2/timecardUtils';

import {
  handleGridRowUpdate,
  handleActiveDayToggle,
  workStatusCascadingChanges,
  clearAdditionalFieldsForIdleStatus,
} from '@/reactComponents/viewsV2/ProjectAdminMemberTimecardV2/timecardTableUtils';

import { dateFmtStr, createDay, copyDay, sortDays } from './utils';

class DefaultDaysStoreClass {
  private _timecard: TemplateTimecard = { id: 0, timecardDays: [] };
  private _projectId: number = 0;

  private _workStatuses = [];
  private _workZones = [];

  private _saving = false;
  private _loading = true;
  private _unsavedChanges = false;

  constructor() {
    makeAutoObservable(this, {}, { autoBind: true });
  }

  //Getters/Setters
  get timecard() {
    return this._timecard;
  }
  set timecard(value) {
    this._timecard = value;
  }

  get workStatuses() {
    return this._workStatuses;
  }
  set workStatuses(value) {
    this._workStatuses = value;
  }
  get workZones() {
    return this._workZones;
  }
  set workZones(value) {
    this._workZones = value;
  }

  get projectId(): number {
    return this._projectId;
  }
  set projectId(value: number) {
    this._projectId = value;
  }

  get loading() {
    return this._loading;
  }
  set loading(value) {
    this._loading = value;
  }
  get saving() {
    return this._saving;
  }
  set saving(value) {
    this._saving = value;
  }

  get unsavedChanges() {
    return this._unsavedChanges;
  }
  set unsavedChanges(value) {
    this._unsavedChanges = value;
  }

  //Computed/Watched Values
  get activeDays() {
    return this.timecard.timecardDays.filter((day) => day.isActive);
  }

  get validate() {
    const errors = validateTimecard(this.timecard, true);
    return errors;
  }

  async init(projectId: number) {
    this.projectId = projectId;
    this.loading = true;
    await Promise.all([
      this.fetchDayTemplate(projectId),
      this.fetchWorkStatuses(),
      this.fetchWorkZones(),
    ]).catch(() => {
      snackbarErr('Error loading templates');
      this.unmount();
    });
    this.loading = false;
  }

  unmount() {
    this._timecard = { id: 0, timecardDays: [] };
    this._workStatuses = [];
    this._workZones = [];
  }

  async fetchDayTemplate(projectId: number) {
    const { data } = await listDayTemplates(projectId);
    const timecardDays = data.map(this.convertToTemplateDay);
    timecardDays.sort(sortDays);
    this.timecard = {
      id: 1, //Change in ID trigger initial additionalField check
      timecardDays,
    };
    this.unsavedChanges = false;
  }

  convertToTemplateDay(dtoDay: TemplateDayDTO): TemplateDay {
    if (dtoDay.date === null) {
      throw new Error('Date is null');
    }

    const utcDate = dtoDay.date.toUTC();

    dtoDay.startsAt = dtoDay.startsAt?.toUTC() || null;
    dtoDay.endsAt = dtoDay.endsAt?.toUTC() || null;
    dtoDay.generalCrewCall = dtoDay.generalCrewCall?.toUTC() || null;
    dtoDay.meals.forEach((meal: TemplateDayMeal) => {
      meal.startsAt = meal.startsAt?.toUTC() || null;
      meal.endsAt = meal.endsAt?.toUTC() || null;
    });

    const newDay = {
      ...dtoDay,
      date: utcDate,
      updated: false,
      isNewDay: false,
    };
    return newDay;
  }

  async fetchWorkStatuses() {
    const res = await getAllWorkStatuses();
    this.workStatuses = res.data;
  }

  async fetchWorkZones() {
    const res = await getAllWorkZones();
    this.workZones = res.data;
  }

  async save() {
    try {
      this.saving = true;
      const timecard = toJS(this.timecard);
      const activeDays = timecard.timecardDays.filter(
        (d: TemplateDay) => d.updated,
      );
      const payload = activeDays.map((day: TemplateDay) => {
        const { updated, isNewDay, ...rest } = day;
        const payloadDay: TemplateDayDTO = rest;
        if (isNewDay) {
          payloadDay.id = null;
          payloadDay.meals.forEach((meal: TemplateDayMeal) => {
            delete meal.id;
            meal.timecardDayId = null;
          });
        }

        //remove empty meals
        payloadDay.meals = payloadDay.meals.filter(
          (meal: TemplateDayMeal, i: number) => {
            if (meal.startsAt === null && meal.endsAt === null) {
              return false;
            }
            return true;
          },
        );

        return payloadDay;
      });
      const res = await updateDayTemplates(this.projectId, payload);
      const newDays = res.data;
      timecard.timecardDays = newDays.map(this.convertToTemplateDay);
      this.timecard = timecard;
      this.unsavedChanges = false;
      snackbarSuccess('Default days saved successfully');
    } catch (error) {
      console.error('Error saving default days', error);
      snackbarErr('Error saving default days');
    } finally {
      this.saving = false;
    }
  }
  //update single field and handle its cascading changes
  update({
    field,
    value,
    rowId,
    inArray,
    isWorkDaysGrid,
    api,
    cellMode,
  }: {
    field: keyof TemplateDay;
    value: any;
    rowId: string | number;
    inArray: boolean;
    isWorkDaysGrid: boolean;
    api: GridApiPro;
    cellMode: string;
  }) {
    if (isWorkDaysGrid) {
      const timecard = toJS(this.timecard);
      const timecardDays = timecard.timecardDays.map((day: TemplateDay) => {
        if (day.id === rowId) {
          if (inArray) {
            // meals fields
            const row = day;
            set(row, field as string, value);
          } else {
            // regular fields
            (day[field] as TemplateDay[typeof field]) = value;
            workStatusCascadingChanges({
              api,
              value,
              rowId,
              day,
              field,
              workZones: this.workZones,
              member: {},
            });
            clearAdditionalFieldsForIdleStatus({
              api,
              day,
              field,
              rowId,
            });

            handleActiveDayToggle({
              api,
              rowId,
              value,
              field,
              // removeReRate,
            });
            handleGridRowUpdate({
              api,
              rowId,
              field,
              cellMode,
              value,
            });
          }
          day.updated = true;
        }
        this.unsavedChanges = true;
        return day;
      });
      this.timecard = { id: 1, timecardDays };
    }
  }

  //Used for additionalFields to update meal on timecard value
  updateTimecard(updatedVals: any) {
    this.timecard = {
      ...this.timecard,
      ...updatedVals,
    };
    this.unsavedChanges = true;
  }

  addDays(dates: string[]) {
    const newDays = dates.map((date) => createDay(date));
    this.insertNewDays(newDays);
  }

  copyDays(dates: string[], sourceId: number) {
    const sourceDay = this.timecard.timecardDays.find(
      (d: TemplateDay) => d.id === sourceId,
    );
    if (!sourceDay) {
      console.error('Source day not found');
      return;
    }

    const newDays = dates.map((date) => copyDay(date, sourceDay));
    this.insertNewDays(newDays);
  }

  insertNewDays(newDays: TemplateDay[]) {
    const allDays = _cloneDeep(this.timecard.timecardDays);

    //Add days, they might be deleted/isActive:false
    newDays.forEach((newDay) => {
      const existingDayIdx = allDays.findIndex((day: TemplateDay) => {
        return (
          day.date.toFormat(dateFmtStr) === newDay.date.toFormat(dateFmtStr)
        );
      });
      if (existingDayIdx !== -1) {
        // If it exists, replace the existing day with the new one
        const existingDay = allDays[existingDayIdx];
        newDay.id = existingDay.id;
        newDay.isNewDay = false;
        newDay.meals.forEach((meal: TemplateDayMeal) => {
          meal.timecardDayId = existingDay.id;
        });
        allDays[existingDayIdx] = newDay;
      } else {
        allDays.push(newDay);
      }
    });

    allDays.sort(sortDays);
    this.timecard.timecardDays = allDays;
    this.unsavedChanges = true;
  }

  async deleteDay(id: number) {
    const day = this.timecard.timecardDays.find(
      (d: TemplateDay) => d.id === id,
    );
    if (!day) {
      console.error('Day not found');
      return;
    }

    try {
      if (!day.isNewDay) {
        await deleteDayTemplateDay(id);
      }
      this.timecard.timecardDays = this.timecard.timecardDays.filter(
        (d: TemplateDay) => d.id !== id,
      );
      snackbarSuccess('Day deleted successfully');
    } catch (error) {
      console.error('Error deleting day', error);
      snackbarErr('Error deleting day');
    }
  }
}

const DefaultDaysStore = new DefaultDaysStoreClass();
export default DefaultDaysStore;
