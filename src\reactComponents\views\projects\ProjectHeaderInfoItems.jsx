import React from 'react';
import { Icon } from '@/reactComponents/library';
import { DateTime } from 'luxon';
import PropTypes from 'prop-types';

function formatDatetime(datetime) {
  return DateTime.fromISO(datetime).toFormat('MM/dd');
}

const ProjectHeaderInfoItems = (props) => {
  const { project, className } = props;

  const infoItems = React.useMemo(
    () => [
      {
        id: 'productionCompany',
        icon: 'folder',
        text: project?.productionCompany?.name || '',
      },
      {
        id: 'number',
        icon: 'hash-02',
        text: project?.number || '',
      },
      {
        id: 'code',
        icon: 'phone-01',
        text: project?.code || '',
      },
      {
        id: 'dates',
        icon: 'calendar-01',
        text:
          project?.startsAt && project?.endsAt
            ? `${formatDatetime(project.startsAt)} - ${formatDatetime(
                project.endsAt,
              )}`
            : '',
      },
    ],
    [
      project?.productionCompany?.name,
      project?.number,
      project?.code,
      project?.startsAt,
      project?.endsAt,
    ],
  );

  return (
    <div className={`flex flex-wrap mb-2 ${className}`}>
      {infoItems.map((item) => (
        <div
          key={item.id}
          data-testid={`project-${item.id}-item`}
          className="mt-1 mr-6 flex items-center text-sm whitespace-nowrap text-gray-500 dark:text-gray-400"
        >
          <Icon name={item.icon} className="mr-1.5 w-4 h-4" />
          {item.text}
        </div>
      ))}
    </div>
  );
};

ProjectHeaderInfoItems.propTypes = {
  project: PropTypes.object.isRequired,
  className: PropTypes.string,
};

export default ProjectHeaderInfoItems;
