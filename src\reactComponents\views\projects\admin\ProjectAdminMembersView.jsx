import { applyPureVueInReact } from 'veaury';
import ProjectAdminMembersViewVue from '../../../../views/projects/admin/ProjectAdminMembersView.vue';
import { useAuth, useReactRouter } from '../../../AppHooks';
import { useOutletContext } from 'react-router';
const ReactProjectAdminMembersView = applyPureVueInReact(
  ProjectAdminMembersViewVue,
);

const ProjectAdminMembersView = () => {
  useAuth();
  const { route, navigate } = useReactRouter();
  const context = useOutletContext();
  return (
    <ReactProjectAdminMembersView
      route={route}
      navigate={navigate}
      isAdmin={context.isAdmin}
      project={context.project}
    />
  );
};

export default ProjectAdminMembersView;
