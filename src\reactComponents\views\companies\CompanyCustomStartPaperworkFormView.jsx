import { applyPureVueInReact } from 'veaury';
import CompanyCustomStartPaperworkFormViewVue from '../../../views/companies/CompanyCustomStartPaperworkFormView.vue';
import { useAuth, useReactRouter } from '../../AppHooks';
import { useOutletContext } from 'react-router';
const ReactCompanyCustomStartPaperworkFormView = applyPureVueInReact(
  CompanyCustomStartPaperworkFormViewVue,
);

const CompanyCustomStartPaperworkFormView = () => {
  useAuth();
  const { route, navigate } = useReactRouter();
  const context = useOutletContext();
  const editMode = route?.match?.handle?.editMode;
  return (
    <ReactCompanyCustomStartPaperworkFormView
      editMode={editMode}
      company={context.company}
      route={route}
      navigate={navigate}
    />
  );
};

export default CompanyCustomStartPaperworkFormView;
