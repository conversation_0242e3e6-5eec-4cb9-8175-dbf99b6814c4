import React from 'react';
import PropTypes from 'prop-types';
import { useOutletContext } from 'react-router';

import {
  Loader,
  Button,
  Modal,
  TextInput,
  Text,
  Box,
} from '@/reactComponents/library';
import { createBatch } from '@/services/project';
import {
  snackbarSuccess,
  snackbarErr,
} from '@/reactComponents/library/Snackbar';

import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';

const CreateBatchModal = (props) => {
  const {
    variant = 'batchList',
    submitMoveToBatch,
    onCreateComplete = () => {},
  } = props;
  const [open, setOpen] = React.useState(false);
  const [name, setName] = React.useState('');
  const [creating, setCreating] = React.useState(false);

  const { project } = useOutletContext();
  const inputRef = React.useRef(null);

  const isMoveVariant = variant === 'moveBatch';

  React.useEffect(() => {
    if (open) {
      setTimeout(() => {
        inputRef.current.focus();
      }, 100);
    } else {
      setName('');
    }
  }, [open]);

  const onCreateBatch = () => {
    const trimmedName = name.trim();
    if (!trimmedName) {
      snackbarErr('Batch name is required');
      return;
    }
    setCreating(true);
    createBatch(project.id, trimmedName)
      .then(({ data: newBatch }) => {
        setOpen(false);
        setName('');
        if (isMoveVariant) {
          submitMoveToBatch(newBatch, { isNewBatch: true });
        }
        if (onCreateComplete) {
          onCreateComplete(); //TODO rework onCreateComplete to be async/await or promise to let the refetch happen before we drop the modal
        }
        snackbarSuccess('Batch created');
      })
      .catch((err) => {
        snackbarErr('Error creating batch');
        console.error('Error creating batch: ', err);
      })
      .finally(() => setCreating(false));
  };

  return (
    <>
      <Button
        variant={isMoveVariant ? 'text' : 'secondary'}
        sx={{ color: isMoveVariant ? 'gray.500' : 'inherit' }}
        onClick={() => setOpen(true)}
        startIcon={isMoveVariant ? <AddCircleOutlineIcon /> : null}
        data-testid="create-batch-btn"
      >
        Create Batch
      </Button>
      <Modal
        open={open}
        setOpen={setOpen}
        title="Create Batch"
        onSubmit={onCreateBatch}
        onCancel={() => setOpen(false)}
        disableSubmit={creating}
        submitText={isMoveVariant ? 'Create and move' : 'Create'}
        disableCancel={creating}
      >
        {creating ? (
          <Loader />
        ) : (
          <Box sx={{ width: '100%' }}>
            <TextInput
              sx={{ width: '100%' }}
              label={'Batch name'}
              value={name}
              inputProps={{ ref: inputRef }}
              onChange={(e) => setName(e.target.value)}
              data-testid="create-batch-input"
            />
            <Text variant={'smReg'} sx={{ color: 'text.secondary' }}>
              A batch ID will auto-generate with this batch name
            </Text>
          </Box>
        )}
      </Modal>
    </>
  );
};

CreateBatchModal.propTypes = {
  variant: PropTypes.oneOf(['batchList', 'moveBatch']),
  submitMoveToBatch: PropTypes.func,
  onCreateComplete: PropTypes.func,
};

export default CreateBatchModal;
