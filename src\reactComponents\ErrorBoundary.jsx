import React from 'react';

import { useRouteError } from 'react-router';
import ImportCncLogo from '@/assets/icons/fps-logo-light.svg?react';

const ErrorBoundary = () => {
  //TODO - log this error ...somewhere?
  const error = useRouteError();
  console.error('Error:', error);

  const landingPage = `${window.location.origin}/v2/`;

  return (
    <div className="flex w-full items-center flex-col ">
      <header className="flex w-full h-16  bg-violet-800 ">
        <div className="flex w-full items-center justify-center md:justify-start md:pl-20">
          <ImportCncLogo />
        </div>
      </header>
      <div className="shadow-xl rounded-xl px-5 py-10">
        <section className="flex flex-col items-center justify-center text-center gap-12 md:gap-8 ">
          <span className="text-4xl">Something went wrong</span>
          <span className="text-2xl">
            We cannot process your request at this time.
          </span>

          <div className="flex flex-col items-center justify-center gap-6">
            <div className="text-2xl">Try refreshing your browser.</div>
            <div className="text-xl mt-3">
              If you continue experiencing problems, call the digital products
              support team.
            </div>
            <div className="text-2xl flex flex-col items-center gap-1">
              <a className="md:hidden" href="tel:8188607770">
                ************
              </a>
              <span className="hidden md:block">************</span>
              <span className="text-lg">select option 1</span>
            </div>
          </div>
        </section>
        <section className="text-2xl flex flex-col items-center justify-center gap-3 mt-4">
          <a
            href={landingPage}
            className="bg-violet-800 text-white rounded-lg px-4 py-2"
          >
            Back to Landing Page
          </a>
        </section>
      </div>
    </div>
  );
};

export default ErrorBoundary;
