<template>
  <Modal v-model="isOpen" show-overflow>
    <template #activator="{ open }">
      <Button size="sm" @click="open"> Invite Member </Button>
    </template>
    <div>
      <!-- HEADER -->
      <div class="flex justify-between items-center">
        <div class="text-lg font-medium text-gray-900 dark:text-gray-200">
          Invite Project Member
        </div>
      </div>
      <!-- BODY -->
      <div class="my-4 divide-y divide-gray-200 dark:divide-gray-800">
        <!-- USER DETAILS -->
        <div>
          <h2 class="text-zinc-700 dark:text-zinc-400 font-medium mb-2">
            User Details
          </h2>
          <div class="grid grid-cols-12 gap-x-4">
            <div class="col-span-12">
              <TextInput
                label="Email Address"
                required="true"
                v-model="projectMemberInvite.email"
              />
            </div>
          </div>
        </div>
        <!-- PROJECT MEMBER DETAILS -->
        <div>
          <h2 class="text-zinc-700 dark:text-zinc-400 font-medium my-2">
            Project Member Details
          </h2>
          <div class="grid grid-cols-12 gap-x-4">
            <div class="col-span-4">
              <TextInput
                label="Rate ($)"
                type="number"
                v-model="projectMemberInvite.rate"
              />
            </div>
            <div class="col-span-4">
              <Dropdown
                label="Rate Type"
                v-model="projectMemberInvite.rateTypeId"
                :menu-items="rateTypes"
                value-name="id"
                display-name="name"
              />
            </div>
            <div class="col-span-4">
              <DatePicker
                label="Start Date"
                v-model="projectMemberInvite.startDate"
              />
            </div>
            <div class="col-span-6">
              <TextInput
                class="grow"
                label="Hire Location City"
                v-model="projectMemberInvite.hireLocationCity"
              />
            </div>
            <div class="col-span-6">
              <Dropdown
                label="Hire Location State"
                class="basis-1/2"
                v-model="projectMemberInvite.hireLocationStateId"
                :menu-items="states"
                type="state"
                display-name="name"
                value-name="id"
              />
            </div>
            <div class="col-span-12 mb-4">
              <Combobox
                v-model="projectMemberInvite.occupationId"
                class="w-full"
                label="Occupation"
                :items="occupations"
                :search="occupationSearch"
                @update:search="(x) => (occupationSearch = x)"
                display-name="name"
                value-name="id"
              >
                <template #item="{ value: item }">
                  {{ item?.name }}
                </template>
              </Combobox>
            </div>
            <div class="col-span-12">
              <Dropdown
                label="Department"
                v-model="projectMemberInvite.departmentId"
                :menu-items="departments"
                display-name="name"
                :required="true"
                value-name="id"
              />
            </div>
          </div>
        </div>
      </div>
      <!-- BUTTONS -->
      <div class="flex justify-end space-x-2">
        <Button color="error" size="sm" :loading="loading" @click="close"
          >Cancel</Button
        >
        <Button color="primary" size="sm" :loading="loading" @click="invite"
          >Invite</Button
        >
      </div>
    </div>
  </Modal>
</template>

<script setup lang="ts">
import Button from '@/components/library/Button.vue';
import Combobox from '@/components/library/Combobox.vue';
import DatePicker from '@/components/library/DatePicker.vue';
import Dropdown from '@/components/library/Dropdown.vue';
import Modal from '@/components/library/Modal.vue';
import TextInput from '@/components/library/TextInput.vue';
import { getStates } from '@/services/address';
// import { listOccupations } from '@/services/occupations'
import { listRateTypes } from '@/services/rate-types';
import { inviteUserToProject } from '@/services/users';
import SnackbarStore from '@/reactComponents/stores/snackbar';

import type Occupation from '@/types/Occupation';
import type Project from '@/types/Project';
import type ProjectMemberInvite from '@/types/ProjectMemberInvite';
import type { RateType } from '@/types/RateType';
import type State from '@/types/State';
import axios from 'axios';
import { DateTime } from 'luxon';
import { computed, onMounted, ref, watch, type Ref } from 'vue';

const props = defineProps<{
  project: Project;
}>();

const isOpen: Ref<boolean> = ref(false);
const projectMemberInvite: Ref<ProjectMemberInvite> = ref({
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  projectHash: props.project.hashId as string,
  rateTypeId: null,
  rate: null,
  occupationId: null,
  hireLocationCity: '',
  hireLocationStateId: null,
  departmentId: null,
  startDate: null,
});
const loading: Ref<boolean> = ref(false);

const occupations: Ref<Occupation[]> = ref([]);
const occupationSearch: Ref<string> = ref('');
const rateTypes: Ref<RateType[]> = ref([]);
const states: Ref<State[]> = ref([]);

const departments = computed(() => {
  return props.project.departments.map(({ id, type }) => ({
    id,
    name: type.name,
  }));
});

const getRateTypes = async (): Promise<void> => {
  const { data } = await listRateTypes();
  rateTypes.value = data;
};

const getStatesList = async (): Promise<void> => {
  const { data } = await getStates();
  states.value = data.data;
};

const getOccupations = async (): Promise<void> => {
  const { data } = { data: [] }; // await listOccupations(occupationSearch.value)
  occupations.value = data;
};

const clear = () => {
  projectMemberInvite.value = {
    firstName: '',
    lastName: '',
    email: '',
    projectHash: props.project.hashId!,
    rateTypeId: null,
    rate: null,
    occupationId: null,
    hireLocationCity: '',
    hireLocationStateId: null,
    departmentId: null,
    startDate: DateTime.now(),
  };
};

const close = () => {
  clear();
  isOpen.value = false;
};

watch(occupationSearch, async () => {
  await getOccupations();
});

onMounted(() => {
  getRateTypes();
  getStatesList();
});

const invite = async () => {
  loading.value = true;
  try {
    await inviteUserToProject(projectMemberInvite.value);
    SnackbarStore.triggerSnackbar('Project member invited', 2500, 'success');
    loading.value = false;
    close();
  } catch (err) {
    if (axios.isAxiosError(err)) {
      const msg = err.response?.data?.message
        ? err.response.data.message
        : err.response?.data?.['errors']?.[0]?.message;
      SnackbarStore.triggerSnackbar(msg, 2500, 'error');
    } else {
      SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
    }
    loading.value = false;
  }
};
</script>
