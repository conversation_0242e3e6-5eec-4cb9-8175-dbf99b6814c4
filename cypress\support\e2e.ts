// ***********************************************************
// This example support/e2e.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands';
import 'cypress-mochawesome-reporter/register';

Cypress.on('log:added', (options, log) => {
  if (['xhr', 'request'].includes(log.name)) {
    log.set('displayName', false);
    log.set('consoleProps', () => ({}));
  }
});

Cypress.on('uncaught:exception', (err) => {
  console.error('Uncaught error:', err);
  return false; // Ignore uncaught errors
});

declare global {
  namespace Cypress {
    interface Chainable {
      loginWithSession(
        phone: string,
        otc: string,
        baseURL: string,
        userType: 'supervisor' | 'crew',
      ): Cypress.Chainable;
      loginWithoutSession(
        phone: string,
        otc: string,
        baseURL: string,
      ): Cypress.Chainable;
      pickDropdownOption(
        dropdown: string,
        option: string,
        search?: boolean,
      ): Cypress.Chainable;
      pickSearchOption(searchInput: string, option: string): Cypress.Chainable;
      selectDay(datePicker: string, date: string): Cypress.Chainable;
      selectTime(timePicker: string, time: string): Cypress.Chainable;
      signatureApproval(): Cypress.Chainable;
    }
  }
}
