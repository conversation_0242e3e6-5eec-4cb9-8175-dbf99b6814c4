{"executionRoleArn": "arn:aws:iam::576349523908:role/ecs_task_execution_role", "containerDefinitions": [{"name": "ts-vuejs-webapp", "portMappings": [{"hostPort": 0, "protocol": "tcp", "containerPort": 80}], "environment": [], "secrets": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/ts-vuejs-webapp", "awslogs-region": "us-west-1", "awslogs-stream-prefix": "ts-vuejs-webapp-log-stream"}}}], "cpu": "256", "memory": "512", "family": "ts-vuejs-webapp"}