import React from 'react';

import { Box, Button } from '@/reactComponents/library';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';

import DefaultDaysStore from './store';

import DaysCalendarModal from './DaysCalendarModal';

const AddDays = () => {
  const [showModal, setShowModal] = React.useState(false);

  return (
    <Box sx={{ display: 'flex', width: '100%' }}>
      <Button
        onClick={() => setShowModal(true)}
        variant="text"
        startIcon={<AddCircleOutlineIcon />}
      >
        Add Days
      </Button>
      <DaysCalendarModal
        open={showModal}
        setOpen={setShowModal}
        onSubmit={DefaultDaysStore.addDays}
      />
    </Box>
  );
};

export default AddDays;
