import { useImperativeHandle, useRef, forwardRef } from 'react';
import { applyPureVueInReact } from 'veaury';
import ProjectTimecardKitRentalFormViewVue from '../../../views/projects/ProjectTimecardKitRentalFormView.vue';
import { useAuth, useReactRouter } from '../../AppHooks';
import { useOutletContext } from 'react-router';
const ReactProjectTimecardKitRentalFormView = applyPureVueInReact(
  ProjectTimecardKitRentalFormViewVue,
);

const ProjectTimecardKitRentalFormView = forwardRef(() => {
  useAuth();
  const { navigate, route } = useReactRouter();
  const context = useOutletContext();
  const vueRef = useRef();

  useImperativeHandle(context.ref, () => ({
    vueRef: vueRef.current.__veauryVueRef__,
  }));

  return (
    <ReactProjectTimecardKitRentalFormView
      ref={vueRef}
      project={context.project}
      editDisabled={context.editDisabled}
      componentLoaded={context.componentLoaded}
      route={route}
      navigate={navigate}
    />
  );
});

export default ProjectTimecardKitRentalFormView;
