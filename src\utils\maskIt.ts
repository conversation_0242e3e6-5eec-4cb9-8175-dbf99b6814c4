type MaskDefinition = {
  pattern?: RegExp;
  transform?: (v: string) => string;
  escape?: boolean;
};

const tokens: { [k: string]: MaskDefinition } = {
  // numbers
  '#': { pattern: /\d/ },
  // alphanumerics
  X: { pattern: /[0-9a-zA-Z]/ },
  // single letters
  S: { pattern: /[a-zA-Z]/ },
  // single letters, upper case
  A: { pattern: /[a-zA-Z]/, transform: (v: string) => v.toLocaleUpperCase() },
  // single letters, lower case
  a: { pattern: /[a-zA-Z]/, transform: (v: string) => v.toLocaleLowerCase() },
  // anything
  '!': { escape: true },
};

// Copied from:
// https://github.com/vuejs-tips/vue-the-mask/blob/master/src/maskit.js
const maskIt = (value: string, mask: string, masked: boolean = true) => {
  value = value || '';
  mask = mask || '';

  let iMask = 0;
  let iValue = 0;
  let output = '';

  while (iMask < mask.length && iValue < value.length) {
    let cMask = mask[iMask];
    const masker = tokens[cMask];
    const cValue = value[iValue];
    if (masker && !masker.escape) {
      if (masker?.pattern?.test(cValue)) {
        output += masker.transform ? masker.transform(cValue) : cValue;
        iMask++;
      }
      iValue++;
    } else {
      if (masker && masker.escape) {
        iMask++; // take the next mask char and treat it as char
        cMask = mask[iMask];
      }
      if (masked) output += cMask;
      if (cValue === cMask) iValue++; // user typed the same char
      iMask++;
    }
  }

  // fix mask that ends with a char: (#)
  let restOutput = '';
  while (iMask < mask.length && masked) {
    const cMask = mask[iMask];
    if (tokens[cMask]) {
      restOutput = '';
      break;
    }
    restOutput += cMask;
    iMask++;
  }

  return output + restOutput;
};

export default maskIt;
