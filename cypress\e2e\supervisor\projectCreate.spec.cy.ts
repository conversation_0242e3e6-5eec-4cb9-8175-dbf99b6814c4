import { faker } from '@faker-js/faker';
import ProjectPageObject from '../../pageObjects/project/projectForm';
import ProjectListPageObject from '../../pageObjects/project/projectList';
import ProjectViewPageObject from '../../pageObjects/project/projectView';

describe('User Supervisor _ Project Update', () => {
  const projectPO = new ProjectPageObject();
  const projectListPO = new ProjectListPageObject();
  const projectViewPO = new ProjectViewPageObject();

  it('Verify Supervisor is able to create a project ', () => {
    // ----------- Arrange: Generate project data -----------
    cy.log('Arrange: Generate project data');
    const projectName = `TEST-${faker.company.name()}`;
    const projectNumber = `${faker.number.int({ min: 100, max: 9999 })}`;
    const description = faker.commerce.productDescription();
    const duration = 'Ten Minutes';
    const mileageAicpNumber = `${faker.number.int({ min: 100, max: 9999 })}`;
    const startsOn = faker.date.soon({ days: 7 });
    const endsOn = faker.date.soon({ days: 7, refDate: startsOn });
    const company = 'TEST COMMERCIALS - TEST01';
    const address = '2300 Empire Avenue';
    const projectType = 'COMMERCIAL';
    const city = 'Los Angeles';
    const department = 'Art';

    // ----------- Arrange: Intercept project creation API -----------
    cy.log('Arrange: Intercept project creation API');
    cy.intercept('POST', `${Cypress.env('BASE_URL')}api/core/projects`).as(
      'createProject',
    );

    // ----------- Arrange: Login as Supervisor -----------
    cy.log('Arrange: Login as Supervisor');
    cy.loginWithSession(
      Cypress.env('PHONE_NUMBER'),
      Cypress.env('OTC_CODE'),
      Cypress.env('BASE_URL'),
      'supervisor',
    );

    // ----------- Arrange: Visit Projects dashboard -----------
    cy.log('Arrange: Visit Projects dashboard');
    cy.visit(Cypress.env('BASE_URL'));

    // ----------- Action: Go to Projects section -----------
    cy.log('Action: Go to Projects section');
    cy.get('[data-testid="card-Projects"]').click(); //move to a PO

    // ----------- Action: Click create project button -----------
    cy.log('Action: Click create project button');
    projectListPO.clickCreateProjectButton();

    // ----------- Action: Fill out project form -----------
    cy.log('Action: Fill out project form');
    projectPO.fillOutProjectForm(
      projectName,
      projectNumber,
      description,
      duration,
      mileageAicpNumber,
      startsOn,
      endsOn,
      company,
      address,
      projectType,
      city,
      department,
    );

    // ----------- Assert: Validate project was created (API) -----------
    cy.log('Assert: Validate project was created (API)');
    cy.wait('@createProject').then((interception) => {
      const projectId = interception.response?.body.id;

      // ----------- Assert: Validate project appears in the list -----------
      cy.log('Assert: Validate project appears in the list');
      projectListPO.validateProjectOnList(
        projectId,
        projectName,
        projectNumber,
        endsOn,
        company,
        projectType,
      );

      // ----------- Action: Go to project details -----------
      cy.log('Action: Go to project details');
      projectListPO.goToProjectDetails(projectId);

      // ----------- Assert: Validate project details in view -----------
      cy.log('Assert: Validate project details in view');
      projectViewPO.validateProjectCreated(
        projectName,
        projectNumber,
        endsOn,
        startsOn,
        company,
      );
    });
  });
});
