import React from 'react';
import { Box, Skeleton } from '@/reactComponents/library';

const LoadingSkeleton = () => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        width: '100%',
        height: '100vh',
      }}
    >
      <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
        <Skeleton variant="circular" width={70} height={70} />
        <Box>
          <Skeleton variant="text" width={200} height={40} />
          <Skeleton variant="text" width={500} height={40} />
        </Box>
      </Box>
      <Box
        sx={{
          display: 'flex',
          gap: 2,
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Skeleton variant="rounded" width={300} height={40} />
            <Skeleton variant="rounded" width={100} height={40} />
          </Box>
          <Skeleton variant="text" width={200} height={20} />
        </Box>
        <Skeleton variant="text" width={250} height={40} />
      </Box>
      <Skeleton variant="rounded" width={'100%'} height={200} />
      <Skeleton variant="rounded" width={'100%'} height={600} />
      <Skeleton variant="rounded" width={'100%'} height={250} />
    </Box>
  );
};

export default LoadingSkeleton;
