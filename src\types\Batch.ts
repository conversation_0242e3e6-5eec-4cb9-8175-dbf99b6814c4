import type { DateTime } from 'luxon';

export default interface Batch {
  createdAt: DateTime;
  id: number;
  name: string;
  projectId: number;
  statusId: number;
  status: BatchStatus;
  capsPayId: number | null;
  submittedAt: DateTime | null;
  updatedAt: DateTime;
}

export interface BatchStatus {
  id: number;
  name: string;
  key: string;
  description: string;
  created_at: DateTime;
  updated_at: DateTime;
}

// Should sync with BE: app\Enums\BatchStatuses.ts
export enum BatchStatusEnum {
  OPEN = 3,
  PENDING_ADMIN_APPROVAL = 4,
  SUBMITTED_TO_PAYROLL = 5,
  REOPENED = 6,
  READY_FOR_CLIENT_REVIEW = 7,
  RESUBMITTED = 8,
  APPROVED = 9,
  PAID = 10,
  UNCLAIMED = 11,
}

export enum BatchStatusCapsPayEnum {
  Open = 'Open',
  Completed = 'Completed',
  Submitted = 'Submitted',
  Reopen = 'Reopen',
  Approved = 'Approved',
  Resubmitted = 'Resubmitted',
  Invoiced = 'Invoiced',
}
