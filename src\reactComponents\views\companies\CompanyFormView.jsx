import { applyPureVueInReact } from 'veaury';
import CompanyFormViewVue from '../../../views/companies/CompanyFormView.vue';
import { useAuth, useReactRouter } from '../../AppHooks';
const ReactCompanyFormView = applyPureVueInReact(CompanyFormViewVue);

const CompanyFormView = () => {
  useAuth();
  const { navigate, route } = useReactRouter();
  const editMode = route?.match?.handle?.editMode;
  return (
    <ReactCompanyFormView
      route={route}
      navigate={navigate}
      editMode={editMode}
    />
  );
};

export default CompanyFormView;
