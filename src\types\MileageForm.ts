import type { DateTime } from 'luxon';
import type ProjectShootLocation from './ProjectShootLocation';

export interface MileageForm {
  id: number;
  totalMileage: number;
  date: DateTime;
  workLocation: ProjectShootLocation;
  timecardId: number;
  documentTemplateId: number;
  documentId: string;
  createdAt: DateTime;
  updatedAt: DateTime;
  lineNumber: string;
  rate?: number;
  totalAmount?: number;
}
