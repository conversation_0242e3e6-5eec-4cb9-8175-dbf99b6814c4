<template>
  <div>
    <label
      v-if="label"
      for="colorPicker"
      class="block text-sm font-medium text-gray-700"
      >{{ label }}</label
    >
    <input
      :value="modelValue"
      type="color"
      id="colorPicker"
      class="w-full min-h-12"
      :disabled="disabled"
      @input.stop="update"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

export default defineComponent({
  emits: ['update:modelValue'],
  props: ['modelValue', 'label', 'disabled'],
  name: 'ColorPicker',
  setup() {
    const hexColor = ref('#ff0000');
    return { hexColor };
  },
  methods: {
    update(event: any) {
      const value: string = event.target!.value!;
      this.$emit('update:modelValue', value);
    },
  },
});
</script>
