<template>
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pt-40">
    <div class="py-2 mb-6">
      <CompanyMemberHeader :member="member" class="mb-2" />
      <SecondaryTab
        v-for="tab in tabs"
        :key="tab.label"
        :tab="tab"
        :currentTab="currentTab"
        @tabClick="goTo"
      />
    </div>
    <component
      :is="activeComponent"
      :member="member"
      :company-id="company.id"
      @refresh="load"
    />
  </div>
</template>

<script lang="ts">
// todo: in the future, rewrite this componenet to use react router with Outlet
// components under tabs should be rendered under Outlet
import CompanyMemberDetailsPersonalInfo from '@/components/CompanyMemberDetailsPersonalInfo.vue';
import CompanyMemberHeader from '@/components/CompanyMemberHeader.vue';
import { getProductionCompanyMember } from '@/services/production-company';
import type Company from '@/types/Company';
import type ProductionCompanyMember from '@/types/ProductionCompanyMember';
import { defineComponent, provide, type PropType } from 'vue';
import SecondaryTab from '@/components/library/SecondaryTab.vue';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';

interface Tab {
  label: string;
  key: string;
  routeName: string;
  component: any;
  routePath: string;
}

export default defineComponent({
  name: 'CompanyMemberDetailsView',

  components: { CompanyMemberHeader, SecondaryTab },
  props: {
    company: {
      type: Object as PropType<Company>,
      required: true,
    },
    route: {
      type: Object as PropType<ParsedRoute>,
      required: true,
    },
    navigate: {
      type: Function,
      required: true,
    },
  },
  setup(props, ctx) {
    provide('navigate', props.navigate);
  },
  data() {
    return {
      member: {} as ProductionCompanyMember,
    };
  },
  computed: {
    tabs(): Tab[] {
      return [
        {
          label: 'Personal Info',
          key: 'personalInfo',
          routeName: 'company-member-details-personal-info',
          routePath: '',
          component: CompanyMemberDetailsPersonalInfo,
        },
      ];
    },
    activeComponent() {
      return this.currentTab?.component;
    },
    currentTab() {
      const currentPath = this.route.location.pathname;
      const lastRoutePostion = 5;
      const routeSplits = currentPath.split('/');
      const currentRoute = routeSplits[lastRoutePostion] || '';
      return this.tabs.find((tab) => {
        return tab.routePath === currentRoute;
      });
    },
  },
  async created(): Promise<void> {
    await this.load();
  },
  methods: {
    async load(): Promise<void> {
      await this.getProductionCompanyMember();
    },
    async getProductionCompanyMember(): Promise<void> {
      const { data } = await getProductionCompanyMember(
        this.company.id.toString(),
        this.route.params.userId as string,
      );
      this.member = data;
    },
    goTo(routeName: Tab): void {
      this.navigate({
        pathname: `/companies/${this.company.id}/members/${
          this.member?.user?.id
        }/${routeName.routePath || ''}`,
      });
    },
  },
});
</script>
