<template>
  <header
    class="bg-gray-50 dark:bg-gray-700 z-10 w-full pt-6 shadow"
    :class="{ 'pb-2': !isProjectAdminRoute }"
  >
    <div
      class="px-4 sm:px-6 lg:px-8 flex items-center justify-between"
      :class="{ 'mx-auto max-w-7xl': !fullWidthHeader }"
    >
      <div class="min-w-0 flex-1">
        <ProjectHeaderBreadcrumb
          :project="project"
          class="sm:block mb-3"
          :class="{ hidden: isCollapsed }"
        />

        <div class="items-left">
          <div class="page-title-bar bg-gray-200 w-2 h-16 mr-3"></div>
          <h1
            class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-200 sm:truncate sm:text-3xl sm:tracking-tight"
            :class="{
              'mt-2': !isCollapsed,
              'mt-0': isCollapsed,
            }"
          >
            {{ project.name }}
          </h1>
          <ProjectHeaderInfoItems
            :project="project"
            class="sm:flex"
            :class="{ hidden: isCollapsed }"
          />
        </div>
      </div>
      <div class="flex justify-end">
        <ProjectHeaderAdminActions
          v-if="isProjectAdminRoute"
          :project="project"
        />
      </div>
    </div>
    <div
      class="flex justify-center pb-3 cursor-pointer sm:hidden"
      :class="{ 'pt-3': !isCollapsed, 'pt-0': isCollapsed }"
      @click="toggleCollapse"
    >
      <ChevronUpIcon v-if="!isCollapsed" class="w-5 h-5" />
      <ChevronDownIcon v-else class="w-5 h-5" />
    </div>
    <ProjectAdminTabs
      v-if="isProjectAdminRoute"
      :fullWidthHeader="fullWidthHeader"
    />
    <div v-else class="y-5" />
  </header>
</template>

<script lang="ts">
// todo: Projectheader.jsx ready, this file can be deleted
import ProjectAdminTabs from '@/components/ProjectAdminTabs.vue';
import ProjectHeaderAdminActions from '@/components/ProjectHeaderAdminActions.vue';
import ProjectHeaderBreadcrumb from '@/components/ProjectHeaderBreadcrumb.vue';
import ProjectHeaderInfoItems from '@/components/ProjectHeaderInfoItems.vue';
import type Project from '@/types/Project';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/vue/20/solid';
import { defineComponent, provide, ref, watchEffect, type PropType } from 'vue';

import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';

export default defineComponent({
  components: {
    ProjectHeaderBreadcrumb,
    ProjectHeaderInfoItems,
    ProjectHeaderAdminActions,
    ChevronDownIcon,
    ChevronUpIcon,
    ProjectAdminTabs,
  },
  props: {
    project: {
      type: Object as () => Project,
      required: true,
    },
    isAdmin: {
      type: Boolean as PropType<boolean>,
      required: true,
    },
    route: {
      // type: Object as PropType<any>,
      type: Object as PropType<ParsedRoute>,
      required: false,
    },
    navigate: {
      type: Function,
      required: false,
    },
    matches: {
      type: Object as PropType<any>,
      required: false,
    },
    fullWidthHeader: {
      type: Boolean as PropType<boolean>,
      required: false,
    },
  },
  setup(props) {
    const matches = ref(props.matches);
    const route = ref(props.route);
    const project = ref(props.project);
    const isAdmin = ref(props.isAdmin);
    watchEffect(() => {
      matches.value = props.matches;
      route.value = props.route;
      project.value = props.project;
      isAdmin.value = props.isAdmin;
    });

    provide('project', project);
    provide('isAdmin', isAdmin);
    provide('route', route);
    provide('matches', matches);
    provide('navigate', props.navigate);
  },
  data() {
    return {
      isLoading: true,
      isCollapsed: true,
    };
  },
  computed: {
    isProjectAdminRoute(): boolean {
      return !!this?.route?.match?.handle?.isProjectAdmin;
    },
  },
  methods: {
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed;
    },
  },
  async mounted(): Promise<void> {
    this.isLoading = false;
  },
});
</script>
<style>
.page-title-bar {
  float: left;
  border-radius: 2px;
}
</style>
