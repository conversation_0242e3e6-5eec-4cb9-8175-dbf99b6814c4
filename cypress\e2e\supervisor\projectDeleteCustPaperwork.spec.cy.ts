/// <reference types="cypress" />

import {
  fetchProjectIdentifiersByName,
  fetchProjectMemberByName,
} from '../../support/apiHelpers';

import {
  interceptCreatePaperWork,
  interceptDeletePaperWork,
} from '../../support/apiTimecardInterceptors';

import Project from '../../pageObjects/project/projectForm';
import CustomPaperWork from '../../pageObjects/project/customPaperWork';
import 'cypress-file-upload';
import { faker } from '@faker-js/faker';

const paperworkAudience = 'All';
const paperworkType = 'Non Union';
const paperworkLoan = 'Loan Out and Non Loan Out';

describe('User Supervisor - Project Custom Start Paperwork Delete', () => {
  const projectForm = new Project();
  const customPaperWork = new CustomPaperWork();
  const projectName = Cypress.env('projectName');

  beforeEach((): void => {
    // ----------- Arrange: Login as Supervisor -----------
    cy.log('Arrange: Login as Supervisor');
    cy.loginWithSession(
      Cypress.env('PHONE_NUMBER'),
      Cypress.env('OTC_CODE'),
      Cypress.env('BASE_URL'),
      'supervisor',
    );

    // ----------- Arrange: Get project and member details by API -----------
    cy.log('Arrange: Get project and member details by API');
    cy.then(() => {
      // Step 1: Get the project Id by project Name
      return fetchProjectIdentifiersByName(projectName).then((project) => {
        if (!project || !project.id) {
          throw new Error('Project not found or invalid Project ID');
        }
        Cypress.env('projectId', project.id);
        cy.log(`Project ID set: ${project.id}`);
        Cypress.env('projectHashId', project.hashId);

        // Step 2: Get the member by first/last name
        return fetchProjectMemberByName('PA-Test', 'PA-test').then((member) => {
          if (!member || !member.id) {
            throw new Error('Project member not found or invalid Member ID');
          }
          Cypress.env('projectMemberId', member.id);
          cy.log(`Project Member ID set: ${member.id}`);
        });
      });
    });
  });

  it('Verify Supervisor is able to delete Custom Start Paperwork at Project level', () => {
    const filenameTemplate = `Paperwork-${faker.word.sample()}`;

    // ----------- Arrange: Visit Project Time page -----------
    cy.log('Arrange: Visit Project Time page');
    cy.visit(
      `${Cypress.env('BASE_URL')}projects/${Cypress.env(
        'projectHashId',
      )}/admin/time`,
    );

    // ----------- Arrange: Intercept paperwork creation and deletion API -----------
    cy.log('Arrange: Intercept paperwork creation and deletion API');
    interceptCreatePaperWork();
    interceptDeletePaperWork();

    // ----------- Action: Go to Custom Paperwork section -----------
    cy.log('Action: Go to Custom Paperwork section');
    projectForm.goToCustomPaperworkProject();

    // ----------- Action: Create new paperwork -----------
    cy.log('Action: Create new paperwork');
    customPaperWork.createPaperWork();

    // ----------- Action: Upload paperwork file -----------
    cy.log('Action: Upload paperwork file');
    customPaperWork.uploadPaperWork();

    // ----------- Action: Fill out paperwork information -----------
    cy.log('Action: Fill out paperwork information');
    customPaperWork.fillPaperworkInformation(
      filenameTemplate,
      paperworkAudience,
      paperworkType,
      paperworkLoan,
    );

    // ----------- Action: Fill out paperwork schema -----------
    cy.log('Action: Fill out paperwork schema');
    customPaperWork.fillPaperworkSchema();

    // ----------- Action: Save paperwork schema -----------
    cy.log('Action: Save paperwork schema');
    customPaperWork.savePaperworkSchema();

    // ----------- Assert: Validate paperwork was created -----------
    cy.log('Assert: Validate paperwork was created');
    cy.wait('@createdPaperWork').then((interception): void => {
      expect(interception.response?.statusCode).to.equal(200);
      Cypress.env('documentId', interception.response?.body.id);

      // ----------- Action: Delete the created paperwork -----------
      cy.log('Action: Delete the created paperwork');
      customPaperWork.deletePaperWork(filenameTemplate);

      // ----------- Assert: Validate paperwork was deleted (API and UI) -----------
      cy.log('Assert: Validate paperwork was deleted (API and UI)');
      cy.wait('@deletedPaperWork').then((interception): void => {
        // API Assert
        expect(interception.response?.statusCode).to.equal(200);

        // UI Assert
        cy.get(`[data-testid="custom-doc-${filenameTemplate}"]`).should(
          'not.exist',
        );
      });
    });
  });
});
