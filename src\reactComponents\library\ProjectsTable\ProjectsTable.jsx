import React from 'react';
import { PillBadge } from '@/reactComponents/library';
import { listProjects } from '@/services/project';
import { convertFiltersToQuery } from '@/utils/filter';
import { Link, useSearchParams } from 'react-router-dom';
import {
  BriefcaseIcon,
  CalendarIcon,
  FilmIcon,
} from '@heroicons/react/24/outline';
import { TableFilters } from './TableFilters';
import { FilterOperator, FilterUIType } from '@/types/Filter';
import Pagination from './Pagination';

const defaultFilters = [
  {
    id: 'project_name',
    field: 'name',
    label: 'Name',
    value: '',
    type: FilterUIType.Text,
    operator: FilterOperator.ILike,
    active: false,
  },
  {
    id: 'production_company_name',
    field: 'productionCompany.name',
    label: 'Production Company',
    value: '',
    type: FilterUIType.Text,
    operator: FilterOperator.ILike,
    active: false,
  },
];

const defaultPagination = {
  page: 1,
};

const ProjectsTable = () => {
  const [searchParams] = useSearchParams();
  const filterParam = searchParams.get('filters');

  const [projects, setProjects] = React.useState([]);

  const [pagination, setPagination] = React.useState(defaultPagination);

  const [filters, setFilters] = React.useState(defaultFilters);

  const activeFilters = filters.filter((filter) => filter.active);

  const filtersString = convertFiltersToQuery(activeFilters);

  React.useEffect(() => {
    if (!filterParam) return;

    const filtersArray = filterParam.split(';');

    const values = {};

    filtersArray.forEach((filter) => {
      const [key, value] = filter.split('=');
      values[key] = value;
    });

    setFilters((prev) => {
      const newFilters = prev.map((filter) => {
        const { field, id } = filter;
        const value = values[field] || values[id];

        if (value) {
          return {
            ...filter,
            value: decodeURIComponent(value),
            active: true,
          };
        }

        return filter;
      });

      //Validate if the filters are the same as before
      // If they are the same, return the previous state to avoid re-rendering
      if (JSON.stringify(prev) === JSON.stringify(newFilters)) {
        return prev;
      }

      return newFilters;
    });
  }, [filterParam]);

  React.useEffect(() => {
    const controller = new AbortController();
    const signal = controller.signal;

    const getListProjects = async () => {
      try {
        const response = await listProjects(
          signal,
          {
            page: pagination.page,
          },
          filtersString,
        );

        const { meta, data } = response.data;

        setProjects(data);

        setPagination((prev) => ({
          ...prev,
          page: meta.current_page,
          limit: meta.per_page,
          total: meta.total,
        }));
      } catch (error) {
        if (error.code !== 'ERR_CANCELED') {
          console.error('Error fetching projects:', error);
        }
      }
    };

    getListProjects();

    return () => {
      controller.abort();
    };
  }, [pagination.page, filtersString]);

  return (
    <div>
      <TableFilters filters={filters} onFiltersChange={setFilters} />
      <div className="bg-white dark:bg-gray-900 shadow sm:rounded-md">
        <ul className="divide-y divide-gray-200 dark:divide-gray-500 p-0">
          {projects?.map((project, projectIndex) => (
            <li
              key={project.id}
              data-testid="projects-list"
              className="list-none"
            >
              <Link
                className={`block no-underline hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer ${
                  projectIndex === 0 ? 'rounded-t-md' : ''
                } ${
                  projectIndex === projects.length - 1 ? 'rounded-b-md' : ''
                }`}
                to={{
                  pathname: `/projects/${project.hashId}`,
                }}
              >
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <p
                      className="truncate text-sm font-medium text-indigo-600 dark:text-indigo-300"
                      data-testid={`project-name-${project.id}`}
                    >
                      {project.name}
                    </p>
                    <div
                      className="ml-2 flex flex-shrink-0"
                      data-testid={`project-number-${project.id}`}
                    >
                      <PillBadge variant="success">
                        <span className="font-semibold">{project?.number}</span>
                      </PillBadge>
                    </div>
                  </div>
                  <div className="mt-2 sm:flex sm:justify-between">
                    <div className="flex justify-start space-x-2">
                      <div className="sm:flex">
                        <p
                          className="flex gap-x-1.5 items-center text-sm text-gray-500 dark:text-gray-400"
                          data-testid={`project-company-${project.id}`}
                        >
                          <BriefcaseIcon className="w-4 h-4" />
                          {project?.productionCompany?.name}
                        </p>
                      </div>
                      <div className="sm:flex">
                        <p
                          className="flex items-center gap-x-1.5 text-sm text-gray-500 dark:text-gray-400"
                          data-testid={`project-type-${project.id}`}
                        >
                          <FilmIcon className="w-4 h-4" />
                          {project?.type?.name}
                        </p>
                      </div>
                    </div>
                    <div
                      className="mt-2 flex gap-x-1.5 items-center text-sm text-gray-500 dark:text-gray-400 sm:mt-0"
                      data-testid={`project-endsOn-${project.id}`}
                    >
                      <CalendarIcon className="stroke stroke-gray-400 dark:stroke-gray-500 w-4 h-4" />
                      <p>
                        Ends On{' '}
                        <time dateTime={project.endsAt.toISOTime()}>
                          {project.endsAt.toFormat('MM/dd')}
                        </time>
                      </p>
                    </div>
                  </div>
                </div>
              </Link>
            </li>
          ))}
        </ul>
      </div>
      {pagination.total > pagination.limit && (
        <Pagination pagination={pagination} onChange={setPagination} />
      )}
    </div>
  );
};

export default ProjectsTable;
