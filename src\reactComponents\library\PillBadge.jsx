import { Box } from '@/reactComponents/library';
import { styled } from '@mui/material';
import PropTypes from 'prop-types';

const PillBox = styled(Box, { label: 'PillBadge' })((props) => {
  return {
    borderRadius: 16,
    whiteSpace: 'nowrap',
    display: 'inline-block',
    boxSizing: 'border-box',
    padding: '4px 7px',
    fontFamily: 'inherit',
    fontSize: 12,
    fontWeight: 400,
    lineHeight: '16px',
    textDecoration: 'none',
  };
});

const styles = {
  success: {
    backgroundColor: 'success.200',
    color: 'success.700',
    border: `1px solid`,
    borderColor: 'success.700',
  },
  warning: {
    backgroundColor: 'warning.200',
    color: 'warning.700',
    border: `1px solid`,
    borderColor: 'warning.700',
  },
  info: {
    backgroundColor: 'blueLight.200',
    color: 'blueLight.700',
    border: `1px solid`,
    borderColor: 'blueLight.700',
  },
  error: {
    backgroundColor: 'error.200',
    color: 'error.500',
    border: `1px solid`,
    borderColor: 'error.500',
  },
  gray: {
    backgroundColor: 'gray.200',
    color: 'gray.500',
    border: `1px solid`,
    borderColor: 'gray.500',
  },
  lightGray: {
    backgroundColor: 'gray.50',
    color: 'gray.700',
    fontWeight: 500,
    border: `1px solid`,
    borderColor: 'gray.200',
    '&:hover': {
      backgroundColor: 'gray.100',
      cursor: 'pointer',
    },
  },
  squared: {
    backgroundColor: 'background.paper',
    borderRadius: '4px',
    border: `1px solid`,
    borderColor: 'gray.200',
    padding: '3px 6px',
    color: 'black',
    fontWeight: 600,
  },
  tertiaryAttention: {
    backgroundColor: 'warning.600',
  },
  primaryInfo: {
    backgroundColor: '#004EEB',
  },
  tertiaryPositive: {
    backgroundColor: 'success.500',
  },
  primaryInteractive: {
    backgroundColor: 'gray.700',
  },
  infoBtn: {
    backgroundColor: 'blueLight.200',
    color: 'blueLight.700',
    border: `1px solid`,
    borderColor: 'blueLight.700',
    '&:hover': {
      backgroundColor: 'blue.200',
      cursor: 'pointer',
    },
    '&:active': {
      backgroundColor: 'blue.300',
    },
  },
  successIcon: {
    backgroundColor: 'success.50',
    color: 'success.700',
    border: `1px solid`,
    borderColor: 'success.200',
    display: 'inline-flex',
    alignItems: 'center',
    width: '1.5rem',
    height: '1.5rem',
    mr: '0.5rem',
    fontSize: '0.75rem',
    lineHeight: '1rem',
    padding: '3px',
  },
};

const PillBadge = (props) => {
  const { variant, squared, children, statusIcon, sx, onClick } = props;

  let innerSx = styles[variant];
  if (!innerSx) {
    innerSx = styles.gray;
  }
  if (squared) {
    innerSx = { ...innerSx, ...styles.squared };
  }
  const handleClick = (e) => {
    if (onClick) onClick(e);
  };
  return (
    <PillBox sx={{ ...innerSx, ...sx }} onClick={handleClick}>
      {statusIcon && (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box
            sx={{
              ...styles[variant],
              width: '6px',
              height: '6px',
              borderRadius: '60px',
              marginRight: '4px',
            }}
          />
          {children}
        </Box>
      )}
      {!statusIcon && children}
    </PillBox>
  );
};

PillBadge.propTypes = {
  variant: PropTypes.oneOf([
    'success',
    'warning',
    'info',
    'error',
    'gray',
    'lightGray',
    'infoBtn',
    'successIcon',
  ]),
  children: PropTypes.node.isRequired,
  sx: PropTypes.object,
  onClick: PropTypes.func,
  squared: PropTypes.bool,
  statusIcon: PropTypes.bool,
};

export default PillBadge;
