import React from 'react';
import { Outlet } from 'react-router';
import { useAuth, useReactRouter } from '../../AppHooks';
import { useDidMount } from '@/reactComponents/utils/customHooks';
import ProjectStore from '../../stores/project';
import ProjectHeader from './ProjectHeader';
const ProjectView = () => {
  useAuth();

  const { params, route, matches, navigate } = useReactRouter();
  const { hashId } = params;

  const [project, setProject] = React.useState({});
  const [isAdmin, setIsAdmin] = React.useState(false);
  const [doneLoading, setLoading] = React.useState(false);
  const fullWidthHeader = route?.match?.handle?.fullWidthHeader;

  const init = async () => {
    const projectData = await ProjectStore.fetchProject(hashId, true);
    setProject(projectData);
    if (!projectData) {
      setLoading(true);
      return;
    }

    const isAdminData = await ProjectStore.fetchIsAdmin(projectData?.id);
    setIsAdmin(isAdminData);
    setLoading(true);
  };

  useDidMount(() => {
    init();
  });

  return (
    <div className="flex flex-col h-screen pt-16">
      <ProjectHeader
        isAdmin={isAdmin}
        route={route}
        matches={matches}
        navigate={navigate}
        fullWidthHeader={fullWidthHeader}
      />
      <div className="w-full overflow-y-auto flex-grow bg-gray-100 dark:bg-gray-800 justify-center">
        {/* TODO update this to use ProjectStore instead of Outlet context */}
        {/* {Update: needs to test LOTS of children under Outlet to make sure ProjectStore actually works} */}
        {doneLoading && <Outlet context={{ isAdmin, project }} />}
      </div>
    </div>
  );
};

export default ProjectView;
