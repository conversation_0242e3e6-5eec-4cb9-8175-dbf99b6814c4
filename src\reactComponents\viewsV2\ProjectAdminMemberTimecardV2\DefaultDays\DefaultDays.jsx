import React from 'react';
import { observer } from 'mobx-react-lite';
import { useParams } from 'react-router';

import { Box, Button, Text, Tooltip } from '@/reactComponents/library';
import {
  RootBox,
  FullWidthPageBox,
  SubPageBox,
} from '@/reactComponents/library/styledComponents';
import UnsavedChangesModal from '@/reactComponents/viewsV2/sharedComponents/UnsavedChangesModal';
import { snackbarErr } from '@/reactComponents/library/Snackbar';

import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';

import { useWillUnmount } from '@/reactComponents/utils/customHooks';
import { makeSaveTooltip } from '@/reactComponents/viewsV2/ProjectAdminMemberTimecardV2/timecardUtils';

import ProjectStore from '@/reactComponents/stores/project';

import DefaultDaysStore from './store';

import DefaultDaysTable from './DefaultDaysTable';
import AddDays from './AddDays';
import { tabTitle } from '@/reactComponents/utils/tabTitle';

const DefaultDays = (props) => {
  const { projectMemberId } = useParams();
  const { id } = ProjectStore.project;

  const [showErrors, setShowErrors] = React.useState(false);

  React.useEffect(() => {
    DefaultDaysStore.init(id);
    ProjectStore.fetchShootLocations();
    window.document.title = `${tabTitle} - Default Days`;
  }, [id]);

  useWillUnmount(() => {
    DefaultDaysStore.unmount();
    window.document.title = tabTitle;
  });

  React.useEffect(() => {
    ProjectStore.fetchProjectMember(projectMemberId);
  }, [projectMemberId]);

  const infoTitle =
    'Default days are preset values for crew timecards. Crew members must select that they worked on a day for these presets to apply, but they can change the values if needed. Defaults do not affect timecards created before the presets are applied. The General crew call time applies to any timecard with an NDB for that day.';

  const tcErrors = DefaultDaysStore.validate;
  const isValid = tcErrors.isValid;
  const saving = DefaultDaysStore.saving;
  const unsavedChanges = DefaultDaysStore.unsavedChanges;

  const handleSave = () => {
    if (!isValid) {
      setShowErrors(true);
      snackbarErr('Please fix the errors before saving.');
    } else {
      DefaultDaysStore.save();
      setShowErrors(false);
    }
  };

  let saveTitle = undefined;
  if (!isValid) {
    saveTitle = (
      <Box>
        {makeSaveTooltip(tcErrors).map((str, i) => (
          // eslint-disable-next-line react/no-array-index-key
          <Box key={`${str}${i}`}>{str}</Box>
        ))}
      </Box>
    );
  }

  return (
    <RootBox>
      <FullWidthPageBox>
        <SubPageBox>
          <Box className="w-full flex gap-2 justify-between items-center">
            <Box className="flex items-center gap-x-1">
              <Text variant="disSemi">Default Days</Text>
              <Tooltip title={infoTitle} arrow placement="top">
                <InfoOutlinedIcon />
              </Tooltip>
            </Box>
            <Box className="flex items-center gap-x-2">
              {unsavedChanges && (
                <Box sx={{ color: 'warning.500' }}>
                  <ErrorOutlineIcon />
                  Unsaved Changes
                </Box>
              )}
              <Tooltip
                arrow
                onOpen={() => console.warn(tcErrors)}
                title={saveTitle}
              >
                <span>
                  <Button
                    onClick={handleSave}
                    disabled={
                      (!isValid && showErrors) || saving || !unsavedChanges
                    }
                    loading={saving}
                  >
                    Save
                  </Button>
                </span>
              </Tooltip>
            </Box>
          </Box>
          <Box sx={{ width: '100%', height: '100%' }}>
            <DefaultDaysTable showErrors={showErrors} />
          </Box>
          <AddDays />
        </SubPageBox>
      </FullWidthPageBox>
      <UnsavedChangesModal hasUnsavedChanges={unsavedChanges} />
    </RootBox>
  );
};

export default observer(DefaultDays);
