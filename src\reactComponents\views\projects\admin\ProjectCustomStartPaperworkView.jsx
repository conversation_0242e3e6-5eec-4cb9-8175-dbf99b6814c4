import { applyPureVueInReact } from 'veaury';
import ProjectCustomStartPaperworkViewVue from '../../../../views/projects/admin/ProjectCustomStartPaperworkView.vue';
import { useAuth, useReactRouter } from '../../../AppHooks';
import { useOutletContext } from 'react-router';

const ReactProjectCustomStartPaperworkView = applyPureVueInReact(
  ProjectCustomStartPaperworkViewVue,
);

const ProjectCustomStartPaperworkView = () => {
  useAuth();
  const { navigate } = useReactRouter();
  const context = useOutletContext();
  return (
    <ReactProjectCustomStartPaperworkView
      project={context.project}
      navigate={navigate}
    />
  );
};

export default ProjectCustomStartPaperworkView;
