import React from 'react';
import PropTypes from 'prop-types';

import { Dialog, DialogTitle, Box } from '@mui/material';
import {
  Button,
  IconButton,
  Text,
  Loader,
  db,
} from '@/reactComponents/library';
import CloseIcon from '@mui/icons-material/Close';
import { useIsMobile } from '../utils/customHooks';

const styles = {
  rightSide: {
    left: 'inherit',
  },
  dialogTitle: {
    display: 'flex',
    justifyContent: 'space-between',
    fontWeight: 'bold',
    alignItems: 'center',
  },
  content: {
    minWidth: '400px',
    minHeight: '100px',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: 3,
    px: 2,
    pb: 2,
    pt: 1,
    maxHeight: 'calc(100% - 64px)',
    overflowY: 'auto',
  },
  footer: {
    display: 'flex',
    justifyContent: 'center',
    p: 2,
    gap: 2,
    width: '100%',
  },
  button: {
    flexGrow: 1,
  },
};

/**
 *
 * @param {func} onCancel - optional function to run when the cancel button is clicked - expected to handle closing the modal
 * @returns
 */
const Modal = (props) => {
  const {
    title,
    open,
    setOpen,
    children,
    onCancel,
    onSubmit,
    cancelText = 'Cancel', //TODO add check so if cancelText is empty string to hide button. Will allow onCancel to be defined but hide the button if needed.
    submitText = 'Submit',
    isSidebar = false,
    disableSubmit = false,
    disableCancel = false,
    disableAntiShrink = false,
    disableTitle = false,
    loading = false,
    loadingMsg = '',
    useClassic = false,
    sx = {},
  } = props;

  const hasBtn = !!onCancel || !!onSubmit;

  if (isSidebar) {
    if (!sx.left) {
      sx.left = 'inherit';
    } else {
      console.warn(
        "sx.left is being set on props and will over write the 'isSidebar' prop",
      );
    }
  }

  //prevent modal from shrinking, once it gets to one size keep it
  const contentRef = React.useRef(null);
  const [minHeight, setMinHeight] = React.useState('100px');

  React.useEffect(() => {
    const minNum = parseFloat(minHeight);
    setTimeout(() => {
      if (
        contentRef.current?.clientHeight &&
        contentRef.current?.clientHeight > minNum
      ) {
        setMinHeight(`${contentRef.current?.clientHeight}px`);
      }
    }, 150);
  }, [minHeight, contentRef.current?.clientHeight]);

  React.useEffect(() => {
    if (!open) {
      setMinHeight('100px');
    }
  }, [open]);

  const onClose = () => {
    if (onCancel) {
      onCancel();
    } else {
      setOpen(false);
    }
  };

  const isMobile = useIsMobile();

  return (
    <Dialog
      sx={sx}
      open={open}
      onClose={onClose}
      onKeyDown={(e) => {
        if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
          if (!onSubmit) {
            db(`No onSubmit function for ${title} modal`);
          } else {
            onSubmit();
          }
        }
      }}
    >
      {!disableTitle && (
        <DialogTitle sx={styles.dialogTitle}>
          <Box>
            <Text variant={'lgSemi'}>{title}</Text>
          </Box>
          <IconButton
            onClick={(e) => {
              e.stopPropagation();
              onClose();
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
      )}
      {useClassic ? (
        <Box className={`fixed inset-0 z-10 overflow-y-auto`}>
          <Box
            className={`flex min-h-full justify-center p-4 text-center items-center sm:p-0`}
          >
            <Box
              ref={contentRef}
              className={`relative transform rounded-lg bg-white dark:bg-gray-800 px-4 pt-5 pb-4 text-left shadow-xl transition-all my-8 w-full sm:max-w-sm sm:p-6`}
            >
              {loading ? <Loader message={loadingMsg} /> : children}
            </Box>
          </Box>
        </Box>
      ) : (
        <Box
          sx={{
            ...styles.content,
            justifyContent: loading ? 'center' : 'flex-start',
            minHeight: disableAntiShrink ? '0px' : minHeight,
            minWidth: isMobile ? '100%' : styles.content.minWidth,
          }}
          ref={contentRef}
          className={`modalContent`}
        >
          {loading ? <Loader message={loadingMsg} /> : children}
        </Box>
      )}

      {!!hasBtn && (
        <Box className="modalFooter" sx={styles.footer}>
          {onCancel && (
            <Button
              disabled={disableCancel || loading}
              sx={styles.button}
              onClick={onCancel}
              variant="secondary"
              data-testid={`${cancelText}-btn`}
            >
              {cancelText}
            </Button>
          )}
          {onSubmit && (
            <Button
              disabled={disableSubmit || loading}
              sx={styles.button}
              onClick={onSubmit}
              variant="primary"
              data-testid={`${submitText}-btn`}
            >
              {submitText}
            </Button>
          )}
        </Box>
      )}
    </Dialog>
  );
};

const setOpenPropType = (props, propName, componentName) => {
  if (!props.onCancel && !props[propName]) {
    return new Error(
      `Prop \`${propName}\` is required when \`onCancel\` is not defined in \`${componentName}\`.`,
    );
  }
  if (props[propName] && typeof props[propName] !== 'function') {
    return new Error(
      `Invalid prop \`${propName}\` of type \`${typeof props[
        propName
      ]}\` supplied to \`${componentName}\`, expected \`function\`.`,
    );
  }
};

Modal.propTypes = {
  open: PropTypes.bool.isRequired,
  setOpen: setOpenPropType,
  title: PropTypes.string,
  children: PropTypes.node,
  onCancel: PropTypes.func,
  onSubmit: PropTypes.func,
  cancelText: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  submitText: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  isSidebar: PropTypes.bool,
  disableSubmit: PropTypes.bool,
  disableCancel: PropTypes.bool,
  sx: PropTypes.object,
  loading: PropTypes.bool,
  disableAntiShrink: PropTypes.bool,
  loadingMsg: PropTypes.string,
  disableTitle: PropTypes.bool,
  useClassic: PropTypes.bool,
};

export default Modal;
