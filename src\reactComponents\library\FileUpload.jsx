import React from 'react';
import PropTypes from 'prop-types';

import { uploadPdf } from '@/services/pdf';

import { Box, Button, CircularProgress } from '@/reactComponents/library';
import FileUploadOutlinedIcon from '@mui/icons-material/FileUploadOutlined';
import { snackbarErr } from '@/reactComponents/library/Snackbar';

const styles = {
  dragNDropArea: {
    width: '99%',
    border: `1px dashed`,
    borderColor: 'gray.50',
    borderRadius: '8px',
    backgroundColor: 'background.paper',
    maxHeight: '100px',
  },
  dragActive: {
    border: `1px dashed`,
    borderColor: 'primary.main',
    backgroundColor: 'rgba(0, 0, 255, 0.04)',
  },
  fileUploadIcon: {
    display: 'flex',
    alignItems: 'center',
  },
  fileUploadText: {
    fontSize: '14px',
    color: 'gray.main',
    marginLeft: '16px',
  },
  cell: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
  },
};

const toBase64 = (file) =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });

const FileUpload = (props) => {
  const { setDocumentId, disabled } = props;

  const [uploading, setUploading] = React.useState(false);
  const uploadInputRef = React.useRef(null);
  const [dragActive, setDragActive] = React.useState(false);

  const filesChange = async (files) => {
    try {
      setUploading(true);
      const document = await toBase64(files[0]);
      const { data } = await uploadPdf({
        document,
        name: 'mileage_form_upload',
      });
      setDocumentId(data.documentId);
    } catch (err) {
      snackbarErr('Error uploading file, must be a valid pdf');
    } finally {
      setUploading(false);
    }
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      !uploading && setDragActive(true);
    }
    if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  let showDragBox = !uploading;

  // if (uploadsInProgress.length > 2) {
  //   showDragBox = false;
  // }

  let dragBoxSx = styles.dragNDropArea;
  if (dragActive) {
    dragBoxSx = {
      ...styles.dragNDropArea,
      ...styles.dragActive,
    };
  }

  return (
    <Box>
      <Box sx={styles.cell}>
        {showDragBox ? (
          <Box sx={dragBoxSx}>
            <form
              id={`drag-upload-control-container`}
              onDragEnter={handleDrag}
              onDragOver={handleDrag}
              onDragLeave={handleDrag}
              onDrop={(e) => {
                e.preventDefault();
                e.stopPropagation();
                const listArray = Array.from(e.dataTransfer.files);
                !uploading && filesChange(listArray);
                setDragActive(false);
              }}
              disabled={disabled}
            >
              <input
                ref={uploadInputRef}
                type="file"
                id="input-file-upload"
                multiple={false}
                style={{ display: 'none' }}
                accept=".pdf"
                onChange={(e) => {
                  const array = Array.from(e.target.files);
                  !uploading && filesChange(array);
                }}
                onClick={(e) => {
                  e.target.value = null;
                  e.stopPropagation();
                }}
                disabled={disabled}
              />

              <Button
                variant="text"
                startIcon={<FileUploadOutlinedIcon />}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  uploadInputRef.current.click();
                }}
                disabled={disabled}
              >
                Upload
              </Button>
            </form>
          </Box>
        ) : (
          <CircularProgress />
        )}
        {/* {uploadsInProgress.map(inProgress => (
          <UploadProgressCard
            key={inProgress.uploadId}
            inProgress={inProgress}
          />
        ))} */}
      </Box>
    </Box>
  );
};

FileUpload.propTypes = {
  documentId: PropTypes.string,
  setDocumentId: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
};

export default FileUpload;
