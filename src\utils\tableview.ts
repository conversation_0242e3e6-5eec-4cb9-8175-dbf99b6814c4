import type { Pagination } from '@/types/Pagination';
import type { TableviewMetadata } from '@/types/Tableview';
import type { RouteLocationNormalized } from 'vue-router';

export const copyMetaToPagination = (meta: TableviewMetadata): Pagination => {
  return {
    total: meta?.total,
    limit: meta?.per_page,
    page: meta?.current_page,
  };
};

export const updateRouteWithPagination = (
  router: { push: Function },
  pagination: Pagination,
  routeQuery: RouteLocationNormalized['query'],
): Promise<void> => {
  return router
    .push({
      query: {
        ...routeQuery,
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      },
    })
    .catch((err: Error) => {
      if (err.name !== 'NavigationDuplicated') {
        console.error(err);
      }
    });
};

export const updateReactRouteWithPagination = (
  navigate: Function,
  pagination: Pagination,
  searchQuery: any,
): void => {
  const search = new URLSearchParams(searchQuery);
  Object.entries(searchQuery).forEach(([key, value]: [string, any]) => {
    search.set(key, value);
  });

  search.set('page', pagination.page.toString());
  search.set('limit', pagination.limit.toString());
  navigate({
    search: search.toString(),
  });
};
