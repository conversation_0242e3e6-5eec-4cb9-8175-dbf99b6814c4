<template>
  <section class="mt-5 bg-white dark:bg-gray-700 py-4 px-6 rounded-md">
    <h2
      class="text-xl font-bold pb-4 mb-5 border-b border-gray-200 dark:border-gray-500"
    >
      Audit
    </h2>
    <div class="flow-root">
      <ul role="list" class="-mb-4">
        <TimecardDetailAudit
          v-for="(audit, auditIndex) in props.audits"
          :key="`audit--${audit.id}-${auditIndex}`"
          :audit="audit"
          :last-item="auditIndex === audits.length - 1"
        />
      </ul>
    </div>
  </section>
</template>

<script setup lang="ts">
import TimecardDetailAudit from '@/components/TimecardDetailAudit.vue';
import type TimecardAudit from '@/types/TimecardAudit';

const props = defineProps<{
  audits: TimecardAudit[];
}>();
</script>
