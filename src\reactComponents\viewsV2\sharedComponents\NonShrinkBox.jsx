import React from 'react';
import { Box } from '@/reactComponents/library';
import PropTypes from 'prop-types';

/**
 *  Box that maintains its height even when the content inside it changes.
 *  prevents page from jumping when the content inside it changes.
 */
const NonShrinkBox = ({ children, ...props }) => {
  const ref = React.useRef(null);
  const minHeight = React.useRef(0);

  React.useEffect(() => {
    //There's probably a better way to do this than an interval but resize observer wasn't dbut boundary
    // observer wasn't doing it and I didn't want to send too much time on this
    const int = setInterval(() => {
      if (ref.current) {
        const scrollHeight = ref.current.scrollHeight;
        if (scrollHeight > minHeight.current) {
          minHeight.current = scrollHeight;
          ref.current.style.minHeight = `${minHeight.current}px`;
        }
      }
    }, 750);

    return () => {
      clearInterval(int);
    };
  });

  return (
    <Box ref={ref} {...props}>
      {children}
    </Box>
  );
};

NonShrinkBox.propTypes = {
  children: PropTypes.node,
};

export default NonShrinkBox;
