type Theme = {
  [key: string]: any;
};

type AddBase = {
  (arg: any): void;
};

interface PluginUtils {
  addBase: AddBase;
  theme: (path: string) => Theme;
}

const flattenColors = (
  colors: Theme,
  parent: string = '',
): Record<string, string> => {
  return Object.entries(colors).reduce((acc, [name, value]) => {
    const key = parent ? `${parent}-${name}` : name;
    if (typeof value === 'object') {
      return { ...acc, ...flattenColors(value as Theme, key) };
    }
    return { ...acc, [key]: value };
  }, {} as Record<string, string>);
};

const colorVariablesPlugin = ({ addBase, theme }: PluginUtils): void => {
  const colors = theme('colors');
  const flattenedColors = flattenColors(colors);
  const colorVariables = Object.entries(flattenedColors).reduce(
    (acc, [name, value]) => {
      return { ...acc, [`--tw-${name}`]: value };
    },
    {},
  );
  addBase({
    ':root': colorVariables,
  });
};

export default colorVariablesPlugin;
