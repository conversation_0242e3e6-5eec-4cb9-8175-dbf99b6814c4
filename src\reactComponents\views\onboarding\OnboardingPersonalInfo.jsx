import React from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router';

import { register } from '@/services/auth';
import AuthStore from '@/reactComponents/stores/auth';

import { useAuth } from '@/reactComponents/AppHooks';
import { Box, Button, CircularProgress, Text } from '@/reactComponents/library';
import { snackbarErr } from '@/reactComponents/library/Snackbar';
import { RegisterForm, BackToLogin } from '@/reactComponents/library/OktaLogin';
import OktaLoginContext from '@/reactComponents/library/OktaLogin/Context';
import { decodeAccessToken } from '@/reactComponents/library/OktaLogin/utils';
import { AxiosError } from 'axios';

const styles = {
  root: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100vh',
  },
  box: {
    width: 400,
    backgroundColor: 'background.default',
    p: '2.5rem',
    borderRadius: '0.5rem',
    margin: 'auto',
    boxShadow: '0 1px 2px 0 rgb(0 0 0 / .05)',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 2,
  },
  title: {
    lineHeight: '2.25rem',
    fontWeight: 800,
    fontSize: '1.875rem',
  },
  actions: {
    display: 'flex',
    flexDirection: 'row',
    gap: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    px: 1,
  },
};

const OnboardingPersonalInfo = () => {
  useAuth();
  const { encryptedPhone } = useParams();
  const { client, user } = React.useContext(OktaLoginContext);
  const [loading, setLoading] = React.useState();
  const [token, setToken] = React.useState();
  const [data, setData] = React.useState({
    encryptedPhone,
    email: user?.email,
    oktaId: user?.oktaId,
    firstName: user?.firstName || '',
    middleName: '',
    lastName: user?.lastName || '',
  });
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const updateData = (newData) => setData((prev) => ({ ...prev, ...newData }));

  const submitRegistration = async (e) => {
    e.preventDefault();
    if (loading) return;

    try {
      setLoading(true);
      const { data: user } = await register(data);
      AuthStore.login(user);
      goToNextPage();
    } catch (e) {
      if (e instanceof AxiosError) {
        const error = e.response?.data?.errors?.[0]?.message;
        snackbarErr(error || e.message);
      } else {
        snackbarErr(e.message);
      }
    } finally {
      setLoading(false);
    }
  };

  // TODO: Should we still support invitation code query param?
  //        src/views/onboarding/OnboardingPersonalInfo.vue#onMounted
  const goToNextPage = async () => {
    const redirect = searchParams.get('redirect');
    if (redirect) {
      navigate(decodeURIComponent(redirect.toString()));
    } else {
      navigate('/');
    }
  };

  React.useEffect(() => {
    const getToken = async () => {
      const { accessToken } = await client.getTokens();
      setToken(accessToken.accessToken);
      setLoading(false);
    };
    setLoading(true);
    getToken();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  React.useEffect(() => {
    if (!token) return;

    const { sub: email, uid: oktaId } = decodeAccessToken(token) || {};
    updateData({ email, oktaId });
  }, [token]);

  return (
    <Box sx={styles.root}>
      <Box sx={styles.box} component="form" onSubmit={submitRegistration}>
        <Text sx={styles.title}>Personal Information</Text>
        <RegisterForm
          data={data}
          onChange={(fieldName) => (e) =>
            setData({ ...data, [fieldName]: e.target.value })}
          disable={['email']}
        />
        <Box sx={styles.actions}>
          <BackToLogin />
          <Button
            type="submit"
            disabled={loading}
            endIcon={
              loading ? <CircularProgress size={20} color="white" /> : null
            }
          >
            Register
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default OnboardingPersonalInfo;
