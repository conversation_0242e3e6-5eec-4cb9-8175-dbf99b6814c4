<template>
  <TextInput
    class="cursor-pointer"
    :model-value="modelValue"
    :label="label"
    type="date"
    :min="min"
    :max="max"
    :disabled="disabled"
    @update:model-value="emit('update:modelValue', $event)"
    @click="open"
  />
  <Modal v-model="isOpen">
    <h2 class="text-center text-2xl mb-3">{{ headerText }}</h2>
    <div
      v-if="view === 'day'"
      class="text-center lg:col-start-8 lg:col-end-13 lg:row-start-1 xl:col-start-9"
    >
      <div class="flex items-center">
        <button
          type="button"
          class="-m-1.5 flex flex-none items-center justify-center p-1.5 text-gray-400 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300"
        >
          <span class="sr-only">Previous month</span>
          <ChevronLeftIcon
            class="h-5 w-5"
            aria-hidden="true"
            @click="prevMonth"
          />
        </button>
        <div
          @click="view = 'month'"
          class="flex-auto font-semibold text-gray-500 dark:text-gray-400 cursor-pointer"
        >
          {{ currentMonth }}
        </div>
        <button
          type="button"
          class="-m-1.5 flex flex-none items-center justify-center p-1.5 text-gray-400 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300"
        >
          <span class="sr-only">Next month</span>
          <ChevronRightIcon
            class="h-5 w-5"
            aria-hidden="true"
            @click="nextMonth"
          />
        </button>
      </div>
      <div
        class="mt-6 grid grid-cols-7 text-xs leading-6 text-gray-500 dark:text-gray-400"
      >
        <div>S</div>
        <div>M</div>
        <div>T</div>
        <div>W</div>
        <div>T</div>
        <div>F</div>
        <div>S</div>
      </div>
      <div
        class="isolate mt-2 grid grid-cols-7 gap-px rounded-lg bg-gray-200 dark:bg-gray-700 ring-1 ring-gray-200 dark:ring-gray-700 text-sm shadow"
      >
        <button
          v-for="(day, dayIdx) in days"
          :key="dayIdx"
          type="button"
          :class="dayButtonClass(day, dayIdx)"
          @click="selectDate(dayIdx)"
        >
          <time
            v-if="day.date"
            :datetime="(day.date.toISODate() as string)"
            :class="timeClass(day)"
          >
            {{ formatDayOfMonth(day) }}
          </time>
        </button>
      </div>
    </div>
    <div
      v-else-if="view === 'month'"
      class="text-center lg:col-start-8 lg:col-end-13 lg:row-start-1 xl:col-start-9"
    >
      <div class="flex items-center text-gray-900">
        <button
          type="button"
          class="-m-1.5 flex flex-none items-center justify-center p-1.5 text-gray-400 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300"
        >
          <span class="sr-only">Previous year</span>
          <ChevronLeftIcon
            class="h-5 w-5"
            aria-hidden="true"
            @click="prevYear"
          />
        </button>
        <div
          @click="view = 'year'"
          class="flex-auto font-semibold text-xl dark:text-gray-400 cursor-pointer"
        >
          {{ displayedYear.toFormat('yyyy') }}
        </div>
        <button
          type="button"
          class="-m-1.5 flex flex-none items-center justify-center p-1.5 text-gray-400 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300"
        >
          <span class="sr-only">Next year</span>
          <ChevronRightIcon
            class="h-5 w-5"
            aria-hidden="true"
            @click="nextYear"
          />
        </button>
      </div>
      <div
        class="isolate mt-2 grid grid-cols-3 gap-px rounded-lg bg-gray-200 dark:bg-gray-700 ring-1 ring-gray-200 dark:ring-gray-700 text-sm shadow"
      >
        <button
          v-for="(month, monthIdx) in months"
          :key="monthIdx"
          type="button"
          :class="monthButtonClass(month, monthIdx)"
          @click="selectMonth(monthIdx)"
        >
          <time
            :datetime="month.date.toFormat('LLL')"
            :class="monthClass(month)"
          >
            {{ month.date.toFormat('LLL') }}</time
          >
        </button>
      </div>
    </div>
    <div
      v-else-if="view === 'year'"
      class="text-center lg:col-start-8 lg:col-end-13 lg:row-start-1 xl:col-start-9"
    >
      <div class="flex items-center text-gray-900">
        <button
          type="button"
          class="-m-1.5 flex flex-none items-center justify-center p-1.5 text-gray-400 hover:text-gray-500"
        >
          <span class="sr-only">Previous years</span>
          <ChevronLeftIcon
            class="h-8 w-8"
            aria-hidden="true"
            @click="prevDecade"
          />
        </button>
        <div
          @click="view = 'year'"
          class="flex-auto font-semibold dark:text-gray-400 text-lg"
        >
          {{ yearFrom }} - {{ yearTo }}
        </div>
        <button
          type="button"
          class="-m-1.5 flex flex-none items-center justify-center p-1.5 text-gray-400 hover:text-gray-500"
        >
          <span class="sr-only">subsequent years</span>
          <ChevronRightIcon
            class="h-8 w-8"
            aria-hidden="true"
            @click="nextDecade"
          />
        </button>
      </div>
      <div
        class="isolate mt-2 grid grid-cols-3 gap-px rounded-lg bg-gray-200 dark:bg-gray-700 text-sm shadow ring-1 ring-gray-200 dark:ring-gray-700"
      >
        <button
          v-for="(year, yearIdx) in years"
          :key="yearIdx"
          type="button"
          class="py-1.5 focus:z-10"
          :class="yearButtonClass(year)"
          @click="selectYear(year.date.year)"
        >
          <time
            :datetime="year.date.toFormat('YYYY')"
            class="text-gray-800 dark:text-gray-200 text-lg"
            :class="yearButtonTextClass(year)"
          >
            {{ year.date.toFormat('yyyy') }}</time
          >
        </button>
      </div>
    </div>
    <div class="flex justify-center">
      <Button class="mt-3 mr-1" color="gray" @click="close"> Cancel </Button>
      <Button v-if="view === 'day'" class="mt-3 ml-1" @click="set">
        Select
      </Button>
    </div>
  </Modal>
</template>

<script lang="ts" setup>
import Button from '@/components/library/Button.vue';
import Modal from '@/components/library/Modal.vue';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/vue/20/solid';
import { DateTime, Interval } from 'luxon';
import { computed, onMounted, ref, type Ref } from 'vue';
import TextInput from './TextInput.vue';

const props = withDefaults(
  defineProps<{
    label: string;
    modelValue: DateTime | null;
    initialView?: string;
    min?: string;
    max?: string;
    disabled?: boolean;
  }>(),
  {
    initialView: 'day',
  },
);

const emit = defineEmits<{
  (e: 'update:modelValue', value: DateTime): void;
  (e: 'change'): void;
}>();

const set = () => {
  emit('update:modelValue', selectedDate.value);
  close();
  view.value = props.initialView;
  emit('change');
};

interface DatePickerDate {
  date: DateTime;
  isCurrentMonth: boolean;
}

interface DatePickerMonth {
  date: DateTime;
  isCurrentYear: boolean;
}

interface DatePickerYear {
  date: DateTime;
}

const days: Ref<DatePickerDate[]> = ref([] as DatePickerDate[]);
const months: Ref<DatePickerMonth[]> = ref([] as DatePickerMonth[]);
const years: Ref<DatePickerYear[]> = ref([] as DatePickerYear[]);

onMounted(() => {
  view.value = props.initialView;
});

const generateDaysFromInterval = (
  interval: Interval,
  isCurrentMonth: boolean,
) => {
  const daysList = interval.splitBy({ day: 1 }).map((d) => d.start);
  for (const d of daysList) {
    days.value.push({
      date: d as DateTime,
      isCurrentMonth,
    });
  }
};

const generateMonthsFromInterval = (
  interval: Interval,
  isCurrentYear: boolean,
) => {
  const newMonths = [];
  const monthsList = interval.splitBy({ month: 1 }).map((d) => d.start);
  for (const d of monthsList) {
    newMonths.push({
      date: d as DateTime,
      isCurrentYear,
    });
  }
  months.value = newMonths;
};

const generateYearsFromInterval = (interval: Interval) => {
  const newYears: DatePickerYear[] = [];
  const yearsList = interval.splitBy({ year: 1 }).map((d) => d.start);
  for (const d of yearsList) {
    newYears.push({
      date: d as DateTime,
    });
  }
  years.value = newYears;
};

const headerText = computed(() => {
  switch (view.value) {
    case 'day':
      return 'Select a Day';
    case 'month':
      return 'Select a Month';
    default:
      return 'Select a Year';
  }
});

const yearFrom = computed(() => {
  return years.value?.[0]?.date?.toFormat('yyyy') || '';
});

const yearTo = computed(() => {
  return years?.value?.[years?.value?.length - 1]?.date?.toFormat('yyyy') || '';
});

const currentMonth = computed(() => {
  return displayedMonth.value.toFormat('MMMM yyyy');
});

const currentDate: Ref<DateTime> = ref(props.modelValue || DateTime.now());
const displayedMonth: Ref<DateTime> = ref(currentDate.value.startOf('month'));
const displayedYear: Ref<DateTime> = ref(currentDate.value.startOf('year'));
const selectedDate: Ref<DateTime> = ref(currentDate.value);
const view: Ref<string> = ref('day');

const generateDaysFromDate = (date: DateTime) => {
  days.value = [];
  const daysBeforeInMonthInterval = Interval.fromDateTimes(
    date.startOf('month').startOf('week').minus({ day: 1 }),
    date.startOf('month'),
  );
  generateDaysFromInterval(daysBeforeInMonthInterval, false);
  const inMonthInterval = Interval.fromDateTimes(
    date.startOf('month'),
    date.endOf('month'),
  );
  generateDaysFromInterval(inMonthInterval, true);
  const daysAfterInMonthInterval = Interval.fromDateTimes(
    date.endOf('month').plus({ day: 1 }),
    date.endOf('month').endOf('week'),
  );
  generateDaysFromInterval(daysAfterInMonthInterval, false);
};

const generateMonthsFromDate = (date: DateTime) => {
  const inYearInterval = Interval.fromDateTimes(
    date.startOf('year'),
    date.endOf('year'),
  );
  generateMonthsFromInterval(inYearInterval, true);
};

const generateYearsFromDate = (date: DateTime) => {
  const nineYearInterval = Interval.fromDateTimes(
    date.startOf('year').minus({ year: 4 }),
    date.endOf('year').plus({ year: 4 }),
  );
  generateYearsFromInterval(nineYearInterval);
};

generateDaysFromDate(displayedMonth.value);
generateMonthsFromDate(displayedYear.value);
generateYearsFromDate(displayedYear.value);

const isOpen = ref(false);

const open = () => {
  isOpen.value = true;
  if (props.modelValue) {
    const baseDate =
      props.modelValue && props.modelValue.isValid
        ? props.modelValue
        : DateTime.now();
    selectedDate.value = baseDate;
    displayedMonth.value = baseDate.startOf('month');
    displayedYear.value = baseDate.startOf('year');
    generateDaysFromDate(displayedMonth.value);
    generateMonthsFromDate(displayedYear.value);
    generateYearsFromDate(displayedYear.value);
  }
};

const close = () => {
  isOpen.value = false;
};

const selectDate = (dateIndex: number) => {
  selectedDate.value = days.value[dateIndex].date;
};

const selectMonth = (monthIndex: number) => {
  displayedMonth.value = displayedMonth.value.set({
    month: months.value[monthIndex].date.month,
  });
  displayedYear.value = displayedYear.value.set({
    year: months.value[monthIndex].date.year,
  });
  selectedDate.value = selectedDate.value.set({
    year: months.value[monthIndex].date.year,
    month: months.value[monthIndex].date.month,
  });
  view.value = 'day';
  days.value = [];
  generateDaysFromDate(selectedDate.value);
};

const selectYear = (year: number) => {
  displayedYear.value = displayedYear.value.set({ year });
  displayedMonth.value = displayedMonth.value.set({ year });
  selectedDate.value = selectedDate.value.set({ year });
  months.value = [];
  generateMonthsFromDate(displayedMonth.value);
  view.value = 'month';
};

const prevMonth = () => {
  displayedMonth.value = displayedMonth.value.minus({ month: 1 });
  displayedYear.value = displayedYear.value.minus({ month: 1 });
  days.value = [];
  generateDaysFromDate(displayedMonth.value);
};
const nextMonth = () => {
  displayedMonth.value = displayedMonth.value.plus({ month: 1 });
  displayedYear.value = displayedYear.value.plus({ month: 1 });
  days.value = [];
  generateDaysFromDate(displayedMonth.value);
};

const prevYear = () => {
  displayedYear.value = displayedYear.value.minus({ year: 1 });
  displayedMonth.value = displayedMonth.value.minus({ year: 1 });
  months.value = [];
  generateMonthsFromDate(displayedYear.value);
};

const nextYear = () => {
  displayedYear.value = displayedYear.value.plus({ year: 1 });
  displayedMonth.value = displayedMonth.value.plus({ year: 1 });
  months.value = [];
  generateMonthsFromDate(displayedYear.value);
};

const prevDecade = () => {
  const midYear = years.value[1].date.minus({ year: 9 });
  years.value = [];
  generateYearsFromDate(midYear);
};

const nextDecade = () => {
  const midYear = years.value[1].date.plus({ year: 9 });
  years.value = [];
  generateYearsFromDate(midYear);
};

const dayButtonClass = (day: DatePickerDate, dayIdx: number) => {
  const dayIsSelected = day.date.toISODate() === selectedDate.value.toISODate();
  const dayIsCurrentDate =
    day.date.toISODate() === currentDate.value.toISODate();

  const classData = {
    'py-1.5 hover:bg-gray-100 dark:hover:bg-gray-800 focus:z-10': true,
    'bg-gray-50 dark:bg-gray-700': day.isCurrentMonth,
    'bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-900': !day.isCurrentMonth,
    'font-semibold': dayIsSelected || dayIsCurrentDate,
    'text-white': dayIsSelected,
    'text-gray-800 dark:text-gray-200':
      !dayIsSelected && day.isCurrentMonth && !dayIsCurrentDate,
    'text-gray-600 dark:text-gray-400':
      !dayIsSelected && !day.isCurrentMonth && !dayIsCurrentDate,
    'text-indigo-600': dayIsCurrentDate && !dayIsSelected,
    'rounded-tl-lg': dayIdx === 0,
    'rounded-tr-lg': dayIdx === 6,
    'rounded-bl-lg': dayIdx === days.value.length - 7,
    'rounded-br-lg': dayIdx === days.value.length - 1,
  };

  return classData;
};

const yearButtonClass = (year: DatePickerYear) => {
  const yearStr = year.date.toFormat('yyyy');
  const yearIsSelected = yearStr === selectedDate.value.toFormat('yyyy');
  const isCurrentYear = yearStr === currentDate.value.toFormat('yyyy');

  const classData = {
    'bg-gray-50 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-800':
      !yearIsSelected && !isCurrentYear,
    'bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-900':
      isCurrentYear,
    'bg-indigo-700 hover:bg-indigo-600 dark:bg-indigo-400 dark:hover:bg-indigo-500':
      yearIsSelected,
  };

  return classData;
};

const yearButtonTextClass = (year: DatePickerYear) => {
  const yearStr = year.date.toFormat('yyyy');
  const yearIsSelected = yearStr === selectedDate.value.toFormat('yyyy');
  const isCurrentYear = yearStr === currentDate.value.toFormat('yyyy');

  const classData = {
    'font-medium': isCurrentYear,
    'text-white font-bold': yearIsSelected,
  };

  return classData;
};

const monthButtonClass = (month: DatePickerMonth, monthIdx: number) => {
  const monthStr = month.date.toFormat('MM/yyyy');
  const monthIsSelected = monthStr === selectedDate.value.toFormat('MM/yyyy');
  const isCurrentMonth = monthStr === currentDate.value.toFormat('MM/yyyy');

  const classData = {
    'py-1.5 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800 focus:z-10':
      true,
    'font-semibold': monthIsSelected || isCurrentMonth,
    'text-white': monthIsSelected,
    'text-gray-900 dark:text-gray-200':
      !monthIsSelected && month.isCurrentYear && !isCurrentMonth,
    'text-gray-400':
      !monthIsSelected && !month.isCurrentYear && !isCurrentMonth,
    'dark:text-indigo-400 text-indigo-600': isCurrentMonth && !monthIsSelected,
    'rounded-tl-lg': monthIdx === 0,
    'rounded-tr-lg': monthIdx === 2,
    'rounded-bl-lg': monthIdx === months.value.length - 3,
    'rounded-br-lg': monthIdx === months.value.length - 1,
  };

  return classData;
};

const timeClass = (day: DatePickerDate) => {
  const dayIsSelected = day.date.toISODate() === selectedDate.value.toISODate();
  const dayIsCurrentDate =
    day.date.toISODate() === currentDate.value.toISODate();
  return [
    'mx-auto flex h-7 w-7 items-center justify-center rounded-full',
    dayIsSelected && dayIsCurrentDate && 'bg-indigo-400 dark:bg-indigo-600',
    dayIsSelected && !dayIsCurrentDate && 'bg-indigo-400 dark:bg-indigo-600',
  ];
};

const monthClass = (month: DatePickerMonth) => {
  const monthStr = month.date.toFormat('MM/yyyy');
  const monthIsSelected = monthStr === selectedDate.value.toFormat('MM/yyyy');
  const isCurrentMonth = monthStr === currentDate.value.toFormat('MM/yyyy');

  const classData = {
    'mx-auto flex h-8 w-8 items-center justify-center rounded-full': true,
    'bg-indigo-600': monthIsSelected && isCurrentMonth,
    'bg-indigo-400': monthIsSelected && !isCurrentMonth,
  };

  return classData;
};

const formatDayOfMonth = (day: DatePickerDate) => {
  return day.date.day;
};
</script>
