import { DateTime } from 'luxon';
import { createTimecard } from '@/services/timecards';
import { useState } from 'react';

const useCreateTimecard = (projectId, projectHashId, navigate) => {
  const [createTimecardLoading, setCreateTimecardLoading] = useState(false);
  const createTimecardHandler = async (payPeriod) => {
    try {
      setCreateTimecardLoading(true);
      const createTimecardPayload = {
        projectId: projectId,
        payPeriodId: payPeriod,
        timeZone: DateTime.now().zoneName,
      };
      const { data } = await createTimecard(createTimecardPayload);
      const timecard = data;
      setCreateTimecardLoading(false);
      navigate({
        pathname: `/projects/${projectHashId}/timecards/${timecard.id}`,
      });
    } catch (err) {
      console.error('Failed to create timecard:', err);
      throw new Error('Failed to create timecard. Please try again.');
    }
  };

  return { createTimecardHandler, createTimecardLoading };
};

export default useCreateTimecard;
