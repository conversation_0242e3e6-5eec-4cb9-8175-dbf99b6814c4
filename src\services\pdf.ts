import type DocumentTemplateUploadPayload from '@/types/DocumentTemplateUploadPayload';
import axios, { type AxiosResponse } from 'axios';
import { coreBaseUrl } from '../utils/constants';

export const uploadPdf = async (
  documentTemplateUploadPayload: DocumentTemplateUploadPayload,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/pdf/save`;
  const response = await axios.post(url, documentTemplateUploadPayload, {
    withCredentials: true,
  });
  return response;
};

export const getPdf = (documentId: number): Promise<AxiosResponse<any>> => {
  return axios.get(`${coreBaseUrl()}/pdf/${documentId}`);
};

export const getPdfUrl = (documentId: string): string => {
  return `${coreBaseUrl()}/pdf/${documentId}`;
};
