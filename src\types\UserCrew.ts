import type { DateTime } from 'luxon';
import type Address from './Address';
import type User from './User';

export default interface UserCrew {
  id: number;
  user: User;
  dateOfBirth: DateTime;
  personalAddressId: number;
  address: Address;
  ethnicityId: number;
  ethnicity: any;
  citizenshipStatusId: number;
  citizenshipStatus: any;
  socialSecurityNumber: string;
  confirmSocialSecurityNumber?: string;
  createdAt: DateTime;
  updatedAt: DateTime;
  genderId: number;
  loanOut: any;
  gender: any;
  unions: any[];

  workAuthorizationNumber: string;
  workAuthorizationType: any;
  foreignPassportIssuingCountry: string;
  workAuthorizationExpirationDate?: DateTime;

  withholdingsBasedOnDependents: number;
  withholdingsFromOtherIncome: number;
  extraWithholdings: number;
  exemptFromWithholdingsBasedOnDependents: boolean;
}
