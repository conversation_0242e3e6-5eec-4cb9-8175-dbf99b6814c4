import React from 'react';
import { applyPureVueInReact } from 'veaury';
import { useAuth, useReactRouter } from '../../AppHooks';
import { Outlet, useMatch } from 'react-router';
import { getProductionCompanyById } from '../../../services/production-company';
import CompanyHeaderVue from '../../../components/CompanyHeader.vue';
import { useDidMount } from '@/reactComponents/utils/customHooks';

const CompanyHeader = applyPureVueInReact(CompanyHeaderVue);

const CompanyView = () => {
  useAuth();
  const { navigate, route } = useReactRouter();
  const [company, setCompany] = React.useState({});
  const [doneLoading, setDoneLoading] = React.useState(false);

  const fetchCompany = async () => {
    const { data: companyData } = await getProductionCompanyById(
      route.params.id,
    );
    setCompany(companyData);
    setDoneLoading(true);
  };

  useDidMount(() => {
    fetchCompany();
  });

  return (
    <div className="min-h-full pt-14">
      <CompanyHeader
        route={route}
        navigate={navigate}
        company={company}
        useMatch={useMatch}
      />
      {doneLoading && <Outlet context={{ company: company }} />}
    </div>
  );
};

export default CompanyView;
