import { faker } from '@faker-js/faker';
import ProjectViewPageObject from '../../pageObjects/project/projectView';
import TimecardListPageObject from '../../pageObjects/timecardCrew/timecardList';
import {
  TimecardFormPageObject,
  TimeInformation,
} from '../../pageObjects/timecardCrew/timecardForm';
import {
  addPersonalInfo,
  fetchPayPeriodsRemainingByProject,
  fetchProjectInformation,
  fetchShootLocationsByProject,
  fetchTimecardById,
  logout,
} from '../../support/apiHelpers';
import { DateFormatter } from '../../utils/DateFormatter';
import { startPaperWorkFlow } from '../../support/apiFlows/startPaperWorkFlow';
import { createProjectFlow } from '../../support/apiFlows/createProjectFlow';
import { projectOnboardingFlow } from '../../support/apiFlows/projectOnboardingFlow';
import {
  interceptSubmitTimecard,
  interceptTimecardRevision,
  interceptUserInfo,
} from '../../support/apiTimecardInterceptors';
import { TimecardViewPageObject } from '../../pageObjects/timecardCrew/timecardView';
import { createTimecardCrewFlow } from '../../support/apiFlows/createTimecardCrewFlow';

describe('User Crew - Duplicate Timecard', () => {
  const projectViewPO = new ProjectViewPageObject();
  const timecardListPO = new TimecardListPageObject();
  const timecardFormPO = new TimecardFormPageObject();
  const timecardViewPO = new TimecardViewPageObject();
  const payPeriod = { start: '0', end: '0' };
  const timeInformation: TimeInformation = {
    day: 'Tuesday',
    workStatus: 'WORK',
    workLocation: '',
    startsAt: '8:00 AM',
    mealStart: ['12:00 PM'],
    mealEnd: ['1:00 PM'],
    endsAt: '5:00 PM',
    notes: faker.lorem.sentence(),
    ndb: false,
    copyLastDay: false,
  };

  let timecardId;

  beforeEach(() => {
    cy.loginWithSession(
      Cypress.env('PHONE_NUMBER'),
      Cypress.env('OTC_CODE'),
      Cypress.env('BASE_URL'),
      'supervisor',
    );
    //Create project by API
    cy.then(() => {
      return createProjectFlow();
    })
      .then(() => {
        //Get project info to add user
        return fetchProjectInformation().then((res) => {
          const departmentId = res.departments[0].id;
          const projectUrl = `${Cypress.env('BASE_URL')}/projects/${Cypress.env(
            'projectHashId',
          )}?onboard=true&departmentId=${departmentId}`;
          interceptUserInfo();
          return logout().then(() => {
            //adding crew to the project
            cy.loginWithoutSession(
              Cypress.env('PHONE_NUMBER_CREW'),
              Cypress.env('OTC_CODE'),
              projectUrl,
            );
            cy.wait('@userInfo').then((interception) => {
              const responseBody = interception.response?.body;
              Cypress.env('projectMemberId', responseBody.id);
            });
          });
        });
      })
      //filling out information needed
      .then(() => {
        return projectOnboardingFlow();
      })
      .then(() => {
        return addPersonalInfo();
      })
      .then(() => {
        return startPaperWorkFlow();
      })
      .then(() => {
        // Selection shoot location
        return fetchShootLocationsByProject();
      })
      .then((locations) => {
        cy.log(JSON.stringify(locations));
        timeInformation.workLocation = locations[0].shootLocation.locationName;
      })
      .then(() => {
        // Selecting period for timecard
        return fetchPayPeriodsRemainingByProject();
      })
      .then((periods) => {
        payPeriod.start = periods[0].startsAt;
        payPeriod.end = periods[0].endsAt;
        Cypress.env('payPeriodId', periods[0].id);
        return createTimecardCrewFlow(periods[0]).then((res) => {
          timecardId = res.body.id;
        });
      });
  });
  it('Verify Crew member is able to duplicate timecard', () => {
    interceptTimecardRevision(timecardId);
    cy.reload();
    projectViewPO.goToTimecards();
    timecardListPO.goToDuplicateTimecard(timecardId);
    cy.wait('@timecardRevision').then((intercept) => {
      const revisionTimecardId = intercept.response?.body.id;
      interceptSubmitTimecard(revisionTimecardId);
      timecardFormPO.fillOutTimecardForm([timeInformation]);

      timecardListPO.goToTimecard(revisionTimecardId);
      const today = `${DateFormatter.formatDateMonthNameDayYear(
        new Date().toISOString().split('T')[0],
      )}`;
      // Validate timecard information
      timecardViewPO.validateTimecardDetails(
        `${DateFormatter.formatDateMonthNameDayYear(
          payPeriod.start,
        )} - ${DateFormatter.formatDateMonthNameDayYear(payPeriod.end)}`,
        'Submitted',
        today,
        today,
      );
      fetchTimecardById(revisionTimecardId).then((timecard) => {
        // get date for time card
        const timeDate = timecard.timecardDays[2].startsAt;
        timecardViewPO.validateTimecardTime(
          DateFormatter.formatDateMMDDYYYY(timeDate.split('T')[0]),
          timeInformation.startsAt,
          timeInformation.endsAt,
          '8.00',
          `${timeInformation.mealStart[0]} - ${timeInformation.mealEnd[0]}`,
        );
      });
    });
  });
});
