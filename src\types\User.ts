import type { DateTime } from 'luxon';
import type Role from './Role';
import type UserCrew from './UserCrew';

export default interface User {
  id?: number;
  firstName: string;
  middleName?: string;
  lastName: string;
  email: string;
  phone: string;
  role: Role;
  roleId?: number;
  userCrew: UserCrew;
  createdAt: DateTime;
  updatedAt: DateTime;
  isSiteAdmin?: boolean;
  isCompanyAdmin?: boolean;
}
