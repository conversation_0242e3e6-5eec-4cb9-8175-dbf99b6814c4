<template>
  <div
    class="relative flex min-h-screen flex-col justify-center overflow-hidden"
  >
    <div class="m-auto p-10 bg-white dark:bg-gray-900 rounded-lg shadow-sm">
      <div class="flex justify-center"></div>
      <h2 class="text-center text-3xl font-extrabold leading-9 pt-4">Verify</h2>
      <p class="py-2">Enter code for {{ formattedPhone }}</p>
      <div>
        <TextInput
          label="Code"
          :model-value="code"
          type="otc"
          inputmode="numeric"
          auto-complete="one-time-code"
          autofocus="true"
          data-testid="verify-phone-input"
          @keyup.enter="verify"
          @update:rawValue="code = $event"
        />
        <div class="flex justify-center">
          <Button
            color="primary"
            @click="verify"
            :loading="loading"
            data-testid="verify-phone-btn"
          >
            Enter
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Auth from '@/reactComponents/stores/auth';
import PermissionStore from '@/reactComponents/stores/permission';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import { handleInviteRouteInReactComponent } from '@/utils/invite';
import { formatPhoneNumber } from '@/utils/phone';
import axios from 'axios';
import { computed, ref } from 'vue';
import Button from '../components/library/Button.vue';
import TextInput from '../components/library/TextInput.vue';
import { passwordlessCode } from '../services/auth';

const authStore = Auth;
const permissionStore = PermissionStore;
const snackbarStore = SnackbarStore;

const { navigate, route } = defineProps<{
  navigate: Function;
  params: { phone?: string };
  route: { query: any; params: any };
}>();

const code = ref('');
const loading = ref(false);

const formattedPhone = computed(() => {
  const phone = route.params.phone as string;
  return formatPhoneNumber(phone) || '';
});

const verify = async () => {
  if (loading.value) return;
  loading.value = true;
  const redirect = route.query.redirect;
  const phone = route.params.phone as string;
  try {
    const response = await passwordlessCode(phone, code.value);
    if (response.data.status === 'user_does_not_exist') {
      const query: any = { redirect };

      if (route.query?.code) {
        query.code = route.query.code;
      }
      const urlSearchParams = new URLSearchParams(query);
      const url = `/onboarding/${encodeURIComponent(
        response.data.encryptedPhone,
      )}/personal-info?${urlSearchParams.toString()}`;
      navigate(url);
    } else {
      authStore.login(response.data.user);
      await permissionStore.fetchPermissions();
      await permissionStore.fetchAdmin();
      snackbarStore.triggerSnackbar('Logged in Successfully.');
      if (route.query?.code) {
        await handleInviteRouteInReactComponent(route, navigate);
        return;
      } else if (redirect) {
        const path = decodeURIComponent(redirect.toString());
        navigate(path, { state: { from: 'verify' } });
        return;
      } else {
        navigate('/');
        return;
      }
    }
  } catch (err) {
    if (axios.isAxiosError(err)) {
      snackbarStore.triggerSnackbar('Invalid code.', 2500, 'error');
    } else {
      snackbarStore.triggerSnackbar(err as string, 2500, 'error');
    }
  }
  loading.value = false;
};
</script>
