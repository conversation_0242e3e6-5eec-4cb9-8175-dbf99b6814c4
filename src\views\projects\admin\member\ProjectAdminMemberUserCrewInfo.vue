<template>
  <section aria-labelledby="applicant-information-title">
    <div class="shadow sm:rounded-lg bg-white dark:bg-gray-900">
      <div
        class="px-4 py-5 sm:px-6 bg-gray-50 dark:bg-gray-700 sm:rounded-t-lg"
      >
        <h2
          id="applicant-information-title"
          class="text-lg font-medium leading-6"
        >
          User Crew Information
        </h2>
      </div>
      <div
        class="border-t border-gray-200 dark:border-gray-600 px-4 py-5 sm:px-6"
      >
        <dl
          v-if="userCrew && userCrew.id"
          class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"
        >
          <div
            v-for="(detail, detailIndex) in detailsList"
            class="sm:col-span-1"
            :key="detailIndex"
          >
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
              {{ detail.label }}
            </dt>
            <dd class="mt-1 text-sm">
              {{ detail.value }}
            </dd>
          </div>

          <div class="sm:col-span-1">
            <dt
              class="flex text-sm font-medium text-gray-500 dark:text-gray-400"
            >
              Social Security Number
              <EyeIcon
                v-if="hideSSN"
                class="ml-1 w-5 h-5 cursor-pointer text-indigo-500 hover:text-indigo-600 dark:text-indigo-400 dark:hover:text-indigo-300"
                @click="hideSSN = false"
              />
              <EyeSlashIcon
                v-else
                class="ml-1 w-5 h-5 cursor-pointer text-indigo-500 hover:text-indigo-600 dark:text-indigo-400 dark:hover:text-indigo-300"
                @click="hideSSN = true"
              />
            </dt>
            <dd class="mt-1 text-sm">
              <h2 class="text-l text-gray-700 dark:text-gray-400 mr-2">
                {{ hideSSN ? '***-**-****' : formattedSSN }}
              </h2>
            </dd>
          </div>

          <!-- <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
              Unions
            </dt>
            <dd class="mt-1 text-sm">
              <div class="flex items-center space-x-2">
                <div
                  v-for="(union, unionIndex) in userCrew.unions"
                  :key="`union-${unionIndex}`"
                  class="rounded-full bg-green-100 hover:bg-green-200 px-2 text-xs font-semibold leading-5 text-green-800 cursor-pointer"
                >
                  {{ union.name }}
                </div>
              </div>
            </dd>
          </div> -->
        </dl>
        <div v-else>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            User has not filled out crew info yet.
          </p>
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { getUserCrewByUserId } from '@/services/users';
import type ProjectMember from '@/types/ProjectMember';
import type UserCrew from '@/types/UserCrew';
import { formatSSN } from '@/utils/ssn';
import { EyeIcon, EyeSlashIcon } from '@heroicons/vue/24/solid';
import { defineComponent, type PropType } from 'vue';

export default defineComponent({
  components: { EyeIcon, EyeSlashIcon },
  props: {
    projectMember: { type: Object as PropType<ProjectMember>, required: true },
  },
  data() {
    return {
      hideSSN: true,
      userCrew: null as UserCrew | null,
    };
  },
  computed: {
    detailsList(): { label: string; value: string }[] {
      return [
        {
          label: 'Date of Birth',
          value: this.formattedDateOfBirth,
        },
        {
          label: 'Gender',
          value: this.gender,
        },
        {
          label: 'Ethnicity',
          value: this.ethnicity,
        },
        {
          label: 'Citizenship Status',
          value: this.citizenshipStatus,
        },
        {
          label: 'Phone',
          value: this.phone,
        },
      ];
    },
    formattedSSN(): string {
      if (!this.userCrew) return '';
      return formatSSN(this.userCrew?.socialSecurityNumber) || '';
    },
    phone(): string {
      const { phone } = this.userCrew?.user || {};
      return phone || '';
    },
    formattedDateOfBirth(): string {
      return this.userCrew?.dateOfBirth?.toFormat('MM/dd/yy') || '';
    },
    gender(): string {
      return this.userCrew?.gender?.name;
    },
    ethnicity(): string {
      return this.userCrew?.ethnicity?.name;
    },
    citizenshipStatus(): string {
      return this.userCrew?.citizenshipStatus?.name;
    },
  },
  methods: {
    async load() {
      const { data: userCrew } = await getUserCrewByUserId(
        this.projectMember.userId,
      );
      this.userCrew = userCrew;
    },
  },
  async mounted() {
    await this.load();
  },
});
</script>
