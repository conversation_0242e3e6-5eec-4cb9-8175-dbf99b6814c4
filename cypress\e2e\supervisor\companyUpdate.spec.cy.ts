import { faker } from '@faker-js/faker';
import CompanyPage from '../../pageObjects/company/companyPage';
import CompanyList from '../../pageObjects/company/companyList';
import {
  interceptCreateCompany,
  interceptEditCompany,
} from '../../support/apiCompanyInterceptors';

// ----------- Constants -----------
const companyPhone = '**********';
const newCompanyPhone = '*********';
const state = 'California';
const zipCode = '90001';
const address = '2300 Empire Avenue';
const city = 'Los Angeles';
const newCity = 'Burbank';
const taxClassification = 'Individual/sole proprietor or single-member LLC';
const payFrequency = 'Weekly';
const payDay = 'Monday';

describe('User Supervisor - Company Update', () => {
  const companyList = new CompanyList();
  const companyPage = new CompanyPage();

  beforeEach(() => {
    // ----------- Arrange: Login as Supervisor -----------
    cy.log('Arrange: Login as Supervisor');
    cy.loginWithSession(
      Cypress.env('PHONE_NUMBER'),
      Cypress.env('OTC_CODE'),
      Cypress.env('BASE_URL'),
      'supervisor',
    );
  });

  it('Verify Supervisor is able to edit a Client/Company admin', () => {
    const companyName = `TEST-${faker.company.name()}`;
    const newCompanyName = `${faker.company.name()}`;

    // ----------- Arrange: Visit Companies page -----------
    cy.log('Arrange: Visit Companies page');
    cy.visit(`${Cypress.env('BASE_URL')}companies`);

    // ----------- Arrange: Intercept company create and edit API -----------
    cy.log('Arrange: Intercept company create and edit API');
    interceptCreateCompany();
    interceptEditCompany();

    // ----------- Action: Click create company button -----------
    cy.log('Action: Click create company button');
    companyList.clickCreateCompanyButton();

    // ----------- Action: Fill out company form with initial data -----------
    cy.log('Action: Fill out company form with initial data');
    companyPage.fillOutCompanyForm(
      companyName,
      companyPhone,
      address,
      city,
      state,
      zipCode,
      taxClassification,
      payFrequency,
      payDay,
    );

    // ----------- Action: Submit create company -----------
    cy.log('Action: Submit create company');
    companyPage.submitCreateCompany();

    // ----------- Assert: Validate company was created -----------
    cy.log('Assert: Validate company was created');
    cy.wait('@createdCompany').then((interception) => {
      expect(interception.response?.statusCode).to.equal(200);
      const companyId = interception.response?.body.id;

      // ----------- Action: Go to company details and click edit -----------
      cy.log('Action: Go to company details and click edit');
      companyList.goToProjectDetails(companyName);
      companyList.clickEditCompanyButton();
      cy.visit(`${Cypress.env('BASE_URL')}companies/${companyId}/edit`);

      // ----------- Action: Fill out company form with new data -----------
      cy.log('Action: Fill out company form with new data');
      companyPage.fillOutCompanyForm(
        newCompanyName,
        newCompanyPhone,
        address,
        newCity,
        state,
        zipCode,
        taxClassification,
        payFrequency,
        payDay,
      );

      // ----------- Action: Submit edited company -----------
      cy.log('Action: Submit edited company');
      companyPage.submitCreateCompany();

      // ----------- Assert: Validate company was edited -----------
      cy.log('Assert: Validate company was edited');
      cy.wait('@editedCompany').then((interception) => {
        //Api Asserts
        expect(interception.response?.statusCode).to.equal(200);
        expect(interception.response?.body.name).to.not.equal('');
        expect(interception.response?.body.phone).to.not.be.equal('');
        expect(interception.response?.body.isActive).to.equal(true);
        expect(interception.response?.body.taxClassificationId).to.equal(1);
        expect(interception.response?.body.payDayId).to.equal(1);
        expect(interception.response?.body.payDayFrequencyId).to.equal(1);
      });
    });
  });
});
