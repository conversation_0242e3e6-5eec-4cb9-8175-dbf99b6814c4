<template>
  <div class="relative flex items-center min-h-screen flex-col overflow-hidden">
    <div class="m-auto mx-2 max-w-lg">
      <h2 class="text-center text-3xl font-extrabold leading-9 mb-6">
        Personal Information
      </h2>
      <div>
        <div class="flex space-x-2">
          <TextInput
            class="basis-1/2"
            label="First Name*"
            v-model="onboarding.firstName"
            type="name"
            auto-complete="off"
          />
          <TextInput
            class="basis-1/2"
            label="Middle Name"
            v-model="onboarding.middleName"
            type="name"
            auto-complete="off"
          />
        </div>
        <TextInput
          class="basis-1"
          label="Last Name*"
          v-model="onboarding.lastName"
          type="name"
          auto-complete="off"
        />
        <TextInput
          class="grow"
          label="Email*"
          v-model="onboarding.email"
          type="email"
          @keyup.enter="registerUser"
        />
        <div class="flex justify-center">
          <Button class="mt-4" @click="registerUser" :loading="loading">
            Enter
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { register } from '@/services/auth';
import { getInvite } from '@/services/users';
import type Onboarding from '@/types/Onboarding';
import type ProjectMemberInvite from '@/types/ProjectMemberInvite';
import { handleInviteRouteInReactRouter } from '@/utils/invite';
import axios from 'axios';
import { onMounted, type PropType, ref, type Ref } from 'vue';
import Button from '../../components/library/Button.vue';
import TextInput from '../../components/library/TextInput.vue';
import AuthStore from '@/reactComponents/stores/auth';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';

const props = defineProps({
  route: {
    type: Object as PropType<ParsedRoute>,
    required: true,
  },
  navigate: {
    type: Function,
    required: true,
  },
});

const onboarding = ref<Partial<Onboarding>>({
  encryptedPhone: '',
  firstName: '',
  lastName: '',
  email: '',
});
const invite: Ref<ProjectMemberInvite | null> = ref(null);
const code: Ref<string> = ref('');

const loading: Ref<boolean> = ref(false);

const registerUser = async () => {
  try {
    loading.value = true;
    const { data: user } = await register(onboarding.value);
    AuthStore.login(user);
    goToNextPage();
    loading.value = false;
  } catch (err) {
    loading.value = false;
    if (axios.isAxiosError(err)) {
      const msg = err.response?.data?.['errors']?.[0]?.message;
      SnackbarStore.triggerSnackbar(msg, 2500, 'error');
    } else {
      SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
    }
  }
};

const goToNextPage = async () => {
  const { projectHash } = invite.value || {};
  if (projectHash) {
    await handleInviteRouteInReactRouter(props.route, props.navigate);
    return;
  }
  const redirect = props.route?.query?.redirect;

  if (redirect) {
    props.navigate(decodeURIComponent(redirect.toString()));
    return;
  } else {
    props.navigate('/');
    return;
  }
};

onMounted(async () => {
  onboarding.value.encryptedPhone = decodeURI(
    props.route.params.encryptedPhone as string,
  );
  await parseInviteData();
});

const parseInviteData = async () => {
  const inviteCode = props.route.query?.code as string;
  if (!inviteCode) {
    return;
  }
  code.value = inviteCode;
  const inviteData = (await getInvite(code.value)).data;
  invite.value = inviteData;
};
</script>
