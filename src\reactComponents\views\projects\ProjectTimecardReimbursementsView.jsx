import { useRef, useImperativeHandle, forwardRef } from 'react';
import { applyPureVueInReact } from 'veaury';
import ProjectTimecardReimbursementsViewVue from '../../../views/projects/ProjectTimecardReimbursementsView.vue';
import { useAuth, useReactRouter } from '../../AppHooks';
import { useOutletContext } from 'react-router';

const ReactProjectTimecardReimbursementsView = applyPureVueInReact(
  ProjectTimecardReimbursementsViewVue,
);

const ProjectTimecardReimbursementsView = forwardRef(() => {
  useAuth();
  const { navigate, route } = useReactRouter();
  const context = useOutletContext();
  const vueRef = useRef();

  useImperativeHandle(context.ref, () => ({
    vueRef: vueRef.current.__veauryVueRef__,
  }));

  return (
    <ReactProjectTimecardReimbursementsView
      ref={vueRef}
      project={context.project}
      editDisabled={context.editDisabled}
      componentLoaded={context.componentLoaded}
      route={route}
      navigate={navigate}
    />
  );
});

export default ProjectTimecardReimbursementsView;
