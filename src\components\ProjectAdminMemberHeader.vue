<template>
  <div :class="className">
    <div class="flex items-center space-x-2">
      <div class="flex-shrink-0">
        <div class="relative">
          <Avatar
            :firstName="user.firstName"
            :lastName="user.lastName"
            size="lg"
            bgColor="white"
          />
          <span
            class="absolute inset-0 rounded-full shadow-inner"
            aria-hidden="true"
          />
        </div>
      </div>
      <div>
        <div class="flex justify-start items-center space-x-2">
          <h1 v-if="fullName" class="text-2xl font-bold">
            {{ fullName }}
          </h1>
          <div v-if="!projectMember?.isActive" class="flex justify-start pt-1">
            <div class="bg-red-200 rounded-xl px-1 mx-1'">Deleted</div>
          </div>
        </div>
        <h2 v-if="phone" class="text-xl text-zinc-700 dark:text-zinc-400">
          {{ phone }}
        </h2>
        <div v-if="ssn" class="flex">
          <h2 class="text-l text-gray-700 dark:text-gray-400 mr-2">
            <span class="font-bold">SSN:</span>
            {{ hideSSN ? '***-**-****' : ssn }}
          </h2>
          <EyeIcon
            v-if="hideSSN"
            class="w-5 h-5 cursor-pointer text-indigo-500 hover:text-indigo-600 dark:text-indigo-400 dark:hover:text-indigo-300"
            @click="hideSSN = false"
          />
          <EyeSlashIcon
            v-else
            class="w-5 h-5 cursor-pointer text-indigo-500 hover:text-indigo-600 dark:text-indigo-400 dark:hover:text-indigo-300"
            @click="hideSSN = true"
          />
        </div>
      </div>
      <div class="flex grow" />
      <div class="text-sm font-light pr-3">
        <div class="text-sm font-bold">Project Role</div>
        <div class="text-lg font-light">
          {{ projectRole }}
        </div>
      </div>
      <Button v-if="!isProjectAdmin" size="sm" @click="makeAdminModal = true">
        Make Project Admin
      </Button>
      <Modal v-model="makeAdminModal" @close="makeAdminModal = false">
        <div>
          <h1 class="text-xl font-bold">Make Project Admin</h1>
          <p>Are you sure you want to make this project member an admin?</p>
          <div class="flex justify-end space-x-2 pt-4">
            <Button size="sm" @click="makeAdminModal = false" color="gray">
              Cancel
            </Button>
            <Button
              size="sm"
              color="primary"
              :loading="loadAdmin"
              @click="upgradeToProjectAdmin"
              class="mr-2"
            >
              Make Admin
            </Button>
          </div>
        </div>
      </Modal>
      <Button
        v-if="projectMember?.isActive"
        class="error"
        size="sm"
        color="error"
        @click="deleteModal = true"
      >
        <div class="flex items-center space-x-1 stroke-gray-600">
          <Icon name="trash" class="w-5 h-5" />
          <div>Delete</div>
        </div>
      </Button>
      <Button
        v-else
        class="error"
        size="sm"
        color="gray"
        @click="reactivate.modal = true"
      >
        <div class="flex items-center">
          <ArrowUturnUpIcon class="w-5 h-5 mr-1" />
          <div>Reactivate</div>
        </div>
      </Button>
      <Modal v-model="deleteModal" @close="deleteModal = false">
        <div>
          <h1 class="text-xl font-bold">Delete Project Member</h1>
          <p>Are you sure you want to delete this project member?</p>
          <div class="flex justify-end space-x-2 pt-4">
            <Button size="sm" @click="deleteModal = false"> Cancel </Button>
            <Button
              size="sm"
              color="error"
              :loading="loadDeletion"
              @click="deleteMember"
              class="mr-2"
            >
              Delete
            </Button>
          </div>
        </div>
      </Modal>
      <Modal v-model="reactivate.modal" @close="reactivate.modal = false">
        <div>
          <h1 class="text-xl font-bold">Reactivate Project Member</h1>
          <p>Are you sure you want to reactivate this project member?</p>
          <div class="flex justify-end space-x-2 pt-4">
            <Button size="sm" color="gray" @click="reactivate.modal = false">
              Cancel
            </Button>
            <Button
              size="sm"
              color="primary"
              :loading="reactivate.loading"
              @click="reactivateMember"
              class="mr-2"
            >
              Reactivate
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  </div>
</template>

<script lang="ts">
//TODO - UNUSED FPS-1388
import Icon from '@/components/Icon.vue';
import Avatar from '@/components/library/Avatar.vue';
import Button from '@/components/library/Button.vue';
import Modal from '@/components/library/Modal.vue';
import {
  deleteProjectMember,
  makeAdmin,
  reactivateProjectMember,
} from '@/services/project-members';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import type Project from '@/types/Project';
import type ProjectMember from '@/types/ProjectMember';
import type User from '@/types/User';
import type UserCrew from '@/types/UserCrew';
import { ProjectMemberTypeId } from '@/utils/enum';
import { formatSSN } from '@/utils/ssn';
import {
  ArrowUturnUpIcon,
  EyeIcon,
  EyeSlashIcon,
  TrashIcon,
} from '@heroicons/vue/24/solid';
import axios from 'axios';
import { defineComponent, type PropType } from 'vue';

export default defineComponent({
  components: {
    Modal,
    EyeIcon,
    EyeSlashIcon,
    Button,
    TrashIcon,
    ArrowUturnUpIcon,
    Icon,
    Avatar,
  },
  props: {
    userCrew: {
      type: Object as PropType<UserCrew>,
      required: true,
    },
    projectMember: {
      type: Object as PropType<ProjectMember>,
      required: true,
    },
    project: {
      type: Object as PropType<Project>,
      required: true,
    },
    user: {
      type: Object as PropType<User>,
      required: true,
    },
    refresh: {
      type: Function as PropType<() => void>,
      required: false,
    },
    navigate: {
      type: Function as PropType<Function>,
      required: false,
    },
    className: {
      type: String as PropType<string>,
      required: false,
    },
  },
  data() {
    return {
      hideSSN: true,
      formatSSN,
      deleteModal: false,
      loadDeletion: false,
      makeAdminModal: false,
      loadAdmin: false,
      reactivate: {
        modal: false,
        loading: false,
      },
    };
  },
  computed: {
    fullName(): string {
      const { firstName, lastName } = this.user || {};
      if (!firstName || !lastName) return '';
      return `${firstName} ${lastName}`;
    },
    phone(): string {
      return this.user?.phone || '';
    },
    ssn(): string {
      const ssn = this.userCrew?.socialSecurityNumber;
      if (!ssn) {
        return '';
      }
      return formatSSN(ssn) || '';
    },
    projectRole(): string {
      return this.projectMember?.projectMemberType?.name || '';
    },
    isProjectAdmin(): boolean {
      return (
        this.projectMember?.projectMemberType.id === ProjectMemberTypeId.Admin
      );
    },
  },
  methods: {
    callRefresh() {
      if (this.$props.refresh) this.$props.refresh();
    },
    async deleteMember() {
      this.loadDeletion = true;
      try {
        await deleteProjectMember(this.projectMember.id!);
        this.deleteModal = false;
        this.callRefresh();
        SnackbarStore.triggerSnackbar(
          'Project Member deleted.',
          2500,
          'success',
        );
        if (this.navigate) {
          this.navigate({
            pathname: `/projects/${this.project.hashId}/admin/members`,
          });
        }
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
      }
      this.loadDeletion = false;
    },
    async reactivateMember() {
      this.reactivate.loading = true;
      try {
        await reactivateProjectMember(this.projectMember.id!);
        this.callRefresh();
        SnackbarStore.triggerSnackbar(
          'Project Member reactivated.',
          2500,
          'success',
        );
        this.reactivate.modal = false;
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
      }
      this.reactivate.loading = false;
    },
    async upgradeToProjectAdmin() {
      this.loadAdmin = true;
      try {
        await makeAdmin(this.projectMember.id!);
        this.callRefresh();
        SnackbarStore.triggerSnackbar(
          'Project Member upgraded to admin.',
          2500,
          'success',
        );
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
      }
      this.loadAdmin = false;
      this.makeAdminModal = false;
    },
  },
});
</script>
