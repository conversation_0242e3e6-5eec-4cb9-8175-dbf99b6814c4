import { applyPureVueInReact } from 'veaury';
import CompanyDashboardViewVue from '../../../views/companies/CompanyDashboardView.vue';
import { useAuth } from '../../AppHooks';
import { useOutletContext } from 'react-router';
const ReactCompanyDashboardView = applyPureVueInReact(CompanyDashboardViewVue);

const CompanyDashboardView = () => {
  useAuth();
  const context = useOutletContext();
  return <ReactCompanyDashboardView company={context.company} />;
};

export default CompanyDashboardView;
