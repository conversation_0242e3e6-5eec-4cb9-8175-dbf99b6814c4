import axios, { type AxiosResponse } from 'axios';
import { coreBaseUrl } from '../utils/constants';

export const deleteProjectDocumentTemplate = async (
  id: number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/project-document-templates/${id}/`;
  const response = await axios.delete(url, { withCredentials: true });
  return response;
};

export const enableDocumentTemplate = async (
  documentTemplateId: number,
  projectId: number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/project-document-templates/${documentTemplateId}/enable`;
  const response = await axios.post(
    url,
    { projectId },
    { withCredentials: true },
  );
  return response;
};

export const disableDocumentTemplate = async (
  documentTemplateId: number,
  projectId: number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/project-document-templates/${documentTemplateId}/disable`;
  const response = await axios.post(
    url,
    { projectId },
    { withCredentials: true },
  );
  return response;
};
