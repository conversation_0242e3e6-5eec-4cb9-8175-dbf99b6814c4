<template>
  <div class="flow-root m-3">
    <ul role="list" class="-mb-8">
      <li v-for="(step, eventIdx) in modelValue" :key="step.id">
        <div class="relative pb-8">
          <span
            v-if="eventIdx !== modelValue.length - 1"
            class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-700"
            aria-hidden="true"
          />
          <div class="relative flex space-x-3">
            <div>
              <span
                :class="[
                  step.iconBackground,
                  'h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white dark:ring-gray-700',
                ]"
              >
                <component
                  :is="step.icon"
                  class="h-5 w-5 text-white"
                  aria-hidden="true"
                />
              </span>
            </div>
            <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
              <div>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ step.content }}
                  <a
                    :href="step.href"
                    class="font-medium text-gray-900 dark:text-gray-200"
                    >{{ step.target }}</a
                  ><br />
                  <slot name="subcontent" :data="step" />
                  <slot name="action" :data="step.data" />
                </p>
              </div>
              <div
                class="text-right text-sm whitespace-nowrap text-gray-500 dark:text-gray-400"
              >
                <time :datetime="step.datetime">{{ step.date }}</time>
              </div>
            </div>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  props: ['modelValue'],
  name: 'Timeline',
});
</script>
