import PropTypes from 'prop-types';
import {
  ListItemIcon,
  ListItemText,
  MenuItem as MuiMenuItem,
} from '@mui/material';

const MenuItem = (props) => {
  const { children, label = null, icon = null, ...restProps } = props;

  return (
    <MuiMenuItem {...restProps}>
      {icon && (
        <ListItemIcon sx={{ '& .MuiIcon-root': { fontSize: 10 } }}>
          {icon}
        </ListItemIcon>
      )}
      {label && (
        <ListItemText data-testid={`label-${label}`}>{label}</ListItemText>
      )}
      {children}
    </MuiMenuItem>
  );
};

MenuItem.propTypes = {
  label: PropTypes.string,
  icon: PropTypes.node,
  children: PropTypes.node,
};

export default MenuItem;
