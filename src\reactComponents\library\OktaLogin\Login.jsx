import React from 'react';
import PropTypes from 'prop-types';

import { Box } from '@/reactComponents/library';
import { PasswordlessFlow } from './Flow';

const styles = {
  root: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    gap: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
};

const Login = ({ onSuccess, onError }) => {
  return (
    <Box sx={styles.root}>
      <PasswordlessFlow onError={onError} onSuccess={onSuccess} />
    </Box>
  );
};
Login.propTypes = {
  onSuccess: PropTypes.func.isRequired,
  onError: PropTypes.func.isRequired,
};

export default Login;
