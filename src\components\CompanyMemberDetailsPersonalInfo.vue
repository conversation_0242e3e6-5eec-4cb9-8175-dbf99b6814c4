<template>
  <section aria-labelledby="applicant-information-title">
    <div class="shadow sm:rounded-lg bg-white dark:bg-gray-900">
      <div
        class="px-4 py-5 sm:px-6 bg-gray-50 dark:bg-gray-700 sm:rounded-t-lg"
      >
        <h2
          id="applicant-information-title"
          class="text-lg font-medium leading-6"
        >
          Personal Information
        </h2>
      </div>
      <div
        class="border-t border-gray-200 dark:border-gray-600 px-4 py-5 sm:px-6"
      >
        <dl
          v-if="member && member.id"
          class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"
        >
          <div
            v-for="(detail, detailIndex) in detailsList"
            class="sm:col-span-1"
            :key="detailIndex"
          >
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
              {{ detail.label }}
            </dt>
            <dd class="mt-1 text-sm">
              {{ detail.value }}
            </dd>
          </div>
        </dl>
        <div v-else>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            User has not filled out info yet.
          </p>
        </div>
        <div class="py-4">
          <Button
            class="mt-2"
            @click="removeCompanyMemberModal = true"
            size="sm"
            color="error"
            >Remove Member</Button
          >
          <Modal v-model="removeCompanyMemberModal">
            <h2 class="font-semibold mb-2">Remove Company Member</h2>
            <div class="flex justify-start space-x-2">
              <Button
                class="mt-2"
                @click="removeCompanyMember"
                size="sm"
                color="error"
                :loading="removingCompanyMember"
                >Remove</Button
              >
              <Button
                class="mt-2"
                @click="removeCompanyMemberModal = false"
                size="sm"
                color="gray"
                >Cancel</Button
              >
            </div>
          </Modal>
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import Button from '@/components/library/Button.vue';
import Modal from '@/components/library/Modal.vue';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import { deleteProductionCompanyMember } from '@/services/production-company';
import type ProductionCompanyMember from '@/types/ProductionCompanyMember';
import type { SnackType } from '@/types/Snackbar';
import axios from 'axios';
import { DateTime } from 'luxon';
import { defineComponent, inject, type PropType } from 'vue';

export default defineComponent({
  components: { Button, Modal },
  props: {
    companyId: {
      type: String,
      required: true,
    },
    member: {
      type: Object as PropType<ProductionCompanyMember>,
      required: true,
    },
  },
  setup(props, ctx) {
    const navigate = inject('navigate') as Function;
    return { navigate };
  },
  data() {
    return {
      removeCompanyMemberModal: false,
      removingCompanyMember: false,
    };
  },
  methods: {
    triggerSnackbar(message: string, timeout: number, type: SnackType) {
      SnackbarStore.triggerSnackbar(message, timeout, type);
    },
    async removeCompanyMember() {
      if (this.removingCompanyMember) return;
      this.removingCompanyMember = true;
      try {
        await deleteProductionCompanyMember(
          this.companyId,
          this.member.id.toString(),
        );
        this.triggerSnackbar(
          'Successfully removed company member.',
          2500,
          'success',
        );
        this.navigate({
          pathname: `/companies/${this.companyId}/members`,
        });
      } catch (err: any) {
        const errorMessage = axios.isAxiosError(err)
          ? err.response?.data?.['errors']?.[0]?.message
          : err.toString();
        this.triggerSnackbar(errorMessage, 2500, 'error');
      } finally {
        this.removingCompanyMember = false;
        this.removeCompanyMemberModal = false;
      }
    },
  },
  computed: {
    detailsList(): { label: string; value: string }[] {
      return [
        {
          label: 'First Name',
          value: this.member?.user?.firstName,
        },
        {
          label: 'Last Name',
          value: this.member?.user?.lastName,
        },
        {
          label: 'Phone',
          value: this.phone,
        },
        {
          label: 'Added To Company',
          value: this.formattedDateAddedToCompany,
        },
      ];
    },
    phone(): string {
      const { phone } = this.member?.user || {};
      return phone || '';
    },
    formattedDateAddedToCompany(): string {
      return this.member?.createdAt?.toLocaleString(DateTime.DATE_FULL) || '';
    },
  },
});
</script>
