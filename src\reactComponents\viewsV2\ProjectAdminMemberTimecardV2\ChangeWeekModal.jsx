import React from 'react';
import PropTypes from 'prop-types';

import { Autocomplete, Box, Modal, Text } from '@/reactComponents/library';
import { setTimeZone } from '@/reactComponents/utils/dateTime';

import { getAvailablePayPeriods } from '@/services/project-members';
import { snackbarErr } from '@/reactComponents/library/Snackbar';

const ChangeWeekModal = (props) => {
  const { open, onClose, member, timecard, onSubmit } = props;
  const { payPeriod: { id: timecardPayPeriodId } = {} } = timecard || {};

  const [availablePayPeriods, setAvailablePayPeriods] = React.useState([]);
  const [selectedWeek, setSelectedWeek] = React.useState(null);
  const [oldPayPeriod, setOldPayPeriod] = React.useState(null);

  const fetchAvailablePayPeriods = React.useCallback(async () => {
    let { data: payPeriods } = await getAvailablePayPeriods(member.id);
    payPeriods.forEach((pp) => {
      pp.startsAt = setTimeZone(pp.startsAt, 'UTC');
      pp.endsAt = setTimeZone(pp.endsAt, 'UTC');
    });

    if (oldPayPeriod) {
      const oldInList = payPeriods.find((pp) => pp.id === oldPayPeriod.id);

      payPeriods = payPeriods.filter((pp) => pp.id !== timecardPayPeriodId);

      if (!oldInList) {
        payPeriods.push(oldPayPeriod);
      }
    }
    payPeriods.sort((a, b) => a.startsAt - b.startsAt);
    setAvailablePayPeriods(payPeriods);
    setSelectedWeek(null);
  }, [member, oldPayPeriod, timecardPayPeriodId]);

  React.useEffect(() => {
    if (!open) {
      setSelectedWeek(null);
    } else {
      fetchAvailablePayPeriods();
    }
  }, [open, fetchAvailablePayPeriods]);

  const handleChangeWeek = async () => {
    if (!selectedWeek) {
      snackbarErr('Please select a new pay period.');
      return;
    }
    const oldPay = { ...timecard.payPeriod, isOldPayPeriod: true };

    setOldPayPeriod(oldPay);

    onSubmit({ newWorkWeek: selectedWeek });
    onClose();
  };

  let onChange = (event, newValue) => {
    setSelectedWeek(newValue);
  };

  const getOptionLabel = (availablePayPeriod) => {
    const startsAt = availablePayPeriod?.startsAt?.toFormat('ccc. MM/dd') || '';
    const endsAt = availablePayPeriod?.endsAt?.toFormat('ccc. MM/dd') || '';
    return `${startsAt} - ${endsAt}`;
  };

  return (
    <Box>
      <Modal
        title={'Change Week'}
        open={open}
        setOpen={() => onClose()}
        onCancel={() => onClose()}
        onSubmit={handleChangeWeek}
        submitText="Change"
      >
        <Box sx={{ width: '350px' }}>
          <Text>
            If the crew member submitted this timecard for the wrong week, you
            can change it here.
          </Text>
          <Box sx={{ margin: '20px 20px 30px 20px' }}>
            <Text sx={{ mb: 1 }}>Change week to</Text>
            <Autocomplete
              value={selectedWeek}
              options={availablePayPeriods}
              getOptionLabel={getOptionLabel}
              onChange={onChange}
              placeholder="Select"
              autoFocus
            />
          </Box>
        </Box>
      </Modal>
    </Box>
  );
};

ChangeWeekModal.propTypes = {
  member: PropTypes.object.isRequired,
  timecard: PropTypes.object.isRequired,
  onSubmit: PropTypes.func.isRequired,
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default ChangeWeekModal;
