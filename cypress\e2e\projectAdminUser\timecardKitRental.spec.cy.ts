/// <reference types="cypress" />

import BatchModals from '../../pageObjects/timecards/batchModals';
import TimecardDetails from '../../pageObjects/timecards/timecardDetails';
import { createTimecardFlowReimbursement } from '../../support/apiFlows/createTimecardFlowReimbursement';
import { deleteTimecard } from '../../support/apiHelpers';
import {
  interceptGetApprovedTimecard,
  interceptProjectTimecards,
} from '../../support/apiTimecardInterceptors';

describe('User Project Admin - Add timecard Kit rental/Mileage', () => {
  const batchModals = new BatchModals();
  const timecardDetails = new TimecardDetails();

  beforeEach(() => {
    // ----------- Arrange: Login as Project Admin -----------
    cy.loginWithSession(
      Cypress.env('PHONE_NUMBER_PROJECT_ADMIN'),
      Cypress.env('OTC_CODE'),
      Cypress.env('BASE_URL'),
      'project-admin',
    );
    // ----------- Arrange: Create a new timecard for the member via API -----------
    const [firstName, lastName] = Cypress.env('employeeName').split(' ');
    createTimecardFlowReimbursement(firstName, lastName);
  });

  afterEach(() => {
    // ----------- Cleanup: Delete the created timecard -----------
    cy.log('Cleanup: Delete the created timecard');
    deleteTimecard(Cypress.env('timecardId'));
  });

  //https://castandcrew.atlassian.net/browse/FPS-1536
  it('Verify Project Admin is able to add Kit rental/Mileage in timecards page', () => {
    // ----------- Arrange: Visit the Project Timecards Page -----------
    cy.log('Arrange: Visit the Project Timecards Page ');
    cy.visit(
      `${Cypress.env('BASE_URL')}projects/${Cypress.env(
        'projectHashId',
      )}/admin/time`,
    );

    // ----------- Arrange: Get unbatched timecards and set up interceptors -----------
    cy.log('Arrange: Get unbatched timecards and set up interceptors');
    batchModals.unbatchedTimecard();
    interceptProjectTimecards(Cypress.env('projectId'));

    // ----------- Action: Wait for timecards to load and open the created timecard -----------
    cy.log('Action: Wait for timecards to load and open the created timecard');
    cy.wait('@getTimecards').then(() => {
      timecardDetails.navigateToTimecard(Cypress.env('timecardId'));
      cy.log('Timecard Day ID:', Cypress.env('timecardDayIds')?.[2]);

      // ----------- Action: Fill out reimbursement, mileage, and kit rental details -----------
      cy.log('Action: Fill out reimbursement, mileage, and kit rental details');
      timecardDetails.fillOutReimbursementDetails();
      timecardDetails.fillAddMileageLog();
      timecardDetails.fillKitRentalDetails(Cypress.env('timecardDayIds')?.[1]);

      // ----------- Action: Save and approve the timecard -----------
      cy.log('Action: Save and approve the timecard');
      timecardDetails.save();
      timecardDetails.approve();
      cy.signatureApproval();

      // ----------- Assert: Validate the final timecard status is "Approved" -----------
      cy.log('Assert: Validate the final timecard status is "Approved"');
      interceptGetApprovedTimecard();
      cy.wait('@getApprovedTimecard').then((getIntercept) => {
        const statusName: string = getIntercept.response?.body?.status?.name;
        cy.log(`Final timecard status: ${statusName}`);
        expect(statusName).to.eq('Approved');
        cy.log(
          'Success: Verify Project Admin is able to add Kit rental/Mileage in timecards page',
        );
      });
    });
  });
});
