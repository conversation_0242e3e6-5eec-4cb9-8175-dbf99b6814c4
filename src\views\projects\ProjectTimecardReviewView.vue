<template>
  <div class="flex justify-center mx-auto w-full py-2 px-5">
    <div class="max-w-7xl">
      <h2 class="text-xl font-bold mb-3">Review</h2>
      <div v-if="!editDisabled">
        <p class="mb-2">
          Your timecard is complete! If you'd like to submit your timecard,
          click the submit button below.
        </p>
        <p class="mb-2">
          By submitting your timecard, your production supervisor will be able
          to approve or request changes.
        </p>
        <p>
          If you are not quite done with your timecard and want to work on it
          some more, click the save and exit button instead.
        </p>
        <div class="flex justify-center pt-5">
          <div>
            <div class="dark:text-zinc-300">Sign:</div>
            <SignatureArea ref="signaturePad" />
          </div>
        </div>
      </div>
      <div v-else>
        <p class="mb-2">
          Your timecard has already been submitted. click below to return.
        </p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Button from '@/components/library/Button.vue';
import SignatureArea from '@/components/library/SignatureArea.vue';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';
import { submitTimecard } from '@/services/timecards';
import { useSnackbarStore } from '@/stores/snackbar';
import SnackbarStore from '@/reactComponents/stores/snackbar';

import type { SnackType } from '@/types/Snackbar';

import type Project from '@/types/Project';
import axios from 'axios';
import { DateTime } from 'luxon';
import { defineComponent, type PropType } from 'vue';

export default defineComponent({
  props: {
    project: {
      type: Object as PropType<Project>,
      required: true,
    },
    editDisabled: {
      type: Boolean,
      default: false,
    },
    route: {
      type: Object as PropType<ParsedRoute>,
      required: true,
    },
    navigate: {
      type: Function as PropType<Function>,
      required: true,
    },
    componentLoaded: {
      type: Function as PropType<Function>,
      required: true,
    },
    refresh: {
      type: Function as PropType<Function>,
      required: true,
    },
  },
  components: {
    Button,
    SignatureArea,
  },
  data() {
    return {};
  },
  methods: {
    triggerSnackbar(message: string, timeout: number, type: SnackType) {
      SnackbarStore.triggerSnackbar(message, timeout, type);
    },
    async save() {
      // do nothing
    },
    async saveAndContinue() {
      const signatureRef = this.$refs.signaturePad as any;
      if (signatureRef.isEmpty()) {
        this.triggerSnackbar(
          'Please sign your timecard before submitting',
          3500,
          'error',
        );
        throw Error('Please sign your timecard before submitting.');
      }
      await this.save();
      const signature = signatureRef.getSignatureImage();
      if (this.editDisabled) {
        this.navigate({
          pathname: `/projects/${this.project.hashId}/timecards`,
        });
      }
      const timeZone = DateTime.now().zoneName;
      if (!timeZone) {
        throw new Error('Could not determine time zone');
      }
      try {
        await submitTimecard(
          this.route.params.timecardId as string,
          signature,
          timeZone,
        );
        this.refresh();
        const curPath = this.route.location.pathname;
        this.navigate({
          pathname: curPath.replace('review', 'complete'),
        });
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          this.triggerSnackbar(msg, 2500, 'error');
        } else {
          this.triggerSnackbar(err as string, 2500, 'error');
        }
        throw err;
      }
    },
  },
  async mounted() {
    this.componentLoaded();
  },
});
</script>

<style></style>
