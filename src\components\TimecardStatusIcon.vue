<template>
  <div>
    <Badge
      v-if="timecard.status.key == 'in_progress'"
      :text="timecard.status.name"
      type="blue"
    />
    <Badge
      v-if="timecard.status.key == 'submitted'"
      :text="timecard.status.name"
      type="warning"
    />
    <Badge
      v-if="timecard.status.key == 'production_supervisor_requested_changes'"
      :text="timecard.status.name"
      type="error"
    />
    <Badge
      v-if="timecard.status.key == 'production_supervisor_approved'"
      :text="timecard.status.name"
      type="success"
    />
    <Badge
      v-if="timecard.status.key == 'processing'"
      :text="timecard.status.name"
      type="gray"
    />
    <Badge
      v-if="timecard.status.key == 'paid'"
      :text="timecard.status.name"
      type="gray"
    />
  </div>
</template>

<script lang="ts">
import Icon from '@/components/Icon.vue';
import Badge from '@/components/library/Badge.vue';
import type Timecard from '@/types/Timecard';
import {
  CheckCircleIcon,
  ClockIcon,
  ExclamationCircleIcon,
  PencilSquareIcon,
} from '@heroicons/vue/24/outline';
import { defineComponent, type PropType } from 'vue';

export default defineComponent({
  props: {
    timecard: {
      type: Object as PropType<Timecard>,
      required: true,
    },
  },
  components: {
    CheckCircleIcon,
    PencilSquareIcon,
    ExclamationCircleIcon,
    ClockIcon,
    Badge,
    Icon,
  },
});
</script>
