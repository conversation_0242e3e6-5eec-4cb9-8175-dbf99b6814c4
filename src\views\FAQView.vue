<template>
  <div>
    <div class="mx-auto max-w-7xl px-6 py-24 sm:pt-32 lg:py-40 lg:px-8">
      <div class="lg:grid lg:grid-cols-12 lg:gap-8">
        <div class="lg:col-span-5">
          <h2 class="text-2xl font-bold leading-10 tracking-tight">
            Frequently asked questions
          </h2>
          <p class="mt-4 text-base leading-7 text-gray-600 dark:text-gray-400">
            Can’t find the answer you’re looking for? Reach out to our customer
            support <NAME_EMAIL>
          </p>
        </div>
        <div class="mt-10 lg:col-span-7 lg:mt-0">
          <dl class="space-y-10">
            <div v-for="faq in faqs" :key="faq.question">
              <dt class="text-base font-semibold leading-7">
                {{ faq.question }}
              </dt>
              <dd
                class="mt-2 text-base leading-7 text-gray-600 dark:text-gray-400"
              >
                <p v-html="faq.answer" />
              </dd>
            </div>
          </dl>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const faqs = [
  {
    question: 'What is FPS',
    answer:
      'FPS is a production management software company. We interface with the existing payroll providers to create a seamless experience for crew.',
  },
  {
    question: 'How is my data secured?',
    answer:
      'We use the latest security protocols to ensure your data is safe. We also use the latest encryption protocols to ensure your data is safe. \
      Finally, we work with a company called Drata (<a style="text-decoration:underline" href="https://www.drata.com">drata.com</a>) to continuously monitor our security and ensure we are up to date with the latest \
      trends and protocols.',
  },
];
</script>
