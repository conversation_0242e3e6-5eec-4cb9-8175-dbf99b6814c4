import type { Location, UIMatch, Params } from 'react-router';

interface anyObject {
  [key: string]: any;
}

export interface CustomHandle {
  isProjectAdmin?: boolean;
  authRequired?: boolean;
  editMode?: boolean;
  breadcrumb?: string;
  supervisorView?: boolean;
  [key: string]: any;
}

export interface ParsedRoute {
  query: anyObject;
  params: Params;
  location: Location;
  match: UIMatch<any, CustomHandle>;
}
