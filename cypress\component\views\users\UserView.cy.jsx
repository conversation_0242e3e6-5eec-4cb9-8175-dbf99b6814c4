import { createMemoryRouter, RouterProvider, Outlet } from 'react-router-dom';
import UserView from '@/reactComponents/views/users/UserView';
import UserFormView from '@/reactComponents/views/users/UserFormView';
import PropTypes from 'prop-types';

const TestWrapper = ({ contextValue }) => {
  const router = createMemoryRouter(
    [
      {
        path: '/users/:id',
        element: <Outlet context={contextValue} />,
        children: [{ path: '/users/:id', element: <UserView /> }],
      },
      {
        path: '/users/:id/edit',
        element: <UserFormView />,
      },
    ],
    { initialEntries: ['/users/56'] },
  );
  return <RouterProvider router={router} />;
};

TestWrapper.propTypes = {
  contextValue: PropTypes.object.isRequired,
};

describe('UserView component testing', () => {
  beforeEach(function () {
    cy.fixture('roles').then((roles) => {
      cy.intercept('GET', '**/v2/api/core/roles/list-roles', {
        statusCode: 200,
        body: roles,
      }).as('getRolesList');
    });

    cy.fixture('userRoleAdmin').as('contextData');
    cy.intercept('GET', '**/v2/api/core/users/**', {
      fixture: 'userRoleAdmin.json',
    }).as('getUser');
    cy.mount(<TestWrapper contextValue={this.contextData} />);
  });

  it('should present user name', function () {
    cy.fixture('userRoleAdmin').then((user) => {
      cy.get('[data-testid="userHeader-breadcrumb"]')
        .should('be.visible')
        .should('contain', `${user.firstName} ${user.lastName}`);
      cy.get('[data-testid="userHeader-text"]')
        .should('be.visible')
        .should('contain', `${user.firstName} ${user.lastName}`);
    });
  });

  it('should present View and Dashboard option', function () {
    cy.get('[data-testid="userHeader-views-btn"]').should('be.visible').click();
    cy.get('[data-testid="userHeader-views-menu"]')
      .contains('Dashboard')
      .should('be.visible')
      .click();
  });

  it('should present edit option', function () {
    cy.fixture('userRoleAdmin').then((user) => {
      cy.get('[data-testid="userHeader-edit-btn"]')
        .should('be.visible')
        .scrollIntoView();
      cy.get('[data-testid="userHeader-edit-btn"]').click();
      cy.get('[data-testid="user-name-text"]').should(
        'contain',
        `${user.firstName} ${user.lastName}`,
      );
    });
  });

  it('should allow to cancel the delete option', function () {
    cy.get('[data-testid="userHeader-delete-btn"]')
      .should('be.visible')
      .click();
    cy.get('[data-testid="userHeader-cancel-btn"]')
      .should('be.visible')
      .click();
  });

  it('should allow to confirm the delete option', function () {
    cy.get('[data-testid="userHeader-delete-btn"]')
      .should('be.visible')
      .click();
    cy.get('[data-testid="userHeader-confirmation-btn"]')
      .should('be.visible')
      .click();
  });
});
