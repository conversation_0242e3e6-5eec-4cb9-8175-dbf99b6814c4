import { createPaperWork } from '../apiCreateHelpers';
import { fetchMemberCurrentByProject, triggerPaperWork } from '../apiHelpers';

/**
 * Creates a paper work flow for the current project and user
 * It fetches the member information, start paper work, and creates paper work.
 */
export function startPaperWorkFlow(): Cypress.Chainable<any> {
  return cy
    .then(() => {
      return fetchMemberCurrentByProject();
    })
    .then((project) => {
      return triggerPaperWork(project.id);
    })
    .then(() => {
      return createPaperWork();
    });
}
