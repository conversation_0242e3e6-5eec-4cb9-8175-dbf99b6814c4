import { DateTime } from 'luxon';
import DotsVerticalIcon from '../../assets/icons/DotsVerticalIcon';
import TextsmsOutlinedIcon from '@mui/icons-material/TextsmsOutlined';
import { useEffect, useMemo, useRef, useState } from 'react';
import {
  Box,
  Checkbox,
  IconButton,
  Menu,
  Switch,
  MenuItem,
  Typography,
  Tooltip,
} from '../';
import KeyboardControlBox from './CellKeyboardControl';

export const SwitchCell = (params) => {
  const api = params.api;
  const update = params.update;
  const field = params.colDef.field;
  const rowId = params.row.id;

  const handleChange = (e) => {
    e.stopPropagation();
    update({
      ...params.row,
      field: field,
      value: e.target.checked,
      rowId: rowId,
      isWorkDaysGrid: true,
      api: api,
    });
    api.updateRows([
      {
        ...params.row,
        [field]: e.target.checked,
      },
    ]);
    const editRows = api?.state?.editRows;
    const rowInEditMode = editRows?.[rowId];
    if (e.target.checked) {
      api.startRowEditMode({
        id: rowId,
      });
      api.setCellFocus(rowId, 'lineNumber');
    }
    if (!e.target.checked && rowInEditMode) {
      api.stopRowEditMode({
        id: rowId,
      });
    }
  };

  return (
    <Switch
      tabIndex={0}
      checked={params.value}
      onChange={(e) => {
        handleChange(e);
      }}
      disabled={params.readOnlyMode}
      onKeyDown={(e) => {
        const key = e.key;
        switch (key) {
          case ' ': // space bar
            handleChange(e);
            break;
          default:
            break;
        }
      }}
    />
  );
};

export const DateCell = (params) => {
  const value = params.value;
  if (value instanceof DateTime) {
    return (
      <Box
        tabIndex={-1}
        sx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          justifyContent: 'center',
        }}
      >
        <Typography>
          <span style={{ fontWeight: 'bold' }}>{value.weekdayShort}</span>
        </Typography>
        <Typography>{value.toFormat('LLL d')}</Typography>
      </Box>
    );
  } else {
    console.warn('value is not a DateTime object');
    return <div></div>;
  }
};

export const CheckboxCell = (params) => {
  const isActive = params.row.isActive;

  const field = params.colDef.field;
  const cellMode = params.cellMode;
  const api = params.api;
  const update = params.update;
  const rowId = params.row.id;
  const isHidden = params.colDef?.isHidden;
  const isEditable = params.isEditable;
  const readOnlyMode = params.readOnlyMode;

  const hidden = useMemo(() => {
    if (isHidden) {
      return isHidden(params.row);
    }
    return !isEditable;
  }, [params.row, isHidden, isEditable]);
  return (
    <Box sx={{ display: 'flex', justifyContent: 'center', height: '100%' }}>
      {isActive && (
        <Box>
          {!hidden && (
            <Checkbox
              // tabIndex={0}
              checked={!!params.value}
              disabled={!isActive || readOnlyMode}
              onChange={(e) => {
                e.stopPropagation();

                update({
                  field: field,
                  value: e.target.checked,
                  rowId: rowId,
                  isWorkDaysGrid: true,
                  api: api,
                  cellMode: cellMode,
                });
              }}
            />
          )}
        </Box>
      )}
    </Box>
  );
};

export const GridCommonCell = (params) => {
  const isActive = params.row.isActive;
  const isHidden = params.colDef?.isHidden;
  const field = params.colDef?.field;
  let errMsg = '';
  const displayErrors = params.api?.state?.displayErrors || {};
  const dayId = `day${params.row.id}`;
  if (displayErrors?.[dayId]?.[field]) {
    errMsg = displayErrors[dayId][field]?.message || '';
  }

  const hidden = useMemo(() => {
    if (isHidden) {
      return isHidden(params.row);
    }
    return false;
  }, [params.row, isHidden]);

  if (!isActive || hidden) {
    return <Box />;
  }
  const displayValue =
    params.formattedValue || params.formattedValue === ''
      ? params.formattedValue
      : params.value;

  return (
    <Tooltip
      arrow
      variant="html"
      title={errMsg ? errMsg : displayValue}
      enterDelay={500}
      placement="top"
    >
      <Box
        tabIndex={0}
        sx={(theme) => ({
          display: 'flex',
          justifyContent: 'center',
          height: '100%',
          ...(errMsg
            ? {
                borderWidth: '1px !important',
                borderStyle: 'solid !important',
                borderColor: `${theme.palette.error[500]}`,
                borderRadius: '4px',
              }
            : {}),
        })}
      >
        <span style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>
          {displayValue}
        </span>
      </Box>
    </Tooltip>
  );
};

export const TooltipCell = (params) => {
  const { row, hasFocus, api, colDef } = params;
  const isActive = params.row.isActive;

  const divRef = useRef(null);
  useEffect(() => {
    if (hasFocus) {
      divRef?.current?.focus();
    }
  }, [hasFocus]);

  const value = params.value;
  return (
    <KeyboardControlBox
      sx={{
        display: 'flex',
        height: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
      }}
      ref={value ? null : divRef}
      api={api}
      rowId={row.id}
      colDef={colDef}
      tabIndex={value ? -1 : 0}
    >
      <Tooltip title={value} arrow variant="html" placement="top">
        {value && isActive ? (
          <Box
            ref={divRef}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'pink.700',
              padding: '4px',
              borderRadius: '4px',
              '&:hover': {
                cursor: 'pointer',
              },
            }}
            tabIndex={0}
          >
            <TextsmsOutlinedIcon sx={{ height: 20, width: 20 }} />
            <Typography sx={{ marginLeft: '8px', fontSize: '14px' }}>
              View
            </Typography>
          </Box>
        ) : (
          <Box />
        )}
      </Tooltip>
    </KeyboardControlBox>
  );
};

export const MenuCell = (params) => {
  const { row, addReRate, removeReRate, api, onOpenCopyToDays, readOnlyMode } =
    params;

  const [anchorEl, setAnchorEl] = useState(null);
  const isActive = row.isActive;

  const hasExistingRerate = useMemo(() => {
    return row.hasRerate;
  }, [row]);

  const onChangeRowEditMode = (e) => {
    const rowMode = api.getRowMode(row.id);
    if (rowMode === 'edit') {
      api.stopRowEditMode({ id: row.id });
    }
  };

  const handleRerate = () => {
    const deletingRerate = hasExistingRerate;
    setAnchorEl(null);
    onChangeRowEditMode();
    if (deletingRerate) {
      removeReRate({ parentRowId: row.id, api });
    } else {
      const parentRowId = row.id;
      addReRate({ parentRowId: parentRowId, api, row });
    }
  };

  const handleCopyToDays = () => {
    setAnchorEl(null);
    onChangeRowEditMode();
    onOpenCopyToDays({ rowId: row.id, api });
  };

  return (
    <Box>
      <IconButton
        tabIndex={-1}
        onClick={(e) => setAnchorEl(e.target)}
        disabled={!isActive || readOnlyMode}
      >
        <DotsVerticalIcon />
      </IconButton>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={() => setAnchorEl(null)}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={handleCopyToDays}>Copy to day(s)</MenuItem>
        <MenuItem onClick={handleRerate}>
          {hasExistingRerate ? 'Remove Re-rate' : 'Re-rate'}
        </MenuItem>
      </Menu>
    </Box>
  );
};
