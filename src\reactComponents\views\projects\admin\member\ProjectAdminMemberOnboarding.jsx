import { applyPureVueInReact } from 'veaury';
import ProjectAdminMemberOnboardingVue from '../../../../../views/projects/admin/member/ProjectAdminMemberOnboarding.vue';
import { useAuth } from '../../../../AppHooks';
import { useNavigate, useOutletContext } from 'react-router';

const ReactProjectAdminMemberOnboarding = applyPureVueInReact(
  ProjectAdminMemberOnboardingVue,
);

const ProjectAdminMemberOnboarding = () => {
  useAuth();
  const navigate = useNavigate();
  const context = useOutletContext();
  return (
    <ReactProjectAdminMemberOnboarding
      projectMember={context.projectMember}
      user={context.user}
      userCrew={context.userCrew}
      project={context.project}
      navigate={navigate}
    />
  );
};

export default ProjectAdminMemberOnboarding;
