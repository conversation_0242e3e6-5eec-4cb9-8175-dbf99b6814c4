<template>
  <div
    ref="Snackbar"
    class="fixed sm:right-10 sm:bottom-20 w-full sm:w-auto mt-20 sm:m-0 px-5 py-4 bg-white dark:bg-gray-900 border-b-8 sm:border-b-0 sm:border-r-8 border-blue-400 drop-shadow-lg transition-opacity ease-in-out duration-700 opacity-0"
  >
    <div class="text-sm flex justify-start items-center">
      <div
        v-if="type === 'success'"
        class="mr-2 inline-block text-white font-extrabold"
      >
        <Badge type="success" text="" size="icon" class="w-6 h-6">
          <template #icon>
            <Icon name="checked" class="w-4 h-4" />
          </template>
        </Badge>
      </div>
      <div
        v-if="type === 'error'"
        class="mr-2 inline-block px-1 py-1 rounded-full bg-red-500 text-white font-extrabold"
      >
        <XMarkIcon class="w-4 h-4" />
      </div>
      <div
        v-if="type === 'warning'"
        class="mr-2 inline-block px-1 py-1 rounded-full bg-yellow-500 text-white font-extrabold"
      >
        <ExclamationCircleIcon class="w-4 h-4" />
      </div>
      <div
        v-if="type === 'info'"
        class="mr-2 inline-block rounded-full text-white font-extrabold"
      >
        <QuestionMarkCircleIcon
          class="w-6 h-6 text-gray-500 dark:text-gray-400"
        />
      </div>
      <div class="flex flex-col">
        <div
          v-for="(msg, index) in msgArr"
          :key="index"
          class="text-gray-800 dark:text-gray-200"
        >
          {{ msg }}
        </div>
      </div>
      <div
        v-if="showHide"
        class="ml-2 inline-block rounded-full text-white font-extrabold cursor-pointer"
        @click="hide"
      >
        <XMarkIcon class="w-6 h-6 text-gray-500 dark:text-gray-400" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Icon from '@/components/Icon.vue';
import Badge from '@/components/library/Badge.vue';
import {
  // CheckIcon,
  ExclamationCircleIcon,
  XMarkIcon,
} from '@heroicons/vue/20/solid';
import { QuestionMarkCircleIcon } from '@heroicons/vue/24/outline';
import { ref, type Ref, watch } from 'vue';

const Snackbar: Ref<HTMLDivElement | null> = ref<HTMLDivElement | null>(null);

const props = defineProps<{
  reactMsg?: {
    msg: string | null;
    type: string | null;
    duration: number;
  };
}>();

const msgArr = ref([]);
const showHide = ref(false);
const type = ref('');

watch(
  () => props.reactMsg,
  (reactMsg) => {
    const newMsg = capitalizeFirstLetter(reactMsg?.msg) || '';
    const arr = newMsg.split('|');
    msgArr.value = arr;
    const multiLine = arr.length > 1;
    showHide.value = multiLine;
    type.value = reactMsg?.type || 'success';
    const duration = multiLine ? 20000 : reactMsg?.duration || 2500;
    show(duration);
  },
);

function capitalizeFirstLetter(str: any) {
  if (!str) return str;
  return str?.charAt(0).toUpperCase() + str.slice(1);
}

function show(timeout: number) {
  const snackbarRef: HTMLDivElement = Snackbar.value as HTMLDivElement;
  snackbarRef.classList.remove('z-0');
  snackbarRef.classList.add('z-[1500]');
  snackbarRef.classList.remove('opacity-0');
  snackbarRef.classList.add('opacity-100');
  setTimeout(() => {
    snackbarRef.classList.remove('opacity-100');
    snackbarRef.classList.add('opacity-0');
    setTimeout(() => {
      snackbarRef.classList.remove('z-[1500]');
      snackbarRef.classList.add('z-0');
    }, 650);
  }, timeout);
}

function hide() {
  const snackbarRef: HTMLDivElement = Snackbar.value as HTMLDivElement;

  snackbarRef.classList.remove('opacity-100');
  snackbarRef.classList.add('opacity-0');
  setTimeout(() => {
    snackbarRef.classList.remove('z-[1500]');
    snackbarRef.classList.add('z-0');
  }, 650);
}
</script>
