import React from 'react';
import PropTypes from 'prop-types';

import { Box, Text, Button } from '@/reactComponents/library';
import ZeroStateUnbatched from '@/assets/images/ReactSvg/ZeroStateUnbatched';
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';

export const zeroStateVariants = {
  filtersActive: 'filtersActive',
  unbatched: 'unbatched',
  noTimecardsInProj: 'noTimecardsInProj',
};

const styles = {
  emptyState: {
    display: 'flex',
    gap: 2,
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    p: 4,
  },
  emptyText: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    maxWidth: '500px',
  },
};

const ZeroState = (props) => {
  const { name = '', variant = 'default', clearFilters } = props;

  const timecardsText =
    'Timecards for employees will begin displaying once they start completing their timecards';

  const zeroStateTexts = {
    [zeroStateVariants.filtersActive]: {
      titleText: 'There are no timecards based on your filters',
      bottomText: (
        <Button
          startIcon={<DeleteOutlineOutlinedIcon />}
          onClick={clearFilters}
          variant="secondary"
        >
          Clear All Filters
        </Button>
      ),
    },
    [zeroStateVariants.unbatched]: {
      titleText: 'There are no unbatched timecards at this time',
      bottomText: timecardsText,
    },
    [zeroStateVariants.noTimecardsInProj]: {
      titleText: `Hi ${name}, welcome to Time.`,
      bottomText: timecardsText,
    },
    default: { titleText: 'No timecards in batch' },
  };

  const text = zeroStateTexts[variant] || zeroStateTexts.default;

  return (
    <Box sx={styles.emptyState}>
      <Box sx={styles.emptyText}>
        {text.titleText && <Text variant="disSemi">{text.titleText}</Text>}
        {text.bottomText && (
          <Text variant="lgReg" sx={{ mt: 2 }}>
            {text.bottomText}
          </Text>
        )}
      </Box>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
        }}
      >
        <ZeroStateUnbatched sx={{ height: 264, width: 450 }} />
      </Box>
    </Box>
  );
};
ZeroState.propTypes = {
  name: PropTypes.string.isRequired,
  variant: PropTypes.string,
  clearFilters: PropTypes.func,
};

export default ZeroState;
