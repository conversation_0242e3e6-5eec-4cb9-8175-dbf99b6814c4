import React from 'react';
import PropTypes from 'prop-types';

import { Box, Tooltip } from '@/reactComponents/library';

import AutocompleteCell from '../EditCells/AutocompleteCell';
import DisplayCell from '../EditCells/DisplayCell';
import TextCell from '../EditCells/TextCell';
import ReimbursementTypeCell from '../EditCells/ReimbursementTypeCell';
import DateCell from '../EditCells/DateCell';
import RentalDaysCell from '../EditCells/RentalDaysCell';
import FileUploadCell from '../EditCells/FileUploadCell';
import DeleteCell from '../EditCells/DeleteCell';

const CellWrapper = (props) => {
  const { column, cellError, ...restProps } = props;
  const { type, columnId, width } = column;

  let Cell;
  if (columnId === 'expense') {
    Cell = ReimbursementTypeCell;
  } else {
    switch (type) {
      case 'text':
        Cell = TextCell;
        break;
      case 'autocomplete':
        Cell = AutocompleteCell;
        break;
      case 'date':
        Cell = DateCell;
        break;
      case 'display':
        Cell = DisplayCell;
        break;
      case 'rentalDays':
        Cell = RentalDaysCell;
        break;

      case 'deleteBtn':
        Cell = DeleteCell;
        break;
      case 'fileUpload':
        Cell = FileUploadCell;
        break;
      default:
        Cell = TextCell;
        break;
    }
  }

  const sx = {};

  if (width) {
    sx.width = width;
  }

  return (
    <Tooltip title={cellError?.message || ''} arrow placement="top">
      <Box sx={sx}>
        <Cell error={!!cellError} column={column} {...restProps} />
      </Box>
    </Tooltip>
  );
};

CellWrapper.propTypes = {
  column: PropTypes.object.isRequired,
  workLocations: PropTypes.array.isRequired,
  reimbursement: PropTypes.object.isRequired,
  updateCell: PropTypes.func.isRequired,
  timecard: PropTypes.object,
  cellError: PropTypes.object,
  disabled: PropTypes.bool,
};

export default CellWrapper;
