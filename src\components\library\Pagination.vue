<template>
  <nav
    class="flex items-align-start justify-between border-t border-gray-200 dark:border-gray-700 px-4 sm:px-0"
  >
    <div class="-mt-px flex w-0 flex-1">
      <button
        class="inline-flex items-center border-t-2 border-transparent px-3 py-4 text-sm font-medium select-none"
        @click="previous"
        :class="{
          'cursor-not-allowed text-gray-300 dark:text-gray-600':
            previousDisabled,
          'text-gray-500 dark:text-gray-400 hover:border-gray-300 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-200 cursor-pointer':
            !previousDisabled,
        }"
        :disabled="previousDisabled"
      >
        <ArrowLongLeftIcon class="mr-3 h-5 w-5" aria-hidden="true" />
        Previous
      </button>
    </div>
    <div class="hidden md:-mt-px md:flex">
      <button
        v-if="showFrontHiddenPages"
        class="inline-flex items-center border-t-2 px-4 py-2 text-sm font-medium select-none cursor-default border-transparent text-gray-500 dark:text-gray-400"
      >
        ...
      </button>
      <button
        v-for="page in visiblePages"
        :key="page"
        @click="gotoPage(page)"
        :class="pageButtonClasses(page)"
      >
        {{ page }}
      </button>
      <button
        v-if="showBackHiddenPages"
        class="inline-flex items-center border-t-2 px-4 py-2 text-sm font-medium select-none cursor-default border-transparent text-gray-500 dark:text-gray-400"
      >
        ...
      </button>
    </div>
    <div class="-mt-px flex w-0 flex-1 justify-end">
      <button
        class="inline-flex items-center border-t-2 border-transparent px-3 py-4 text-sm font-medium select-none"
        @click="next"
        :class="{
          'cursor-not-allowed text-gray-300 dark:text-gray-600': nextDisabled,
          'text-gray-500 dark:text-gray-400 hover:border-gray-300 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-200 cursor-pointer':
            !nextDisabled,
        }"
        :disabled="nextDisabled"
      >
        Next
        <ArrowLongRightIcon
          class="ml-3 h-5 w-5 text-gray-400 dark:text-gray-500"
          aria-hidden="true"
        />
      </button>
    </div>
  </nav>
</template>

<script lang="ts">
import type { Pagination } from '@/types/Pagination';
import { ArrowLongLeftIcon, ArrowLongRightIcon } from '@heroicons/vue/20/solid';
import { defineComponent, type PropType } from 'vue';

export default defineComponent({
  components: {
    ArrowLongLeftIcon,
    ArrowLongRightIcon,
  },
  emits: ['update:modelValue'],
  props: {
    modelValue: {
      type: Object as PropType<Pagination>,
      required: true,
    },
    maxVisiblePages: {
      type: Number,
      default: 5,
    },
  },
  data() {
    return {
      didRecentEmit: false,
    };
  },
  computed: {
    nextDisabled(): boolean {
      return this.currentPage >= this.totalPages;
    },
    previousDisabled(): boolean {
      return this.currentPage <= 1;
    },
    totalPages(): number {
      const { limit, total } = this.modelValue;
      return Math.ceil(total / limit);
    },
    currentPage(): number {
      return this.modelValue.page;
    },
    visiblePages(): number[] {
      if (this.totalPages < this.maxVisiblePages) {
        return this.buildRange(1, this.totalPages);
      }

      const even = this.maxVisiblePages % 2 === 0 ? 1 : 0;
      const left = Math.floor(this.maxVisiblePages / 2);
      const right = this.totalPages - left + 1 + even;

      if (this.currentPage > left && this.currentPage < right) {
        const start = this.currentPage - left;
        const end = this.currentPage + left - even;

        return this.buildRange(start, end);
      } else if (this.currentPage >= right) {
        const start = this.totalPages - this.maxVisiblePages + 1;
        return this.buildRange(start, this.totalPages);
      } else {
        return this.buildRange(1, this.maxVisiblePages);
      }
    },
    showFrontHiddenPages(): boolean {
      return this.totalPages > 0 && !this.visiblePages.includes(1);
    },
    showBackHiddenPages(): boolean {
      return (
        this.totalPages > 0 && !this.visiblePages.includes(this.totalPages)
      );
    },
  },
  methods: {
    updatePage(newPage: number): void {
      if (this.didRecentEmit) return;

      const pagination = { ...this.modelValue, page: newPage };
      this.$emit('update:modelValue', pagination);
      this.didRecentEmit = true;

      setTimeout(() => {
        this.didRecentEmit = false;
      }, 500);
    },
    previous(): void {
      if (this.modelValue.page <= 1) {
        return;
      }
      this.updatePage(this.modelValue.page - 1);
    },
    next(): void {
      if (this.modelValue.page >= this.totalPages) {
        return;
      }
      this.updatePage(this.modelValue.page + 1);
    },
    gotoPage(page: number): void {
      this.updatePage(page);
    },
    isPageActive(page: number): boolean {
      return this.currentPage === page;
    },
    pageButtonClasses(page: number) {
      return {
        'inline-flex items-center border-t-2 px-4 py-2 text-sm font-medium select-none cursor-pointer  hover:text-gray-700 dark:hover:text-gray-200':
          true,
        'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400':
          !this.isPageActive(page),
        'border-indigo-500 text-indigo-600 dark:text-indigo-400':
          this.isPageActive(page),
      };
    },
    buildRange(from: number, to: number): number[] {
      const range = [];

      from = from > 0 ? from : 1;
      for (let i = from; i <= to; i++) {
        range.push(i);
      }

      return range;
    },
  },
});
</script>
