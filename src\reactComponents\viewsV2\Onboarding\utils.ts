import { I9ExpReq } from './types';

export const stringDateFormat = 'yyyy-MM-dd';

export const requiredEstEmpValidation = (value: any, form: any) => {
  if (!value && form.proofOfIdentity?.estEmpAuth === false) {
    return 'This field is required';
  }
  return true;
};

export const defaultI9Values = Object.freeze({
  citizenshipStatus: null,
  workAuthorizationType: null,
  workAuthorizationExpirationDate: '',
  workAuthorizationNumber: '',
  sec1Acknowledgement: false,
  proofOfIdentity: null,
  proofOfIdentityIssuingAuthority: '',
  proofOfIdentityDocumentNumber: '',
  proofOfIdentityExpirationDate: '',
  employmentAuthorizationType: null,
  employmentAuthorizationIssuingAuthority: '',
  employmentAuthorizationDocumentNumber: '',
  employmentAuthorizationExpirationDate: '',
});

export const setI9IncomingValues = (data: any, setValue: Function) => {
  setValue('citizenshipStatus', data.citizenshipStatus || null);
  setValue('workAuthorizationType', data.workAuthorizationType || null);

  setValue('workAuthorizationNumber', data.workAuthorizationNumber || '');
  setValue('sec1Acknowledgement', data.sec1Acknowledgement || false);
  setValue('proofOfIdentity', data.proofOfIdentity || null);
  setValue(
    'proofOfIdentityIssuingAuthority',
    data.proofOfIdentityIssuingAuthority || '',
  );
  setValue(
    'proofOfIdentityDocumentNumber',
    data.proofOfIdentityDocumentNumber || '',
  );

  setValue(
    'employmentAuthorizationType',
    data.employmentAuthorizationType || null,
  );
  setValue(
    'employmentAuthorizationIssuingAuthority',
    data.employmentAuthorizationIssuingAuthority || '',
  );
  setValue(
    'employmentAuthorizationDocumentNumber',
    data.employmentAuthorizationDocumentNumber || '',
  );

  const workExpDate = data.workAuthorizationExpirationDate;
  if (workExpDate) {
    setValue(
      'workAuthorizationExpirationDate',
      workExpDate.toUTC().toFormat(stringDateFormat) || '',
    );
  }

  const proofExpDate = data.proofOfIdentityExpirationDate;
  if (proofExpDate) {
    setValue(
      'proofOfIdentityExpirationDate',
      proofExpDate.toUTC().toFormat(stringDateFormat) || '',
    );
  }

  const empAuthExpDate = data.employmentAuthorizationExpirationDate;
  if (empAuthExpDate) {
    setValue(
      'employmentAuthorizationExpirationDate',
      empAuthExpDate.toUTC().toFormat(stringDateFormat) || '',
    );
  }
};

//removed unused values from from before sending
export const prepI9Payload = (data: any) => {
  const { citizenshipStatus, proofOfIdentity } = data;

  if (citizenshipStatus?.key === 'citizen') {
    delete data.workAuthorizationNumber;
    delete data.workAuthorizationExpirationDate;
    delete data.workAuthorizationType;
  } else if (citizenshipStatus?.key === 'non_citizen_national') {
    delete data.workAuthorizationExpirationDate;
    delete data.workAuthorizationType;
  } else if (citizenshipStatus?.key === 'permanent_resident') {
    delete data.workAuthorizationExpirationDate;
  }

  if (proofOfIdentity.provesEmploymentEligibility === true) {
    delete data.employmentAuthorizationDocumentNumber;
    delete data.employmentAuthorizationExpirationDate;
    delete data.employmentAuthorizationType;
    delete data.employmentAuthorizationIssuingAuthority;
  } else {
    if (data?.employmentAuthorizationType?.expiryRequirement === I9ExpReq.NA) {
      delete data.employmentAuthorizationExpirationDate;
    }
  }

  return data;
};
