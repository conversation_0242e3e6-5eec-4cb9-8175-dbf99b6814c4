import type { I9DocWorkAuthType } from './types';
import { I9ExpReq } from './types';

// export const i9DocumentTypes: I9DocumentType[] = [
//   {
//     id: 1,
//     name: 'US Passport or Passport Card',
//     key: 'us-passport',
//     estEmpAuth: true,
//     expDate: I9ExpReq.Required,
//   },
//   {
//     id: 7,
//     name: 'Driver’s license',
//     key: 'driver-license',
//     estEmpAuth: false,
//     expDate: I9ExpReq.Optional,
//   },
//   {
//     id: 2,
//     name: 'Permanent Resident Card or Alien Registration Receipt Card',
//     key: 'permanent-resident-card',
//     estEmpAuth: true,
//     expDate: I9ExpReq.Required,
//   },
//   {
//     id: 3,
//     name: 'Foreign Passport, with I-551',
//     key: 'foreign-passport-i551',
//     estEmpAuth: true,
//     expDate: I9ExpReq.Required,
//   },
//   {
//     id: 4,
//     name: 'Foreign Passport, with Form I-94 or I-94A',
//     key: 'foreign-passport-i94',
//     estEmpAuth: true,
//     expDate: I9ExpReq.Required,
//   },
//   {
//     id: 5,
//     name: 'Passport from Federated States of Micronesia or Republic of the Marshall Islands, with Form I-94 or I-94A',
//     key: 'passport-federated-form',
//     estEmpAuth: true,
//     expDate: I9ExpReq.Required,
//   },
//   {
//     id: 6,
//     name: 'Employment Authorization Document with photograph',
//     key: 'emp-auth-doc-photo',
//     estEmpAuth: true,
//     expDate: I9ExpReq.Required,
//   },
//   {
//     id: 8,
//     name: 'ID card',
//     key: 'id-card',
//     estEmpAuth: false,
//     expDate: I9ExpReq.Optional,
//   },
//   {
//     id: 9,
//     name: 'School ID card',
//     key: 'school-id-card',
//     estEmpAuth: false,
//     expDate: I9ExpReq.Optional,
//   },
//   {
//     id: 10,
//     name: 'Voter’s registration card',
//     key: 'voter-reg-card',
//     estEmpAuth: false,
//     expDate: I9ExpReq.Optional,
//   },
//   {
//     id: 11,
//     name: 'U.S. Military card or draft card',
//     key: 'us-military-card',
//     estEmpAuth: false,
//     expDate: I9ExpReq.Optional,
//   },
//   {
//     id: 12,
//     name: 'Military dependent’s ID card',
//     key: 'military-dependent-id-card',
//     estEmpAuth: false,
//     expDate: I9ExpReq.Optional,
//   },
//   {
//     id: 13,
//     name: 'US Coast Guard Merchant Mariner card',
//     key: 'us-coast-guard-card',
//     estEmpAuth: false,
//     expDate: I9ExpReq.Optional,
//   },
//   {
//     id: 14,
//     name: 'Native American tribal document',
//     key: 'tribal-doc',
//     estEmpAuth: false,
//     expDate: I9ExpReq.Optional,
//   },
//   {
//     id: 15,
//     name: 'Driver’s license issued by a Canadian government authority',
//     key: 'driver-license-canadian',
//     estEmpAuth: false,
//     expDate: I9ExpReq.Optional,
//   },
//   {
//     id: 16,
//     name: 'School record or report card',
//     key: 'school-record',
//     estEmpAuth: false,
//     expDate: I9ExpReq.Optional,
//   },
//   {
//     id: 17,
//     name: 'Clinic, doctor, or hospital record',
//     key: 'hospital-record',
//     estEmpAuth: false,
//     expDate: I9ExpReq.Optional,
//   },
//   {
//     id: 18,
//     name: 'Day-care or nursery school record',
//     key: 'day-care-record',
//     estEmpAuth: false,
//     expDate: I9ExpReq.Optional,
//   },
// ];

// export const i9DocEmpAuthTypes: I9DocWorkAuthType[] = [
//   {
//     id: 1,
//     name: 'Social Security Account Number card',
//     key: 'ssn-card',
//     expDate: I9ExpReq.NA,
//   },
//   {
//     id: 2,
//     name: 'Birth certificate',
//     key: 'birth-cert',
//     expDate: I9ExpReq.NA,
//   },
//   {
//     id: 3,
//     name: 'Native American tribal document',
//     key: 'tribal-doc',
//     expDate: I9ExpReq.NA,
//   },
//   {
//     id: 4,
//     name: 'US Citizen ID card',
//     key: 'citizen-id-card',
//     expDate: I9ExpReq.NA,
//   },
//   {
//     id: 5,
//     name: 'Identification card for use Resident Citizen in the United States',
//     key: 'resident-citizen-id-card',
//     expDate: I9ExpReq.NA,
//   },
//   {
//     id: 6,
//     name: 'Employment authorization document',
//     key: 'emp-auth-doc',
//     expDate: I9ExpReq.Required,
//   },
// ];

export const permResidentWorkAuthTypes: I9DocWorkAuthType[] = [
  {
    id: 1,
    name: 'Alien Registration Number',
    key: 'alien_registration_number',
    expDate: I9ExpReq.Required,
  },
  {
    id: 2,
    name: 'USCIS Number',
    key: 'uscis_number',
    expDate: I9ExpReq.Required,
  },
];
export const alienWorkAuthTypes: I9DocWorkAuthType[] = [
  {
    id: 1,
    name: 'Alien Registration Number',
    key: 'alien_registration_number',
    expDate: I9ExpReq.Required,
  },
  {
    id: 2,
    name: 'USCIS Number',
    key: 'uscis_number',
    expDate: I9ExpReq.NA,
  },
  {
    id: 3,
    name: 'I-94 Number',
    key: 'i94_number',
    expDate: I9ExpReq.Required,
  },
  {
    id: 4,
    name: 'Foreign Passport Number',
    key: 'foreign_passport_number',
    expDate: I9ExpReq.Required,
  },
];

export const requiredEstEmpValidation = (value: any, form: any) => {
  if (!value && form.proofOfIdentity?.estEmpAuth === false) {
    return 'This field is required';
  }
  return true;
};
