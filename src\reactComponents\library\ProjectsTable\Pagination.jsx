import React, { useMemo, useCallback, useState } from 'react';
import PropTypes from 'prop-types';
import {
  ArrowLongLeftIcon,
  ArrowLongRightIcon,
} from '@heroicons/react/20/solid';

const Pagination = ({ pagination, onChange, maxVisiblePages = 5 }) => {
  const [didRecentEmit, setDidRecentEmit] = useState(false);

  const totalPages = useMemo(
    () => Math.ceil(pagination.total / pagination.limit),
    [pagination.total, pagination.limit],
  );

  const currentPage = pagination.page;

  const previousDisabled = currentPage <= 1;
  const nextDisabled = currentPage >= totalPages;

  const updatePage = useCallback(
    (newPage) => {
      if (didRecentEmit || newPage === currentPage) return;
      onChange({ ...pagination, page: newPage });
      setDidRecentEmit(true);
      setTimeout(() => setDidRecentEmit(false), 500);
    },
    [didRecentEmit, currentPage, pagination, onChange],
  );

  const buildRange = useCallback((from, to) => {
    const range = [];
    from = Math.max(from, 1);
    for (let i = from; i <= to; i++) {
      range.push(i);
    }
    return range;
  }, []);

  const visiblePages = useMemo(() => {
    if (totalPages <= maxVisiblePages) {
      return buildRange(1, totalPages);
    }

    const even = maxVisiblePages % 2 === 0 ? 1 : 0;
    const left = Math.floor(maxVisiblePages / 2);
    const right = totalPages - left + 1 + even;

    if (currentPage > left && currentPage < right) {
      return buildRange(currentPage - left, currentPage + left - even);
    } else if (currentPage >= right) {
      return buildRange(totalPages - maxVisiblePages + 1, totalPages);
    } else {
      return buildRange(1, maxVisiblePages);
    }
  }, [currentPage, totalPages, maxVisiblePages, buildRange]);

  const showFrontHiddenPages = totalPages > 0 && !visiblePages.includes(1);
  const showBackHiddenPages =
    totalPages > 0 && !visiblePages.includes(totalPages);

  const isPageActive = (page) => page === currentPage;

  const pageButtonClasses = (page) =>
    `inline-flex items-center border-t-2 px-4 py-2 text-sm font-medium select-none cursor-pointer 
    ${
      isPageActive(page)
        ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400'
        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:border-gray-600'
    }`;

  return (
    <nav className="flex items-start justify-between border-t border-gray-200 dark:border-gray-700 px-4 sm:px-0">
      <div className="-mt-px flex w-0 flex-1">
        <button
          className={`inline-flex items-center border-t-2 border-transparent px-3 py-4 text-sm font-medium select-none ${
            previousDisabled
              ? 'cursor-not-allowed text-gray-300 dark:text-gray-600'
              : 'text-gray-500 dark:text-gray-400 hover:border-gray-300 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-200 cursor-pointer'
          }`}
          onClick={() => updatePage(currentPage - 1)}
          disabled={previousDisabled}
        >
          <ArrowLongLeftIcon className="mr-3 h-5 w-5" aria-hidden="true" />
          Previous
        </button>
      </div>
      <div className="hidden md:-mt-px md:flex">
        {showFrontHiddenPages && (
          <button className="inline-flex items-center border-t-2 px-4 py-2 text-sm font-medium select-none cursor-default border-transparent text-gray-500 dark:text-gray-400">
            ...
          </button>
        )}
        {visiblePages.map((page) => (
          <button
            key={page}
            onClick={() => updatePage(page)}
            className={pageButtonClasses(page)}
          >
            {page}
          </button>
        ))}
        {showBackHiddenPages && (
          <button className="inline-flex items-center border-t-2 px-4 py-2 text-sm font-medium select-none cursor-default border-transparent text-gray-500 dark:text-gray-400">
            ...
          </button>
        )}
      </div>
      <div className="-mt-px flex w-0 flex-1 justify-end">
        <button
          className={`inline-flex items-center border-t-2 border-transparent px-3 py-4 text-sm font-medium select-none ${
            nextDisabled
              ? 'cursor-not-allowed text-gray-300 dark:text-gray-600'
              : 'text-gray-500 dark:text-gray-400 hover:border-gray-300 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-200 cursor-pointer'
          }`}
          onClick={() => updatePage(currentPage + 1)}
          disabled={nextDisabled}
        >
          Next
          <ArrowLongRightIcon
            className="ml-3 h-5 w-5 text-gray-400 dark:text-gray-500"
            aria-hidden="true"
          />
        </button>
      </div>
    </nav>
  );
};

Pagination.propTypes = {
  pagination: PropTypes.shape({
    total: PropTypes.number.isRequired,
    limit: PropTypes.number.isRequired,
    page: PropTypes.number.isRequired,
  }).isRequired,
  onChange: PropTypes.func.isRequired,
  maxVisiblePages: PropTypes.number,
};

export default Pagination;
