/// <reference types="cypress" />

import BatchModals from '../../pageObjects/timecards/batchModals';
import TimecardModal from '../../pageObjects/timecards/timecardModal';
import { randomBatchName } from '../../utils/batchUtils';
import {
  interceptCreateBatch,
  interceptCreateTimecard,
  interceptExportPdf,
  interceptGetApprovedTimecard,
} from '../../support/apiTimecardInterceptors';
import {
  deleteBatch,
  deleteTimecard,
  fetchProjectIdentifiersByName,
} from '../../support/apiHelpers';
import TimecardDetails from '../../pageObjects/timecards/timecardDetails';

function setupInterceptors() {
  interceptCreateTimecard();
  interceptCreateBatch();
}

function goToProjectTimeTab() {
  cy.visit(
    `${Cypress.env('BASE_URL')}projects/${Cypress.env(
      'projectHashId',
    )}/admin/time`,
  );
}

describe('User Project Admin - Batch Export', () => {
  const batchModals = new BatchModals();
  const timecardModal = new TimecardModal();
  const timecardDetails = new TimecardDetails();
  const employeeName = Cypress.env('employeeName');
  const projectName = Cypress.env('projectName');
  const batchName = randomBatchName();

  beforeEach(() => {
    cy.loginWithSession(
      Cypress.env('PHONE_NUMBER_PROJECT_ADMIN'),
      Cypress.env('OTC_CODE'),
      Cypress.env('BASE_URL'),
      'project-admin',
    );
    cy.then(() => {
      return fetchProjectIdentifiersByName(projectName).then((project) => {
        if (!project || !project.id) {
          throw new Error('Project not found or invalid Project ID');
        }
        Cypress.env('projectId', project.id);
        Cypress.env('projectHashId', project.hashId);
      });
    });
  });

  afterEach(() => {
    deleteTimecard(Cypress.env('timecardId')).then(() => {
      deleteBatch(Cypress.env('batchId'));
    });
  });

  //https://castandcrew.atlassian.net/browse/FPS-1548
  it('Verify Project Admin is able to Export pdf from Batches page', () => {
    // ----------- Arrange: Go to Project Time Tab -----------
    cy.log('Go to Project');
    goToProjectTimeTab();
    setupInterceptors();

    // ----------- Action: Create Batch By UI -----------
    cy.log('Action: Create Batch By UI');
    batchModals.createBatch(batchName);

    // ----------- Assert: Verify Batch Created checking on the API response-----------
    cy.log('Verify Batch Created checking on the API response');
    cy.wait('@createdBatch').then((interception) => {
      expect(interception.response?.statusCode, 'Create status code').to.equal(
        200,
      );
      expect(
        interception.response?.body.name,
        'Batch name after edit',
      ).to.equal(batchName);
      expect(interception.response?.body.statusId, 'Status Open').to.equal(3);

      // ----------- Action: Create Timecard by and Move to Batch by UI -----------
      cy.log('Action: Create Timecard by and Move to Batch by UI');
      Cypress.env('batchId', interception.response?.body.id);
      timecardModal.createTimecard(employeeName);
      batchModals.moveToBatchWithSelection(batchName);
    });

    // ----------- Assert: Verify Timecard Created checking on the API -----------
    cy.log('Assert: Verify Timecard Created checking on the API ');
    cy.wait('@createTimecard').then((interception) => {
      const response = interception.response?.body;
      Cypress.env('timecardId', response.id);
      const timecardDaysId: string = response?.timecardDays?.[1]?.id;

      // ---------- Action: Fill Timecard Details by UI Save Sign and Approve -----------
      cy.log('Action: Fill Timecard Details by UI Save Sign and Approve');
      timecardDetails.fillOutTimecardDetails(timecardDaysId);
      timecardDetails.save();
      timecardDetails.approve();
      cy.signatureApproval();

      // ----------- Assert: Verify Timecard Approval checking on the API -----------
      cy.log('Assert: Verify Timecard Approval checking on the API');
      interceptGetApprovedTimecard();
      cy.wait('@getApprovedTimecard', { timeout: 10000 }).then(
        (getIntercept) => {
          const statusName: string = getIntercept.response?.body?.status?.name;
          cy.log(`Final timecard status: ${statusName}`);
          expect(statusName, 'Timecard should be approved').to.eq('Approved');

          // ---------- Action: Go Back to Timecards Export Pdf option -----------
          cy.log('Action: Go Back to Timecards Export Pdf option');
          interceptExportPdf();
          batchModals.goBackFromTimecard();
          timecardModal.goToBatchOptions('Export PDFs');
        },
      );

      // ----------- Assert: Verify the PDF was generated checking on the API -----------
      cy.log('Assert: Verify the PDF was generated checking on the API');
      cy.wait('@getExportPDF').then((interception) => {
        const responseBody = interception.response?.body;
        expect(interception.response?.statusCode, 'Export PDF status').to.equal(
          200,
        );
        expect(responseBody.fileName, 'Exported file name').to.match(/\.pdf$/);
        expect(responseBody.filePath, 'Exported file path')
          .to.be.a('string')
          .and.have.length.greaterThan(0);
        cy.log(
          'Success: Verify Project Admin is able to Export pdf from Batches page',
        );
      });
    });
  });
});
