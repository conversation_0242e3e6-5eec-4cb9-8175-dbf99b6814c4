/* eslint-disable cypress/unsafe-to-chain-command */
import { create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Router<PERSON>rovider, Outlet } from 'react-router-dom';
import { Snac<PERSON>bar<PERSON>rovider } from 'notistack';
import PropTypes from 'prop-types';

import CompanyFormView from '@/reactComponents/views/companies/CompanyFormView';

// ----- Selectors -----
const nameInput = '[data-testid="Name-input"]';
const phoneInput = '[data-testid="Phone-input"]';
const castCrewIdInput = '[data-testid="Cast and Crew Id-input"]';
const generateStartToggle =
  '[data-testid="company-generate-start-toggle"] [role="switch"]';
const generateWtpaToggle =
  '[data-testid="company-generate-wtpa-toggle"] [role="switch"]';
const streetAddressInput = '[data-testid="Street Address-input"]';
const streetAddress2Input = '[data-testid="Street Address Line 2-input"]';
const cityInput = '[data-testid="City-input"]';
const stateDropdownBtn = '[data-testid="company-state-dropdown"] button';
const zipInput = '[data-testid="ZIP / Postal code-input"]';
const taxClassificationDropdown =
  '[data-testid="company-taxClassification-dropdown"]';
const payFrequencyDropdown = '[data-testid="company-payFrequency-dropdown"]';
const payDaysDropdown = '[data-testid="company-payDays-dropdown"]';
const createUpdateBtn = '[data-testid="company-create-update-btn"]';
const cancelBtn = '[data-testid="company-cancel-button"]';
const addAddressIcon = '[data-testid="add-address-icon"]';

const mockCompany = {
  id: 123,
  name: 'Test Company',
};

const LayoutWithOutletContext = ({ context }) => (
  <SnackbarProvider>
    <Outlet context={context} />
  </SnackbarProvider>
);

LayoutWithOutletContext.propTypes = {
  context: PropTypes.object.isRequired,
};

// TestWrapper to simulate React Router context
const TestWrapper = () => {
  const router = createMemoryRouter(
    [
      {
        path: '/',
        handle: { editMode: true },
        element: (
          <LayoutWithOutletContext
            context={{ editMode: true, company: mockCompany }}
          />
        ),
        children: [
          {
            path: '/',
            element: <CompanyFormView />,
            handle: { editMode: true },
          },
        ],
      },
      {
        path: '/companies',
        element: (
          <div data-testid="dashboard-companies">dashboard-companies</div>
        ),
      },
    ],
    { initialEntries: ['/'] },
  );

  return <RouterProvider router={router} />;
};

TestWrapper.propTypes = {
  company: PropTypes.object.isRequired,
};

describe('EditCompanyFormView component testing', () => {
  beforeEach(() => {
    cy.fixture('/componentPayloads/companyInformation').then((company) => {
      cy.intercept('GET', '**/v2/api/core/production-companies/**', {
        statusCode: 200,
        body: company,
      }).as('companyInformation');
    });

    cy.fixture('/componentPayloads/addressesStates').then((addressesStates) => {
      cy.intercept('GET', '**/v2/api/core/addresses/states', {
        statusCode: 200,
        body: addressesStates,
      }).as('addressesStates');
    });

    cy.fixture('/componentPayloads/taxClassification').then(
      (taxClassification) => {
        cy.intercept('GET', '**/v2/api/core/tax-classifications/', {
          statusCode: 200,
          body: taxClassification,
        }).as('taxClassification');
      },
    );

    cy.fixture('/componentPayloads/payDay').then((payDay) => {
      cy.intercept('GET', '**/v2/api/core/payday', {
        statusCode: 200,
        body: payDay,
      }).as('payDay');
    });

    cy.fixture('/componentPayloads/payDayFrequency').then((payDayFrequency) => {
      cy.intercept('GET', '**/v2/api/core/payday/frequencies', {
        statusCode: 200,
        body: payDayFrequency,
      }).as('payDayFrequency');
    });

    cy.fixture('/componentPayloads/addressesStates').then((addressesStates) => {
      cy.intercept('GET', '**/v2/api/core/addresses/states', {
        statusCode: 200,
        body: addressesStates,
      }).as('addressesStates');
    });
    cy.mount(<TestWrapper />);
  });

  it('should present the Company Form with previous created information', () => {
    // ----------- Verify Main Labels and Fields -----------
    cy.contains('Company').should('be.visible');
    cy.contains('This form is used to create a new production company.').should(
      'be.visible',
    );
    cy.contains('Name').should('be.visible');
    cy.get(nameInput).should('be.visible').and('have.value', 'TEST-Company');
    cy.contains('Phone').should('be.visible');
    cy.get(phoneInput).should('be.visible').and('have.value', '831867530993');
    cy.contains('Cast and Crew Id').should('be.visible');
    cy.get(castCrewIdInput).should('be.visible').and('have.value', '9000');

    // ----------- Verify Toggles Actions -----------
    cy.contains('Generate Start Form').should('be.visible');
    cy.contains('Generate WTPA').should('be.visible');
    cy.get(generateStartToggle)
      .should('exist')
      .and('have.attr', 'aria-checked', 'true');
    cy.get(generateWtpaToggle)
      .should('exist')
      .and('have.attr', 'aria-checked', 'true');

    // ----------- Verify Addresses Section -----------
    cy.contains('Addresses').should('be.visible');
    cy.contains('Street Address').should('be.visible');
    cy.get(streetAddressInput)
      .should('be.visible')
      .and('have.value', '2300 Empire Avenue');
    cy.contains('Street Address Line 2').should('be.visible');
    cy.get(streetAddress2Input)
      .should('be.visible')
      .and('have.value', 'Suite 100');
    cy.contains('City').should('be.visible');
    cy.get(cityInput).should('be.visible').and('have.value', 'Burbank');
    cy.contains('State').should('be.visible');
    cy.get(stateDropdownBtn).should('exist').and('have.text', 'California');
    cy.contains('ZIP / Postal code').should('be.visible');
    cy.get(zipInput).should('be.visible').and('have.value', '90001');
    cy.get(addAddressIcon).should('be.visible').click();

    // -----------Verify Tax Classification /Payments Section -----------
    cy.contains('Tax Classification').should('be.visible');
    cy.get(taxClassificationDropdown)
      .should('be.visible')
      .contains('Individual/sole proprietor or single-member LLC');
    cy.contains('Pay Day Frequency').should('be.visible');
    cy.get(payFrequencyDropdown).should('be.visible').contains('Weekly');
    cy.contains('Pay Day').should('be.visible');
    cy.get(payDaysDropdown).should('be.visible').contains('Monday');

    // -----------Verify Action Buttons -----------
    cy.get(createUpdateBtn).should('be.visible').and('not.be.disabled');
    cy.get(cancelBtn).should('be.visible').and('not.be.disabled');
  });

  it('should allow editing all fields and reflect the new values', () => {
    cy.intercept('PATCH', '**/v2/api/core/production-companies/123', {
      statusCode: 200,
    }).as('updateCompany');

    // -----------Verify Main Labels and Fields after edit -----------
    cy.get(nameInput)
      .clear()
      .type('NEW-Company')
      .should('have.value', 'NEW-Company');
    cy.get(phoneInput)
      .clear()
      .type('5551234567')
      .should('have.value', '5551234567');
    cy.get(castCrewIdInput).clear().type('9999').should('have.value', '9999');

    // -----------Verify Toggles after edit -----------
    cy.get(generateStartToggle)
      .click()
      .should('have.attr', 'aria-checked', 'false');
    cy.get(generateWtpaToggle)
      .click()
      .should('have.attr', 'aria-checked', 'false');

    // -----------Verify Addresses Section after edit -----------
    cy.get(streetAddressInput)
      .clear()
      .type('123 New Street')
      .should('have.value', '123 New Street');
    cy.get(streetAddress2Input)
      .clear()
      .type('Apt 202')
      .should('have.value', 'Apt 202');
    cy.get(cityInput).clear().type('New City').should('have.value', 'New City');
    cy.get(stateDropdownBtn).click();
    cy.contains('Texas').click();
    cy.get(stateDropdownBtn).should('have.text', 'Texas');
    cy.get(zipInput).clear().type('75001').should('have.value', '75001');

    // -----------Verify Tax Classification /Payments Section after edit -----------
    cy.get(taxClassificationDropdown).click();
    cy.contains('C Corporation').click();
    cy.get(taxClassificationDropdown).should('contain', 'C Corporation');
    cy.get(payFrequencyDropdown).click();
    cy.contains('Biweekly').click();
    cy.get(payFrequencyDropdown).should('contain', 'Biweekly');
    cy.get(payDaysDropdown).click();
    cy.contains('Friday').click();
    cy.get(payDaysDropdown).should('contain', 'Friday');

    // ----------- Verify Update -----------
    cy.get(createUpdateBtn).should('be.visible').and('not.be.disabled').click();
    cy.wait('@updateCompany').its('response.statusCode').should('eq', 200);
  });
});
