 server {
  listen 80;
  server_name localhost;
  charset utf-8;

  # files transfer
  client_body_in_file_only clean;
  client_body_buffer_size 32K;
  client_max_body_size 1026g;
  sendfile on;
  send_timeout 300s;

  # response body
  proxy_max_temp_file_size 1048m;

  # redirect server error pages / and set response status to 200 / ok
  error_page 404 =200 /;

  root /usr/share/nginx/html/v2;
  index index.html index.html;

  location /v2/ { 
      root /usr/share/nginx/html;
      try_files $uri $uri/ /index.html;
  }

  ## Proxy requests to "/auth" and "/api" to api-server.
  #location ~ ^/(auth|api) {
  #  proxy_pass http://api-server;
  #  proxy_redirect off;
  #}

  # deny access to .htaccess files, if Apache's document root concurs with nginx's one
  location ~ /\.ht {
    deny all;
  }

  # deny access to hidden files (beginning with a period)
  location ~ /\.(?!well-known).* {
      access_log off; log_not_found off; deny all;
  }
}
