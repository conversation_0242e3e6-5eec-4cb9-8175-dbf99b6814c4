<template>
  <Menu
    v-if="!disabled"
    as="div"
    class="relative inline-block text-left"
    v-slot="{ open }"
  >
    <template>
      {{ captureOpenState(open) }}
    </template>
    <label
      v-if="label"
      for="email"
      class="block text-xs font-xs text-gray-700 dark:text-gray-400 pb-1"
    >
      {{ label }}
    </label>
    <div>
      <MenuButton
        class="inline-flex w-full justify-between rounded-md border border-gray-300 dark:border-gray-500 bg-white dark:bg-gray-700 px-3 py-1.5 text-sm font-sm text-gray-800 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 light:focus:ring-indigo-500 focus:ring-offset-2 focus:ring-offset-gray-100 dark:focus:ring-offset-gray-300"
        :disabled="disabled || loading"
      >
        <div v-if="loading" class="animate-pulse">Loading...</div>
        <div v-else>
          <slot name="label" :value="modelValue" />
          <div v-if="!$slots.label">
            {{ displayValue || placeholder }}
          </div>
        </div>
        <ChevronDownIcon class="-mr-1 ml-2 h-5 w-5" aria-hidden="true" />
      </MenuButton>
    </div>

    <transition
      enter-active-class="transition ease-out duration-100"
      enter-from-class="transform opacity-0 scale-95"
      enter-to-class="transform opacity-100 scale-100"
      leave-active-class="transition ease-in duration-75"
      leave-from-class="transform opacity-100 scale-100"
      leave-to-class="transform opacity-0 scale-95"
    >
      <MenuItems
        ref="menuItems"
        class="absolute left-0 max-h-64 z-30 w-60 origin-top-right rounded-md bg-white dark:bg-gray-700 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none overflow-scroll"
        :class="positionClass"
        @scroll="onScroll"
      >
        <div class="py-1">
          <div class="relative mx-2 pt-1 flex row">
            <MagnifyingGlassIcon class="w-6 dark:text-gray-200 mr-2" />
            <input
              v-on:keydown.stop
              id="email-address"
              :v-model="search"
              @input="updateSearch"
              placeholder="Search..."
              class="appearance-none block w-full px-3 py-2 rounded-md shadow-sm text-gray-800 dark:text-gray-200 dark:bg-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
          <MenuItem v-if="!items.length && !loading" disabled>
            <span
              class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-400"
            >
              No items found
            </span>
          </MenuItem>
          <MenuItem v-if="loading" disabled>
            <span
              class="block px-4 py-2 text-sm animate-pulse text-gray-700 dark:text-gray-400"
            >
              ...
            </span>
          </MenuItem>
          <MenuItem
            v-for="(item, itemIndex) in items"
            :key="`combobox-item-${itemIndex}`"
            v-slot="{ active }"
          >
            <span
              class="cursor-pointer justify-between w-full flex items-center"
              @click="updateValue(item)"
              :class="[
                active
                  ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-200'
                  : 'text-gray-700 dark:text-gray-400',
                checkTheStatus(item, displayValue)
                  ? 'bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-gray-200'
                  : 'text-gray-700 dark:text-gray-400',
                'block px-4 py-2 text-sm',
              ]"
            >
              <slot name="item" :value="item" />
              <Icon
                v-if="checkTheStatus(item, displayValue)"
                name="dropdown-check"
                class="inline-flex w-6 h-4 stroke-indigo-600 dark:stroke-indigo-50"
              />
            </span>
          </MenuItem>
          <div v-if="loading" class="flex justify-center">
            <Spinner class="w-4 h-4" />
          </div>
        </div>
      </MenuItems>
    </transition>
  </Menu>
  <TextInput
    v-else
    :label="label"
    :disabled="disabled"
    :model-value="displayValue"
  />
</template>

<script lang="ts">
import Icon from '@/components/Icon.vue';
import Spinner from '@/components/library/Spinner.vue';
import TextInput from '@/components/library/TextInput.vue';
import type { Pagination } from '@/types/Pagination';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
import { ChevronDownIcon, MagnifyingGlassIcon } from '@heroicons/vue/20/solid';
import { debounce, type DebounceSettings } from 'lodash';
import { defineComponent, type PropType } from 'vue';

export default defineComponent({
  emits: ['update:modelValue', 'update:search', 'update:pagination', 'change'],
  props: {
    modelValue: {
      // Use a custom validator to allow null or undefined
      validator(value) {
        // Check if the value is one of the allowed types or null/undefined
        return (
          value === null ||
          value === undefined ||
          typeof value === 'string' ||
          typeof value === 'number' ||
          typeof value === 'object'
        );
      },
      required: true,
    },
    items: {
      type: Array,
      default() {
        return [];
      },
    },
    search: {
      type: String,
      default: null,
    },
    placeholder: {
      type: String,
      default: 'Select',
    },
    displayName: {
      type: String,
      default: 'label',
    },
    valueName: {
      type: String,
      default: null,
    },
    label: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    pagination: {
      type: Object as PropType<Pagination>,
      default() {
        return {
          page: 1,
          limit: 10,
          total: 0,
        };
      },
    },
    infiniteScroll: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    MagnifyingGlassIcon,
    ChevronDownIcon,
    Menu,
    MenuButton,
    MenuItem,
    MenuItems,
    TextInput,
    Spinner,
    Icon,
  },
  data() {
    return {
      isOpen: false,
      isOff: {
        top: false,
        right: false,
        bottom: false,
        left: false,
      },
    };
  },
  watch: {
    isOpen: {
      handler() {
        if (!this.isOpen) {
          this.isOff = {
            top: false,
            right: false,
            bottom: false,
            left: false,
          };
          this.updateSearch({ target: { value: '' } });
        }
        this.$nextTick(() => {
          this.isOff.top = this.checkIsOffTop();
          this.isOff.right = this.checkIsOffRight();
          this.isOff.left = this.checkIsOffLeft();
          this.isOff.bottom = this.checkIsOffBottom();
        });
      },
      immediate: true,
    },
  },
  computed: {
    displayValue(): string {
      let item: any = this.modelValue;
      if (this.valueName) {
        item = this.items.find(
          (i: any) => i[this.valueName] === this.modelValue,
        );
      }
      const value = item?.[this.displayName];
      return value ? `${value}` : '';
    },
    menuItemsBoundingBox(): any {
      const element = (this.$refs.menuItems as any)?.$el;
      return element ? element.getBoundingClientRect() : null;
    },
    positionClass(): string {
      const { top, right, bottom, left } = this.isOff;
      let positionClasses = [];

      if (bottom) {
        positionClasses.push('bottom-full top-auto');
      } else {
        positionClasses.push('top-full');
      }

      if (left && right) {
        positionClasses.push('left-0 right-0');
      } else if (left) {
        positionClasses.push('left-0');
      } else if (right) {
        positionClasses.push('right-0');
      }

      return positionClasses.join(' ');
    },
  },
  methods: {
    updateSearch: async function (event: any) {
      debounce(async () => {
        this.$emit('update:search', event.target.value);
      }, 500)();
    },
    updateValue(event: any) {
      const value = this.valueName ? event[this.valueName] : event;
      this.$emit('update:modelValue', value);
      this.$emit('change');
    },
    captureOpenState(open: boolean) {
      this.isOpen = open;
    },
    checkIsOffTop() {
      const rect = this.menuItemsBoundingBox;
      return rect && rect.top < 0;
    },
    checkIsOffRight() {
      const rect = this.menuItemsBoundingBox;
      return rect && rect.right > window.innerWidth;
    },
    checkIsOffLeft() {
      const rect = this.menuItemsBoundingBox;
      return rect && rect.left < 0;
    },
    checkIsOffBottom() {
      const rect = this.menuItemsBoundingBox;
      return rect && rect.bottom > window.innerHeight;
    },
    checkIsScrollable() {
      const noOfPages = this.pagination.total / this.pagination.limit;
      return noOfPages > this.pagination.page;
    },
    onScroll: async function (event: any) {
      debounce(
        async () => {
          let isScrollable = this.checkIsScrollable();
          if (!this.infiniteScroll || !isScrollable) return;
          const { scrollTop, scrollHeight, clientHeight } = event.target;
          if (scrollTop + clientHeight >= scrollHeight - 1) {
            this.$emit('update:pagination', {
              ...this.pagination,
              page: this.pagination.page + 1,
            } as Pagination);
          }
        },
        1500,
        { leading: true, maxWait: 0 } as DebounceSettings,
      )();
    },
    checkTheStatus(item: any, selectedItem: any) {
      return item[this.displayName] === selectedItem;
    },
  },
});
</script>
