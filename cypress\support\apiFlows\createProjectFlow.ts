import { faker } from '@faker-js/faker';
import {
  fetchMinuteIncrements,
  fetchCompanies,
  fetchCompanyAddress,
  fetchProjectTypesByCompany,
  fetchWorkLocationByProjectType,
} from '../apiHelpers';
import { createProject } from '../apiCreateHelpers';

/**
 * Creates a project flow using company id
 * It fetches the minute increments, companies, company address, 
  project types, work location and then creates a project.
 */
export function createProjectFlow(
  projectName?: string,
): Cypress.Chainable<any> {
  const startsOn = faker.date.soon({ days: 7 });
  const endsOn = faker.date.soon({ days: 7, refDate: startsOn });
  const projectDTO = {
    name:
      projectName ||
      `TEST-${faker.company.name()} ${faker.number.int({
        min: 1000,
        max: 9999,
      })}`.slice(0, 45),
    number: `${faker.number.int({ min: 100, max: 9999 })}`,
    description: faker.commerce.productDescription(),
    mileageAicpNumber: `${faker.number.int({ min: 100, max: 9999 })}`,
    startsAt: startsOn,
    endsAt: endsOn,
    payPeriods: [],
    isApplyingForTaxIncentives: true,
    departments: [
      {
        type: {
          id: 2,
          name: 'General',
          key: 'general',
          description: '',
        },
      },
    ],
  };

  // Step 1: Fetch minute increments
  return fetchMinuteIncrements().then((minuteIncrements) => {
    const minuteIncrement =
      minuteIncrements[
        faker.number.int({ min: 0, max: minuteIncrements.length - 1 })
      ];
    projectDTO['minuteIncrement'] = minuteIncrement;
    // Step 2: Fetch companies
    fetchCompanies().then((companies) => {
      const company = companies.data.find(
        (company) => company.name === Cypress.env('companyName'),
      );
      const companyId = company.id;
      projectDTO['productionCompany'] = company;
      // Step 3: Fetch company address
      fetchCompanyAddress(companyId).then((companyAddress) => {
        projectDTO['productionCompanyAddress'] = companyAddress[0];
        // Step 4: Fetch project types
        fetchProjectTypesByCompany(companyId).then((projectTypes) => {
          const castAndCrewId = projectTypes[0].castAndCrewId;
          projectDTO['type'] = projectTypes[0];
          // Step 5: Fetch companies work locations
          fetchWorkLocationByProjectType(companyId, castAndCrewId).then(
            (workLocation) => {
              const location =
                workLocation.result.items[
                  faker.number.int({
                    min: 0,
                    max: workLocation.result.items.length - 1,
                  })
                ];

              projectDTO['projectShootLocations'] = [
                {
                  shootLocation: location,
                  shootLocationId: location.locationId,
                  zip: location.zipCode,
                },
              ];
              // Step 6: Create project
              return createProject(projectDTO).then((res) => {
                Cypress.env('projectId', res.body.id);
                const hashId = res.body.hashId;
                Cypress.env('projectHashId', hashId);
                return res;
              });
            },
          );
        });
      });
    });
  });
}
