import { applyPureVueInReact } from 'veaury';
import ProjectTimecardsViewVue from '../../../views/projects/ProjectTimecardsView.vue';
import { useAuth, useReactRouter } from '../../AppHooks';
import { useOutletContext } from 'react-router';
const ReactProjectTimecardsView = applyPureVueInReact(ProjectTimecardsViewVue);

const ProjectTimecardsView = () => {
  useAuth();
  const { navigate, route } = useReactRouter();
  const context = useOutletContext();
  return (
    <ReactProjectTimecardsView
      route={route}
      navigate={navigate}
      project={context.project}
    />
  );
};

export default ProjectTimecardsView;
