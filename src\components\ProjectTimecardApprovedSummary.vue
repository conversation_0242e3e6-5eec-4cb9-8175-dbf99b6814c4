<template>
  <div
    class="max-w-4xl mx-auto mt-6 mb-10 p-6 bg-white dark:bg-gray-700 shadow-lg rounded-lg"
  >
    <div class="flex align-center justify-between mb-3">
      <h2 class="text-xl font-semibold">Timecard Details</h2>
      <Button
        v-if="timecard.fileId"
        color="gray"
        size="sm"
        @click="downloadFullTimecard"
        data-testid="download-timecard-btn"
      >
        <div class="flex items-center space-x-1">
          <ArrowDownTrayIcon class="h-4 w-4" />
          <div>Download</div>
        </div>
      </Button>
    </div>
    <p
      v-for="(timecardDetail, timecardDetailIndex) in timecardDetails"
      :key="timecardDetailIndex"
      class="dark:text-gray-200"
      :data-testid="`timeCard-${timecardDetail?.label}`"
    >
      {{ timecardDetail.label }}:
      <span class="font-medium">{{ timecardDetail.value }}</span>
    </p>
    <div class="border-b border-gray-200 dark:border-gray-500 my-6" />
    <div v-for="day in activeTimecardDays" :key="day.id" class="mb-4">
      <h4
        class="text-lg font-semibold"
        :data-testid="`timeCard-title-${day?.date.toFormat('MM/dd/yyyy')}`"
      >
        {{ formatDate(day.date) }}
      </h4>
      <p
        v-for="(
          timecardDayDetail, timecardDayDetailIndex
        ) in getTimecardDayDetails(day)"
        :key="timecardDayDetailIndex"
        class="dark:text-gray-200"
        :data-testid="`timeCard-${
          timecardDayDetail?.label
        }-${day?.date.toFormat('MM/dd/yyyy')}`"
      >
        {{ timecardDayDetail.label }}:
        <span class="font-medium">
          {{ timecardDayDetail.value }}
        </span>
      </p>
    </div>
    <template v-if="timecard?.mileageForm">
      <div class="border-b border-gray-200 dark:border-gray-500 my-6" />
      <div>
        <div class="flex justify-between align-center">
          <h2 class="text-lg font-semibold">Mileage</h2>
          <Button
            v-if="timecard.mileageForm.documentId"
            color="none"
            size="sm"
            @click="
              downloadOtherPdf(timecard.mileageForm.documentId, 'Mileage Form')
            "
          >
            <div class="flex items-center space-x-1">
              <ArrowDownTrayIcon class="h-4 w-4" />
              <div>Download</div>
            </div>
          </Button>
        </div>
        <p class="dark:text-gray-200">
          Total Mileage:
          <span class="font-medium">{{
            timecard.mileageForm.totalMileage
          }}</span>
        </p>
      </div>
    </template>
    <template v-if="timecard?.kitRental">
      <div class="border-b border-gray-200 dark:border-gray-500 my-6" />
      <div>
        <div class="flex justify-between align-center">
          <h2 class="text-lg font-semibold">Kit Rental</h2>
          <Button
            v-if="timecard.kitRental.documentId"
            color="none"
            size="sm"
            @click="
              downloadOtherPdf(timecard.kitRental.documentId, 'Kit Rental')
            "
          >
            <div class="flex items-center space-x-1">
              <ArrowDownTrayIcon class="h-4 w-4" />
              <div>Download</div>
            </div>
          </Button>
        </div>
        <p class="dark:text-gray-200">
          Rate:
          <span class="font-medium"
            >{{ centsToDollars(timecard.kitRental.rentalRate) }}
            {{ timecard.kitRental.rateType.name }}</span
          >
        </p>
      </div>
    </template>
    <template v-if="reimbursements?.length">
      <div class="border-b border-gray-200 dark:border-gray-500 my-6" />
      <div>
        <h2 class="text-lg font-semibold">Reimbursements</h2>
        <div
          v-for="(reimbursement, reimbursementIndex) in reimbursements"
          :key="reimbursementIndex"
          class="dark:text-gray-200 flex justify-start space-x-2 items-center"
        >
          <div>{{ reimbursement.name }}:</div>
          <div class="font-medium">
            {{ centsToDollars(reimbursement.rate * reimbursement.quantity) }}
            ({{ reimbursement.quantity }} @
            {{ centsToDollars(reimbursement.rate) }})
          </div>
          <div>
            <span class="font-semibold"> Date:</span>
            {{ reimbursement.date.toUTC().toFormat('MM/dd/yy') }}
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import Button from '@/components/library/Button.vue';
import { getPdfUrl } from '@/services/pdf';
import { listTimecardReimbursements } from '@/services/reimbursements';
import { downloadTimecard } from '@/services/timecards';
import type Reimbursement from '@/types/Reimbursements';
import type Timecard from '@/types/Timecard';
import type TimecardDay from '@/types/TimecardDay';
import { WorkStatusKeys } from '@/types/WorkStatus';
import { centsToDollars } from '@/utils/string';
import { ArrowDownTrayIcon } from '@heroicons/vue/20/solid';
import { DateTime } from 'luxon';
import { computed, onMounted, ref } from 'vue';
const props = defineProps<{
  timecard: Timecard;
}>();

const reimbursements = ref<Reimbursement[]>([]);

onMounted(async () => {
  const timecardId = props.timecard.id;
  if (timecardId) {
    const reimbursementsResposne = await listTimecardReimbursements(timecardId);
    reimbursements.value = reimbursementsResposne.data;
  }
});

const activeTimecardDays = computed(() => {
  return props.timecard?.timecardDays.filter(({ isActive }) => isActive) || [];
});

const formatDate = (date: DateTime): string => {
  if (!date) return '';
  return date.toLocaleString(DateTime.DATE_FULL);
};

const formatTime = (date: DateTime): string => {
  if (!date) return '';
  return date.toFormat('h:mm a');
};

const formatDateTimeConditionally = (
  dayDate: DateTime,
  timeDate: DateTime,
): string => {
  const dayDateString = formatDate(dayDate);
  const timeDateString = formatDate(timeDate);
  const timeString = formatTime(timeDate);

  return dayDateString === timeDateString
    ? timeString
    : `${timeDateString} ${timeString}`;
};

const formattedMeals = (timecardDay: TimecardDay): string => {
  return timecardDay.meals
    .map((meal) => {
      return `${formatDateTimeConditionally(
        timecardDay.date,
        meal.startsAt,
      )} - ${formatDateTimeConditionally(timecardDay.date, meal.endsAt)}`;
    })
    .join(', ');
};

const getTimecardDayDetails = (
  day: TimecardDay,
): { label: string; value: string }[] => {
  const preventTimeDetails =
    day.workStatus?.key === WorkStatusKeys.KEY_IDLE ||
    day.workStatus?.key === WorkStatusKeys.KEY_PAID_IDLE ||
    day.workStatus?.key === WorkStatusKeys.KEY_TRAVEL ||
    day.workStatus?.key === WorkStatusKeys.KEY_HNW ||
    day.workStatus?.key === WorkStatusKeys.KEY_DOWN ||
    day.workStatus?.key === WorkStatusKeys.KEY_CANCELPAY_NOREQ_NOPW ||
    day.workStatus?.key === WorkStatusKeys.KEY_CANCELPAY_REQ_WITHPW ||
    day.workStatus?.key === WorkStatusKeys.KEY_DGA_IDLE_PEN_ONLY ||
    day.workStatus?.key === WorkStatusKeys.KEY_HOL ||
    day.workStatus?.key === WorkStatusKeys.KEY_HOLNOPAY;
  const parseHoursWorked = Number(day.hoursWorked).toFixed(2);
  return [
    {
      label: 'Start',
      value: preventTimeDetails
        ? ' - '
        : formatDateTimeConditionally(day.date, day.startsAt),
    },
    {
      label: 'End',
      value: preventTimeDetails
        ? ' - '
        : formatDateTimeConditionally(day.date, day.endsAt),
    },
    {
      label: 'Hours Worked',
      value: parseHoursWorked.toString(),
    },
    {
      label: 'Meals',
      value: preventTimeDetails ? ' - ' : formattedMeals(day),
    },
  ];
};

const timecardDetails = computed(() => {
  return [
    {
      label: 'Pay Period',
      value: `${formatDate(
        props.timecard?.payPeriod?.startsAt!,
      )} - ${formatDate(props.timecard?.payPeriod?.endsAt!)}`,
    },
    {
      label: 'Status',
      value: props.timecard.status.name,
    },
    {
      label: 'Created',
      value: formatDate(props.timecard.createdAt),
    },
    {
      label: 'Updated',
      value: formatDate(props.timecard.updatedAt),
    },
  ];
});

const downloadFullTimecard = () => {
  if (!props.timecard.fileId) return;
  const link = document.createElement('a');
  link.href = downloadTimecard(props.timecard.id);

  link.setAttribute(
    'download',
    `${props.timecard.payPeriod?.startsAt?.toFormat('MM_dd')}_timecard.pdf`,
  );
  document.body.appendChild(link);
  link.click();
};

const downloadOtherPdf = (fileId: string, fileName: string) => {
  const link = document.createElement('a');
  link.href = getPdfUrl(fileId);
  link.setAttribute('download', fileName);
  document.body.appendChild(link);
  link.click();
};
</script>
