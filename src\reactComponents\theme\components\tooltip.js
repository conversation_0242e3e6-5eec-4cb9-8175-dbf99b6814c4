import { tooltipClasses } from '@mui/material/Tooltip';

const tooltip = {
  styleOverrides: {
    tooltip: {
      variants: [
        {
          props: { variant: 'html' },
          style: (buttonProps) => {
            const { theme } = buttonProps;
            const { palette } = theme;
            const newStyles = {
              backgroundColor: palette.background.paper,
              color: palette.gray[600],
              fontWeight: 500,
              border: `1px solid ${palette.gray[100]}`,
              fontSize: theme.typography.pxToRem(12),
              boxShadow: `4px 6px 6px -4px rgba(0,0,0,0.2),0px 8px 10px 1px rgba(0,0,0,0.14),0px 3px 14px 2px rgba(0,0,0,0.12)`,

              [`& .${tooltipClasses.arrow}`]: {
                color: palette.background.paper,
              },
              ...theme.applyStyles('dark', {
                color: '#fff',
                [`& .${tooltipClasses.arrow}`]: {
                  color: '#fff',
                },
              }),
            };

            return newStyles;
          },
        },
      ],
    },
  },
};

export default tooltip;
