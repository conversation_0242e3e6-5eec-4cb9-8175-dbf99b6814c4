import React from 'react';
import PropTypes from 'prop-types';

//services (api)
import { listTimecardAudit } from '@/services/timecards';

import { snackbarErr } from '@/reactComponents/library/Snackbar';
//components
import { Modal } from '@/reactComponents/library';
//local components
import HistoryItem from './HistoryItem';

const HistoryModal = (props) => {
  const { timecardId, open, setOpen } = props;
  const [history, setHistory] = React.useState([]);
  const [loading, setLoading] = React.useState(true);

  const getTimecardAudits = React.useCallback(async () => {
    try {
      const { data } = await listTimecardAudit(timecardId);
      // can remove these checks after MTG-974 is resolved
      if (!Array.isArray(data)) throw new Error('Error fetching history');
      if (data.errors) throw new Error(data.errors[0].message);

      data.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

      setHistory(data);
    } catch (error) {
      snackbarErr('Error fetching history');
      console.error('Error fetching history', error);
      setOpen(false);
    } finally {
      setLoading(false);
    }
  }, [setOpen, timecardId]);

  React.useEffect(() => {
    if (open) {
      setLoading(true);
      getTimecardAudits();
    }
  }, [getTimecardAudits, open]);

  if (!open) return null;

  return (
    <Modal
      isSidebar={true}
      title={'History'}
      open={open}
      setOpen={setOpen}
      loading={loading}
    >
      {history.map((audit) => (
        <HistoryItem key={audit.id} audit={audit} />
      ))}
    </Modal>
  );
};

HistoryModal.propTypes = {
  timecardId: PropTypes.number.isRequired,
  open: PropTypes.bool.isRequired,
  setOpen: PropTypes.func.isRequired,
};

export default HistoryModal;
