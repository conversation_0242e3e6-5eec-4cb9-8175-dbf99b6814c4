import { applyPureVueInReact } from 'veaury';
import CompaniesViewVue from '../../../views/companies/CompaniesView.vue';
import { useAuth, useReactRouter } from '../../AppHooks';

const ReactCompaniesView = applyPureVueInReact(CompaniesViewVue);

const CompaniesView = () => {
  useAuth();
  const { navigate, route } = useReactRouter();
  return <ReactCompaniesView route={route} navigate={navigate} />;
};

export default CompaniesView;
