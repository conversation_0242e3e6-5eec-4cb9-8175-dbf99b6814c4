import type ProjectShootLocation from '@/types/ProjectShootLocation';
import axios, { type AxiosResponse } from 'axios';
import { coreBaseUrl } from '../utils/constants';

export const updateMileageForm = async (
  mileageFormId: number | string,
  payload: {
    lineNumber: string;
    totalMileage: number;
    documentId?: string;
    workLocation: ProjectShootLocation;
    date: string;
  },
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/mileage-forms/${mileageFormId}`;
  const response = await axios.patch(url, payload, { withCredentials: true });
  return response;
};

export const deleteMileageForm = async (
  mileageFormId: number | string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/mileage-forms/${mileageFormId}`;
  const response = await axios.delete(url, { withCredentials: true });
  return response;
};
