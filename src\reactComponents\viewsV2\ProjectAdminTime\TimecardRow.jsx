import React from 'react';
import PropTypes from 'prop-types';
import { Link, useParams } from 'react-router';

import {
  Box,
  Avatar,
  MenuItem,
  Text,
  StatusBadge,
  IconButton,
  Menu,
  Tooltip,
} from '@/reactComponents/library';

import MoveToBatchModal from '../sharedComponents/MoveToBatchModal';
import RemoveFromBatchModal from './RemoveFromBatchModal';
import { BatchStatusEnum } from '@/types/Batch';

import MoreVertIcon from '@mui/icons-material/MoreVert';

import TimeStore from './store';
import { observer } from 'mobx-react-lite';

import { UNBATCHED_ID } from './utils';
import ReportOutlinedIcon from '@mui/icons-material/ReportOutlined';

const styles = {
  tableRow: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    backgroundColor: 'background.paper',
    border: '1px solid',
    borderColor: 'background.border',
    borderRadius: '8px',
    p: 1,
  },
  tableCell: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    p: 1,
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    width: '100%',

    '&:last-of-type': {
      ml: 'auto',
    },
  },
  hiddenContainer: {
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
  },
  hiddenText: { overflow: 'hidden', textOverflow: 'ellipsis' },
  dragImage: {
    position: 'absolute',
    top: '-9999px',
    backgroundColor: 'background.paper',
    display: 'flex',
    width: 'fit-content',
    maxWidth: '250px',
    zIndex: 1000,
    border: '2px solid',
    borderColor: 'primary.main',
    borderRadius: 1,
    alignItems: 'flex-start',
    flexDirection: 'column',
    color: 'text.secondary',
    py: 1,
    px: 2,
  },
  errorText: {
    color: '#B42318',
    display: 'flex',
    alignItems: 'center',
    fontSize: '14px',
    fontWeight: 600,
  },
};

const TimecardRow = observer((props) => {
  const {
    timecard,
    columns,
    batch,
    project,
    fetchBatches,
    fetchTimecards,
    checkUnapprovedTimecards,
  } = props;

  const { hashId } = useParams();

  const [menuOpen, setMenuOpen] = React.useState(false);
  const [moveModalOpen, setMoveModalOpen] = React.useState(false);
  const [removeBatchOpen, setRemoveBatchOpen] = React.useState(false);
  const [dragActive, setDragActive] = React.useState(false);
  const menuAnchorRef = React.useRef(null);

  const canMove = React.useMemo(() => {
    return (
      batch.statusId === BatchStatusEnum.OPEN ||
      batch.statusId === BatchStatusEnum.REOPENED ||
      batch.id === UNBATCHED_ID
    );
  }, [batch]);

  const tcUser = timecard.userCrew.user;
  const projectMemberId = timecard.projectMemberId;
  const name = `${tcUser.firstName} ${tcUser.lastName}`;
  const occupation = timecard.occupation?.name || '';
  const url = `/projects/${hashId}/admin/members/${projectMemberId}/member/timecards/${timecard.id}`;

  const isUnbatched = batch.id === UNBATCHED_ID;

  let rowStyles = styles.tableRow;

  if (dragActive) {
    rowStyles = {
      ...rowStyles,
      backgroundColor: 'background.drag',
      border: '2px solid',
      borderColor: 'primary.main',
    };
  }

  return (
    <React.Fragment key={timecard.id}>
      <Link draggable={false} to={url}>
        <MenuItem
          sx={rowStyles}
          draggable={canMove}
          disabled={TimeStore.isMovingTimecardId(timecard.id)}
          onDragStart={(e) => {
            setDragActive(true);
            e.dataTransfer.setData('text/plain', timecard.id);
            e.dataTransfer.setDragImage(
              document.getElementById(`${timecard.id}-dragImage`),
              50,
              50,
            );
            e.dataTransfer.effectAllowed = 'move';

            TimeStore.draggedTimecard = timecard;
          }}
          onDragEnd={(e) => {
            setDragActive(false);
            e.dataTransfer.clearData();
            e.dataTransfer.dropEffect = 'none';
            TimeStore.draggedTimecard = null;
          }}
          disableRipple
          data-testid={`timecard-row-${timecard.id}`}
        >
          {columns.map((column) => {
            const cellSx = {
              ...styles.tableCell,
              width: column.width,
              maxWidth: column.width,
            };
            let value = '';
            switch (column.id) {
              case 'avatar':
                value = <Avatar name={name} size={'md'} />;
                break;
              case 'name':
                value = (
                  <Box sx={styles.hiddenContainer}>
                    <Text variant="smMed" sx={styles.hiddenText}>
                      {name}
                    </Text>
                    <Tooltip title={occupation?.length > 19 ? occupation : ''}>
                      <Text variant="smReg" sx={styles.hiddenText}>
                        {occupation}
                      </Text>
                    </Tooltip>
                  </Box>
                );
                break;
              case 'department':
                value = (
                  <Box sx={styles.hiddenContainer}>
                    <Text variant="smMed" sx={styles.hiddenText}>
                      {timecard.projectMember?.department?.type.name || ''}
                    </Text>
                  </Box>
                );
                break;
              case 'payPeriod': {
                const startsAtStr =
                  timecard.payPeriod.startsAt.toFormat('MM/dd/yy');
                const endsAtStr =
                  timecard.payPeriod.endsAt.toFormat('MM/dd/yy');
                value = `${startsAtStr} - ${endsAtStr}`;
                break;
              }
              case 'gross':
                value = timecard.calculationErrorMessage ? (
                  <>
                    <ReportOutlinedIcon sx={{ color: '#B42318' }} />{' '}
                    <Text sx={styles.errorText}>Errors</Text>
                  </>
                ) : (
                  timecard.gross || '0.00'
                );
                break;
              case 'status': {
                const statusId = timecard.status?.id;
                value = <StatusBadge tcStatusId={statusId} />;
                break;
              }
              case 'actions':
                value = (
                  <>
                    <Tooltip
                      title={canMove ? '' : 'Batch is not Open'}
                      arrow
                      placement="top"
                    >
                      <span onClick={(e) => e.stopPropagation()}>
                        <IconButton
                          ref={menuAnchorRef}
                          disabled={!canMove}
                          onClick={(e) => {
                            e.stopPropagation();
                            e.preventDefault();
                            setMenuOpen(true);
                          }}
                        >
                          <MoreVertIcon />
                        </IconButton>
                      </span>
                    </Tooltip>
                  </>
                );

                break;
              default:
                break;
            }

            if (typeof value === 'string') {
              value = <Text variant="smReg">{value}</Text>;
            }

            return (
              <Box key={column.id} sx={cellSx}>
                {value}
              </Box>
            );
          })}
        </MenuItem>
        <Box id={`${timecard.id}-dragImage`} sx={styles.dragImage}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar name={name} size={'md'} />
            <Box sx={{ ml: 1, display: 'flex', flexDirection: 'column' }}>
              <Text variant={'smStrong'}>{name}</Text>
              <Text variant={'smReg'}>{occupation}</Text>
            </Box>
          </Box>

          <Text variant="smReg" sx={{ mt: 1 }}>
            Pay Period {timecard.payPeriod.startsAt.toFormat('MM/dd/yy')}
          </Text>
        </Box>
      </Link>
      <Menu
        open={menuOpen}
        onClose={() => setMenuOpen(false)}
        anchorEl={menuAnchorRef.current}
      >
        <MenuItem
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            setMenuOpen(false);
            setMoveModalOpen(true);
          }}
        >
          <Text variant="baseMed">Move to batch</Text>
        </MenuItem>
        {!isUnbatched && (
          <MenuItem
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              setMenuOpen(false);
              setRemoveBatchOpen(true);
            }}
          >
            <Text variant="baseMed">Remove from batch</Text>
          </MenuItem>
        )}
      </Menu>
      {moveModalOpen && (
        <MoveToBatchModal
          open={moveModalOpen}
          setOpen={setMoveModalOpen}
          batch={batch}
          project={project}
          onCreateComplete={fetchBatches}
          timecard={timecard}
          onMoveComplete={fetchTimecards}
          checkUnapprovedTimecards={checkUnapprovedTimecards}
        />
      )}

      {removeBatchOpen && (
        <RemoveFromBatchModal
          open={removeBatchOpen}
          setOpen={setRemoveBatchOpen}
          batch={batch}
          project={project}
          fetchBatches={fetchBatches}
          timecard={timecard}
          fetchTimecards={fetchTimecards}
          checkUnapprovedTimecards={checkUnapprovedTimecards}
        />
      )}
    </React.Fragment>
  );
});

TimecardRow.propTypes = {
  batch: PropTypes.object,
  columns: PropTypes.array.isRequired,
  fetchBatches: PropTypes.func.isRequired,
  project: PropTypes.object,
  fetchTimecards: PropTypes.func.isRequired,
  timecard: PropTypes.object.isRequired,
  checkUnapprovedTimecards: PropTypes.func.isRequired,
};

export default TimecardRow;
