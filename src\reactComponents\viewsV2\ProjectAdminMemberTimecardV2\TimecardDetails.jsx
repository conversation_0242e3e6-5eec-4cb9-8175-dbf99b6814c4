import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

import { styled } from '@mui/material';
import {
  Autocomplete,
  Box,
  PillBadge,
  Paper,
  GridTextField,
  Checkbox,
  Tooltip,
} from '@/reactComponents/library';

import TextCell from './EditCells/TextCell';
import AutocompleteCell from './EditCells/AutocompleteCell';

import PayrollNoteModal from './PayrollNoteModal';
import {
  rateTypeFilter,
  // guaranteedHoursFilter,
  supervisorRateTypeFilter,
} from '@/utils/filterRateTypes';
import { useDidMount } from '@/reactComponents/utils/customHooks';

const styles = {
  rootPaper: { width: '100%', p: 0, gap: 2 },
  title: {
    height: '68px',
    display: 'flex',
    alignItems: 'center',
    fontSize: '18px',
    fontWeight: 'bold',
    borderBottom: '1px solid',
    borderColor: 'background.border',
    pl: 2,
  },
};

const Details = styled(Box)(({ theme }) => ({
  paddingBottom: '16px',
  margin: '0px 16px 0px 16px',
}));
const HeaderRowBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  width: '100%',
  justifyContent: 'space-between',
  padding: '24px 24px 16px',
}));
const HeaderCellBox = styled(Box)(({ theme }) => ({
  whiteSpace: 'nowrap',
  fontWeight: 'bold',
}));
const DetailRowBox = styled(Box)(({ theme }) => {
  const { palette, shape } = theme;
  return {
    display: 'flex',
    width: '100%',
    justifyContent: 'space-between',
    border: '1px solid',
    borderColor: palette.background.border,
    borderRadius: '12px',
    padding: '8px 24px',
    '& .gridInput, & .MuiInputBase-root': {
      borderRadius: shape.borderRadius,
      border: '1px solid',
      borderColor: 'transparent',
      padding: '0px 8px',
    },
    '& .gridInput, & .MuiInputBase-root.Mui-error': {
      border: `2px solid ${palette.error.main}`,
    },

    '& .MuiAutocomplete-popupIndicator, & .MuiAutocomplete-clearIndicator': {
      display: 'none',
    },

    '&:hover': {
      '& .gridInput, & .MuiInputBase-root': {
        textDecoration: 'underline',
      },
    },
    '&:focus-within': {
      '& .gridInput, & .MuiInputBase-root': {
        borderColor: palette.background.border,
        textDecoration: 'none',
      },
      '& .MuiAutocomplete-popupIndicator, & .MuiAutocomplete-clearIndicator': {
        display: 'inline-flex',
      },
    },
  };
});
const DetailCellBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  whiteSpace: 'nowrap',
}));

const COLUMNS = [
  {
    columnId: 'union.name',
    label: 'Union',
    type: 'pill',
    width: '75px',
  },
  {
    columnId: 'occupation',
    label: 'Occupation',
    type: 'autocomplete',
    width: '200px',
  },
  {
    columnId: 'hireLocation',
    label: 'Hire location',
    type: 'autocomplete',
    width: '200px',
  },
  {
    columnId: 'hourlyRate',
    label: 'Rate ($)',
    isStrNum: true,
    type: 'text',
    width: '100px',
    decimalPlaces: [2, 4],
  },
  {
    columnId: 'rateType',
    label: 'Rate Type',
    type: 'autocomplete',
    width: '200px',
  },
  {
    columnId: 'guarHours',
    label: 'Guaranteed Hours',
    type: 'text',
    isStrNum: true,
    width: '130px',
  },
  {
    columnId: 'payPremiumOvertime',
    label: 'Pay premium OT',
    type: 'Checkbox',
    width: '130px',
  },
  {
    columnId: 'payrollNote',
    label: 'Payroll note',
    type: 'text',
    width: '130px',
  },
];

const TimecardDetails = (props) => {
  const {
    timecard,
    batchDetails,
    updateTimecard,
    hireLocations,
    occupations = [],
    fetchOccupations,
    loadingOccupations,
    occupationsHasNextPage,
    contractSettings,
    member,
    rateTypesAll,
    displayErrors,
    readOnlyMode = false,
  } = props;
  const [rateTypes, setRateTypes] = useState([]);

  const columns = React.useMemo(() => {
    return COLUMNS.filter((column) => {
      if (column.columnId !== 'hireLocation') {
        return true;
      } else {
        return timecard.isHireLocationRequired && timecard.occupation?.name;
      }
    });
  }, [timecard.isHireLocationRequired, timecard.occupation?.name]);

  const handlePayPremiumOT = (newValue) => {
    updateTimecard({ payPremiumOvertime: newValue });
  };

  const handleHireLocationChange = (event, hireLocation) => {
    updateTimecard({ hireLocation });
  };

  const filterRateTypes = (rateTypesData) => {
    const allowedRateTypes = ['daily', 'hourly'];
    return rateTypesData.filter((rt) => allowedRateTypes.includes(rt.key));
  };

  const calculateGuarHoursAndRate = () => {
    let guarHours = 0;
    if (timecard.rateType.key === 'for_10_hours') {
      guarHours = 10;
    } else if (timecard.rateType.key === 'for_12_hours') {
      guarHours = 12;
    } else if (timecard.rateType.key === 'for_8_hours') {
      guarHours = 8;
    } else if (timecard.rateType.key === 'daily') {
      guarHours = timecard.isOnCall ? 12 : 1;
    }
    timecard.guarHours = guarHours;
    timecard.guarRate = Number(
      (timecard.guarHours * timecard.hourlyRate).toFixed(2),
    );
  };

  useDidMount(() => {
    loadData();
  }, []);

  useEffect(() => {
    if (rateTypesAll.length > 0) {
      const filteredRateTypes = filterRateTypes(rateTypesAll);

      const validRateTypes = supervisorRateTypeFilter(
        contractSettings,
        rateTypesAll,
        member?.rateType,
      );
      if (validRateTypes.length === 1) {
        if (timecard.rateTypeId !== validRateTypes[0].id) {
          setRateTypes(filterRateTypes);
          //TODO?? show a message to the user that the rateType doesn't match?
          // rateTypes don't match here, but we shouldn't update the timecard onLoad
        } else {
          setRateTypes(validRateTypes);
        }
        // keeping this incase we want to add notification for guarHours being incorrect
        // const guarHours = guaranteedHoursFilter(
        //   contractSettings,
        //   rateTypesList,
        //   member?.rateType,
        // );
        // if (timecard.guarHours !== guarHours.toFixed(2)) {
        //   //TODO?? show a message to the user that the guarHours don't match?
        //   // guarHours don't match here, but we shouldn't update the timecard onLoad
        // }
      } else {
        setRateTypes(filteredRateTypes);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [contractSettings]);

  const getRateTypes = async () => {
    const filteredRateTypes = filterRateTypes(rateTypesAll);
    const validRateTypes = rateTypeFilter(
      contractSettings,
      filteredRateTypes,
      'supervisorView',
    );
    setRateTypes(validRateTypes);
    if (!timecard.guarHours) {
      calculateGuarHoursAndRate();
    }
  };

  const loadData = async () => {
    getRateTypes();
  };

  return (
    <Paper sx={styles.rootPaper}>
      <Box sx={styles.title}>Timecard Details</Box>
      <Details>
        <HeaderRowBox>
          {columns.map((column) => (
            <HeaderCellBox
              key={column.columnId}
              sx={{ width: column.width }}
              data-testid={`timecard-details-${column.label}`}
            >
              {column.label}
            </HeaderCellBox>
          ))}
        </HeaderRowBox>
        <DetailRowBox>
          {columns.map((column) => {
            const { columnId, width } = column;

            let cellError;
            if (displayErrors[columnId]) {
              cellError = displayErrors[columnId];
            }
            const sx = {};
            if (width) sx.width = width;
            return (
              <Tooltip
                key={columnId}
                title={cellError?.message || ''}
                arrow
                placement="top"
              >
                <DetailCellBox
                  key={columnId}
                  sx={sx}
                  data-testid={`timecard-details-${columnId}`}
                >
                  {columnId === 'union.name' ? (
                    <PillBadge variant="success">
                      {timecard.union?.name}
                    </PillBadge>
                  ) : columnId === 'occupation' ? (
                    <AutocompleteCell
                      reimbursement={timecard}
                      column={column}
                      updateCell={(newVal) => {
                        updateTimecard({
                          occupation: newVal,
                          hireLocationId: null,
                          isHireLocationRequired: false,
                          hireLocation: null,
                        });
                      }}
                      disabled={readOnlyMode}
                      occupations={occupations}
                      loading={loadingOccupations}
                      fetchOptions={fetchOccupations}
                      infiniteScrolling={true}
                      hasNextPage={occupationsHasNextPage}
                      error={!!cellError}
                    />
                  ) : columnId === 'hireLocation' &&
                    timecard.isHireLocationRequired ? (
                    <Autocomplete
                      //TODO verify this with autocompleteCell
                      updateCell={(newVal) => {
                        updateTimecard({
                          hireLocation: newVal,
                        });
                      }}
                      disabled={readOnlyMode}
                      value={timecard?.hireLocation || null}
                      options={hireLocations}
                      getOptionLabel={(option) => option.name}
                      onChange={handleHireLocationChange}
                      renderInput={(params) => {
                        return (
                          <GridTextField error={!!cellError} {...params} />
                        );
                      }}
                    />
                  ) : columnId === 'hourlyRate' ? (
                    <TextCell
                      reimbursement={timecard}
                      updateCell={(newVal) => {
                        updateTimecard({ hourlyRate: newVal });
                      }}
                      column={column}
                      disabled={readOnlyMode}
                      error={!!cellError}
                    />
                  ) : columnId === 'rateType' ? (
                    <AutocompleteCell
                      reimbursement={timecard}
                      column={column}
                      updateCell={(newVal) => {
                        updateTimecard({ rateType: newVal });
                      }}
                      disabled={readOnlyMode}
                      rateTypes={rateTypes}
                    />
                  ) : columnId === 'guarHours' ? (
                    <TextCell
                      reimbursement={timecard}
                      updateCell={(newVal) => {
                        updateTimecard({ guarHours: newVal });
                      }}
                      disabled={readOnlyMode}
                      column={column}
                    />
                  ) : columnId === 'payPremiumOvertime' ? (
                    <Checkbox
                      checked={timecard.payPremiumOvertime}
                      disabled={timecard.disablePayPremiumOt || readOnlyMode}
                      onChange={(e) => {
                        handlePayPremiumOT(e.target.checked);
                      }}
                      data-testid="pay-premium-checkbox"
                    />
                  ) : columnId === 'payrollNote' ? (
                    <PayrollNoteModal
                      timecard={timecard}
                      batchDetails={batchDetails}
                      updateTimecard={updateTimecard}
                      data-testid="payroll-note-modal"
                      disabled={readOnlyMode}
                    />
                  ) : null}
                </DetailCellBox>
              </Tooltip>
            );
          })}
        </DetailRowBox>
      </Details>
    </Paper>
  );
};

TimecardDetails.propTypes = {
  timecard: PropTypes.object.isRequired,
  member: PropTypes.object.isRequired,
  updateTimecard: PropTypes.func.isRequired,
  hireLocations: PropTypes.array.isRequired,
  occupations: PropTypes.array.isRequired,
  batchDetails: PropTypes.object.isRequired,
  fetchOccupations: PropTypes.func.isRequired,
  loadingOccupations: PropTypes.bool,
  occupationsHasNextPage: PropTypes.bool,
  contractSettings: PropTypes.object.isRequired,
  rateTypesAll: PropTypes.array.isRequired,
  displayErrors: PropTypes.object.isRequired,
  readOnlyMode: PropTypes.bool,
};

export default TimecardDetails;
