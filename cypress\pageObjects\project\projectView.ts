import { DateFormatter } from '../../utils/DateFormatter';

export default class ProjectViewPageObject {
  projectTitle: string = '[data-testid="project-name-title"]';
  projectCompany: string = '[data-testid="project-productionCompany-item"]';
  projectNumber: string = '[data-testid="project-number-item"]';
  projectDates: string = '[data-testid="project-dates-item"]';
  viewTimecards: string = '[data-testid="project-timecards-btn"]';
  viewProjectOnboarding: string = '[data-testid="project-onboarding-btn"]';
  viewStartPaperWork: string =
    '[data-testid="project-starting-paperwork-generate-btn"]';

  validateProjectCreated(
    projectName: string,
    projectNumber: string,
    endsOn: Date,
    startsOn: Date,
    company: string,
  ) {
    const startFormatted = DateFormatter.formatDateMMDD(startsOn).trim();
    const endFormatted = DateFormatter.formatDateMMDD(endsOn).trim();
    const expectedText = `${startFormatted} - ${endFormatted}`;

    cy.get(this.projectTitle).should('have.text', projectName);
    cy.get(this.projectCompany).should('include.text', company);
    cy.get(this.projectNumber).should('include.text', projectNumber);
    cy.get(this.projectDates).should('include.text', expectedText);
  }

  goToTimecards() {
    cy.get(this.viewTimecards).click();
  }

  goToProjectOnboarding() {
    cy.get(this.viewProjectOnboarding).should('be.visible').click();
  }

  goToStartPaperwork() {
    cy.get(this.viewStartPaperWork).scrollIntoView().click();
  }
}
