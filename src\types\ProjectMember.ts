import type { DateTime } from 'luxon';
import type { ProjectDepartment } from './ProjectDepartment';
import type ProjectMemberLoanOutStatus from './ProjectMemberLoanOutStatus';
import type ProjectMemberType from './ProjectMemberType';
import type ProjectShootLocation from './ProjectShootLocation';
import type { RateType } from './RateType';
import type State from './State';
import type { Union } from './Union';
import type User from './User';

export default interface ProjectMember {
  createdAt: DateTime;
  workLocation: ProjectShootLocation;
  hireLocationCity: string;
  hireLocationState: State;
  hireLocationStateId: number;
  id: number;
  occupationId: number;
  occupation: any;
  lineNumber: string;
  shootLineNumber: string;
  projectId: number;
  projectMemberType: ProjectMemberType;
  projectMemberTypeId: number;
  department: ProjectDepartment;
  departmentId: number;
  loanOutStatusId: number;
  loanOutStatus: ProjectMemberLoanOutStatus;
  requiresHireLocation: boolean;
  hireLocationId: number | null;
  hireLocation: any;
  rate: number;
  rateType: RateType;
  rateTypeId: number;
  startDate: DateTime;
  startPaperwork: any;
  updatedAt: DateTime;
  user: User;
  userId: number;
  isOnCall: boolean;
  isExempt: boolean;
  isHalfDayAllowed: boolean;
  isNdbAllowed: boolean;
  isActive: boolean;
  unions: Union[];
}
