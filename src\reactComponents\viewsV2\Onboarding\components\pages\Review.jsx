import React from 'react';

import { Box } from '@/reactComponents/library';
import Section from '@/reactComponents/viewsV2/Onboarding/components/Section';
import PageTitle from '@/reactComponents/viewsV2/Onboarding/components/PageTitle';

import { PageBackgroundBox, PageSectionsBox } from '../../styledComponents';

const Review = (props) => {
  return (
    <PageBackgroundBox
      component={'form'}
      id="reviewOnboardForm"
      // onSubmit={handleSubmit(onSubmit)}
      onKeyDown={(e) => {
        if (e.ctrlKey && e.key === 'Enter') {
          const form = document.getElementById('reviewOnboardForm');
          form.requestSubmit();
        }
      }}
    >
      <PageTitle topText={'Review'} />
      <PageSectionsBox>
        <Section title={'Review Details'}>
          Details
          <Box>Details</Box>
        </Section>
        <Section title={'Review Settings'}>Settings</Section>
      </PageSectionsBox>
    </PageBackgroundBox>
  );
};

Review.propTypes = {};

export default Review;
