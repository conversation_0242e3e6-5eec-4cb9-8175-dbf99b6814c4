import React from 'react';
import PropTypes from 'prop-types';

import {
  Box,
  Button,
  CircularProgress,
  Loader,
} from '@/reactComponents/library';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import { registerUser } from '@/services/okta';
import { <PERSON><PERSON> } from '@/utils/cookie';

import OktaLoginContext from '../../Context';
import { getTransactionStep } from '../../utils';

import RegisterForm from './RegisterForm';
import BackToLogin from './BackToLogin';

const styles = {
  root: {
    width: '100%',
  },
  actions: {
    display: 'flex',
    flexDirection: 'row',
    gap: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
    mt: 2,
  },
};

const Register = (props) => {
  const { onSuccess, form } = props;
  const { encryptedPhone, user: fpsUser = null } = form || {};
  const { client: authClient, setTransaction } =
    React.useContext(OktaLoginContext);
  const [loading, setLoading] = React.useState(false);
  const [data, setData] = React.useState({
    firstName: '',
    middleName: '',
    lastName: '',
    email: '',
  });
  const hasFpsUser = React.useMemo(() => Boolean(fpsUser), [fpsUser]);

  const registerInProgress = (val = null) => {
    if (val === null) {
      return Cookie.get('registerInProgress') === 'true';
    } else {
      Cookie.set('registerInProgress', val, { lifetime: 0.5 });
    }
  };

  const identifyUser = async (data) => {
    const { email } = data;
    await authClient.authenticate();
    let response = await authClient.proceed({ username: email });
    let nextStep = getTransactionStep(response);
    setTransaction(response);
    return nextStep;
  };

  const submitRegistration = async (data) => {
    const { data: responseData } = await registerUser({
      ...data,
      encryptedPhone,
    });

    const nextStep = await identifyUser(responseData);
    registerInProgress(false);
    onSuccess({ ...data, nextStep });
  };

  const submit = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      await submitRegistration(data);
    } catch (e) {
      SnackbarStore.triggerSnackbar(e.message, 2500, 'error');
    } finally {
      setLoading(false);
    }
  };

  const changeData = (field) => (e) =>
    setData({ ...data, [field]: e.target.value });

  // submit registration
  React.useEffect(() => {
    if (!hasFpsUser || registerInProgress()) return;

    registerInProgress(true);
    window.setTimeout(() => {
      try {
        setLoading(true);
        submitRegistration(fpsUser);
      } catch (e) {
        SnackbarStore.triggerSnackbar(e.message, 2500, 'error');
      } finally {
        setLoading(false);
      }
    }, 200);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fpsUser, hasFpsUser]);

  return (
    <Box sx={styles.root} component="form" onSubmit={submit}>
      {hasFpsUser && <Loader />}
      {!hasFpsUser && (
        <>
          <RegisterForm data={data} onChange={changeData} />
          <Box sx={styles.actions}>
            <BackToLogin />
            <Button
              type="submit"
              disabled={loading}
              endIcon={
                loading ? <CircularProgress size={20} color="white" /> : null
              }
              data-testid="login-register-submit-btn"
            >
              Register
            </Button>
          </Box>
        </>
      )}
    </Box>
  );
};
Register.propTypes = {
  onSuccess: PropTypes.func,
  form: PropTypes.object,
};

export default Register;
