import React from 'react';
import PropTypes from 'prop-types';
import { useNavigate } from 'react-router-dom';

const Card = ({ action }) => {
  const navigate = useNavigate();

  const routeTo = (action) => {
    if (action.path) {
      navigate(action.path); // Navigate to the internal route
    } else {
      window.open(action.url, '_blank'); // Open external URL in a new tab
    }
  };

  return (
    <div
      key={action.title}
      data-testid={`card-${action.title}`}
      onClick={() => routeTo(action)}
      className="cursor-pointer relative group bg-white hover:bg-gray-50 dark:bg-gray-700 hover:dark:bg-gray-600 p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 rounded-lg"
    >
      <div>
        <span
          className={`${action.iconBackground} ${action.iconForeground} rounded-lg inline-flex p-3 ring-4 ring-white dark:ring-gray-700`}
        >
          <action.icon className="h-6 w-6" aria-hidden="true" />
        </span>
      </div>
      <div className="mt-8">
        <h3 className="text-lg font-medium">
          <div className="focus:outline-none">
            <span className="absolute inset-0" aria-hidden="true" />
            {action.title}
          </div>
        </h3>
        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
          {action.body}
        </p>
      </div>
    </div>
  );
};

Card.propTypes = {
  action: PropTypes.shape({
    title: PropTypes.string.isRequired,
    body: PropTypes.string.isRequired,
    path: PropTypes.string,
    url: PropTypes.string,
    icon: PropTypes.elementType.isRequired,
    iconForeground: PropTypes.string.isRequired,
    iconBackground: PropTypes.string.isRequired,
  }).isRequired,
};

export default Card;
