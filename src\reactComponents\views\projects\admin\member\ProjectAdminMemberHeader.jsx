import { useReducer } from 'react';
import PropTypes from 'prop-types';
import { Avatar, Modal, Button, Icon } from '@/reactComponents/library';
import { ArrowUturnUpIcon } from '@heroicons/react/24/solid';
import {
  deleteProjectMember,
  makeAdmin,
  reactivateProjectMember,
  revokeAdmin,
} from '@/services/project-members';
import { ProjectMemberTypeId } from '@/utils/enum';
import {
  snackbarAxiosErr,
  snackbarSuccess,
  snackbarErr,
} from '@/reactComponents/library/Snackbar';
import { useNavigate } from 'react-router';
import ProjectStore from '@/reactComponents/stores/project';
import PermissionStore from '@/reactComponents/stores/permission';
import AuthStore from '@/reactComponents/stores/auth';

const OPEN_MODAL = 'OPEN_MODAL';
const CLOSE_MODAL = 'CLOSE_MODAL';
const SET_LOADING = 'SET_LOADING';
const DELETE_MODAL = 'DELETE_MODAL';
const MAKE_ADMIN_MODAL = 'MAKE_ADMIN_MODAL';
const REACTIVATE_MODAL = 'REACTIVATE_MODAL';
const REMOVE_ADMIN_MODAL = 'REMOVE_ADMIN_MODAL';

const modals = [
  DELETE_MODAL,
  MAKE_ADMIN_MODAL,
  REACTIVATE_MODAL,
  REMOVE_ADMIN_MODAL,
];

const modalReducer = (state, action) => {
  switch (action.type) {
    case OPEN_MODAL:
      return {
        ...state,
        [action.modal]: {
          ...state[action.modal],
          open: true,
        },
      };
    case CLOSE_MODAL:
      return {
        ...state,
        [action.modal]: {
          ...state[action.modal],
          open: false,
        },
      };
    case SET_LOADING:
      return {
        ...state,
        [action.modal]: {
          ...state[action.modal],
          loading: action.loading,
        },
      };
    default:
      return state;
  }
};

const getInitialState = (modals) => {
  const initialState = {};
  modals.forEach((modal) => {
    initialState[modal] = {
      open: false,
      loading: false,
    };
  });
  return initialState;
};

const ProjectAdminMemberHeader = ({ refresh, className }) => {
  const navigate = useNavigate();
  const { projectMember, project } = ProjectStore;
  const { user } = projectMember ?? {};
  const [modalState, dispatch] = useReducer(
    modalReducer,
    modals,
    getInitialState,
  );

  if (!projectMember || !user) {
    return null;
  }

  const openModal = (modal) => {
    dispatch({ type: OPEN_MODAL, modal });
  };

  const closeModal = (modal) => {
    dispatch({ type: CLOSE_MODAL, modal });
  };

  const setLoading = (modal, loading) => {
    dispatch({ type: SET_LOADING, modal, loading });
  };

  const fullName = `${user?.firstName || ''} ${user?.lastName || ''}`.trim();
  const phone = user?.phone || '';
  const projectRole = projectMember?.projectMemberType?.name || '';
  const isProjectAdmin =
    projectMember?.projectMemberType?.id === ProjectMemberTypeId.Admin;
  const isActiveUser = user?.isActive && projectMember?.isActive;
  const userId = AuthStore?.getUser?.id;
  const isHavingAccess =
    PermissionStore.isCompanyAdmin || PermissionStore.isSiteAdmin;

  const callRefresh = () => {
    if (refresh) refresh();
  };

  const deleteMember = async () => {
    setLoading(DELETE_MODAL, true);
    try {
      await deleteProjectMember(projectMember.id);
      closeModal(DELETE_MODAL);
      callRefresh();
      snackbarSuccess('Project Member deleted.', 2500);
      navigate({ pathname: `/projects/${project.hashId}/admin/members` });
    } catch (err) {
      snackbarAxiosErr(
        err,
        err?.message || 'Error deleting project member',
        2500,
      );
    }
    setLoading(DELETE_MODAL, false);
  };

  const reactivateMember = async () => {
    if (!user?.isActive) {
      snackbarErr(
        'This user is no longer in the system. Please contact Cast & Crew Support.',
        2500,
      );
      closeModal(REACTIVATE_MODAL);
      return;
    }
    setLoading(REACTIVATE_MODAL, true);
    try {
      await reactivateProjectMember(projectMember.id);
      callRefresh();
      snackbarSuccess('Project Member reactivated.', 2500);
      closeModal(REACTIVATE_MODAL);
    } catch (err) {
      snackbarAxiosErr(
        err,
        err?.message || 'Error reactivating project member',
        2500,
      );
    }
    setLoading(REACTIVATE_MODAL, false);
  };

  const upgradeToProjectAdmin = async () => {
    if (!isActiveUser) {
      snackbarErr('You cannot make an inactive user a project admin.', 2500);
      closeModal(MAKE_ADMIN_MODAL);
      return;
    }
    setLoading(MAKE_ADMIN_MODAL, true);
    try {
      await makeAdmin(projectMember.id);
      callRefresh();
      snackbarSuccess('Project Member upgraded to admin.', 2500);
    } catch (err) {
      snackbarAxiosErr(
        err,
        err?.message || 'Error upgrading project member',
        2500,
      );
    }
    setLoading(MAKE_ADMIN_MODAL, false);
    closeModal(MAKE_ADMIN_MODAL);
  };

  const removeProjectAdmin = async () => {
    setLoading(REMOVE_ADMIN_MODAL, true);
    try {
      await revokeAdmin(projectMember.id);
      callRefresh();
      if (!isHavingAccess && projectMember?.userId === userId) {
        navigate({ pathname: `/projects` });
      }
      snackbarSuccess('Project Admin Role Removed.', 2500);
    } catch (err) {
      snackbarAxiosErr(
        err,
        err?.message || 'Error removing project admin role',
        2500,
      );
    }
    setLoading(REMOVE_ADMIN_MODAL, false);
    closeModal(REMOVE_ADMIN_MODAL);
  };

  const modalsAttributes = {
    [MAKE_ADMIN_MODAL]: {
      title: 'Make Project Admin',
      submitText: 'Make Admin',
      description:
        'Are you sure you want to make this project member an admin?',
      onSubmit: upgradeToProjectAdmin,
    },
    [DELETE_MODAL]: {
      title: 'Delete Project Member',
      submitText: 'Delete',
      description: 'Are you sure you want to delete this project member?',
      onSubmit: deleteMember,
    },
    [REACTIVATE_MODAL]: {
      title: 'Reactivate Project Member',
      submitText: 'Reactivate',
      description: 'Are you sure you want to reactivate this project member?',
      onSubmit: reactivateMember,
    },
    [REMOVE_ADMIN_MODAL]: {
      title: 'Remove Project Admin Role',
      submitText: 'Remove Admin Role',
      description:
        'Are you sure you want to remove this project member from the admin role?',
      onSubmit: removeProjectAdmin,
    },
  };

  const getModals = () => {
    return modals.map((modal) => {
      const { description, ...modalProps } = modalsAttributes[modal] || {};
      return (
        <Modal
          open={modalState[modal].open}
          onCancel={() => closeModal(modal)}
          loading={modalState[modal].loading}
          key={modal}
          {...modalProps}
        >
          <p>{description}</p>
        </Modal>
      );
    });
  };

  return (
    <div className={className}>
      <div className="flex items-center space-x-2">
        <div className="flex-shrink-0">
          <div className="relative">
            <Avatar
              name={fullName}
              size="lg"
              sx={{
                bgcolor: 'white',
              }}
            />
            <span
              className="absolute inset-0 rounded-full shadow-inner"
              aria-hidden="true"
            />
          </div>
        </div>
        <div>
          <div className="flex justify-start items-center space-x-2">
            {fullName && <h1 className="text-2xl font-bold">{fullName}</h1>}
            {!isActiveUser && (
              <div className="flex justify-start pt-1">
                <div className="bg-red-200 rounded-xl px-1 mx-1">Deleted</div>
              </div>
            )}
          </div>
          {phone && (
            <h2 className="text-xl text-zinc-700 dark:text-zinc-400">
              {phone}
            </h2>
          )}
        </div>
        <div className="flex grow" />
        <div className="text-sm font-light pr-3">
          <div className="text-sm font-bold">Project Role</div>
          <div className="text-lg font-light">{projectRole}</div>
        </div>
        {isProjectAdmin ? (
          <Button
            size="small"
            onClick={() => openModal(REMOVE_ADMIN_MODAL)}
            data-testid="remove-admin-role-btn"
          >
            Remove Admin Role
          </Button>
        ) : (
          <Button
            size="small"
            onClick={() => openModal(MAKE_ADMIN_MODAL)}
            data-testid="make-project-admin-btn"
          >
            Make Project Admin
          </Button>
        )}
        {isActiveUser ? (
          <Button
            size="small"
            variant="secondary"
            onClick={() => openModal(DELETE_MODAL)}
            data-testid="delete-project-member-btn"
          >
            <div className="flex items-center space-x-1 stroke-gray-600">
              <Icon name="trash" className="w-5 h-5" />
              <span>Delete</span>
            </div>
          </Button>
        ) : (
          <Button
            size="small"
            variant="secondary"
            sx={[
              (theme) =>
                theme.applyStyles('dark', {
                  '&:hover': {
                    backgroundColor: 'grey.700',
                  },
                }),
            ]}
            onClick={() => openModal(REACTIVATE_MODAL)}
            data-testid="reactivate-project-member-btn"
          >
            <div className="flex items-center">
              <ArrowUturnUpIcon className="w-5 h-5 mr-1" />
              <span>Reactivate</span>
            </div>
          </Button>
        )}
        {getModals()}
      </div>
    </div>
  );
};

ProjectAdminMemberHeader.propTypes = {
  refresh: PropTypes.func.isRequired,
  className: PropTypes.string,
};

export default ProjectAdminMemberHeader;
