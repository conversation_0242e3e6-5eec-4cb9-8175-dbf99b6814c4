import { applyPureVueInReact } from 'veaury';
import ProjectAdminMemberLoanOutVue from '../../../../../views/projects/admin/member/ProjectAdminMemberLoanOut.vue';
import { useAuth } from '../../../../AppHooks';
import { useOutletContext } from 'react-router';

const ReactProjectAdminMemberLoanOut = applyPureVueInReact(
  ProjectAdminMemberLoanOutVue,
);

const ProjectAdminMemberLoanOut = () => {
  useAuth();
  const context = useOutletContext();
  return (
    <ReactProjectAdminMemberLoanOut
      project={context.project}
      projectMember={context.projectMember}
      user={context.user}
      userCrew={context.userCrew}
      refresh={context.refresh}
    />
  );
};

export default ProjectAdminMemberLoanOut;
