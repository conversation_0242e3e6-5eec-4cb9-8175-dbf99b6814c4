/* eslint-env node */
require('@rushstack/eslint-patch/modern-module-resolution');

module.exports = {
  root: true,
  extends: [
    'react-app',
    'plugin:vue/vue3-essential',
    'eslint:recommended',
    '@vue/eslint-config-typescript',
  ],
  rules: {
    'vue/multi-word-component-names': 'off',
    'vue/no-reserved-component-names': 'off',
    'vue/no-unused-components': 'warn',
    'no-console': [
      'warn',
      {
        allow: ['warn', 'error', 'debug'],
      },
    ],
    'no-unused-vars': 'warn',
    'react/no-unused-state': 'warn',
    'react/jsx-no-duplicate-props': 'warn',
    'react/no-array-index-key': 'warn',
    'react/prop-types': 'warn',
  },
  overrides: [
    {
      files: ['*.ts'],
      rules: {
        'no-unused-vars': 'off',
      },
    },
    {
      files: ['**/*.cy.{js,jsx,ts,tsx}'],
      extends: ['plugin:cypress/recommended'],
      plugins: ['cypress'],
      env: { 'cypress/globals': true },
    },
    {
      files: '*.json',
      parser: 'jsonc-eslint-parser',
      rules: {},
    },
  ],
  parserOptions: {
    ecmaVersion: 'latest',
  },
  env: {
    node: true,
  },
};
