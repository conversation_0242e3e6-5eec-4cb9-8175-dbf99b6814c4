import { handleInviteRoute } from '@/utils/invite';
import type { RouteRecordRaw } from 'vue-router';
import LandingView from '../views/LandingView.vue';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'landing',
    component: LandingView,
    meta: {
      authRequired: true,
    },
  },
  {
    path: '/project-invite',
    name: 'project-invite',
    beforeEnter: async (to: any, _from: any, next: any) => {
      return handleInviteRoute(to, next);
    },
    component: () => import('@/views/Empty.vue'),
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/LoginView.vue'),
    meta: {
      onlyLoggedOut: true,
    },
  },
  {
    path: '/login/:phone/verify',
    name: 'verify',
    component: () => import('@/views/VerifyPhoneView.vue'),
    meta: {
      onlyLoggedOut: true,
    },
  },
  {
    path: '/onboarding/:encryptedPhone/personal-info',
    name: 'onboarding',
    component: () => import('@/views/onboarding/OnboardingPersonalInfo.vue'),
  },
  {
    path: '/loan-outs',
    name: 'loan-outs',
    component: () => import('@/views/onboarding/OnboardingLoanOutView.vue'),
  },
  {
    path: '/faq',
    name: 'faq',
    component: () => import('@/views/FAQView.vue'),
  },
  {
    path: '/profile',
    name: 'profile',
    component: () => import('@/views/ProfileView.vue'),
  },
  {
    path: '/profile/create',
    name: 'profile-create',
    component: () => import('@/views/UserCrewFormView.vue'),
    props: {
      editMode: false,
    },
    meta: {
      authRequired: true,
    },
  },
  {
    path: '/profile/edit',
    name: 'profile-edit',
    component: () => import('@/views/UserCrewFormView.vue'),
    props: {
      editMode: true,
    },
    meta: {
      authRequired: true,
    },
  },
  {
    path: '/projects',
    name: 'projects',
    redirect: {
      name: 'project-list',
    },
    meta: {
      authRequired: true,
      breadcrumb: 'Projects',
    },
    children: [
      {
        path: '',
        name: 'project-list',
        component: () => import('@/views/projects/ProjectsView.vue'),
        meta: {
          authRequired: true,
        },
      },
      {
        path: ':hashId',
        name: 'project-details',
        component: () => import('@/views/projects/ProjectView.vue'),
        redirect: { name: 'project-home' },
        meta: {
          authRequired: true,
          breadcrumb: '{project.name}',
        },
        children: [
          {
            path: '',
            name: 'project-home',
            component: () => import('@/views/projects/ProjectHomeView.vue'),
            meta: {
              authRequired: true,
            },
          },
          {
            path: 'edit',
            name: 'project-edit',
            component: () => import('@/views/projects/ProjectFormView.vue'),
            props: {
              editMode: true,
            },
            meta: {
              authRequired: true,
              breadcrumb: 'Edit',
              isProjectAdmin: true,
            },
          },
          {
            path: 'personal',
            name: 'personal-onboarding',
            component: () =>
              import('@/views/projects/ProjectPersonalOnboardingView.vue'),
            meta: {
              authRequired: true,
              breadcrumb: 'Personal',
            },
          },
          {
            path: 'onboarding',
            name: 'project-onboarding',
            component: () =>
              import('@/views/projects/ProjectOnboardingFormView.vue'),
            meta: {
              authRequired: true,
              breadcrumb: 'Onboarding',
            },
          },
          {
            path: 'start/review',
            name: 'project-starting-paperwork-review',
            component: () =>
              import('@/views/projects/ProjectStartPaperworkView.vue'),
            meta: {
              authRequired: true,
              breadcrumb: 'Start Paperwork',
            },
          },
          {
            path: 'start/generate',
            name: 'project-starting-paperwork-generate',
            component: () =>
              import('@/views/projects/ProjectStartPaperworkGenerateView.vue'),
            meta: {
              authRequired: true,
              breadcrumb: 'Start Paperwork',
            },
          },
          {
            path: 'start/approve',
            name: 'project-starting-paperwork-approve',
            component: () =>
              import('@/views/projects/ProjectStartPaperworkApproveView.vue'),
            meta: {
              authRequired: true,
              breadcrumb: 'Start Paperwork',
            },
          },
          {
            path: 'timecards',
            name: 'project-timecards',
            component: () =>
              import('@/views/projects/ProjectTimecardsView.vue'),
            meta: {
              authRequired: true,
              breadcrumb: 'Timecards',
            },
          },
          {
            path: 'timecards/create',
            name: 'project-timecards-create',
            component: () =>
              import('@/views/projects/ProjectTimecardCreateView.vue'),
            meta: {
              authRequired: true,
              breadcrumb: 'Timecard',
            },
          },
          {
            path: 'admin',
            name: 'project-admin',
            meta: {
              authRequired: true,
              isProjectAdmin: true,
            },
            redirect: {
              name: 'project-admin-members',
            },
            children: [
              {
                path: 'members',
                name: 'project-admin-members',
                meta: {
                  authRequired: true,
                  isProjectAdmin: true,
                  breadcrumb: 'Members',
                },
                redirect: { name: 'project-admin-members-list' },
                children: [
                  {
                    path: '',
                    name: 'project-admin-members-list',
                    component: () =>
                      import(
                        '@/views/projects/admin/ProjectAdminMembersView.vue'
                      ),
                  },
                  {
                    path: ':projectMemberId/member',
                    name: 'project-admin-member-detail',
                    component: () =>
                      import(
                        '@/views/projects/admin/ProjectAdminMemberView.vue'
                      ),
                    meta: {
                      authRequired: true,
                      isProjectAdmin: true,
                      breadcrumb: '{user.firstName} {user.lastName}',
                    },
                    redirect: {
                      name: 'project-admin-member-detail-personal-info',
                    },
                    children: [
                      {
                        path: 'personal-info',
                        name: 'project-admin-member-detail-personal-info',
                        component: () =>
                          import(
                            '@/views/projects/admin/member/ProjectAdminMemberUserCrewInfo.vue'
                          ),
                        meta: {
                          authRequired: true,
                          isProjectAdmin: true,
                          breadcrumb: 'Personal Info',
                        },
                      },
                      {
                        path: 'loan-out',
                        name: 'project-admin-member-detail-loan-out',
                        component: () =>
                          import(
                            '@/views/projects/admin/member/ProjectAdminMemberLoanOut.vue'
                          ),
                        meta: {
                          authRequired: true,
                          isProjectAdmin: true,
                          breadcrumb: 'Loan Out',
                        },
                      },
                      {
                        path: 'onboarding',
                        name: 'project-admin-member-detail-onboarding',
                        component: () =>
                          import(
                            '@/views/projects/admin/member/ProjectAdminMemberOnboarding.vue'
                          ),
                        meta: {
                          authRequired: true,
                          isProjectAdmin: true,
                          breadcrumb: 'Onboarding',
                        },
                      },
                      {
                        path: 'start-paperwork',
                        name: 'project-admin-member-detail-start-paperwork',
                        component: () =>
                          import(
                            '@/views/projects/admin/member/ProjectAdminMemberStartPaperwork.vue'
                          ),
                        meta: {
                          authRequired: true,
                          isProjectAdmin: true,
                          breadcrumb: 'Start Paperwork',
                        },
                      },
                      {
                        path: 'timecards',
                        name: 'project-admin-member-detail-timecards',
                        component: () =>
                          import(
                            '@/views/projects/admin/member/ProjectAdminMemberTimecards.vue'
                          ),
                        meta: {
                          authRequired: true,
                          isProjectAdmin: true,
                          breadcrumb: 'Timecards',
                        },
                      },
                      {
                        path: 'timecards/:timecardId',
                        name: 'project-admin-member-detail-timecard-detail',
                        meta: {
                          authRequired: true,
                          isProjectAdmin: true,
                          breadcrumb: 'Timecard',
                        },
                        component: () =>
                          import(
                            '@/views/projects/admin/member/ProjectAdminMemberTimecardDetail.vue'
                          ),
                      },
                      {
                        path: 'onboarding/edit',
                        name: 'project-admin-member-onboarding-edit',
                        meta: {
                          authRequired: true,
                          isProjectAdmin: true,
                          breadcrumb: 'Edit Onboarding',
                        },
                        props: {
                          supervisorView: true,
                        },
                        component: () =>
                          import(
                            '@/views/projects/ProjectOnboardingFormView.vue'
                          ),
                      },
                      {
                        path: 'start-paperwork/sign',
                        name: 'project-admin-member-start-paperwork-sign',
                        meta: {
                          authRequired: true,
                          isProjectAdmin: true,
                          breadcrumb: 'Start Paperwork',
                        },
                        component: () =>
                          import(
                            '@/views/projects/admin/ProjectAdminSignPaperworkForm.vue'
                          ),
                      },
                    ],
                  },
                ],
              },
              {
                path: 'timecards',
                name: 'project-admin-timecards',
                meta: {
                  authRequired: true,
                  isProjectAdmin: true,
                  breadcrumb: 'Timecards',
                },
                component: () =>
                  import(
                    '@/views/projects/admin/ProjectAdminTimecardsView.vue'
                  ),
              },
              {
                path: 'batches',
                name: 'project-admin-batches',
                meta: {
                  authRequired: true,
                  isProjectAdmin: true,
                  breadcrumb: 'Batches',
                },
                component: () =>
                  import('@/views/projects/admin/ProjectAdminBatchesView.vue'),
              },
              {
                path: 'document-templates',
                name: 'project-admin-templates',
                meta: {
                  authRequired: true,
                  isProjectAdmin: true,
                },
                component: () =>
                  import(
                    '@/views/projects/admin/ProjectCustomStartPaperworkView.vue'
                  ),
              },
              {
                path: 'document-templates/create',
                name: 'project-admin-templates-create',
                meta: {
                  authRequired: true,
                  isProjectAdmin: true,
                },
                component: () =>
                  import(
                    '@/views/projects/admin/ProjectCustomStartPaperworkFormView.vue'
                  ),
                props: {
                  editMode: false,
                },
              },
              {
                path: 'document-templates/:projectDocumentTemplateId/is-Default/:isDefaultDoc',
                name: 'project-admin-templates-edit',
                meta: {
                  authRequired: true,
                  isProjectAdmin: true,
                },
                component: () =>
                  import(
                    '@/views/projects/admin/ProjectCustomStartPaperworkFormView.vue'
                  ),
                props: {
                  editMode: true,
                },
              },
            ],
          },
          {
            path: 'timecards/:timecardId',
            name: 'project-timecard',
            component: () => import('@/views/projects/ProjectTimecardView.vue'),
            meta: {
              authRequired: true,
              breadcrumb: 'Timecards',
            },
            children: [
              {
                path: '',
                name: 'project-timecard-edit',
                component: () =>
                  import('@/views/projects/ProjectTimecardEditView.vue'),
                meta: {
                  authRequired: true,
                  breadcrumb: 'Timecard',
                },
              },
              {
                path: 'mileage',
                name: 'project-timecard-mileage',
                component: () =>
                  import('@/views/projects/ProjectTimecardMileageFormView.vue'),
                meta: {
                  authRequired: true,
                  breadcrumb: 'Timecard',
                },
              },
              {
                path: 'kit-rental',
                name: 'project-timecard-kit-rental',
                component: () =>
                  import(
                    '@/views/projects/ProjectTimecardKitRentalFormView.vue'
                  ),
                meta: {
                  authRequired: true,
                  breadcrumb: 'Timecard',
                },
              },
              {
                path: 'reimbursements',
                name: 'project-timecard-reimbursements',
                component: () =>
                  import(
                    '@/views/projects/ProjectTimecardReimbursementsView.vue'
                  ),
                meta: {
                  authRequired: true,
                  breadcrumb: 'Timecard',
                },
              },
              {
                path: 'review',
                name: 'project-timecard-review',
                component: () =>
                  import('@/views/projects/ProjectTimecardReviewView.vue'),
                meta: {
                  authRequired: true,
                  breadcrumb: 'Timecard',
                },
              },
              {
                path: 'complete',
                name: 'project-timecard-complete',
                component: () =>
                  import('@/views/projects/ProjectTimecardCompleteView.vue'),
                meta: {
                  authRequired: true,
                  breadcrumb: 'Timecard',
                },
              },
            ],
          },
        ],
      },
    ],
  },
  {
    path: '/companies',
    name: 'companies',
    redirect: { name: 'companies-list' },
    meta: {
      authRequired: true,
      breadcrumb: 'Companies',
    },
    children: [
      {
        path: '',
        name: 'companies-list',
        component: () => import('@/views/companies/CompaniesView.vue'),
        meta: {
          authRequired: true,
        },
      },
      {
        path: 'new',
        name: 'company-create',
        component: () => import('@/views/companies/CompanyFormView.vue'),
        meta: {
          authRequired: true,
        },
      },
      {
        path: ':id',
        name: 'company',
        component: () => import('@/views/companies/CompanyView.vue'),
        meta: {
          authRequired: true,
          breadcrumb: '{company.name}',
        },
        children: [
          {
            path: '',
            name: 'company-dashboard',
            component: () =>
              import('@/views/companies/CompanyDashboardView.vue'),
            meta: {
              authRequired: true,
            },
          },
          {
            path: 'edit',
            name: 'company-edit',
            component: () => import('@/views/companies/CompanyFormView.vue'),
            props: {
              editMode: true,
            },
            meta: {
              authRequired: true,
            },
          },
          {
            path: 'members',
            name: 'company-members',
            redirect: { name: 'company-members-list' },
            meta: {
              authRequired: true,
              breadcrumb: 'Members',
            },
            children: [
              {
                path: '',
                name: 'company-members-list',
                component: () =>
                  import('@/views/companies/CompanyMembersView.vue'),
                props: {
                  editMode: false,
                },
              },
              {
                path: ':userId',
                name: 'company-member-details',
                redirect: { name: 'company-member-details-personal-info' },
                meta: {
                  authRequired: true,
                },
                children: [
                  {
                    path: '',
                    name: 'company-member-details-personal-info',
                    component: () =>
                      import('@/views/companies/CompanyMemberDetailsView.vue'),
                  },
                ],
              },
            ],
          },
          {
            path: 'document-templates',
            name: 'company-document-templates',
            component: () =>
              import('@/views/companies/CompanyCustomStartPaperworkView.vue'),
            meta: {
              authRequired: true,
            },
          },
          {
            path: 'document-templates/create',
            name: 'company-document-templates-create',
            component: () =>
              import(
                '@/views/companies/CompanyCustomStartPaperworkFormView.vue'
              ),
            props: {
              editMode: false,
            },
          },
          {
            path: 'document-templates/:productionCompanyDocumentTemplateId/is-default/:isDefaultDoc',
            name: 'company-document-templates-edit',
            component: () =>
              import(
                '@/views/companies/CompanyCustomStartPaperworkFormView.vue'
              ),
            props: {
              editMode: true,
            },
          },
        ],
      },
    ],
  },
  {
    path: '/users',
    name: 'users',
    redirect: { name: 'users-list' },
    meta: {
      authRequired: true,
      breadcrumb: 'Users',
    },
    children: [
      {
        path: '',
        name: 'users-list',
        component: () => import('@/views/users/UsersView.vue'),
        meta: {
          authRequired: true,
        },
      },
      // {
      //   path: ':id',
      //   name: 'user',
      //   component: () => import('@/views/users/UserView.vue'),
      //   meta: {
      //     authRequired: true,
      //   },
      //   children: [
      //     {
      //       path: '',
      //       name: 'user-dashboard',
      //       component: () => import('@/views/users/UserDashboardView.vue'),
      //       meta: {
      //         authRequired: true,
      //       },
      //     },
      //     {
      //       path: '',
      //       name: 'user-edit',
      //       component: () => import('@/views/users/UserFormView.vue'),
      //       props: {
      //         editMode: true,
      //       },
      //       meta: {
      //         authRequired: true,
      //       },
      //     },
      //   ],
      // },
    ],
  },
  {
    path: '/projects/code',
    name: 'project-code',
    component: () => import('@/views/projects/ProjectCodeView.vue'),
    meta: {
      authRequired: true,
    },
  },
  {
    path: '/projects/new',
    name: 'project-create',
    component: () => import('@/views/projects/ProjectFormView.vue'),
    meta: {
      authRequired: true,
    },
  },
];

export default routes;
