import { applyPureVueInReact } from 'veaury';
import ProjectsCreateVue from '@/views/projects/ProjectFormView.vue';
import { useAuth } from '@/reactComponents/AppHooks';
import { useNavigate } from 'react-router';
const ReactProjectsCreateView = applyPureVueInReact(ProjectsCreateVue);

const ProjectCreateView = () => {
  useAuth();
  const navigate = useNavigate();
  return <ReactProjectsCreateView navigate={navigate} editMode={false} />;
};

export default ProjectCreateView;
