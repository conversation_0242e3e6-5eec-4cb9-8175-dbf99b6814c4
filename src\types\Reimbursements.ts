import type { DateTime } from 'luxon';
import type ProjectShootLocation from './ProjectShootLocation';

export enum ReimbursementTypeKeys {
  KIT_RENTAL_NON_TAXABLE = '7',
}

export default interface Reimbursement {
  id?: number;
  name: string;
  documentId?: string;
  lineNumber: string;
  date: DateTime;
  workLocation?: ProjectShootLocation;
  rate: number;
  quantity: number;
  type?: any;
  totalAmount?: number;
}
