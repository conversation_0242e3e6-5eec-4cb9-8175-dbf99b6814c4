<template>
  <div class="w-full">
    <label
      v-if="label"
      for="comment"
      class="block text-xs font-medium text-gray-700 dark:text-gray-400 mb-1"
    >
      {{ label }}
    </label>
    <div>
      <textarea
        :rows="rows"
        name="comment"
        id="comment"
        :value="modelValue"
        :disabled="disabled"
        @input="update"
        class="bg-white text-gray-800 dark:text-gray-200 border-1 border-solid border-gray-300 dark:border-gray-500 shadow-sm dark:bg-gray-700 sm:text-sm w-full rounded-md pb-1"
        :class="{ 'bg-gray-100 dark:bg-gray-600 cursor-not-allowed': disabled }"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  emits: ['update:modelValue'],
  props: ['modelValue', 'label', 'type', 'errors', 'rows', 'disabled'],
  name: 'TextArea',
  methods: {
    update(event: any) {
      const value: string = event.target!.value!;
      this.$emit('update:modelValue', value);
    },
  },
});
</script>
