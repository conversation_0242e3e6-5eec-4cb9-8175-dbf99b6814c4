// import { createPinia } from 'pinia'
// import { createApp } from 'vue'

import Hotjar from '@hotjar/browser';
// import NotificationCenterPlugin from '@novu/notification-center-vue'
import '@novu/notification-center-vue/dist/style.css';
import axios from 'axios';
import React from 'react';
// import App from './App.vue'
// import router from './router'
import { appRoutes as router } from './reactComponents/routes';
import AuthStore from './reactComponents/stores/auth';
import isoConverter from './utils/isoConverter';

import { createRoot } from 'react-dom/client';

import ReactApp from './reactComponents/App';
import { LicenseInfo } from '@mui/x-license';
import config from '@/vite-env';
import './assets/main.css';

const siteId = 3711426;
const hotjarVersion = 6;
Hotjar.init(siteId, hotjarVersion);

axios.interceptors.response.use((response) => {
  isoConverter.adaptIsoStrings(response.data);
  return response;
});

axios.interceptors.response.use(
  function (response) {
    return response;
  },
  function (error) {
    if (error.code === 'ERR_CANCELED') {
      // prevent error on cancelled requests
    } else if (error.response.status === 401) {
      const authStore = AuthStore;
      if (router.state.location.pathname !== '/v2/login') {
        authStore.logout();

        let fullPath = `${router.state.location.pathname}${
          router.state.location.search || ''
        }`;
        fullPath = fullPath.replace(/\/v2/, '');
        const searchparams = new URLSearchParams();
        searchparams.append('redirect', fullPath);

        router.navigate({
          pathname: '/login',
          search: searchparams.toString(),
        });
      }
    }
    return Promise.reject(error);
  },
);

// app.use(NotificationCenterPlugin) //
LicenseInfo.setLicenseKey(config.muiLicenseKey);

createRoot(document.getElementById('app')).render(
  // <React.StrictMode> //Veaury doesn't work with strict mode in react19
  <ReactApp />, //TODO <--- make sure to remove this comma if turning strict mode back on
  // </React.StrictMode>,
);
