import React from 'react';

import { Profile, PlatformProvider } from '@castandcrew/platform-components';

import config from '@/vite-env';

const BASE_URL = config.okta.baseUrl;
const PLATFORM_CONFIG_PROFILE = {
  okta: {
    clients: {
      fps: {
        url: BASE_URL,
        clientId: config.okta.clientId,
        issuer: config.okta.issuer,
        devMode: true,
      },
    },
  },
  api: {
    dashboard: { url: config.apiDashboardUrl },
  },
  forceSubmit: false,
};

const profileConfig = {
  variant: 'large',
  onSuccess: (formData) => {
    console.warn('formData: ', formData);
    //will be called by Profile component when the form is successfully submitted and ready to move on
  },
  onError: (reason, error) => {
    console.error(reason, error);
    //will be called by Profile component when the form is submitted and there is an error
  },
  client: 'fps',
  contentFullWidth: true,
};

const Personal = (props) => {
  return (
    <div>
      <button
        style={{ border: '1px solid black', padding: '10px', margin: '10px' }}
        onClick={() => {
          const form = document.getElementById(
            'Platform-Component-Profile-form',
          );
          form.requestSubmit();
        }}
      >
        Submit Test
      </button>
      <PlatformProvider config={PLATFORM_CONFIG_PROFILE}>
        <Profile config={profileConfig} />
      </PlatformProvider>
    </div>
  );
};

export default Personal;
