import { isLoggedIn } from '@/services/auth';
import { getByCode, isProjectOnboarded } from '@/services/project';
import { getInvite } from '@/services/users';
import type Project from '@/types/Project';
import type { RouteLocationNormalized, Router } from 'vue-router';

export const handleInviteRoute = async (to: any, next: any) => {
  const code = to.query?.code;
  const invite = (await getInvite(code)).data;

  // IF NOT LOGGED IN, TRIGGER OTC AND DIRECT TO CONFIRM
  const isUserLoggedIn = (await isLoggedIn()).data;
  if (!isUserLoggedIn) {
    next({ name: 'login', query: { code } });
    return;
  }

  // IF USER ONBOARDING IS NOT DONE, THEY WILL BE REDIRECTED TO LOGIN ANYWAY BECAUSE THE USER DOESN'T ACTUALLY EXIST

  // GET PROJECT AND ADD PROJECT MEMBER IF THEY DON'T ALREADY EXIST
  const project: Project | null = (await getByCode(invite.projectCode!)).data;
  const isProjectOnboardingComplete = (await isProjectOnboarded(project?.id!))
    .data;

  if (!isProjectOnboardingComplete) {
    next({
      name: 'project-onboarding',
      params: { hashId: invite.projectHash },
      query: { code },
    });
    return;
  }

  // IF ALL CONDITIONS ARE MET, REDIRECT TO PROJECT
  next({
    name: 'project-details',
    params: { hashId: invite.projectHash },
  });
};

export const handleInviteRouteFromComponent = async (
  route: RouteLocationNormalized,
  router: Router,
) => {
  // The current route acts as the 'to' parameter
  const to: RouteLocationNormalized = route;

  // Define a local next function mimicking the navigation guard's next function
  // You might need to adjust the types based on your specific needs
  const next = (location?: string | boolean | object): void => {
    if (location === false) {
      // Example: Abort navigation by redirecting to a fallback route
      // Adjust according to your needs; handle errors as needed
      router.replace({ name: 'fallbackRoute' }).catch((err) => {
        // Handle errors or logging as necessary
        console.error(err);
      });
    } else if (typeof location === 'string' || typeof location === 'object') {
      // Proceed with navigation to the given location
      router.push(location).catch((err) => {
        // Handle errors or logging as necessary
        console.error(err);
      });
    } else {
      // If no argument is provided or it's not valid, do nothing or handle accordingly
      // This branch might represent a call to next() with no arguments in a guard
    }
  };

  await handleInviteRoute(to, next);
};

export const handleInviteRouteInReactRouter = async (
  route: any,
  next: Function,
) => {
  const code = route.query?.code;
  const invite = (await getInvite(code)).data;

  // IF NOT LOGGED IN, TRIGGER OTC AND DIRECT TO CONFIRM
  const isUserLoggedIn = (await isLoggedIn()).data;
  if (!isUserLoggedIn) {
    const urlSearchParams = new URLSearchParams();
    urlSearchParams.set('code', code);
    next({ pathname: '/login', search: urlSearchParams.toString() });
    return;
  }

  // IF USER ONBOARDING IS NOT DONE, THEY WILL BE REDIRECTED TO LOGIN ANYWAY BECAUSE THE USER DOESN'T ACTUALLY EXIST

  // GET PROJECT AND ADD PROJECT MEMBER IF THEY DON'T ALREADY EXIST
  const project: Project | null = (await getByCode(invite.projectCode!)).data;
  const isProjectOnboardingComplete = (await isProjectOnboarded(project?.id!))
    .data;

  if (!isProjectOnboardingComplete) {
    const urlSearchParams = new URLSearchParams();
    urlSearchParams.set('code', code);
    next({
      pathname: `projects/${invite.projectHash}/onboarding`,
      search: urlSearchParams.toString(),
      // name: 'project-onboarding',
      // params: { hashId: invite.projectHash },
      // query: { code }
    });
    return;
  }

  // IF ALL CONDITIONS ARE MET, REDIRECT TO PROJECT
  next({
    pathname: `projects/${invite.projectHash}`,
  });
  // next({
  //   name: 'project-details',
  //   params: { hashId: invite.projectHash }
  // });
};

export const handleInviteRouteInReactComponent = async (
  route: any,
  navigate: Function,
) => {
  const to: any = route;
  const next = (location?: string | boolean | object): void => {
    if (location === false) {
      navigate('fallbackRoute');
    } else if (typeof location === 'string' || typeof location === 'object') {
      navigate(location);
    } else {
      // Do nothing
    }
  };

  await handleInviteRouteInReactRouter(to, next);
};
