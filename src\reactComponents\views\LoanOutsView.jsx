// import React from 'react';
import { useAuth } from '@/reactComponents/AppHooks';
import LoanOutVue from '@/views/onboarding/OnboardingLoanOutView.vue';
import { applyPureVueInReact } from 'veaury';
import { useNavigate } from 'react-router';

const ReactLoanOutsView = applyPureVueInReact(LoanOutVue);

const LoanOutsView = () => {
  useAuth();

  const navigate = useNavigate();

  return <ReactLoanOutsView navigate={navigate} />;
};

export default LoanOutsView;
