import { createBrowserRouter } from 'react-router-dom';
import { NavigateRelativeTo } from './AppHooks';
import ErrorBoundary from '@/reactComponents/ErrorBoundary';
//https://stackoverflow.com/a/79184715/901311
const lz =
  (importFn, componentName = 'default') =>
  async () => {
    try {
      const module = await importFn();
      return { Component: module[componentName] };
    } catch (e) {
      console.error(
        `Error loading component ${componentName} from path ${importFn}:`,
        e,
      );
      throw e;
    }
  };

export const appRoutes = createBrowserRouter(
  [
    {
      path: '/',
      lazy: lz(() => import('./AppFrame.jsx')),
      HydrateFallback: () => <div />,
      errorElement: <ErrorBoundary />,
      children: [
        {
          index: true,
          lazy: lz(() => import('./views/landing/LandingView.jsx')),
          handle: {
            authRequired: true,
          },
        },

        // {
        //   // new timecard
        //   path: 'projects/:hashId/admin/members/:memberId/member/timecardsv2/:timecardId',
        //   lazy: lz(() =>
        //     import('./viewsV2/ProjectAdminMemberTimecardV2/TimecardV2.jsx'),
        //   ),
        //   handle: {
        //     authRequired: true,
        //     isProjectAdmin: true,
        //     breadcrumb: 'Timecard',
        //   },
        // },
        {
          //
          path: 'projects',
          handle: {
            authRequired: true,
            breadcrumb: 'Projects',
          },
          children: [
            {
              index: true,
              lazy: lz(() => import('./views/projects/ProjectsView.jsx')),
              handle: {
                authRequired: true,
              },
            },
            {
              // breadcrumb component in this ProjectView.jsx
              path: ':hashId',
              lazy: lz(() => import('./views/projects/ProjectView.jsx')),
              handle: {
                authRequired: true,
                breadcrumb: '{project.name}',
              },
              children: [
                {
                  index: true,
                  lazy: lz(() =>
                    import('./views/projects/ProjectHomeView.jsx'),
                  ),
                  handle: {
                    authRequired: true,
                  },
                },
                {
                  path: 'edit',
                  lazy: lz(() =>
                    import('./views/projects/ProjectFormView.jsx'),
                  ),
                  props: {
                    editMode: true,
                  },
                  handle: {
                    editMode: true,
                    authRequired: true,
                    breadcrumb: 'Edit',
                    isProjectAdmin: true,
                  },
                },
                {
                  path: 'personal',
                  lazy: lz(() =>
                    import(
                      './views/projects/ProjectPersonalOnboarding/ProjectPersonalOnboardingView.jsx'
                    ),
                  ),
                  handle: {
                    authRequired: true,
                    breadcrumb: 'Personal',
                  },
                },
                {
                  path: 'onboarding',
                  lazy: lz(() =>
                    import('./views/projects/ProjectOnboardingFormView.jsx'),
                  ),
                  handle: {
                    authRequired: true,
                    breadcrumb: 'Onboarding',
                  },
                },
                {
                  path: 'start/review',
                  lazy: lz(() =>
                    import('./views/projects/ProjectStartPaperworkView.jsx'),
                  ),
                  handle: {
                    authRequired: true,
                    breadcrumb: 'Start Paperwork',
                  },
                },
                {
                  path: 'start/generate',
                  lazy: lz(() =>
                    import(
                      './views/projects/ProjectStartPaperworkGenerateView.jsx'
                    ),
                  ),
                  handle: {
                    authRequired: true,
                    breadcrumb: 'Start Paperwork',
                  },
                },
                {
                  path: 'start/approve',
                  lazy: lz(() =>
                    import('./views/projects/ProjectStartPaperworkApproveView'),
                  ),
                  handle: {
                    authRequired: true,
                    breadcrumb: 'Start Paperwork',
                  },
                },
                {
                  path: 'timecards',
                  lazy: lz(() =>
                    import('./views/projects/ProjectTimecardsView.jsx'),
                  ),
                  handle: {
                    authRequired: true,
                    breadcrumb: 'Timecards',
                  },
                },
                {
                  path: 'timecards/create',
                  lazy: lz(() =>
                    import(
                      './views/projects/ProjectTimecardCreate/ProjectTimecardCreateView.jsx'
                    ),
                  ),
                  handle: {
                    authRequired: true,
                    breadcrumb: 'Timecards',
                  },
                },
                {
                  path: 'admin',
                  element: (
                    <NavigateRelativeTo
                      to={'members'}
                      match="/projects/:hashId/admin"
                      isOutlet
                    />
                  ),
                  handle: {
                    authRequired: true,
                    isProjectAdmin: true,
                  },
                  children: [
                    {
                      //
                      path: 'members',
                      handle: {
                        authRequired: true,
                        isProjectAdmin: true,
                      },
                      children: [
                        {
                          index: true,
                          lazy: lz(() =>
                            import(
                              './views/projects/admin/ProjectAdminMembersView.jsx'
                            ),
                          ),
                          handle: {
                            authRequired: true,
                            isProjectAdmin: true,
                            breadcrumb: 'Members',
                          },
                        },
                        {
                          path: ':projectMemberId',
                          element: (
                            <NavigateRelativeTo
                              to={'member/personal-info'}
                              match="/projects/:hashId/admin/members/:projectMemberId"
                              isOutlet
                            />
                          ),
                        },
                        {
                          // member header
                          path: ':projectMemberId/member',
                          lazy: lz(() =>
                            import(
                              './views/projects/admin/ProjectAdminMemberView.jsx'
                            ),
                          ),
                          handle: {
                            redirect: {
                              to: 'personal-info',
                              match:
                                '/projects/:hashId/admin/members/:projectMemberId/member',
                            },
                            authRequired: true,
                            isProjectAdmin: true,
                            breadcrumb: '{user.firstName} {user.lastName}',
                          },
                          children: [
                            {
                              path: 'personal-info',
                              lazy: lz(() =>
                                import(
                                  './views/projects/admin/member/ProjectAdminMemberUserCrewInfo.jsx'
                                ),
                              ),
                              handle: {
                                authRequired: true,
                                isProjectAdmin: true,
                                breadcrumb: 'Personal Info',
                              },
                            },
                            {
                              path: 'loan-out',
                              lazy: lz(() =>
                                import(
                                  './views/projects/admin/member/ProjectAdminMemberLoanOut.jsx'
                                ),
                              ),
                              handle: {
                                authRequired: true,
                                isProjectAdmin: true,
                                breadcrumb: 'Loan Out',
                              },
                            },
                            {
                              path: 'onboarding',
                              lazy: lz(() =>
                                import(
                                  './views/projects/admin/member/ProjectAdminMemberOnboarding.jsx'
                                ),
                              ),
                              handle: {
                                authRequired: true,
                                isProjectAdmin: true,
                                breadcrumb: 'Onboarding',
                              },
                            },
                            {
                              path: 'start-paperwork',
                              lazy: lz(() =>
                                import(
                                  './views/projects/admin/member/ProjectAdminMemberStartPaperwork.jsx'
                                ),
                              ),
                              handle: {
                                authRequired: true,
                                isProjectAdmin: true,
                                breadcrumb: 'Start Paperwork',
                              },
                            },
                            {
                              path: 'timecards',
                              lazy: lz(() =>
                                import(
                                  './views/projects/admin/member/ProjectAdminMemberTimecards.jsx'
                                ),
                              ),
                              handle: {
                                authRequired: true,
                                isProjectAdmin: true,
                                breadcrumb: 'Timecards',
                              },
                            },
                            {
                              path: 'timecards/:timecardId',
                              lazy: lz(() =>
                                import(
                                  './viewsV2/ProjectAdminMemberTimecardV2/TimecardV2.jsx'
                                ),
                              ),
                              handle: {
                                hideMemberHeader: true,
                                fullWidthHeader: true,
                                authRequired: true,
                                isProjectAdmin: true,
                                breadcrumb: 'Timecard',
                              },
                            },

                            {
                              path: 'onboarding/edit',
                              lazy: lz(() =>
                                import(
                                  './views/projects/ProjectOnboardingFormView.jsx'
                                ),
                              ),
                              handle: {
                                supervisorView: true,
                                authRequired: true,
                                isProjectAdmin: true,
                                breadcrumb: 'Edit Onboarding',
                              },
                            },
                            {
                              path: 'start-paperwork/sign',
                              lazy: lz(() =>
                                import(
                                  './views/projects/admin/ProjectAdminSignPaperworkForm.jsx'
                                ),
                              ),
                              handle: {
                                authRequired: true,
                                isProjectAdmin: true,
                                breadcrumb: 'Start Paperwork',
                              },
                            },
                          ],
                        },
                      ],
                    },
                    {
                      path: 'timecards',
                      //TODO legacy timecards tab
                      lazy: lz(() =>
                        import(
                          './views/projects/admin/ProjectAdminTimecardsView.jsx'
                        ),
                      ),
                      handle: {
                        authRequired: true,
                        isProjectAdmin: true,
                        breadcrumb: 'Timecards',
                      },
                    },
                    {
                      path: 'time',
                      lazy: lz(() =>
                        import(
                          './viewsV2/ProjectAdminTime/ProjectAdminTime.jsx'
                        ),
                      ),
                      handle: {
                        //TODO this is newxt in line: 'Time' tab
                        authRequired: true,
                        isProjectAdmin: true,
                        breadcrumb: 'Time',
                      },
                    },
                    {
                      path: 'batches',
                      //TODO legacy batches tab
                      lazy: lz(() =>
                        import(
                          './views/projects/admin/ProjectAdminBatchesView.jsx'
                        ),
                      ),
                      handle: {
                        authRequired: true,
                        isProjectAdmin: true,
                        breadcrumb: 'Batches',
                      },
                    },
                    {
                      path: 'document-templates',
                      lazy: lz(() =>
                        import(
                          './views/projects/admin/ProjectCustomStartPaperworkView'
                        ),
                      ),
                      handle: {
                        authRequired: true,
                        isProjectAdmin: true,
                      },
                    },
                    {
                      path: 'document-templates/create',
                      lazy: lz(() =>
                        import(
                          './views/projects/admin/ProjectCustomStartPaperworkFormView'
                        ),
                      ),
                      handle: {
                        authRequired: true,
                        isProjectAdmin: true,
                        editMode: false,
                      },
                    },
                    {
                      path: 'document-templates/:projectDocumentTemplateId/is-Default/:isDefaultDoc',
                      lazy: lz(() =>
                        import(
                          './views/projects/admin/ProjectCustomStartPaperworkFormView'
                        ),
                      ),
                      handle: {
                        authRequired: true,
                        isProjectAdmin: true,
                        editMode: true,
                      },
                    },
                    {
                      path: 'defaultDays',
                      lazy: lz(() =>
                        import(
                          './viewsV2/ProjectAdminMemberTimecardV2/DefaultDays/DefaultDays.jsx'
                        ),
                      ),
                      handle: {
                        hideMemberHeader: true,
                        fullWidthHeader: true,
                        authRequired: true,
                        isProjectAdmin: true,
                        breadcrumb: 'Default Days',
                      },
                    },
                  ], // end /projects/admin
                },
                {
                  path: 'timecards/:timecardId',
                  lazy: lz(() =>
                    import('./views/projects/ProjectTimecardView.jsx'),
                  ),
                  handle: {
                    authRequired: true,
                    breadcrumb: 'Timecards',
                  },
                  children: [
                    {
                      index: true,
                      lazy: lz(() =>
                        import('./views/projects/ProjectTimecardEditView.jsx'),
                      ),
                      handle: {
                        authRequired: true,
                        breadcrumb: 'Timecard',
                      },
                    },
                    {
                      path: 'mileage',
                      lazy: lz(() =>
                        import(
                          './views/projects/ProjectTimecardMileageFormView.jsx'
                        ),
                      ),
                      handle: {
                        authRequired: true,
                        breadcrumb: 'Timecard',
                      },
                    },
                    {
                      path: 'kit-rental',
                      lazy: lz(() =>
                        import(
                          './views/projects/ProjectTimecardKitRentalFormView.jsx'
                        ),
                      ),
                      handle: {
                        authRequired: true,
                        breadcrumb: 'Timecard',
                      },
                    },
                    {
                      path: 'reimbursements',
                      lazy: lz(() =>
                        import(
                          './views/projects/ProjectTimecardReimbursementsView.jsx'
                        ),
                      ),
                      handle: {
                        authRequired: true,
                        breadcrumb: 'Timecard',
                      },
                    },
                    {
                      path: 'review',
                      lazy: lz(() =>
                        import(
                          './views/projects/ProjectTimecardReviewView.jsx'
                        ),
                      ),
                      handle: {
                        authRequired: true,
                        breadcrumb: 'Timecard',
                      },
                    },
                    {
                      path: 'complete',
                      lazy: lz(() =>
                        import(
                          './views/projects/ProjectTimecardCompleteView.jsx'
                        ),
                      ),
                      handle: {
                        authRequired: true,
                        breadcrumb: 'Timecard',
                      },
                    },
                  ],
                },
              ],
            },
          ],
        },

        {
          path: '/projects/code',
          lazy: lz(() => import('./views/projects/ProjectsCodeView.jsx')),
          handle: { authRequired: true },
        },
        {
          path: '/projects/new',
          lazy: lz(() => import('./views/projects/ProjectsCreateView.jsx')),
          handle: { authRequired: true },
        },
        {
          path: '/users',
          handle: {
            authRequired: true,
            breadcrumb: 'Users',
          },
          children: [
            {
              index: true,
              lazy: lz(() => import('./views/users/UsersView.jsx')),
              handle: {
                authRequired: true,
              },
            },
            {
              path: ':id',
              lazy: lz(() => import('./views/users/UserView.jsx')),
              handle: { authRequired: true },
              children: [
                {
                  index: true,
                  lazy: lz(() => import('./views/users/UserDashboardView.jsx')),
                  handle: { authRequired: true },
                },
                {
                  path: 'edit',
                  lazy: lz(() => import('./views/users/UserFormView.jsx')),
                  handle: { authRequired: true },
                },
              ],
            },
          ],
        },
        {
          path: '/companies',
          handle: {
            authRequired: true,
            breadcrumb: 'Companies',
          },
          children: [
            {
              index: true,
              lazy: lz(() => import('./views/companies/CompaniesView.jsx')),
              handle: {
                authRequired: true,
              },
            },
            {
              path: 'new',
              lazy: lz(() => import('./views/companies/CompanyFormView.jsx')),
              handle: {
                authRequired: true,
              },
            },
            {
              path: ':id',
              lazy: lz(() => import('./views/companies/CompanyView.jsx')),
              handle: {
                authRequired: true,
                breadcrumb: '{company.name}',
              },
              children: [
                {
                  index: true,
                  lazy: lz(() =>
                    import('./views/companies/CompanyDashboardView.jsx'),
                  ),
                  handle: {
                    authRequired: true,
                  },
                },
                {
                  path: 'edit',
                  lazy: lz(() =>
                    import('./views/companies/CompanyFormView.jsx'),
                  ),
                  handle: {
                    editMode: true,
                    authRequired: true,
                  },
                },
                {
                  path: 'members',
                  handle: {
                    authRequired: true,
                    breadcrumb: 'Members',
                  },
                  children: [
                    {
                      index: true,
                      lazy: lz(() =>
                        import('./views/companies/CompanyMembersView.jsx'),
                      ),
                      handle: {
                        authRequired: true,
                        editMode: false,
                      },
                    },
                    {
                      path: ':userId',
                      lazy: lz(() =>
                        import(
                          './views/companies/CompanyMemberDetailsView.jsx'
                        ),
                      ),
                      handle: {
                        authRequired: true,
                      },
                      // CompanyMemberDetailsView used to be under children in vue routes
                      children: [],
                    },
                  ],
                },
                {
                  path: 'document-templates',
                  lazy: lz(() =>
                    import(
                      './views/companies/CompanyCustomStartPaperworkView.jsx'
                    ),
                  ),
                  handle: {
                    authRequired: true,
                  },
                },
                {
                  path: 'document-templates/create',
                  lazy: lz(() =>
                    import(
                      './views/companies/CompanyCustomStartPaperworkFormView.jsx'
                    ),
                  ),
                  handle: {
                    authRequired: true,
                    editMode: false,
                  },
                },
                {
                  path: 'document-templates/:productionCompanyDocumentTemplateId/is-default/:isDefaultDoc',
                  lazy: lz(() =>
                    import(
                      './views/companies/CompanyCustomStartPaperworkFormView.jsx'
                    ),
                  ),
                  handle: {
                    authRequired: true,
                    editMode: true,
                  },
                },
              ],
            },
          ],
        },
        {
          path: '/login',
          lazy: lz(() => import('./views/LoginView.jsx')),
          handle: {
            onlyLoggedOut: true,
          },
        },
        {
          path: '/login/:phone/verify',
          lazy: lz(() => import('./views/VerifyPhoneView.jsx')),
          handle: {
            onlyLoggedOut: true,
          },
        },
        {
          path: 'faq',
          lazy: lz(() => import('./views/FaqView.jsx')),
          handle: {
            authRequired: true,
          },
        },
        {
          path: 'profile',
          lazy: lz(() => import('./views/profile/ProfileView.jsx')),
          handle: {
            authRequired: true,
          },
        },
        {
          path: 'profile/edit',
          lazy: lz(() => import('./views/profile/ProfileEditView.jsx')),
          handle: {
            authRequired: true,
          },
        },
        {
          path: 'profile/create',
          lazy: lz(() => import('./views/profile/ProfileEditView.jsx')),
          handle: {
            authRequired: true,
          },
        },
        {
          path: '/loan-outs',
          lazy: lz(() => import('./views/LoanOutsView.jsx')),
          handle: {
            authRequired: true,
          },
        },
        {
          path: '/onboarding/:encryptedPhone/personal-info',
          lazy: lz(() =>
            import('./views/onboarding/OnboardingPersonalInfo.jsx'),
          ),
        },
      ],
    },
  ],
  { basename: '/v2/' },
);
