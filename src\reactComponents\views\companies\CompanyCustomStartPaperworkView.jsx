import { applyPureVueInReact } from 'veaury';
import CompanyCustomStartPaperworkViewVue from '../../../views/companies/CompanyCustomStartPaperworkView.vue';
import { useAuth } from '../../AppHooks';
import { useNavigate, useOutletContext } from 'react-router';
const ReactCompanyCustomStartPaperworkView = applyPureVueInReact(
  CompanyCustomStartPaperworkViewVue,
);

const CompanyCustomStartPaperworkView = () => {
  useAuth();
  const navigate = useNavigate();
  const context = useOutletContext();
  return (
    <ReactCompanyCustomStartPaperworkView
      company={context.company}
      navigate={navigate}
    />
  );
};

export default CompanyCustomStartPaperworkView;
