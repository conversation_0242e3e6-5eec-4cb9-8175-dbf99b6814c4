<template>
  <div>
    <div class="m-auto text-center">
      <h1 class="text-xl font-bold mb-2">Admin Sign Start Paperwork</h1>
      <div class="flex justify-center mb-12">
        <PDFViewer class="w-4/5" v-if="pdfUrl" :url="pdfUrl" />
      </div>
      <div
        class="flex justify-center w-100 py-3 fixed bottom-0 left-0 right-0 bg-gray-50 dark:bg-gray-700"
      >
        <Modal v-model="signatureModal">
          <h2 class="font-semibold mb-2">Sign here:</h2>
          <SignatureArea ref="signaturePad" v-model="signature" />
          <div class="flex justify-center space-x-2">
            <Button class="mt-3" color="gray" @click="signatureModal = false">
              Cancel
            </Button>
            <Button class="mt-3" :loading="loadingSignature" @click="sign()">
              Submit
            </Button>
          </div>
        </Modal>
        <Button @click="signatureModal = true"> Sign </Button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Button from '@/components/library/Button.vue';
import Modal from '@/components/library/Modal.vue';
import PDFViewer from '@/components/library/PDFViewer.vue';
import SignatureArea from '@/components/library/SignatureArea.vue';
import {
  adminPreviewProjectMemberStartPaperwork,
  adminSignProjectMemberStartPaperwork,
} from '@/services/project-members';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import type Project from '@/types/Project';
import axios from 'axios';
import { defineComponent, type PropType } from 'vue';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';

export default defineComponent({
  props: {
    project: {
      type: Object as PropType<Project>,
      required: true,
    },
    route: {
      type: Object as PropType<ParsedRoute>,
      required: true,
    },
    navigate: {
      type: Function,
      required: true,
    },
  },
  components: {
    Button,
    Modal,
    SignatureArea,
    PDFViewer,
  },
  data() {
    return {
      pdfSrc: null,
      pdfUrl: null as string | null,
      signatureModal: false,
      signature: null,
      loadingSignature: false as boolean,
    };
  },
  methods: {
    async sign() {
      this.loadingSignature = true;
      const signatureRef = this.$refs.signaturePad as any;
      const signature = signatureRef.getSignatureImage();
      try {
        await adminSignProjectMemberStartPaperwork(
          this.route.params.projectMemberId as string,
          signature,
        );
        SnackbarStore.triggerSnackbar(
          'Successfully signed paperwork',
          2500,
          'success',
        );
        this.navigate({
          pathname: `/projects/${this.project.hashId}/admin/members/${this.route.params.projectMemberId}/member/start-paperwork`,
        });
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
      }
      this.loadingSignature = true;
    },
  },
  async mounted() {
    try {
      this.pdfUrl = adminPreviewProjectMemberStartPaperwork(
        this.route.params.projectMemberId as string,
      );
    } catch (err) {
      if (axios.isAxiosError(err)) {
        const msg = err.response?.data?.['errors']?.[0]?.message;
        SnackbarStore.triggerSnackbar(msg, 2500, 'error');
      } else {
        SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
      }
    }
  },
});
</script>
