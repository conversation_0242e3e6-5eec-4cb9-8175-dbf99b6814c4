<template>
  <div class="flex">
    <div class="flex items-center space-x-3" aria-label="Breadcrumb">
      <div
        v-for="(breadcrumb, breadcrumbIndex) in breadcrumbs"
        :key="`${breadcrumb.text}-${breadcrumbIndex}`"
        class="flex items-center"
      >
        <ChevronRightIcon
          v-if="breadcrumbIndex !== 0"
          class="h-5 w-5 flex-shrink-0 text-gray-400 mr-3"
          aria-hidden="true"
        />
        <a
          class="text-sm cursor-pointer font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          @click="handleNavigate(breadcrumb, $event)"
        >
          <Icon v-if="breadcrumbIndex == 0" name="home" class="w-5 h-5 ml-1" />
          <span v-else>{{ breadcrumb.text }}</span>
        </a>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Icon from '@/components/Icon.vue';
// import routes from '@/router/routes';
import type Project from '@/types/Project';
import type ProjectMember from '@/types/ProjectMember';
import type User from '@/types/User';
import { ChevronRightIcon } from '@heroicons/vue/20/solid';
import { defineComponent, inject, type PropType } from 'vue';
import ProjectStore from '@/reactComponents/stores/project';
import type { RouteRecordRaw } from 'vue-router';

interface Breadcrumb {
  text: string;
  to: {
    pathname: string;
    params: {
      hashId?: string;
      projectMemberId?: string;
    };
  };
}

interface FoundRoute {
  route: RouteRecordRaw;
  path: RouteRecordRaw[];
}

export default defineComponent({
  components: {
    ChevronRightIcon,
    Icon,
  },
  props: {
    project: {
      type: Object as PropType<Project>,
      required: false,
    },
    // matches: {
    //   type: Array as PropType<any>,
    //   required: false,
    // },
  },
  data() {
    return {
      projectMember: null as ProjectMember | null,
      user: null as User | null,
    };
  },
  watch: {
    userCrewProjectIdKey: {
      immediate: true,
      async handler() {
        if (!this.projectMemberId || !this.projectId) {
          return;
        }
        const projectMember = await ProjectStore.fetchProjectMember(
          this.projectMemberId,
        );
        this.projectMember = projectMember;
        this.user = projectMember?.user || null;
      },
    },
  },
  setup() {
    const route: any = inject('route');
    const matches = inject('matches');
    const navigate: Function | undefined = inject('navigate');

    return {
      route,
      navigate,
      matches,
    };
  },
  computed: {
    userCrewProjectIdKey(): string {
      return `${this.projectMemberId}-${this.project?.id}`;
    },

    reactRoutePath() {
      const pathes: any = this.matches;
      const routes = pathes.filter((path: any) => path?.handle?.breadcrumb);
      return routes;
    },

    hashId(): string {
      return this.route.params.hashId as string;
    },
    projectMemberId(): string {
      return this.route.params.projectMemberId as string;
    },
    projectId(): number {
      return this.project?.id as number;
    },

    rawBreadcrumbs(): Breadcrumb[] {
      if (this.reactRoutePath.length) {
        return this.reactRoutePath.map((route: any) => ({
          text: route?.handle?.breadcrumb as string,
          to: {
            pathname: route.pathname as string,
            params: {
              hashId: this.hashId,
              userCrewId: this.projectMemberId,
            },
          },
        }));
      } else {
        return [];
      }
    },
    breadcrumbs(): Breadcrumb[] {
      return this.rawBreadcrumbs.map(({ text, to }) => ({
        text: this.mapDynamicBreadcrumb(text),
        to,
      }));
    },
  },
  methods: {
    handleNavigate(route: Breadcrumb, params: any): void {
      if (this.navigate) {
        this.navigate({
          pathname: route.to.pathname,
        });
      }
    },
    mapDynamicBreadcrumb(breadcrumb: string): string {
      const regex = /{([^{}]+)}/g;

      // Replace all placeholders within the string
      const replacedBreadcrumb = breadcrumb.replace(
        regex,
        (match, variableName) => {
          const variableParts = variableName.split('.');
          let variableValue: any = this;

          for (const part of variableParts) {
            variableValue = variableValue?.[part];

            if (variableValue === undefined) {
              // If any part of the variable path doesn't exist, return an empty string
              return '';
            }
          }

          return variableValue;
        },
      );

      return replacedBreadcrumb;
    },
  },
});
</script>
