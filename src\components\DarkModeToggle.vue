<template>
  <Observer>
    <Icon
      name="moon-star"
      v-if="!AppStore.getIsDarkModeEnabled"
      :class="classes"
      aria-hidden="true"
      @click="setIsDarkModeEnabled(true)"
    />
    <SunIcon
      v-else
      :class="classes"
      aria-hidden="true"
      @click="setIsDarkModeEnabled(false)"
    />
  </Observer>
</template>

<script lang="ts">
import Icon from '@/components/Icon.vue';
import AppStore from '@/reactComponents/stores/app';
import { SunIcon } from '@heroicons/vue/24/outline';
import { Observer } from 'mobx-vue-lite';

export default {
  components: {
    SunIcon,
    Icon,
    Observer,
  },
  computed: {
    getIsDarkModeEnabled() {
      return AppStore.getIsDarkModeEnabled;
    },
    classes(): string {
      return 'text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 h-6 w-6 transition duration-150 ease-in-out group-hover:text-opacity-80 cursor-pointer';
    },
  },
  setup() {
    return {
      AppStore, // add to this. / template scope
    };
  },
  methods: {
    setIsDarkModeEnabled(isDarkModeEnabled: boolean) {
      AppStore.setIsDarkModeEnabled(isDarkModeEnabled);
    },
  },
};
</script>
