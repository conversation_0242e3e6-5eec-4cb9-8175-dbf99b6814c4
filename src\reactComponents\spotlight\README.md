## Platform updates/changes

1. .github/workflows/pr.yml
   - Add `--legacy-peer-deps` &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;TEMP
   - Add `NODE_OPTIONS=--max-old-space-size=4096`
1. scripts/Dockerfile
   - add `--legacy-peer-deps` &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;TEMP
1. src/assets/base.css
   - comment out global rules
1. src/assest/main.css
   - import @castandcrew/common-ui-design-tokens
1. src/reactComponents/App.jsx
   - Font Awesome imports &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;TEMP
1. package.json
   - "@castandcrew/common-ui-design-tokens
   - "@castandcrew/document-signer-react
   - "@castandcrew/dynamic-state-forms
   - "@castandcrew/platform-components
   - "@castandcrew/platform-ui
   - ''
   - "@fortawesome/fontawesome-svg-core
   - "@fortawesome/free-brands-svg-icons
   - "@fortawesome/free-regular-svg-icons
   - "@fortawesome/free-solid-svg-icons
   - "@fortawesome/react-fontawesome
   - "@mui/system
   - "@types/node (--legacy peer issue)

## misc

1. move all VITE\_ config to vite-env.js
