/* eslint-disable react/prop-types */
import React from 'react';

import { TextField } from '@/reactComponents/library';
import { tailwindInputFix } from '@/reactComponents/theme/components/utils';

const TextFieldRaw = (incomingProps, ref) => {
  const props = { ...incomingProps };
  if (!props.inputProps) props.inputProps = {};
  if (!props.inputProps.className) props.inputProps.className = '';
  const inputPropClass = props.inputProps.className || '';
  if (inputPropClass.includes(tailwindInputFix.trim()) === false) {
    props.inputProps.className += //remove tailwind styling on input
      tailwindInputFix;
  }
  return <TextField {...props} />;
};

export default React.forwardRef(TextFieldRaw);
