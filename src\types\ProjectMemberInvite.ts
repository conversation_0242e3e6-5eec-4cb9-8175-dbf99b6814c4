import type { DateTime } from 'luxon';

export default interface ProjectMemberInvite {
  firstName: string | null | undefined;
  lastName: string | null | undefined;
  email: string | null | undefined;
  projectHash: string;
  rateTypeId: number | null | undefined;
  rate: number | null | undefined;
  occupationId: number | null | undefined;
  hireLocationCity: string | null | undefined;
  hireLocationStateId: number | null | undefined;
  departmentId: number | null;
  startDate: DateTime | null;
  isSignedUp?: boolean;
  projectCode?: string;
}
