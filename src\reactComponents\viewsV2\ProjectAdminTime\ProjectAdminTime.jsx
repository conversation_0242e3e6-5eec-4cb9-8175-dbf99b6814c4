import React from 'react';
import PropTypes from 'prop-types';
import _cloneDeep from 'lodash/cloneDeep';

import { useParams } from 'react-router';

import { getCurrentMember, listProjectTimecards } from '@/services/project';
import { getTimecardStatuses } from '@/services/timecard-statuses';
import { getBatches, getUnclaimedBatchDetails } from '@/services/project';

import { Box, Loader } from '@/reactComponents/library';
import { RootBox, PageBox } from '@/reactComponents/library/styledComponents';
import { snackbarAxiosErr } from '@/reactComponents/library/Snackbar';

import Filters from './AdminTimeFilters';
import BatchSideBar from './AdminTimeBatchSidebar';
import BatchTimecards from './AdminTimeBatchTimecards';

import ProjectStore from '@/reactComponents/stores/project';
import { observer } from 'mobx-react-lite';

import {
  UNBATCHED_ID,
  CLEAR_FILTERS,
  INITIAL_TIMECARD_BATCH_FILTER,
  prepFilters,
  setSessionFilters,
  makeFiltersId,
  BATCH_FILTERS,
  ErrorsOption,
} from './utils';
import { TimecardStatusId } from '@/utils/enum';
import { zeroStateVariants } from './ZeroState';

const styles = {
  mainContent: {
    width: '100%',
    display: 'flex',
    minHeight: '500px',
    flexGrow: 1,
  },
};

const ProjectAdminTime = observer((props) => {
  const { project } = ProjectStore;

  const { hashId } = useParams();

  const [timecards, setTimecards] = React.useState([]);
  const [loadingTimecards, setLoadingTimecards] = React.useState(true);
  const [batches, setBatches] = React.useState([]);
  const [unclaimedBatchDetails, setUnclaimedBatchDetails] =
    React.useState(null);
  const [loadingBatches, setLoadingBatches] = React.useState(true);
  const [projectMember, setProjectMember] = React.useState();
  const [reloadingBatches, setReloadingBatches] = React.useState(false);

  const [timecardStatuses, setTimecardStatuses] = React.useState([]);

  const [batchPagination, setBatchPagination] = React.useState({
    page: 1,
    limit: 100,
    total: 0,
  });
  const [timecardPagination, setTimecardPagination] = React.useState({
    page: 1,
    limit: 100,
    total: 0,
  });

  const [batchFilters, setBatchFilters] = React.useState(
    _cloneDeep(BATCH_FILTERS),
  );
  const [filters, setFilters] = React.useState(CLEAR_FILTERS);
  const [filtersReady, setFiltersReady] = React.useState(false);

  const [unapprovedTcCount, setUnapprovedTcCount] = React.useState(0);
  const [errorTcCount, setErrorTcCount] = React.useState(0);

  const filtersId = React.useMemo(() => makeFiltersId(hashId), [hashId]);

  const clearFilters = React.useCallback(() => {
    const batchFiltersHasValues = batchFilters.some(
      (f) => f.options?.length > 0,
    );

    const batchFilter = _cloneDeep(BATCH_FILTERS);
    if (batchFiltersHasValues) setBatchFilters(batchFilter);

    setFilters((prev) => {
      const oldBatch = prev.find((f) => f.id === 'batch');
      const newFilters = _cloneDeep(CLEAR_FILTERS);
      const newBatchIdx = newFilters.findIndex((f) => f.id === 'batch');
      newFilters[newBatchIdx] = _cloneDeep(oldBatch);

      setSessionFilters(filtersId, newFilters, batchFilter);
      return newFilters;
    });
  }, [filtersId, batchFilters]);

  const filterUnapprovedOrErrorTimecards = React.useCallback(
    (type) => {
      setLoadingTimecards(true);
      clearFilters();
      setFilters((prev) => {
        const newFilters = _cloneDeep(prev);
        if (type === 'unapproved') {
          let tcStatus = newFilters.find((f) => f.label === 'Timecard Status');
          const validStatuses = timecardStatuses.filter(
            (s) =>
              s.id === TimecardStatusId.Submitted ||
              s.id === TimecardStatusId.InProgress ||
              s.id === TimecardStatusId.RequestedChanges,
          );
          tcStatus.options = [
            ...validStatuses.map((s) => ({
              ...s,
              active: true,
            })),
          ];
        }
        if (type === 'error') {
          let tcErrorFilter = newFilters.find((f) => f.label === 'Errors');
          const calculationErrorFilterOption = ErrorsOption.find(
            (o) => o.value === 'errors',
          );
          tcErrorFilter.options = [
            { ...calculationErrorFilterOption, active: true },
          ];
        }
        return newFilters;
      });
    },
    [clearFilters, setFilters, timecardStatuses],
  );

  const checkTimecardsWithCalculationErrors = React.useCallback(
    async (batchId) => {
      const clearFilters = _cloneDeep(CLEAR_FILTERS);
      let tcErrorFilter = clearFilters.find((f) => f.label === 'Errors');
      let batchFilter = clearFilters.find((f) => f.id === 'batch');

      const calculationErrorFilterOption = ErrorsOption.find(
        (o) => o.value === 'errors',
      );

      tcErrorFilter.options = [
        { ...calculationErrorFilterOption, active: true },
      ];
      batchFilter.options = [
        {
          id: `${batchId}`,
          value: batchId > 0 ? batchId : null,
          name: '',
          active: true,
        },
      ];

      const preppedFilter = prepFilters(clearFilters);

      const sort = '';
      const pagination = {
        page: 1,
        limit: 100,
        total: 0,
      };
      const { data: res } = await listProjectTimecards(
        project.id,
        preppedFilter,
        sort,
        pagination,
      );
      const { meta } = res;
      setErrorTcCount(meta.total);
    },
    [project],
  );

  const checkUnapprovedTimecards = React.useCallback(
    async (batchId) => {
      setUnapprovedTcCount(0);
      setErrorTcCount(0);
      if (!batchId || batchId === UNBATCHED_ID) return;

      const clearFilters = _cloneDeep(CLEAR_FILTERS);
      let tcStatus = clearFilters.find((f) => f.label === 'Timecard Status');
      let batchFilter = clearFilters.find((f) => f.id === 'batch');
      tcStatus.options = [
        { value: TimecardStatusId.Submitted, active: true },
        { value: TimecardStatusId.InProgress, active: true },
        { value: TimecardStatusId.RequestedChanges, active: true },
      ];
      batchFilter.options = [
        {
          id: `${batchId}`,
          value: batchId > 0 ? batchId : null,
          name: '',
          active: true,
        },
      ];

      const unapprovedTcFilter = prepFilters(clearFilters);

      const sort = '';
      const pagination = {
        page: 1,
        limit: 100,
        total: 0,
      };
      const { data: res } = await listProjectTimecards(
        project.id,
        unapprovedTcFilter,
        sort,
        pagination,
      );
      const { meta } = res;
      setUnapprovedTcCount(meta.total);
      if (meta.total === 0) {
        checkTimecardsWithCalculationErrors(batchId);
      }
    },
    [project, checkTimecardsWithCalculationErrors],
  );

  const selectBatch = React.useCallback(
    (batchId) => {
      setFilters((prev) => {
        const newFilters = _cloneDeep(prev);
        let timecardBatchFilter = newFilters.find((f) => f.id === 'batch');
        if (!timecardBatchFilter) {
          newFilters.push(_cloneDeep(INITIAL_TIMECARD_BATCH_FILTER));
          timecardBatchFilter = newFilters.find((f) => f.id === 'batch');
        }

        timecardBatchFilter.options = [
          {
            id: `${batchId}`,
            value: batchId > 0 ? batchId : null,
            name: '',
            active: true,
          },
        ];

        return newFilters;
      });
      setTimecards([]);
      setLoadingTimecards(true);
      checkUnapprovedTimecards(batchId);
    },
    [setFilters, checkUnapprovedTimecards],
  );

  const selectedBatchId =
    filters?.find((f) => f.id === 'batch')?.options[0]?.value || -1;

  const loadTimecardStatuses = async () => {
    try {
      const { data: timecardStatusData } = await getTimecardStatuses();
      const mappedStatus = timecardStatusData
        .sort((a, b) => a.id - b.id)
        .map((s) => ({
          id: s.id,
          value: s.id,
          name: s.name,
          active: s.active || false,
        }));
      setTimecardStatuses(mappedStatus);
    } catch (err) {
      snackbarAxiosErr(err, 'Error fetching timecard statuses');
    }
  };

  const fetchBatches = React.useCallback(
    (newPage = batchPagination.page) => {
      const newPagination = { ...batchPagination, page: newPage };
      const preppedBatchFilters = prepFilters(batchFilters);

      getBatches(project.id, newPagination, preppedBatchFilters)
        .then(async ({ data: res }) => {
          const { data: newBatches, meta } = res;

          const timecardBatchFilter = filters.find((f) => f.id === 'batch');
          const currentBatch = timecardBatchFilter?.options[0];
          const currentBatchId = currentBatch?.id;

          if (
            !newBatches.find((b) => `${b.id}` === `${currentBatchId}`) &&
            Number(currentBatchId) !== UNBATCHED_ID
          ) {
            selectBatch(UNBATCHED_ID);
          }

          const response = await getUnclaimedBatchDetails(project.id);
          setUnclaimedBatchDetails(response.data);
          setBatches(newBatches);
          setBatchPagination((prev) => ({
            ...prev,
            page: meta.current_page,
            total: meta.total,
          }));
        })
        .finally(() => {
          setLoadingBatches(false);
          setReloadingBatches(false);
        });
    },
    [
      batchPagination,
      project,
      batchFilters,
      filters,
      selectBatch,
      setLoadingBatches,
      setReloadingBatches,
    ],
  );

  const refetchBatchesRef = React.useRef(null);
  const batchFiltersChangedRef = React.useRef(false);
  const initRef = React.useRef(true);

  React.useEffect(() => {
    if (filtersReady) {
      if (initRef.current) {
        let batchFilter = filtersRef.current.find((f) => f.id === 'batch');
        let batch = batchFilter?.options?.find((b) => b.active);
        let batchId = null;

        batchId = batch ? batch.value : null;
        fetchBatches(); //expected to run with initial batchFilters/tcFilters ready ONLY
        checkUnapprovedTimecards(batchId);
      }

      batchFiltersChangedRef.current = true;
      if (!initRef.current) setReloadingBatches(true);
      initRef.current = false;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [batchFilters, filtersReady, checkUnapprovedTimecards]);

  React.useEffect(() => {
    // reloading batches and
    if (batchFiltersChangedRef.current && reloadingBatches) {
      batchFiltersChangedRef.current = false;
      if (refetchBatchesRef.current) {
        clearTimeout(refetchBatchesRef.current);
      }
      refetchBatchesRef.current = setTimeout(() => {
        fetchBatches();
      }, 350);
    }
  }, [fetchBatches, reloadingBatches]);

  const projectIdLoaded = React.useRef(false);
  React.useEffect(() => {
    //only fetch batches when project changes
    if (project?.id) {
      projectIdLoaded.current = true;
    }
  }, [project]);

  React.useEffect(() => {
    const fetchProjectMember = async () => {
      if (project?.id) {
        const { data: projectMemberData } = await getCurrentMember(project.id);
        setProjectMember(projectMemberData);
      }
    };
    fetchProjectMember();
  }, [project?.id]);

  React.useEffect(() => {
    if (projectIdLoaded.current) {
      projectIdLoaded.current = false;
      fetchBatches();
    }
  }, [fetchBatches]);

  const fetchTimecardTimeoutIdRef = React.useRef(null);
  const filtersRef = React.useRef(filters);
  filtersRef.current = filters;
  const fetchTimecards = React.useCallback(
    async (newPage = timecardPagination.page) => {
      const preppedFilters = prepFilters(filtersRef.current);

      const pagination = {
        ...timecardPagination,
        page: newPage,
      };

      if (project?.id) {
        const sort = '';

        try {
          const { data: res } = await listProjectTimecards(
            project.id,
            preppedFilters,
            sort,
            pagination,
          );
          const { data: timecards, meta } = res;
          setTimecards(timecards);

          setTimecardPagination((prev) => ({
            ...prev,
            page: meta.current_page,
            total: meta.total,
          }));
        } catch (err) {
          console.error(err);
          snackbarAxiosErr(err, 'Error fetching timecards');
        } finally {
          setLoadingTimecards(false);
        }
      }
    },
    [project.id, timecardPagination],
  );

  //Fetch Timecards on filter or selectedBatch change
  const filtersChangedRef = React.useRef(false);

  React.useEffect(() => {
    filtersChangedRef.current = true;
  }, [filters]);

  React.useEffect(() => {
    if (filtersReady) {
      setSessionFilters(filtersId, filters, batchFilters);
    }
  }, [filters, batchFilters, filtersId, filtersReady]);

  React.useEffect(() => {
    if (filtersChangedRef.current && filtersReady) {
      filtersChangedRef.current = false;
      fetchTimecardTimeoutIdRef.current &&
        clearTimeout(fetchTimecardTimeoutIdRef.current);
      fetchTimecardTimeoutIdRef.current = setTimeout(() => {
        fetchTimecards(1);
      }, 350);
    }
  }, [fetchTimecards, filters, filtersReady]);

  React.useEffect(() => {
    loadTimecardStatuses();
  }, []);

  const zeroStateVariant = React.useMemo(() => {
    if (timecards.length > 0) {
      return '';
    }

    const noTimecardsInProj =
      batches.reduce((acc, b) => acc + b.timecards?.length || 0, 0) === 0;

    if (noTimecardsInProj) {
      return zeroStateVariants.noTimecardsInProj;
    }

    const anyFiltersActive = filters
      ?.filter((f) => f.id !== 'batch')
      .some((f) => f.options.some((o) => o.active));

    if (anyFiltersActive) {
      return zeroStateVariants.filtersActive;
    }

    const isUnbatched = selectedBatchId === UNBATCHED_ID;
    if (isUnbatched) {
      return zeroStateVariants.unbatched;
    }

    return 'default';
  }, [batches, filters, selectedBatchId, timecards]);

  const batch =
    selectedBatchId === UNBATCHED_ID
      ? { name: 'Unbatched Timecards', id: UNBATCHED_ID }
      : batches.find((b) => b.id === selectedBatchId);

  return (
    <RootBox>
      {!loadingBatches ? (
        <>
          <PageBox sx={{ gap: 0 }}>
            <Filters
              filters={filters}
              setFilters={setFilters}
              project={project}
              timecardStatuses={timecardStatuses}
              selectedBatchId={selectedBatchId}
              filtersReady={filtersReady}
              setFiltersReady={setFiltersReady}
              batches={batches}
              loadingBatches={loadingBatches}
              clearFilters={clearFilters}
              filtersId={filtersId}
              checkUnapprovedTimecards={checkUnapprovedTimecards}
              batchFilters={batchFilters}
              setBatchFilters={setBatchFilters}
            />
            <Box sx={styles.mainContent}>
              <BatchSideBar
                selectedBatchId={selectedBatchId}
                selectBatch={selectBatch}
                project={project}
                batches={batches}
                fetchBatches={fetchBatches}
                batchPagination={batchPagination}
                fetchTimecards={fetchTimecards}
                reloadingBatches={reloadingBatches}
                unclaimedBatchDetails={unclaimedBatchDetails}
                checkUnapprovedTimecards={checkUnapprovedTimecards}
              />
              <BatchTimecards
                timecards={timecards}
                batch={batch}
                loading={loadingTimecards}
                project={project}
                fetchBatches={fetchBatches}
                fetchTimecards={fetchTimecards}
                timecardPagination={timecardPagination}
                zeroStateVariant={zeroStateVariant}
                clearFilters={clearFilters}
                unapprovedTcCount={unapprovedTcCount}
                projectMember={projectMember}
                filterUnapprovedOrErrorTimecards={
                  filterUnapprovedOrErrorTimecards
                }
                checkUnapprovedTimecards={checkUnapprovedTimecards} // only use in modals here
                errorTcCount={errorTcCount}
                unclaimedBatchDetails={unclaimedBatchDetails}
              />
            </Box>
          </PageBox>
        </>
      ) : (
        <Box sx={{ height: '60vh' }}>
          {/* //TODO skeleton */}
          <Loader />
        </Box>
      )}
    </RootBox>
  );
});

ProjectAdminTime.propTypes = {
  project: PropTypes.object,
};

export default ProjectAdminTime;
