<template>
  <section aria-labelledby="applicant-information-title">
    <div class="shadow sm:rounded-lg bg-white dark:bg-gray-900">
      <div
        class="flex justify-between px-4 py-5 sm:px-6 bg-gray-50 dark:bg-gray-700 sm:rounded-t-lg"
      >
        <h2
          id="applicant-information-title"
          class="text-lg font-medium leading-6"
        >
          Timecards
        </h2>
      </div>
      <div
        class="border-t border-gray-200 dark:border-gray-600 px-4 py-5 sm:px-6"
      >
        <ProjectAdminTimecardsTable
          v-if="projectMember?.id"
          :project="project"
          is-admin
          :project-member="projectMember"
        />
        <div v-else class="py-8">
          <h2 class="text-center text-md">
            No timecards found for this crew member
          </h2>
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import ProjectAdminTimecardsTable from '@/components/ProjectAdminTimecardsTable.vue';
import type Project from '@/types/Project';
import type ProjectMember from '@/types/ProjectMember';
import { defineComponent, type PropType, provide, ref, watchEffect } from 'vue';

export default defineComponent({
  components: { ProjectAdminTimecardsTable },
  setup(props, ctx) {
    const route = ref(props.route);
    watchEffect(() => {
      route.value = props.route;
    });
    provide('route', route);
    provide('navigate', props.navigate);
  },
  props: {
    project: { type: Object as PropType<Project>, required: true },
    projectMember: { type: Object as PropType<ProjectMember>, required: true },
    route: { type: Object, required: true },
    navigate: { type: Function, required: false },
  },
});
</script>
