/// <reference types="cypress" />
import 'cypress-file-upload';

export default class TimecardDetails {
  readonly closeModalButton = '[data-testid="CloseIcon"]';
  readonly modalFooterButton = '.modalFooter button';
  readonly saveInput = '[data-testid="save-modal-input"]';
  readonly guaranteedHoursInput =
    '[data-testid="timecard-details-guarHours"] input';
  readonly payPremiumCheckbox = '[data-testid="pay-premium-checkbox"]';
  readonly rateTypeInput = '[data-testid="timecard-details-rateType"] input';
  readonly hourlyRateInput =
    '[data-testid="timecard-details-hourlyRate"] input';
  readonly occupationInput =
    '[data-testid="timecard-details-occupation"] input';
  readonly addReimbursementButton = '[data-testid="add-reimbursement-btn"]';
  readonly addKitRentalButton = '[data-testid="add-kit-rental-btn"]';
  readonly addMileageButton = '[data-testid="add-mileage-btn"]';

  cancel(details: string): void {
    cy.contains('button', 'Save').click();
    this.confirmModal('Cancel');
  }

  approve(): void {
    cy.contains('button', /^Approve$/i, { timeout: 15000 })
      .should('be.visible')
      .click();
  }

  save(): void {
    cy.contains('button', 'Save').should('be.visible').click();
    this.confirmModal('Save');
  }

  fillTimecardDetails(
    occupation?: string,
    rateValue?: string,
    rateTypes?: string,
    guaranteedHoursValue?: string,
    payPremium?: boolean,
  ): void {
    this.selectOccupation(occupation);
    if (rateValue) this.setRates(rateValue);
    if (rateTypes) this.setRateTypes(rateTypes);
    if (guaranteedHoursValue) this.fillGuaranteedHours(guaranteedHoursValue);
    if (payPremium) this.checkPayPremium();
  }

  fillOutReimbursementDetails() {
    cy.get('[data-testid="body-lineNumber"]').eq(0).type('001');
    cy.get('[data-testid="body-workLocation"]').eq(0).click();
    cy.get('ul[role="listbox"] li').first().click();
    cy.get('[data-testid="body-documentId"] button[type="button"]')
      .eq(0)
      .click();
    cy.get('input#input-file-upload').attachFile('customPaperwork.pdf');
  }

  fillKitRentalDetails(day: string) {
    cy.get(this.addKitRentalButton, { timeout: 6000 })
      .scrollIntoView()
      .should('be.visible')
      .click();
    cy.get('[data-testid="body-documentId"] button[type="button"]')
      .eq(1)
      .click();
    cy.get('input#input-file-upload').attachFile('customPaperwork.pdf');
    cy.get('[data-testid="body-lineNumber"]')
      .eq(1)
      .find('input')
      .and('not.be.disabled')
      .clear()
      .type('001', { delay: 100 });
    cy.get('[data-testid="body-workLocation"]').eq(1).click();
    cy.get('ul[role="listbox"] li').first().click();
    cy.get('[data-testid="body-rentalRate"]').eq(0).find('input').type('100');
    cy.get('[data-testid="body-rentalDays"]').eq(0).click();
    cy.get(`[data-testid="rental-days-${day}"]`).click();
    cy.get('[data-testid="Close-btn"]').click();
  }

  fillAddMileageLog() {
    cy.get(this.addMileageButton).scrollIntoView().should('be.visible').click();
    cy.contains('div', 'Mileage');
    cy.get('[data-testid="body-lineNumber"]')
      .eq(1)
      .find('input')
      .and('not.be.disabled')
      .clear()
      .type('001', { delay: 100 });

    cy.get('[data-testid="body-totalMileage"]').eq(0).find('input').type('100');
    cy.get('[data-testid="body-workLocation"]').eq(0).click();
    cy.get('ul[role="listbox"] li').first().click();
    cy.get('[data-testid="body-documentId"] button[type="button"]')
      .eq(0)
      .click();
    cy.get('input#input-file-upload').attachFile('customPaperwork.pdf');
  }

  confirmCheckBox(timecardDaysId: string): void {
    cy.get(`div[data-id='${timecardDaysId}']`)
      .scrollIntoView()
      .should('be.visible')
      .within(() => {
        cy.get(`div[data-field="isActive"] input[type="checkbox"]`)
          .should('exist')
          .click();
      });
  }

  fillLineNumber(timecardDaysId: string, text: string): void {
    cy.get(
      `div[data-id='${timecardDaysId}'] > div[data-field="lineNumber"] input[type="text"]`,
    )
      .scrollIntoView()
      .should('be.visible')
      .type(text);
  }

  selectWorkStatus(timecardDaysId: string, status: string): void {
    cy.get(
      `div[data-id='${timecardDaysId}'] > div[data-field="workStatus"] input[type="text"]`,
    )
      .scrollIntoView()
      .should('exist')
      .type(status)
      .click();

    cy.get('ul[role="listbox"]')
      .should('exist')
      .within(() => {
        cy.get('li')
          .should('have.length.greaterThan', 0)
          .first()
          .should('be.visible')
          .click();
      });
  }

  selectWorkZone(timecardDaysId: string, zone: string): void {
    cy.get(
      `div[data-id='${timecardDaysId}'] > div[data-field="workZone"] input[type="text"]`,
    )
      .scrollIntoView()
      .should('exist')
      .type(zone)
      .click();

    cy.get('ul[role="listbox"]')
      .should('exist')
      .within(() => {
        cy.get('li')
          .should('have.length.greaterThan', 0)
          .first()
          .should('be.visible')
          .click();
      });
  }

  selectProjectShootLocation(timecardDaysId: string): void {
    cy.get(
      `div[data-id='${timecardDaysId}'] > div[data-field="projectShootLocation"] input[type="text"]`,
    )
      .scrollIntoView()
      .should('exist')
      .click();

    cy.get('ul[role="listbox"]')
      .should('exist')
      .within(() => {
        cy.get('li')
          .should('have.length.greaterThan', 0)
          .first()
          .should('be.visible')
          .click();
      });
  }

  fillOutTimecardDetails(dayId: string): void {
    this.confirmCheckBox(dayId);
    this.fillLineNumber(dayId, '001');
    this.selectWorkStatus(dayId, 'WORK');
    this.selectWorkZone(dayId, 'Studio');
    this.selectProjectShootLocation(dayId);
  }

  navigateToTimecard(timecardId: string): void {
    const timecardLink = `a[href*="/timecards/${timecardId}"]`;
    cy.get(timecardLink).scrollIntoView();
    cy.get(timecardLink).should('be.visible').first().click();
  }

  private fillGuaranteedHours(guaranteedHours: string): void {
    cy.get(this.guaranteedHoursInput, { timeout: 5000 })
      .should('be.visible')
      .type(guaranteedHours);
  }

  private checkPayPremium(): void {
    cy.get(this.payPremiumCheckbox, { timeout: 2000 })
      .should('be.visible')
      .click();
  }

  private setRateTypes(rateType: string): void {
    cy.get(this.rateTypeInput, { timeout: 2000 }).should('be.visible').clear();
    cy.get(this.rateTypeInput, { timeout: 1000 })
      .should('be.visible')
      .type(rateType);
    cy.get('ul[role="listbox"]')
      .should('be.visible')
      .within(() => {
        cy.contains('li', rateType).should('be.visible').click();
      });
  }

  private setRates(rateValue: string): void {
    cy.get(this.hourlyRateInput, { timeout: 5000 })
      .should('be.visible')
      .type(rateValue);
  }

  private selectOccupation(occupation?: string): void {
    cy.get(this.occupationInput, { timeout: 15000 })
      .should('be.visible')
      .click();

    if (occupation) {
      cy.get(this.occupationInput).clear().type(occupation);
      cy.get('ul[role="listbox"]')
        .should('be.visible')
        .find('li')
        .contains(occupation)
        .should('be.visible')
        .click();
    } else {
      cy.get('ul[role="listbox"]')
        .should('be.visible')
        .find('li')
        .first()
        .should('be.visible')
        .click();
    }
  }

  private confirmModal(buttonText: string): void {
    cy.get(this.modalFooterButton)
      .contains(new RegExp(`^${buttonText}$`, 'i'))
      .should('be.visible')
      .click();
  }
}
