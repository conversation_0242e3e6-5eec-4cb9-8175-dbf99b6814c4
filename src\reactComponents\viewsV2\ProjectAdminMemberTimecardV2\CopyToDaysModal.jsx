import React from 'react';
import PropTypes from 'prop-types';

import { Box, Modal, Button, Text } from '@/reactComponents/library';

const CopyToDaysModal = (props) => {
  const {
    copyToDays,
    timecard,
    // onOpen,
    open,
    onClose,
    copyFromDay,
  } = props;
  const [selectedDays, setSelectedDays] = React.useState([]);
  const timecardDays = timecard?.timecardDays || [];
  const onSelectDay = (day) => {
    const isSelected = selectedDays.some((d) => d.id === day.id);
    if (isSelected) {
      setSelectedDays(selectedDays.filter((d) => d.id !== day.id));
    } else {
      setSelectedDays([...selectedDays, day]);
    }
  };

  const onApplyCopyToDays = () => {
    copyToDays({ selectedDays, copyFromDay });
  };

  return (
    <Box>
      <Modal
        title={'Copy to day(s)'}
        open={open}
        setOpen={() => onClose()}
        onCancel={() => onClose()}
        onSubmit={onApplyCopyToDays}
        submitText="Apply"
      >
        <Box sx={{ maxWidth: '400px' }}>
          <Box
            sx={{
              width: '150px',
              mb: '8px',
            }}
          >
            <Text variant="smSemi">Select Day(s)</Text>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            {timecardDays.map((day) => {
              let variant = 'secondaryContained';
              let sx = {
                padding: '0px',
                borderWidth: '3px',
                lineHeight: '20px',
              };
              const isSelected = selectedDays.some((d) => d.id === day.id);
              const isTargetRow = day.id === copyFromDay;
              if (isSelected) {
                variant = 'primaryOutlined';
              }
              return (
                <Button
                  variant={variant}
                  disabled={isTargetRow}
                  key={day.date.toISO()}
                  sx={sx}
                  onClick={() => onSelectDay(day)}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      padding: '10px 2px',
                      gap: '4px',
                    }}
                  >
                    <Box>{day.date.toFormat('ccc').slice(0, 2)}</Box>
                    <Box>{day.date.toFormat('MM/dd')}</Box>
                  </Box>
                </Button>
              );
            })}
          </Box>
        </Box>
      </Modal>
    </Box>
  );
};

CopyToDaysModal.propTypes = {
  copyToDays: PropTypes.func.isRequired,
  timecard: PropTypes.object.isRequired,
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  copyFromDay: PropTypes.number.isRequired,
};

export default CopyToDaysModal;
