import type { DateTime } from 'luxon';
import type Address from './Address';
import type { PayDay } from './PayDay';
import type { PayDayFrequency } from './PayDayFrequency';
import type PayrollProvider from './PayrollProvider';
import type { TaxClassification } from './TaxClassification';

export default interface ProductionCompany {
  id?: number;
  name: string;
  phone: string;
  addresses: Address[];
  payDay: PayDay;
  payDayFrequency: PayDayFrequency;
  customPayDay: string;
  taxClassification: TaxClassification;
  payrollProvider: PayrollProvider;
  castAndCrewId?: number;
  generateStartForm: boolean;
  generateWageTheftProtectionAgreement: boolean;
  createdAt: DateTime;
  updatedAt: DateTime;
}
