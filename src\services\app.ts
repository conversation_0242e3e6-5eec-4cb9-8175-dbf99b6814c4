import type { Pagination } from '@/types/Pagination';
import axios, { type AxiosResponse } from 'axios';
import { coreBaseUrl } from '../utils/constants';

export const getAllWorkLocations = async (
  search?: string,
  pagination?: Pagination,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/shoot-locations/`;
  const response = await axios.get(url, {
    params: { search, ...pagination },
    withCredentials: true,
  });
  return response;
};

export const getAllWorkStatuses = async (): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/work-statuses`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const getAllWorkZones = async (): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/work-zones`;
  const response = await axios.get(url, {
    withCredentials: true,
  });
  return response;
};
