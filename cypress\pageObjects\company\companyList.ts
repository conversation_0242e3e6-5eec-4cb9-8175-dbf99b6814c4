export default class CompanyList {
  readonly createCompanyBtn = '[data-testid="add-company-btn"]';
  readonly editCompanyBtn = '[data-testid="edit-company-btn"]';
  readonly companyFilter = '[data-testid="filter-popover-Company Name"]';
  readonly companyFilterByNameInput = '[data-testid="Company Name-input"]';
  readonly companyList = '[data-testid="company-list"]';

  clickCreateCompanyButton() {
    cy.get(this.createCompanyBtn).should('be.visible').click();
    cy.contains('This form is used to create a new production company.').should(
      'be.visible',
    );
  }

  clickEditCompanyButton() {
    cy.get(this.editCompanyBtn).should('be.visible').click();
    cy.contains('This form is used to create a new production company.').should(
      'be.visible',
    );
  }

  goToProjectDetails(companyName: string) {
    cy.get(`[data-testid="company-name-${companyName}"]`)
      .should('be.visible')
      .click();
  }
}
