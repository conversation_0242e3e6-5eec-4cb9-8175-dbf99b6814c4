<template>
  <section aria-labelledby="applicant-information-title">
    <div class="shadow sm:rounded-lg bg-white dark:bg-gray-900">
      <div
        class="flex justify-between px-4 py-5 sm:px-6 bg-gray-50 dark:bg-gray-700 sm:rounded-t-lg"
      >
        <h2
          id="applicant-information-title"
          class="text-lg font-medium leading-6"
        >
          Loan Out
        </h2>
      </div>
      <div
        class="border-t border-gray-200 dark:border-gray-600 px-4 py-5 sm:px-6"
      >
        <dl
          v-if="loanOut"
          class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"
        >
          <div
            v-for="(detail, index) in detailsList"
            class="sm:col-span-1"
            :key="index"
          >
            <dt class="text-sm font-medium text-gray-500">
              {{ detail.label }}
            </dt>
            <dd class="mt-1 text-sm">
              {{ detail.value }}
            </dd>
          </div>
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
              Article of Incorporation
            </dt>
            <dd
              v-if="loanOut.articleOfIncorporationDocumentId"
              class="mt-1 text-sm"
            >
              <div v-if="loanOut?.articleOfIncorporationDocumentId">
                Click
                <span
                  class="underline cursor-pointer"
                  @click="openArticleOfIncorporation"
                  >here</span
                >
                to download the form.
              </div>
            </dd>
            <dd v-else class="mt-1 text-sm">
              No article of incorporation has been uploaded.
            </dd>
          </div>
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
              Agent Authorization
            </dt>
            <dd
              v-if="loanOut.articleOfIncorporationDocumentId"
              class="mt-1 text-sm"
            >
              <div v-if="loanOut?.articleOfIncorporationDocumentId">
                Click
                <span
                  class="underline cursor-pointer"
                  @click="openAgentAuthorization"
                  >here</span
                >
                to download the form.
              </div>
            </dd>
            <dd v-else class="mt-1 text-sm">
              No agent authorization has been uploaded.
            </dd>
          </div>
          <div class="sm:col-span-2">
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
              Loan Out Approval Status
            </dt>
            <dd v-if="!isLoanOutApproved" class="mt-1 text-sm">
              <div class="mb-3">
                This project member has not been approved for loan out useage.
                Click the button below to approve.
              </div>
              <div class="flex">
                <Button @click="approveProjectMemberLoanOut" size="sm">
                  Approve Loan Out Usage
                </Button>
                <Button
                  v-if="isLoanOutRequested"
                  class="ml-3"
                  color="gray"
                  @click="declineProjectMemberLoanOut"
                  size="sm"
                >
                  Decline Loan Out Usage
                </Button>
              </div>
            </dd>
            <dd v-else class="mt-1 text-sm">
              <div class="mb-3 flex justify-start items-center space-x-2">
                <Badge type="success" text="" size="icon" class="w-6 h-6">
                  <template #icon>
                    <Icon name="checked" class="w-4 h-4" />
                  </template>
                </Badge>
                <div>Loan out has been approved!</div>
              </div>
            </dd>
          </div>
        </dl>
        <div v-else>No loan out has been added.</div>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import Icon from '@/components/Icon.vue';
import Badge from '@/components/library/Badge.vue';
import Button from '@/components/library/Button.vue';
import {
  getLoanOutAgentAuthorization,
  getLoanOutArticleOfIncorporation,
  getLoanOutById,
} from '@/services/loan-out';
import { approveLoanOut, declineLoanOut } from '@/services/project-members';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import type Project from '@/types/Project';
import type ProjectMember from '@/types/ProjectMember';
import type User from '@/types/User';
import type UserCrew from '@/types/UserCrew';
import { ProjectMemberLoanOutStatusId } from '@/utils/enum';
import { CheckCircleIcon } from '@heroicons/vue/20/solid';
import axios from 'axios';
import { defineComponent, type PropType } from 'vue';

export default defineComponent({
  components: { Button, CheckCircleIcon, Badge, Icon },
  props: {
    userCrew: {
      type: Object as PropType<UserCrew>,
      required: true,
    },
    user: {
      type: Object as PropType<User>,
      required: true,
    },
    project: { type: Object as PropType<Project>, required: true },
    projectMember: { type: Object as PropType<ProjectMember>, required: true },
    refresh: { type: Function as PropType<() => void>, required: false },
  },
  data() {
    return {
      loanOut: (null as any) || null,
    };
  },
  computed: {
    formattedAddress(): string {
      if (!this.loanOut) return '';
      const { address } = this.loanOut;
      const { street, street2, city, state, zip } = address;
      const streetAddress = `${street}${street2 ? ` ${street2}` : ''}`;
      return `${streetAddress}, ${city}, ${state.name} ${zip}`;
    },
    detailsList(): { label: string; value: string }[] {
      return [
        { label: 'Business Name', value: this.loanOut?.businessName },
        { label: 'EIN', value: this.loanOut?.employerIdentificationNumber },
        {
          label: 'Tax Classification',
          value: this.loanOut?.taxClassification?.name,
        },
        { label: 'Address', value: this.formattedAddress },
      ];
    },
    isLoanOutApproved(): boolean {
      return [
        ProjectMemberLoanOutStatusId.Approved,
        ProjectMemberLoanOutStatusId.Used,
      ].includes(this.projectMember?.loanOutStatusId);
    },
    isLoanOutRequested(): boolean {
      return [ProjectMemberLoanOutStatusId.Requested].includes(
        this.projectMember?.loanOutStatusId,
      );
    },
  },
  methods: {
    handleRefresh() {
      if (this.refresh) {
        this.refresh();
      }
    },
    openArticleOfIncorporation() {
      const link = document.createElement('a');
      link.href = getLoanOutArticleOfIncorporation(this.loanOut.id);
      link.setAttribute('download', 'article_of_incorporation.pdf');
      document.body.appendChild(link);
      link.click();
    },
    openAgentAuthorization() {
      const link = document.createElement('a');
      link.href = getLoanOutAgentAuthorization(this.loanOut.id);
      link.setAttribute('download', 'agent_authorization.pdf');
      document.body.appendChild(link);
      link.click();
    },
    async approveProjectMemberLoanOut() {
      try {
        await approveLoanOut(this.projectMember.id);
        this.handleRefresh();
        SnackbarStore.triggerSnackbar(
          'Loan out usage approved.',
          2500,
          'success',
        );
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
      }
    },
    async declineProjectMemberLoanOut() {
      try {
        await declineLoanOut(this.projectMember.id);
        this.handleRefresh();
        SnackbarStore.triggerSnackbar(
          'Loan out usage declined.',
          2500,
          'success',
        );
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
      }
    },
    async load() {
      const { data: LoanOut } = await getLoanOutById(this.user?.id!);
      this.loanOut = LoanOut;
    },
  },
  async mounted() {
    await this.load();
  },
});
</script>
