import React from 'react';
import PropTypes from 'prop-types';
import { Box, TextInput, Modal } from '@/reactComponents/library';

const RequestChangesModal = (props) => {
  const {
    open,
    setOpen,
    requestChangeMethod,
    saving,
    timecardRequestChangeNote,
  } = props;

  const [note, setNote] = React.useState(timecardRequestChangeNote);

  React.useEffect(() => {
    setNote(timecardRequestChangeNote);
  }, [timecardRequestChangeNote]);

  if (!open) return null;

  return (
    <Modal
      open={open}
      title="Request Changes"
      setOpen={setOpen}
      onSubmit={() => requestChangeMethod(note)}
      submitText="Request"
      onCancel={() => {
        setOpen(false);
      }}
      loading={saving}
    >
      <Box sx={{ width: '100%', height: '100%' }}>
        <TextInput
          sx={{ width: '100%' }}
          value={note}
          multiline
          minRows={4}
          onChange={(e) => setNote(e.target.value)}
        />
      </Box>
    </Modal>
  );
};

RequestChangesModal.propTypes = {
  open: PropTypes.bool.isRequired,
  setOpen: PropTypes.func.isRequired,
  requestChangeMethod: PropTypes.func.isRequired,
  saving: PropTypes.bool.isRequired,
  timecardRequestChangeNote: PropTypes.string,
};

export default RequestChangesModal;
