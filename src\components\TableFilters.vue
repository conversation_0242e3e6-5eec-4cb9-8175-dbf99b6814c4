<template>
  <section aria-labelledby="filter-heading" class="w-full">
    <h2 id="filter-heading" class="sr-only">Filters</h2>
    <div class="border-b border-gray-200 dark:border-gray-500 pb-4">
      <div class="mx-auto flex max-w-7xl items-center px-4 sm:px-6 lg:px-8">
        <!-- BEGIN SORT MENU -->
        <Menu
          v-if="sortOptions?.length"
          as="div"
          class="relative inline-block text-left"
        >
          <div>
            <MenuButton
              class="group inline-flex justify-center text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100"
            >
              Sort
              <ChevronDownIcon
                class="-mr-1 ml-1 h-5 w-5 flex-shrink-0 text-gray-400 group-hover:text-gray-500"
                aria-hidden="true"
              />
            </MenuButton>
          </div>

          <transition
            enter-active-class="transition ease-out duration-100"
            enter-from-class="transform opacity-0 scale-95"
            enter-to-class="transform opacity-100 scale-100"
            leave-active-class="transition ease-in duration-75"
            leave-from-class="transform opacity-100 scale-100"
            leave-to-class="transform opacity-0 scale-95"
          >
            <MenuItems
              class="absolute left-0 z-10 mt-2 w-48 origin-top-left rounded-md bg-white dark:bg-gray-700 shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-none"
            >
              <div class="py-1">
                <MenuItem
                  v-for="(option, optionIndex) in sortOptions"
                  :key="`${option.id}-${optionIndex}`"
                  @click="setSort(option)"
                >
                  <a
                    class="flex align-center justify-between dark:hover:bg-gray-600 hover:bg-gray-100 cursor-pointer px-4 py-2 text-sm"
                    :class="{
                      'bg-gray-100 dark:bg-gray-600': !!option.sortDirection,
                    }"
                  >
                    {{ option.label }}
                    <BarsArrowUpIcon
                      v-if="option.sortDirection === SortDirection.Ascending"
                      class="ml-2 h-5 w-5 text-gray-400 dark:text-gray-300"
                      aria-hidden="true"
                    />
                    <BarsArrowDownIcon
                      v-if="option.sortDirection === SortDirection.Descending"
                      class="ml-2 h-5 w-5 text-gray-400 dark:text-gray-300"
                      aria-hidden="true"
                    />
                  </a>
                </MenuItem>
              </div>
            </MenuItems>
          </transition>
        </Menu>
        <!-- END SORT MENU -->
        <div class="flex grow" />

        <!-- BEGIN FILTER MENU -->

        <!-- Filter Modal (Mobile) -->
        <Modal v-model="isFilterModalOpen">
          <template #activator="{ open }">
            <Button size="sm" @click="open" class="sm:hidden"> Filter </Button>
          </template>
          <h2 class="text-xl font-bold pb-2">Filters</h2>
          <div
            v-for="(filter, filterIndex) in visibleFilters"
            :key="`${filter.label}-${filterIndex}`"
            class="flex align-middle py-2 px-4"
          >
            <FilterInput
              v-if="filter.active || filter.type === FilterUIType.MultiSelect"
              :model-value="filter"
              @update:model-value="updateFilter($event)"
            />
            <template v-else>
              <PlusCircleIcon
                v-if="isDropdownDisabled(filter)"
                class="cursor-pointer mr-1 mt-0.5 h-5 w-5 flex-shrink-0 text-gray-700 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300"
                @click="updateFilter({ ...filter, active: true })"
              />
              {{ filter.label }}
            </template>
          </div>
          <div class="flex justify-center space-x-2 pt-3">
            <Button @click="isFilterModalOpen = false">Close</Button>
          </div>
        </Modal>

        <!-- Filter Menu (Desktop) -->
        <div class="hidden sm:block">
          <div class="flow-root">
            <PopoverGroup
              class="-mx-4 flex items-center divide-x divide-gray-200 dark:divide-gray-800"
            >
              <Popover
                v-for="(filter, filterIndex) in visibleFilters"
                :key="`${filter.label}-${filterIndex}`"
                class="relative inline-block px-4 text-left"
              >
                <div class="flex">
                  <PopoverButton
                    :disabled="isDropdownDisabled(filter)"
                    :data-testid="`filter-popover-${filter?.label}`"
                    class="group inline-flex justify-center text-sm font-medium text-gray-700 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300"
                  >
                    <span>{{ filter.label }}</span>
                    <ChevronDownIcon
                      v-if="!isDropdownDisabled(filter)"
                      class="-mr-1 ml-1 h-5 w-5 flex-shrink-0 text-gray-700 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300"
                      aria-hidden="true"
                    />
                  </PopoverButton>
                  <PlusCircleIcon
                    v-if="isDropdownDisabled(filter)"
                    class="cursor-pointer ml-1 h-5 w-5 flex-shrink-0 text-gray-700 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300"
                    @click="updateFilter({ ...filter, active: true })"
                  />
                </div>

                <transition
                  enter-active-class="transition ease-out duration-100"
                  enter-from-class="transform opacity-0 scale-95"
                  enter-to-class="transform opacity-100 scale-100"
                  leave-active-class="transition ease-in duration-75"
                  leave-from-class="transform opacity-100 scale-100"
                  leave-to-class="transform opacity-0 scale-95"
                >
                  <PopoverPanel
                    class="absolute right-0 z-10 mt-2 origin-top-right rounded-md bg-white dark:bg-gray-800 p-4 shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-none"
                  >
                    <FilterInput
                      :model-value="filter"
                      @update:model-value="updateFilter($event)"
                      :data-testid="`filter-input-${filter?.label}`"
                    />
                  </PopoverPanel>
                </transition>
              </Popover>
            </PopoverGroup>
          </div>
        </div>
        <!-- END FILTER MENU -->
      </div>
    </div>

    <!-- Active filters -->
    <div
      class="mx-auto max-w-7xl px-4 py-3 sm:flex sm:items-center sm:px-6 lg:px-8"
    >
      <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">
        Filters
        <span class="sr-only">, active</span>
      </h3>

      <div
        aria-hidden="true"
        class="hidden h-5 w-px bg-gray-300 dark:bg-gray-500 sm:ml-4 sm:block"
      />

      <div class="mt-2 sm:ml-4 sm:mt-0">
        <div class="-m-1 flex flex-wrap items-center">
          <TableFiltersActiveFilterChip
            v-for="(activeFilter, activeFilterIndex) in activeFilters"
            :key="`${activeFilter.value?.toString()}-${activeFilterIndex}-${
              activeFilter.field
            }`"
            :filter="activeFilter"
            @remove="removeFilter(activeFilter)"
          />
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import FilterInput from '@/components/FilterInput.vue';
import TableFiltersActiveFilterChip from '@/components/TableFiltersActiveFilterChip.vue';
import Button from '@/components/library/Button.vue';
import Modal from '@/components/library/Modal.vue';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';
import { FilterUIType, type Filter } from '@/types/Filter';
import { SortDirection } from '@/utils/enum';
import {
  applyQueryStringToFilters,
  // applyQueryStringToSorts,
  convertFiltersToURLQuery,
} from '@/utils/filter';
import { applyQueryStringToSorts } from '@/utils/sort';
import { convertSortsToURLQuery } from '@/utils/sort';
import {
  Menu,
  MenuButton,
  MenuItem,
  MenuItems,
  Popover,
  PopoverButton,
  PopoverGroup,
  PopoverPanel,
} from '@headlessui/vue';
import {
  BarsArrowDownIcon,
  BarsArrowUpIcon,
  ChevronDownIcon,
  PlusCircleIcon,
} from '@heroicons/vue/24/outline';
import {
  computed,
  ref,
  watch,
  type ComputedRef,
  type PropType,
  inject,
} from 'vue';

const route = inject('route') as { value: ParsedRoute };
const navigate = inject('navigate') as Function;

// eslint-disable-next-line react-hooks/rules-of-hooks
const props = defineProps({
  filters: {
    type: Array as PropType<Filter[]>,
    required: true,
  },
});

const emit = defineEmits<{
  (e: 'update:filters', value: Filter[]): void;
}>();

const isDropdownDisabled = (filter: Filter): boolean => {
  return !filter.active && filter.type !== FilterUIType.MultiSelect;
};

const removeFilter = (filter: Filter): void => {
  filter.active = false;
  if (filter.type === FilterUIType.Text) {
    filter.value = '';
  } else {
    filter.value = null;
  }
  filter.options?.forEach((option) => (option.active = false));
  updateFilter(filter);
};

const updateFilter = (filter: Filter) => {
  const updatedFilters = props.filters.map((f) => {
    if (f.id === filter.id) {
      return filter;
    }
    return f;
  });
  emit('update:filters', updatedFilters);
};

const visibleFilters = computed(() => {
  return props.filters.filter(({ type }) => type !== FilterUIType.Hidden);
});

const filterString = computed(() => {
  return convertFiltersToURLQuery(props.filters);
});

watch(filterString, async () => {
  const routeQuery = route.value.query;
  const query = { ...routeQuery };
  query.filters = filterString.value;
  const searchParams = new URLSearchParams();

  Object.entries(query).forEach(([key, value]) => {
    searchParams.append(key, value);
  });

  await navigate({
    search: searchParams.toString(),
  });
});

const filterQuery = computed(() => {
  const filters = route.value.query?.filters;
  return filters;
  // return router.currentRoute.value.query?.filters
});

watch(
  filterQuery,
  async () => {
    if (!filterQuery.value) {
      return;
    }
    const newFilters = applyQueryStringToFilters(
      filterQuery.value as string,
      props.filters,
    );
    emit('update:filters', newFilters);
  },
  { immediate: true },
);

const isFilterModalOpen = ref(false);

const activeFilters: ComputedRef<Filter[]> = computed((): Filter[] =>
  props.filters.filter(({ active }) => active),
);

const sortOptions = computed(() => {
  return props.filters.filter(({ sortable }) => sortable);
});

const setSort = async (sortOption: Filter) => {
  sortOptions.value.forEach((so) => {
    if (sortOption.id !== so.id) {
      return;
    }
  });

  const updatedSort = sortOptions.value.find((so) => so.id === sortOption.id);
  if (!updatedSort) {
    return;
  }
  if (!updatedSort.sortDirection) {
    updatedSort.sortDirection = SortDirection.Ascending;
  } else if (updatedSort.sortDirection === SortDirection.Ascending) {
    updatedSort.sortDirection = SortDirection.Descending;
  } else if (updatedSort.sortDirection === SortDirection.Descending) {
    updatedSort.sortDirection = SortDirection.None;
  }

  updateFilter(updatedSort);
};

const sortString = computed(() => {
  return convertSortsToURLQuery(props.filters);
});

watch(sortString, async () => {
  const routeQuery = route.value.query;
  const query = { ...routeQuery };
  query.sorts = sortString.value;
  const searchParams = new URLSearchParams();

  Object.entries(query).forEach(([key, value]) => {
    searchParams.append(key, value);
  });

  await navigate({
    search: searchParams.toString(),
  });

  // const query = { ...router.currentRoute.value.query };
  // query.sorts = sortString.value;
});

const sortQuery = computed(() => {
  const sorts = route.value.query?.sorts;
  return sorts;
  // return router.currentRoute.value.query?.sorts
});

watch(
  sortQuery,
  async () => {
    if (!sortQuery.value) {
      return;
    }
    const newFilters = applyQueryStringToSorts(
      sortQuery.value as string,
      props.filters,
    );
    emit('update:filters', newFilters);
  },
  { immediate: true },
);
</script>
