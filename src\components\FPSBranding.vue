<template>
  <div class="w-24 cursor-pointer" @click="navigateTo({ pathname: '/' })">
    <Icon name="cnc-logo-white-sm" />
  </div>
</template>

<script lang="ts">
import { defineComponent, inject } from 'vue';

import Icon from '@/components/Icon.vue';

export default defineComponent({
  setup() {
    const navigate: Function | unknown = inject('navigate');
    const navigateTo = (to: string | object) => {
      typeof navigate === 'function' && navigate(to);
    };
    return {
      navigateTo: navigateTo,
    };
  },
  components: {
    Icon,
  },
});
</script>
