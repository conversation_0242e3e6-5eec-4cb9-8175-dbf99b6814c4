{"timecard": {"id": 415, "rate": "50.0000", "isOnCall": false, "isExempt": false, "isHalfDayAllowed": true, "isNdbAllowed": true, "hourlyRate": "50.0000", "unionId": 5009, "capsPayId": null, "payPremiumOvertime": false, "payrollOpsNote": null, "hourlyExempt": false, "workLocation": {"id": 48, "projectId": 31, "shootLocationId": 3, "createdAt": "2025-04-09T11:20:34.184-05:00", "updatedAt": "2025-04-09T11:20:34.184-05:00", "payrollProjectLocationId": 1639293, "zip": "90265"}, "union": {"id": 5009, "name": "700", "number": "700", "description": "700", "createdAt": "2025-02-20T14:56:06.410-05:00", "updatedAt": "2025-02-20T14:56:06.410-05:00", "castAndCrewId": 669}, "occupation": {"id": 5023, "name": "ACTOR", "key": "ACTOR", "createdAt": "2025-02-24T10:58:05.118-05:00", "updatedAt": "2025-04-22T18:51:06.276-05:00", "castAndCrewId": null, "payrollOccupationCode": "ACT"}, "hireLocation": null, "rateType": {"id": 1, "name": "Hourly", "key": "hourly", "description": "Hourly rate", "created_at": "2025-01-28T02:48:56.097-05:00", "updated_at": "2025-03-10T04:57:05.200-05:00", "crew_display_name": "Per Hour"}, "payPeriod": {"id": 13, "startsAt": "2025-04-20T00:00:00.000-05:00", "endsAt": "2025-04-26T00:00:00.000-05:00", "createdAt": "2025-02-10T00:39:13.746-05:00", "updatedAt": "2025-02-10T00:39:13.746-05:00"}, "timecardDays": [{"id": 2900, "timecardId": 415, "date": "2025-04-21T08:00:00.000Z", "startsAt": "2025-04-21T08:00:00.000Z", "endsAt": "2025-04-21T17:00:00.000Z", "zipCode": null, "rate": null, "comments": null, "createdAt": "2025-04-22T11:05:16.760-05:00", "updatedAt": "2025-04-22T11:05:16.760-05:00", "isActive": true, "isRentalDay": false, "lineNumber": "monday", "workStatusId": null, "mealPenalties": 0, "workZoneId": null, "hasNdb": false, "hoursWorked": "0.000", "projectLocationId": null, "occupationId": null, "guaranteedHours": null, "hasRerate": false, "hasWalkingMeal": false, "hasMealGraceOne": false, "hasMealGraceTwo": false, "hasWrapGrace": false, "hasHalfDay": false, "driveTime": null, "hotelToSetTime": null, "setToHotelTime": null, "hasHoliday": false, "occupation": null, "projectShootLocation": {"id": 48, "projectId": 31, "shootLocationId": 3, "createdAt": "2025-04-09T11:20:34.184-05:00", "updatedAt": "2025-04-09T11:20:34.184-05:00", "payrollProjectLocationId": 1639293, "zip": "90265", "shootLocation": {"id": 3, "createdAt": "2025-01-30T07:01:37.509-05:00", "updatedAt": "2025-01-30T07:01:37.509-05:00", "locationName": "California - Los Angeles County", "locationCode": "CA", "city": "SANTA MONICA", "state": "CA", "zipcode": "90265", "country": "US", "locationId": 69}}, "workStatus": {"id": 5002, "name": "WORK", "key": "WORK", "description": "WORK", "created_at": "2025-01-30T07:17:13.023-05:00", "updated_at": "2025-04-22T10:43:35.297-05:00", "caps_pay_id": "18"}, "meals": [{"id": 2957, "timecardDayId": 2900, "startsAt": "2025-04-21T13:00:00.000Z", "endsAt": "2025-04-21T14:00:00.000Z", "createdAt": "2025-04-22T11:05:16.773-05:00", "updatedAt": "2025-04-22T11:05:16.773-05:00"}], "workZone": {"id": 5007, "key": "Studio", "name": "Studio", "description": "Studio", "created_at": "2025-04-14T14:14:16.095-05:00", "updated_at": "2025-04-14T14:14:16.095-05:00", "caps_pay_id": "5"}}]}}