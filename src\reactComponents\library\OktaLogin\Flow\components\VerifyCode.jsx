import React from 'react';
import PropTypes from 'prop-types';

import {
  Button,
  Box,
  CircularProgress,
  Text,
  TextInput,
} from '@/reactComponents/library';
import { snackbarErr } from '@/reactComponents/library/Snackbar';

import {
  STEP_SUCCESS,
  getTransactionStep,
  authApiError,
  STEP_PHONE_CODE,
} from '../../utils';
import OktaLoginContext from '../../Context';
import BackToLogin from './BackToLogin';

const styles = {
  root: {
    display: 'flex',
    flexDirection: 'column',
    gap: 2,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',
  },
  codeInput: {
    '& input': {
      textAlign: 'center',
      '&:focus': {
        boxShadow: 'none',
      },
    },
    '& + .MuiFormHelperText-root': {
      textAlign: 'center',
    },
  },
};

const VerifyCode = ({ onSuccess, type }) => {
  const [loading, setLoading] = React.useState(false);
  const [code, setCode] = React.useState('');
  const { client: authClient, setTransaction } =
    React.useContext(OktaLoginContext);

  const verifyCode = async () => {
    try {
      setLoading(true);
      const parameters = { verificationCode: code };
      let response = await authClient.proceed(parameters);
      let nextStep = getTransactionStep(response);
      setTransaction(response);
      if (![STEP_SUCCESS, STEP_PHONE_CODE].includes(nextStep)) {
        const error = authApiError(response);
        throw new Error(error || `Invalid Response: ${nextStep}`);
      }

      onSuccess({ nextStep });
    } catch (e) {
      snackbarErr(e.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (code.length > 0) verifyCode();
  };

  return (
    <Box
      sx={styles.root}
      component="form"
      onSubmit={handleSubmit}
      data-testid="login-verifyCode-root"
    >
      {type === 'email' && (
        <Text variant="baseSemi">Seems like this is new device.</Text>
      )}
      <Text>
        Enter the code sent to your {type === 'sms' ? 'phone' : 'email'}
      </Text>
      <TextInput
        label="Enter code"
        value={code}
        onChange={(e) => setCode(e.target.value)}
        slotProps={{
          input: {
            sx: styles.codeInput,
          },
          htmlInput: {
            'data-testid': 'login-verifyCode-input',
          },
        }}
        autoFocus
      />
      <Button
        type="submit"
        disabled={loading}
        endIcon={loading ? <CircularProgress size={20} color="white" /> : null}
        data-testid="login-verifyCode-submit-btn"
      >
        Verify
      </Button>
      <BackToLogin label="Cancel" />
    </Box>
  );
};
VerifyCode.propTypes = {
  onSuccess: PropTypes.func.isRequired,
  type: PropTypes.oneOf(['sms', 'email']).isRequired,
};

export default VerifyCode;
