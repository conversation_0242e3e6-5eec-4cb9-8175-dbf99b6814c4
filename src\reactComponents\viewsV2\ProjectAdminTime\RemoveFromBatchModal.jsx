import React from 'react';
import PropTypes from 'prop-types';
import { removeBatchFromTimecard } from '@/services/timecards';

import {
  snackbarSuccess,
  snackbarAxiosErr,
} from '@/reactComponents/library/Snackbar';
import { Text, Modal } from '@/reactComponents/library';

const RemoveFromBatchModal = (props) => {
  const {
    open,
    setOpen,
    batch,
    fetchBatches,
    timecard,
    fetchTimecards,
    checkUnapprovedTimecards,
  } = props;

  const [removing, setRemoving] = React.useState(false);

  const onRemove = () => {
    setRemoving(true);
    removeBatchFromTimecard(timecard.id)
      .then(() => {
        fetchBatches();
        fetchTimecards().then(() => {
          setOpen(false);
          setRemoving(false);
          checkUnapprovedTimecards(batch?.id);
          snackbarSuccess('Timecard removed from batch');
        });
      })
      .catch((err) => {
        console.error('Error removing timecard from batch: ', err);
        setRemoving(false);
        snackbarAxiosErr(err, 'Error removing timecard from batch');
      });
  };

  return (
    <Modal
      title="Remove from batch"
      open={open}
      setOpen={setOpen}
      onSubmit={onRemove}
      loading={removing}
    >
      <Text variant="baseReg">{`Remove timecard from "${batch.name}"?`}</Text>
    </Modal>
  );
};

RemoveFromBatchModal.propTypes = {
  batch: PropTypes.object,
  open: PropTypes.bool.isRequired,
  project: PropTypes.object,
  fetchBatches: PropTypes.func.isRequired,
  fetchTimecards: PropTypes.func.isRequired,
  setOpen: PropTypes.func.isRequired,
  timecard: PropTypes.object.isRequired,
  checkUnapprovedTimecards: PropTypes.func.isRequired,
};

export default RemoveFromBatchModal;
