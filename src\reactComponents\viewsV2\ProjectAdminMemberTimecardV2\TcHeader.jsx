import React from 'react';
import PropTypes from 'prop-types';

import { Avatar, Box, IconButton } from '@/reactComponents/library';
import { useLocation, useParams, useNavigate } from 'react-router';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';

import { ProjectMemberLoanOutStatusId } from '@/utils/enum';

import TimecardsList from './TimecardsList';

import {
  makeFiltersId,
  updateSessionFilterBatch,
} from '@/reactComponents/viewsV2/ProjectAdminTime/utils';

const styles = {
  header: {
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    gap: 2,
  },
  avatar: {
    display: 'flex',
    alignItems: 'center',
  },
  text: {
    display: 'flex',
    flexDirection: 'column',
    grow: 1,
  },
  userName: { display: 'flex', fontSize: '30px', fontWeight: 'bold' },
  details: { display: 'flex', gap: 2 },
};

const Header = (props) => {
  const { member = {}, project = {}, timecard = {} } = props;
  const { user = {} } = member;

  const navigate = useNavigate();
  const { state } = useLocation();
  const { hashId } = useParams();

  let backUrl = `/projects/${hashId}/admin/time`;
  const { batchId } = timecard;
  const filtersId = makeFiltersId(hashId);

  if (state) {
    const { from } = state;
    switch (from) {
      case 'verify':
        backUrl = `/projects/${hashId}/admin/time`;
        break;
      case 'project-admin-timecards':
        backUrl = `/projects/${hashId}/admin/members/${member.id}/member/timecards`;
        break;
      default:
        break;
    }
  }

  return (
    <Box sx={styles.header}>
      <Box sx={styles.avatar}>
        <IconButton
          onClick={() => {
            updateSessionFilterBatch(filtersId, batchId);
            navigate(backUrl);
          }}
        >
          <ArrowBackIcon />
        </IconButton>
        <Avatar name={`${user.firstName} ${user.lastName}`} />
      </Box>
      <Box sx={styles.text}>
        <Box sx={styles.userName}>
          {user.firstName} {user.lastName}
        </Box>
        <Box sx={styles.details}>
          <Box>
            Employee Type:
            {member.loanOutStatusId === ProjectMemberLoanOutStatusId.Used
              ? ' Loanout'
              : ' W2'}
          </Box>
          <Box>Department: {member?.department?.type?.name}</Box>
          <Box>Project Role: {member.projectMemberType?.name}</Box>
        </Box>
      </Box>
      <Box sx={{ marginLeft: 'auto' }}>
        <TimecardsList member={member} project={project} timecard={timecard} />
      </Box>
    </Box>
  );
};

Header.propTypes = {
  member: PropTypes.object.isRequired,
  project: PropTypes.object.isRequired,
  timecard: PropTypes.object.isRequired,
};

export default Header;
