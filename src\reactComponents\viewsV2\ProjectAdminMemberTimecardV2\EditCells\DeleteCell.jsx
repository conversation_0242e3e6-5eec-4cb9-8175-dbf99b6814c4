import React from 'react';
import PropTypes from 'prop-types';

import { IconButton, Modal } from '@/reactComponents/library';
import DeleteIcon from '@mui/icons-material/Delete';
const DeleteCell = (props) => {
  const { deleteRow, disabled } = props;

  const [modalOpen, setModalOpen] = React.useState(false);

  return (
    <>
      <IconButton onClick={() => setModalOpen(true)} disabled={disabled}>
        <DeleteIcon />
      </IconButton>
      <Modal
        open={modalOpen}
        setOpen={setModalOpen}
        onCancel={() => setModalOpen(false)}
        onSubmit={deleteRow}
        submitText="Delete"
        title="Confirm Delete"
      >
        <div>Are you sure you want to delete this row?</div>
      </Modal>
    </>
  );
};

DeleteCell.propTypes = {
  deleteRow: PropTypes.func,
  disabled: PropTypes.bool,
};

export default DeleteCell;
