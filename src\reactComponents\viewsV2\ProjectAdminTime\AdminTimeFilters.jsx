import React from 'react';
import PropTypes from 'prop-types';
import { useSearchParams } from 'react-router';

import {
  Box,
  Button,
  styled,
  ButtonDropdown,
  Typography,
} from '@/reactComponents/library';
import { snackbarAxiosErr } from '@/reactComponents/library/Snackbar';
import _cloneDeep from 'lodash/cloneDeep';

import {
  getPayPeriods,
  getCurrentMember,
  listProjectMembers,
  listProjectMemberUnions,
} from '@/services/project';
import { useDidMount } from '@/reactComponents/utils/customHooks';
import { DateTime } from 'luxon';
import AdminTimeFiltersApplied from './AdminTimeFiltersApplied';

import {
  updateURLFilters,
  setFiltersFromURL,
  setInitDefaultFilters,
  BatchStatusFilterOptions,
  ErrorsOption,
} from './utils';

export const FiltersBox = styled(Box)((styleProps) => {
  const { theme, sx } = styleProps;
  const { spacing } = theme;
  return {
    ...sx,
    display: 'flex',
    width: '100%',
    alignItems: 'center',
    padding: spacing(2),
  };
});

const ErrorsFilterOptions = _cloneDeep(ErrorsOption);

const AdminTimeFilters = (props) => {
  const {
    filters,
    setFilters,
    project,
    timecardStatuses,
    setFiltersReady,
    filtersReady,
    batches,
    loadingBatches,
    clearFilters,
    filtersId,
    checkUnapprovedTimecards,
    batchFilters,
    setBatchFilters,
  } = props;

  const [unions, setUnions] = React.useState([]);
  const [projectMembers, setProjectMembers] = React.useState([]);
  const [payPeriods, setPayPeriods] = React.useState([]);
  const [memberDept, setMemberDept] = React.useState(null);

  const departments = React.useMemo(
    () =>
      project?.departments.map((d) => ({
        id: d.id,
        value: d.id,
        name: d.type.name,
        active: false,
      })) || [],
    [project],
  );

  const [memberReady, setMemberReady] = React.useState(false);
  const [unionReady, setUnionReady] = React.useState(false);
  const [payPeriodReady, setPayPeriodReady] = React.useState(false);
  const [memberDeptReady, setMemberDeptReady] = React.useState(false);

  const [filtersLoaded, setFiltersLoaded] = React.useState(false);

  const [searchParams, setSearchParams] = useSearchParams();

  useDidMount(() => {
    initialFetchMembers();
    listUnions();
    listProjectPayPeriods();
    fetchMemberDept();
  });

  /**
   * We need to wait until we have the batches to set the filters since we only
   * have the ID from the URL/cookie storage filters.
   *
   * This means the timecard fetch doesn't fire until after we initalize the filters, which
   * is after the batches are loaded.
   *
   * If we NEED to fetch the timecards faster, we can probably rework this since it just needs
   * ID's to be set in the filters.
   *
   */
  React.useEffect(() => {
    if (
      memberReady &&
      unionReady &&
      payPeriodReady &&
      memberDeptReady &&
      !loadingBatches
    ) {
      setTimeout(() => {
        setFiltersLoaded(true); //ensure previous state change propagates
      }, 20);
    }
  }, [
    memberReady,
    unionReady,
    payPeriodReady,
    setFiltersLoaded,
    loadingBatches,
    memberDeptReady,
  ]);

  const filtersInitializedRef = React.useRef(false);
  React.useEffect(() => {
    if (filtersLoaded && !filtersInitializedRef.current) {
      filtersInitializedRef.current = true;

      const tcFiltersQuery = searchParams.get('filters');
      const batchFiltersQuery = searchParams.get('batchFilters');

      if (tcFiltersQuery === null) {
        setInitDefaultFilters(
          memberDept,
          filtersId,
          setFilters,
          setBatchFilters,
        );
        setFiltersReady(true);
      } else {
        const options = {
          batch: batches,
          employee: projectMembers,
          department: departments,
          union: unions,
          payPeriod: payPeriods,
          status: timecardStatuses,
          errors: ErrorsFilterOptions,
        };
        setFiltersFromURL({
          setFilters: setFilters,
          setBatchFilters: setBatchFilters,
          batchFiltersQuery: batchFiltersQuery,
          filtersQuery: tcFiltersQuery,
          options: options,
        });
        setFiltersReady(true);
      }
    }
  }, [
    batches,
    departments,
    filtersId,
    filtersLoaded,
    memberDept,
    payPeriods,
    projectMembers,
    searchParams,
    setFilters,
    setBatchFilters,
    setFiltersReady,
    timecardStatuses,
    unions,
    checkUnapprovedTimecards,
  ]);

  //update url with current filters, after filters are loaded fully
  React.useEffect(() => {
    if (filtersReady) {
      updateURLFilters(filters, batchFilters, setSearchParams);
    }
  }, [filters, batchFilters, filtersReady, setSearchParams]);

  const listProjectPayPeriods = () => {
    getPayPeriods(project.id)
      .then(({ data: payPeriods }) => {
        const payPeriodOptions = payPeriods.map((p) => {
          const startsAt = DateTime.fromISO(p.startsAt);
          const endsAt = DateTime.fromISO(p.endsAt);
          const name =
            startsAt.toFormat('ccc MM/dd') +
            ' - ' +
            endsAt.toFormat('ccc MM/dd');
          return {
            name,
            id: p.id,
            value: p.id,
            active: false,
          };
        });

        setPayPeriods(payPeriodOptions);
      })
      .catch((err) => {
        snackbarAxiosErr(err, 'Failed to fetch pay periods');
      })
      .finally(() => setPayPeriodReady(true));
  };

  const listUnions = () => {
    listProjectMemberUnions(project.id)
      .then(({ data: unions }) => {
        const unionOptions = unions.map((u) => ({
          id: u.id,
          value: u.id,
          name: u.name,
          active: false,
        }));
        setUnions(unionOptions);
        setUnionReady(true);
      })
      .catch((err) => {
        snackbarAxiosErr(err, 'Failed to fetch pay periods');
      })
      .finally(() => setUnionReady(true));
  };

  const fetchMembers = (search, pagination = { limit: 50, page: 1 }) => {
    return listProjectMembers(
      project?.id,
      undefined, //typeKey
      pagination,
      '', //filterString
      undefined, //sorts
      search,
    )
      .then(({ data: res }) => {
        const { data } = res;
        const sortedMembers = data.sort((a, b) => {
          const aName = a.user.lastName;
          const bName = b.user.lastName;
          return aName.localeCompare(bName);
        });

        const memberOptions = sortedMembers.map((m) => ({
          id: m.userId,
          value: m.userId,
          name: m.user.firstName + ' ' + m.user.lastName,
          active: false,
        }));
        return memberOptions;
      })
      .catch((err) => {
        snackbarAxiosErr(err, 'Failed to fetch project members');
      });
  };

  const initialFetchMembers = async () => {
    const memberOptions = await fetchMembers();
    setProjectMembers(memberOptions);
    setMemberReady(true);
  };

  const fetchMemberDept = React.useCallback(async () => {
    try {
      const { data: member } = await getCurrentMember(project.id);

      const memberDept = member?.department;
      if (memberDept) {
        setMemberDept(memberDept);
      }
    } catch (err) {
      console.error(err);
      snackbarAxiosErr(err, 'Error fetching member info');
    } finally {
      setMemberDeptReady(true);
    }
  }, [project]);

  const onBatchFilterChange = (newValues, fieldName) => {
    newValues.forEach((v) => {
      v.active = true;
    });
    setBatchFilters((prev) => {
      const newBatchFilters = prev.map((f) => f);
      const filter = newBatchFilters.find((f) => f.field === fieldName);
      if (!filter) console.error('filter not found', fieldName);
      else filter.options = newValues;

      if (filter.options.some((o) => o.active)) {
        filter.active = true;
      } else {
        filter.active = false;
      }
      return newBatchFilters;
    });
  };

  const onFilterChange = React.useCallback(
    (newValues, filterName) => {
      newValues.forEach((v) => {
        v.active = true;
      });
      setFilters((prevFilters) => {
        const newFilters = prevFilters.map((f) => f);
        const filter = newFilters.find((f) => f.field === filterName);
        if (!filter) console.error('filter not found', filterName);
        else filter.options = newValues;

        if (filter.options.some((o) => o.active)) {
          filter.active = true;
        } else {
          filter.active = false;
        }

        return newFilters;
      });
    },
    [setFilters],
  );

  const timecardStatusesFilter = filters.find(
    (filter) => filter.field === 'statusId',
  );
  const timecardStatusesValues = timecardStatusesFilter?.options || [];

  const payPeriodsFilter = filters.find(
    (filter) => filter.field === 'payPeriodId',
  );
  const payPeriodsValues = payPeriodsFilter?.options || [];

  const unionsFilter = filters.find((filter) => filter.field === 'unionId');
  const unionsValues = unionsFilter?.options || [];

  const departmentsFilter = filters.find(
    (filter) => filter.field === 'projectMember.departmentId',
  );
  const departmentsValues = departmentsFilter?.options || [];

  const projectMembersFilter = filters.find(
    (filter) => filter.field === 'userCrewId',
  );
  const projectMembersValues = projectMembersFilter?.options || [];

  const batchStatusesValues =
    batchFilters.find((filter) => filter.field === 'statusId')?.options || [];

  const errorsFilterValues =
    filters.find((filter) => filter.field === 'errorMessages')?.options || [];

  const appliedFilters = React.useMemo(() => {
    const applied = filters.filter((f) => {
      return f.options?.length > 0 && f.field !== 'batchId';
    });
    const appliedBatchFilters = batchFilters.filter((f) => {
      return f.options?.length > 0;
    });
    return applied.concat(appliedBatchFilters);
  }, [filters, batchFilters]);

  const onSearchEmployee = (search, pagination) => {
    return fetchMembers(search, pagination);
  };

  return (
    <Box sx={{ alignSelf: 'baseline', width: '100%' }}>
      <FiltersBox sx={{ paddingTop: 0 }}>
        <Typography sx={{ mr: '16px', fontSize: '14px', fontWeight: 600 }}>
          Filters:
        </Typography>
        <Box sx={{ display: 'flex', gap: '1rem' }}>
          <ButtonDropdown
            label={'Employee'}
            field={'userCrewId'}
            options={projectMembers}
            values={projectMembersValues}
            onChange={onFilterChange}
            isSelectedValue={(selected, option) => {
              return selected?.id === option?.id;
            }}
            onSearch={onSearchEmployee}
            infiniteScrolling={true}
            pagination={{ limit: 50, page: 1 }}
          />
          <ButtonDropdown
            label={'Dept'}
            field={'projectMember.departmentId'}
            options={departments}
            values={departmentsValues}
            onChange={onFilterChange}
            isSelectedValue={(selected, option) => {
              return selected?.id === option?.id;
            }}
            useTextSearch={false}
          />
          <ButtonDropdown
            label={'Union'}
            field={'unionId'}
            options={unions}
            values={unionsValues}
            onChange={onFilterChange}
            isSelectedValue={(selected, option) => {
              return selected?.id === option?.id;
            }}
            useTextSearch={false}
          />
          <ButtonDropdown
            label={'Pay Period'}
            field={'payPeriodId'}
            options={payPeriods}
            values={payPeriodsValues}
            onChange={onFilterChange}
            isSelectedValue={(selected, option) => {
              return selected?.id === option?.id;
            }}
            useTextSearch={false}
          />
          <ButtonDropdown
            label={'Batch Status'}
            field={'statusId'}
            useTextSearch={false}
            options={BatchStatusFilterOptions}
            values={batchStatusesValues}
            onChange={onBatchFilterChange}
          />
          <ButtonDropdown
            label={'Timecard Status'}
            field={'statusId'}
            options={timecardStatuses}
            values={timecardStatusesValues}
            onChange={onFilterChange}
            isSelectedValue={(selected, option) => {
              return selected?.id === option?.id;
            }}
            useTextSearch={false}
          />
          <ButtonDropdown
            label={'Errors'}
            field={'errorMessages'}
            useTextSearch={false}
            options={ErrorsFilterOptions}
            values={errorsFilterValues}
            onChange={onFilterChange}
          />
          <Button onClick={clearFilters} size="small" variant="text">
            Clear All
          </Button>
        </Box>
      </FiltersBox>
      <AdminTimeFiltersApplied
        appliedFilters={appliedFilters}
        setFilters={setFilters}
        setBatchFilters={setBatchFilters}
      />
    </Box>
  );
};

AdminTimeFilters.propTypes = {
  filters: PropTypes.array.isRequired,
  project: PropTypes.object.isRequired,
  timecardStatuses: PropTypes.array.isRequired,
  setFilters: PropTypes.func.isRequired,
  selectedBatchId: PropTypes.number,
  setFiltersReady: PropTypes.func.isRequired,
  filtersReady: PropTypes.bool.isRequired,
  batches: PropTypes.array.isRequired,
  loadingBatches: PropTypes.bool.isRequired,
  clearFilters: PropTypes.func.isRequired,
  filtersId: PropTypes.string.isRequired,
  checkUnapprovedTimecards: PropTypes.func.isRequired,
  batchFilters: PropTypes.array.isRequired,
  setBatchFilters: PropTypes.func.isRequired,
};

export default AdminTimeFilters;
