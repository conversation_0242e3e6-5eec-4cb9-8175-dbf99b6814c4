import { fileURLToPath, URL } from 'node:url';

import vue from '@vitejs/plugin-vue';
import react from '@vitejs/plugin-react';
import svgLoader from 'vite-svg-loader';
import svgr from 'vite-plugin-svgr';

const viteConfig = (mode) => ({
  mode,
  plugins: [vue(), react(), svgLoader(), svgr()],
  base: '/v2/',
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  server: {
    host: '0.0.0.0',
    port: 5173,
    proxy: {
      '/v2/api/core': {
        target: 'http://127.0.0.1:3334',
        // target: 'https://fps-dev.nonprod.aws.castandcrew.com',
        // target: 'https://fps-stg.nonprod.aws.castandcrew.com',
        changeOrigin: true,
      },
    },
  },
  optimizeDeps: {
    include: [
      '@hotjar/browser',
      'react-dom/client',
      '@mui/x-license',
      'mobx-react-lite',
      '@mui/x-date-pickers/LocalizationProvider',
      '@mui/x-date-pickers/AdapterLuxon',
      '@fortawesome/fontawesome-svg-core',
      '@fortawesome/free-solid-svg-icons',
      '@fortawesome/free-regular-svg-icons',
      '@fortawesome/free-brands-svg-icons',
    ],
  },
});

export default viteConfig;
