<template>
  <header class="bg-gray-50 dark:bg-gray-700 py-6 fixed z-10 w-full shadow">
    <div
      class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 xl:flex xl:items-center xl:justify-between"
    >
      <div class="min-w-0 flex-1">
        <nav class="flex" aria-label="Breadcrumb">
          <ol role="list" class="flex items-center space-x-4">
            <li>
              <div>
                <a
                  class="text-sm cursor-pointer font-medium text-gray-500 hover:text-gray-700 dark:hover:text-gray-400"
                  @click="navigate('/users')"
                  >Users</a
                >
              </div>
            </li>
            <li>
              <div class="flex items-center">
                <ChevronRightIcon
                  class="h-5 w-5 flex-shrink-0 text-gray-400"
                  aria-hidden="true"
                />
                <a
                  class="ml-4 cursor-pointer text-sm font-medium text-gray-500 hover:text-gray-700 dark:hover:text-gray-400"
                  data-testid="userHeader-breadcrumb"
                >
                  {{ user?.firstName }} {{ user?.lastName }}
                </a>
              </div>
            </li>
          </ol>
        </nav>
        <h1
          class="mt-2 text-2xl font-bold leading-7 sm:truncate sm:text-3xl sm:tracking-tight"
          data-testid="userHeader-text"
        >
          {{ user?.firstName }} {{ user?.lastName }}
        </h1>
      </div>

      <div class="mt-5 flex space-x-2 xl:mt-0 xl:ml-4">
        <DropdownMenu
          :itemGroups="viewsActions"
          data-testid="userHeader-views-menu"
        >
          <template #buttonContent>
            <MenuButton
              class="inline-flex items-center gap-x-1.5 rounded-md bg-white dark:bg-gray-800 px-3 py-2 text-sm font-semibold shadow-sm ring-1 ring-inset ring-gray-300 hover:ring-gray-400 dark:hover:ring-gray-300 hover:bg-gray-50 hover:dark:bg-gray-700"
              data-testid="userHeader-views-btn"
            >
              Views
              <ChevronDownIcon
                class="-mr-1 h-5 w-5 text-gray-400"
                aria-hidden="true"
              />
            </MenuButton>
          </template>
        </DropdownMenu>

        <button
          type="button"
          @click="navigate(`/users/${user.id}/edit`)"
          class="inline-flex items-center gap-x-1.5 rounded-md bg-white dark:bg-gray-800 px-3 py-2 text-sm font-semibold shadow-sm ring-1 ring-inset ring-gray-300 hover:ring-gray-400 dark:hover:ring-gray-300 hover:bg-gray-50 hover:dark:bg-gray-700"
          data-testid="userHeader-edit-btn"
        >
          <PencilIcon
            class="-ml-0.5 h-5 w-5 text-gray-400"
            aria-hidden="true"
          />
          Edit
        </button>
        <button
          @click="isConfirmDeleteOpen = true"
          class="inline-flex items-center gap-x-1.5 rounded-md px-3 py-2 text-sm font-semibold shadow-sm ring-1 ring-inset ring-gray-300 dark:hover:ring-gray-300 hover:bg-gray-50 hover:dark:bg-gray-700 btn text-red-700 enabled:dark:text-red-400 disabled:dark:text-gray-500 bg-red-50 outline-red-700 dark:outline-gray-400 hover:outline-none enabled:hover:ring-2 hover:ring-offset-2 hover:ring-red-500 dark:focus:ring-gray-400 dark:bg-gray-700"
          data-testid="userHeader-delete-btn"
        >
          <TrashIcon class="-ml-0.5 h-5 w-5 text-red-400" aria-hidden="true" />
          Delete User
        </button>

        <Modal v-model="isConfirmDeleteOpen">
          <h2 class="font-semibold mb-2">
            Are you sure you want to delete this user?
          </h2>
          <div class="flex justify-center space-x-2 mt-6">
            <Button
              color="gray"
              @click="isConfirmDeleteOpen = false"
              data-testid="userHeader-cancel-btn"
            >
              Cancel
            </Button>
            <Button
              color="error"
              @click="deleteUser"
              data-testid="userHeader-confirmation-btn"
            >
              Delete
            </Button>
          </div>
        </Modal>
      </div>
    </div>
  </header>
</template>

<script lang="ts">
import DropdownMenu from '@/components/library/DropdownMenu.vue';
import Modal from '@/components/library/Modal.vue';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import type { MenuItemGroup } from '@/types/Menu';
import type User from '@/types/User';
import type { SnackType } from '@/types/Snackbar';

import { MenuButton } from '@headlessui/vue';
import { ChevronDownIcon, ChevronRightIcon } from '@heroicons/vue/20/solid';
import { PencilIcon, TrashIcon } from '@heroicons/vue/24/outline';
import axios from 'axios';
import { defineComponent } from 'vue';

import Button from '@/components/library/Button.vue';
import { deleteUser } from '@/services/users';
export default defineComponent({
  components: {
    DropdownMenu,
    MenuButton,
    ChevronDownIcon,
    ChevronRightIcon,
    PencilIcon,
    TrashIcon,
    Button,
    Modal,
  },
  props: {
    user: {
      type: Object as () => User,
      required: true,
    },
    navigate: {
      type: Function,
      required: true,
    },
  },
  setup() {
    return { SnackbarStore };
  },
  data() {
    return {
      isConfirmDeleteOpen: false,
    };
  },
  methods: {
    triggerSnackbar(msg: string, duration = 2500, type: SnackType = 'success') {
      this.SnackbarStore.triggerSnackbar(msg, duration, type);
    },
    async deleteUser() {
      try {
        await deleteUser(this.user.id as number);
        this.triggerSnackbar('Successfully deleted user', 2500, 'success');
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          this.triggerSnackbar(msg, 2500, 'error');
        } else {
          this.triggerSnackbar(err as string, 2500, 'error');
        }
      } finally {
        this.isConfirmDeleteOpen = false;
        this.navigate('/users');
      }
    },
  },
  computed: {
    viewsActions(): MenuItemGroup[] {
      const goToDashboard = (): void => {
        this.navigate(`/users/${this.user.id}`);
      };
      const groups: MenuItemGroup[] = [
        [
          {
            label: 'Dashboard',
            action: () => goToDashboard(),
          },
        ],
      ];
      return groups;
    },
  },
});
</script>
