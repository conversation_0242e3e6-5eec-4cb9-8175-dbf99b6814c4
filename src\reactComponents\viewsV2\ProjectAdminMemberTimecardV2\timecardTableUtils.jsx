import React, { useContext } from 'react';
import {
  Switch<PERSON>ell as Switch<PERSON>ellBase,
  Date<PERSON><PERSON> as DateCellBase,
  <PERSON>ridCommonCell as GridCommonCellBase,
  MenuCell as MenuCellBase,
  CheckboxCell as CheckboxCellBase,
  TooltipCell as Tooltip<PERSON>ellBase,
} from '../../library/DataGrid/GridCells';
import {
  CheckboxEditCell as CheckboxEditCellBase,
  InputEditCell as InputEditCellBase,
} from '../../library/DataGrid/GridEditCells';
import AutocompleteCellBase from '../../library/DataGrid/GridAutocompleteCell';
import TimepickerCellBase from '../../library/DataGrid/TimepickerCell';

import { TimecardContext, parseTime } from './timecardUtils';
import { observer } from 'mobx-react-lite';

import DefaultDaysStore from '@/reactComponents/viewsV2/ProjectAdminMemberTimecardV2/DefaultDays/store';
import ProjectStore from '@/reactComponents/stores/project';

import { get } from './timecardUtils';
import { WorkStatusKeys } from '../../../types/WorkStatus';
import PropTypes from 'prop-types';
import { DateTime } from 'luxon';

const FIXED_OPTIONS_AUTO_COMPLETE_FIELDS = {
  workStatus: 'workStatuses',
  workZone: 'workZones',
  projectShootLocation: 'workLocations',
};

export const WithContext = (Component, Context) => {
  function EnhancedComponent(props) {
    const context = useContext(Context);
    const contextProps = {
      update: context?.update,
      timecard: context?.timecard,
      addReRate: context?.addReRate,
      applyDayDefault: context?.applyDayDefault,
      removeReRate: context?.removeReRate,
      onOpenCopyToDays: context?.onOpenCopyToDays,
      readOnlyMode: context?.readOnlyMode,
    };
    // if (mapContextToProps && typeof mapContextToProps === 'function') {
    //   mapContextToProps(context, contextProps, props);
    // }
    const field = props.colDef?.field;
    if (FIXED_OPTIONS_AUTO_COMPLETE_FIELDS[field]) {
      contextProps.options = context[FIXED_OPTIONS_AUTO_COMPLETE_FIELDS[field]];
    }
    if (props.colDef?.useProjectTimeIncrement === true) {
      contextProps.timeMinuteIncrement = context?.timeMinuteIncrement;
    }
    return <Component {...props} {...contextProps} />;
  }
  EnhancedComponent.propTypes = {
    colDef: PropTypes.object,
  };
  return EnhancedComponent;
};

export const WithReRateContext = (Component, Context) => {
  function EnhancedComponent(props) {
    const context = useContext(Context);
    const contextProps = {
      isReRateCell: true,
      update: context?.updateReRate,
      timecard: context?.timecard,
      loadingOccupations: context?.loadingOccupations,
      fetchOccupations: context?.fetchOccupations,
      occupationsHasNextPage: context?.occupationsHasNextPage,
      // occupations: context?.occupations,
    };
    const field = props.colDef?.field;
    if (field === 'occupation') {
      contextProps.options = context?.occupations;
    }
    return <Component {...props} {...contextProps} />;
  }
  EnhancedComponent.propTypes = {
    colDef: PropTypes.object,
  };
  return EnhancedComponent;
};

const timecardContextWrapper = (Component) => {
  return WithContext(Component, TimecardContext);
};

const storeWrapper = (Component) => {
  const EnhancedComponent = (props) => {
    const storeProps = {
      update: DefaultDaysStore.update,
      timecard: DefaultDaysStore.timecard,
    };

    const field = props.colDef?.field;
    const fieldAlias = FIXED_OPTIONS_AUTO_COMPLETE_FIELDS[field];

    if (fieldAlias && ProjectStore[fieldAlias]) {
      storeProps.options = ProjectStore[fieldAlias];
    }
    if (fieldAlias && DefaultDaysStore[fieldAlias]) {
      storeProps.options = DefaultDaysStore[fieldAlias];
    }

    if (props.colDef?.useProjectTimeIncrement === true) {
      storeProps.timeMinuteIncrement =
        ProjectStore.project?.minuteIncrement?.key;
    }
    return <Component {...props} {...storeProps} />;
  };

  EnhancedComponent.propTypes = {
    colDef: PropTypes.object,
  };

  return observer(EnhancedComponent);
};

// trying to resolve:
// https://stackoverflow.com/questions/57397395/react-has-detected-a-change-in-the-order-of-hooks-but-hooks-seem-to-be-invoked

const SwitchCell = timecardContextWrapper(SwitchCellBase);
const MenuCell = timecardContextWrapper(MenuCellBase);

const ReRateAutoCompleteCell = WithReRateContext(
  AutocompleteCellBase,
  TimecardContext,
);
const ReRateInputEditCell = WithReRateContext(
  InputEditCellBase,
  TimecardContext,
);

export const hideTimeDetails = (row, colDef) => {
  const workStatus = row?.workStatus;
  const key = workStatus?.key;
  return (
    key &&
    (key === WorkStatusKeys.KEY_IDLE ||
      key === WorkStatusKeys.KEY_PAID_IDLE ||
      key === WorkStatusKeys.KEY_TRAVEL ||
      key === WorkStatusKeys.KEY_HNW ||
      key === WorkStatusKeys.KEY_DOWN ||
      key === WorkStatusKeys.KEY_CANCELPAY_NOREQ_NOPW ||
      key === WorkStatusKeys.KEY_CANCELPAY_REQ_WITHPW ||
      key === WorkStatusKeys.KEY_DGA_IDLE_PEN_ONLY ||
      key === WorkStatusKeys.KEY_HOL ||
      key === WorkStatusKeys.KEY_HOLNOPAY)
  );
};

export const hideGeneralCrewCall = (row, colDef) => {
  const hasNdb = row?.hasNdb;
  return hideTimeDetails(row) || !hasNdb;
};

export const TIMECARD_TABLE_FIELDS = {
  meal2Out: 'meals.1.startsAt',
  meal2In: 'meals.1.endsAt',
  walkingMeal: 'hasWalkingMeal',
  wrapGrace: 'hasWrapGrace',
  meal1Grace: 'hasMealGraceOne',
  meal2Grace: 'hasMealGraceTwo',
  halfDay: 'hasHalfDay',
  driveTime: 'driveTime',
  hotelToSetTime: 'hotelToSetTime',
  setToHotelTime: 'setToHotelTime',
};

// re-rate use custom row function to render cells, and it's always in edit mode so no need to renderEditCell
export const RERATE_COLUMNS = [
  {
    field: 'occupation',
    headerName: 'Occupation',
    width: 250,
    isRequired: true,
    type: 'autocomplete',
    infiniteScrolling: true,
    checkHasNextPage: (props) => props.occupationsHasNextPage,
    checkLoading: (props) => props.loadingOccupations,
    onSearch: (props) => props.fetchOccupations,
    // getOptions: (props) => props.occupations,
    renderCell: (params) => <ReRateAutoCompleteCell {...params} />,
    useDefaultKeyboardControl: true,
  },
  {
    field: 'rate',
    headerName: 'Rate ($)',
    width: 115,
    isRequired: true,
    cellType: 'number',
    decimals: 4,
    renderCell: (params) => <ReRateInputEditCell {...params} />,
    useDefaultKeyboardControl: true,
  },
  { field: 'rateType', headerName: 'Rate type', width: 80 },
  {
    field: 'guaranteedHours',
    headerName: 'Guaranteed hours',
    width: 150,
    isRequired: true,
    cellType: 'number',
    decimals: 2,
    renderCell: (params) => <ReRateInputEditCell {...params} />,
    useDefaultKeyboardControl: true,
  },
];

export const LEFT_COLUMNS = [
  {
    field: 'isActive',
    headerName: '',
    width: 70,
    renderCell: (params) => <SwitchCell {...params} />,
  },
];

// RIGHT_COLUMNS
export const Tree_Data_Grouping_Col_Def = {
  headerName: '',
  width: 60,
  renderCell: (params) => <MenuCell {...params} />,
};

/**
 * Apply wrapper function to provide required props to edit cells
 *
 * cellWrapFunc {function} - responsible for supplying values to cell including:
 *  timecard
 *  update
 *  options:
 *    workStatuses
 *    workLocations
 *    occupations
 *    workZones
 */
export const makeWorkDayColumns = (cellWrapFunc) => {
  const DateCell = cellWrapFunc(DateCellBase);
  const GridCommonCell = cellWrapFunc(GridCommonCellBase);
  const CheckboxCell = cellWrapFunc(CheckboxCellBase);
  const TooltipCell = cellWrapFunc(TooltipCellBase);
  const CheckboxEditCell = cellWrapFunc(CheckboxEditCellBase);
  const InputEditCell = cellWrapFunc(InputEditCellBase);
  const AutocompleteCell = cellWrapFunc(AutocompleteCellBase);
  const TimepickerCell = cellWrapFunc(TimepickerCellBase);

  const workDayColumns = [
    {
      field: 'date',
      headerName: 'Day',
      renderCell: (params) => <DateCell {...params} />,
      width: 75,
    },
    {
      field: 'hasHoliday',
      headerName: 'Holiday',
      width: 75,
      editable: true,
      renderCell: (params) => <CheckboxCell {...params} />,
      renderEditCell: (params) => <CheckboxEditCell {...params} />,
    },
    {
      field: 'lineNumber',
      headerName: 'Line #',
      width: 75,
      editable: true,
      renderCell: (params) => <GridCommonCell {...params} />,
      renderEditCell: (params) => <InputEditCell {...params} />,
    },
    {
      field: 'workStatus',
      headerName: 'Work Status',
      width: 150,
      valueFormatter: (value, ...others) => {
        return value?.name || '';
      },
      editable: true,
      // getOptionLabel: (option) => option.name,
      // valueOptions
      renderCell: (params) => <GridCommonCell {...params} />,
      renderEditCell: (params) => <AutocompleteCell {...params} />,
    },
    {
      field: 'workZone',
      headerName: 'Work Zone',
      width: 150,
      editable: true,
      valueFormatter: (value, ...others) => {
        return value?.name || '';
      },
      renderCell: (params) => <GridCommonCell {...params} />,
      renderEditCell: (params) => <AutocompleteCell {...params} />,
    },
    {
      field: 'projectShootLocation',
      headerName: 'Work Location',
      width: 230,
      valueFormatter: (value, ...others) => {
        if (!value?.shootLocation?.locationName) {
          return '';
        }
        const locationName = value.shootLocation.locationName;
        const zip = value.zip ? ` (${value.zip})` : '';
        return `${locationName}${zip}`;
      },
      editable: true,
      renderCell: (params) => <GridCommonCell {...params} />,
      renderEditCell: (params) => <AutocompleteCell {...params} />,
      getOptionLabel: (option) => {
        if (!option?.shootLocation?.locationName) {
          return '';
        }
        const locationName = option.shootLocation.locationName;
        const zip = option.zip ? ` (${option.zip})` : '';
        return `${locationName}${zip}`;
      },
    },
    {
      field: TIMECARD_TABLE_FIELDS.halfDay,
      headerName: 'Half Day',
      width: 85,
      editable: true,
      renderCell: (params) => <CheckboxCell {...params} />,
      renderEditCell: (params) => <CheckboxEditCell {...params} />,
    },
    {
      field: TIMECARD_TABLE_FIELDS.hotelToSetTime,
      headerName: 'Hotel to Set Time',
      width: 160,
      editable: true,
      cellType: 'number',
      decimals: 2,
      isHidden: hideTimeDetails,
      renderCell: (params) => <GridCommonCell {...params} />,
      renderEditCell: (params) => <InputEditCell {...params} />,
    },
    {
      field: 'hasNdb',
      headerName: 'NDB',
      width: 75,
      editable: true,
      isHidden: hideTimeDetails,
      renderCell: (params) => <CheckboxCell {...params} />,
      renderEditCell: (params) => <CheckboxEditCell {...params} />,
    },
    {
      field: 'generalCrewCall',
      headerName: 'General Crew Call',
      width: 155,
      valueFormatter: (value, ...others) => {
        return parseTime(value);
      },
      isHidden: hideGeneralCrewCall,
      renderCell: (params) => <GridCommonCell {...params} />,
      renderEditCell: (params) => <TimepickerCell {...params} />,
      useProjectTimeIncrement: true,
      showClearBtn: true,
      editable: true,
    },
    {
      field: 'startsAt',
      headerName: 'Time In',
      width: 120,
      valueFormatter: (value, ...others) => {
        return parseTime(value);
      },
      isHidden: hideTimeDetails,
      renderCell: (params) => <GridCommonCell {...params} />,
      renderEditCell: (params) => <TimepickerCell {...params} />,
      useProjectTimeIncrement: true,
      editable: true,
    },
    {
      field: 'hasMealGraceOne',
      headerName: 'Meal 1 Grace',
      width: 120,
      isHidden: hideTimeDetails,
      renderCell: (params) => <CheckboxCell {...params} />,
      renderEditCell: (params) => <CheckboxEditCell {...params} />,
      editable: true,
    },
    {
      field: 'meals.0.startsAt',
      inArray: true,
      headerName: 'Meal 1 Out',
      width: 160,
      isHidden: hideTimeDetails,
      valueFormatter: (value, row, colDef, api) => {
        const field = colDef.field;
        const inArray = colDef.inArray;
        if (inArray) {
          const v = get(row, field);
          return parseTime(v);
        }
        const time = parseTime(value);
        return time;
      },
      renderCell: (params) => <GridCommonCell {...params} />,
      renderEditCell: (params) => <TimepickerCell {...params} />,
      useProjectTimeIncrement: true,
      showClearBtn: true,
      editable: true,
    },
    {
      field: 'meals.0.endsAt',
      inArray: true,
      headerName: 'Meal 1 In',
      width: 160,
      isHidden: hideTimeDetails,
      valueFormatter: (value, row, colDef, api) => {
        const field = colDef.field;
        const inArray = colDef.inArray;
        if (inArray) {
          const v = get(row, field);
          return parseTime(v);
        }
        return parseTime(value);
      },
      renderCell: (params) => <GridCommonCell {...params} />,
      renderEditCell: (params) => <TimepickerCell {...params} />,
      useProjectTimeIncrement: true,
      showClearBtn: true,
      editable: true,
    },
    {
      field: 'hasMealGraceTwo',
      headerName: 'Meal 2 Grace',
      width: 120,
      isHidden: hideTimeDetails,
      renderCell: (params) => <CheckboxCell {...params} />,
      renderEditCell: (params) => <CheckboxEditCell {...params} />,
      editable: true,
    },
    {
      field: TIMECARD_TABLE_FIELDS.meal2Out,
      inArray: true,
      headerName: 'Meal 2 Out',
      width: 160,
      isHidden: hideTimeDetails,
      valueFormatter: (value, row, colDef) => {
        const field = colDef.field;
        const inArray = colDef.inArray;
        if (inArray) {
          const v = get(row, field);
          return parseTime(v);
        }

        return parseTime(value);
      },
      renderCell: (params) => <GridCommonCell {...params} />,
      renderEditCell: (params) => <TimepickerCell {...params} />,
      useProjectTimeIncrement: true,
      showClearBtn: true,
      editable: true,
    },
    {
      field: TIMECARD_TABLE_FIELDS.meal2In,
      inArray: true,
      headerName: 'Meal 2 In',
      width: 160,
      isHidden: hideTimeDetails,
      valueFormatter: (value, row, colDef, api) => {
        const field = colDef.field;
        const inArray = colDef.inArray;
        if (inArray) {
          const v = get(row, field);
          return parseTime(v);
        }
        return parseTime(value);
      },
      renderCell: (params) => <GridCommonCell {...params} />,
      renderEditCell: (params) => <TimepickerCell {...params} />,
      useProjectTimeIncrement: true,
      showClearBtn: true,
      editable: true,
    },
    {
      field: TIMECARD_TABLE_FIELDS.walkingMeal,
      headerName: 'Walking Meal',
      width: 130,
      isHidden: hideTimeDetails,
      renderCell: (params) => <CheckboxCell {...params} />,
      renderEditCell: (params) => <CheckboxEditCell {...params} />,
      editable: true,
    },
    {
      field: TIMECARD_TABLE_FIELDS.wrapGrace,
      headerName: 'Wrap Grace',
      width: 100,
      isHidden: hideTimeDetails,
      renderCell: (params) => <CheckboxCell {...params} />,
      renderEditCell: (params) => <CheckboxEditCell {...params} />,
      editable: true,
    },
    {
      field: 'endsAt',
      headerName: 'Time Out',
      width: 120,
      isHidden: hideTimeDetails,
      valueFormatter: (value, ...others) => {
        return parseTime(value);
      },
      renderCell: (params) => <GridCommonCell {...params} />,
      renderEditCell: (params) => <TimepickerCell {...params} />,
      useProjectTimeIncrement: true,
      editable: true,
    },
    {
      field: TIMECARD_TABLE_FIELDS.setToHotelTime,
      headerName: 'Set to Hotel Time',
      width: 160,
      cellType: 'number',
      decimals: 2,
      isHidden: hideTimeDetails,
      renderCell: (params) => <GridCommonCell {...params} />,
      renderEditCell: (params) => <InputEditCell {...params} />,
      editable: true,
    },
    {
      field: TIMECARD_TABLE_FIELDS.driveTime,
      headerName: 'Drive Time',
      width: 160,
      cellType: 'number',
      decimals: 2,
      isHidden: hideTimeDetails,
      renderCell: (params) => <GridCommonCell {...params} />,
      renderEditCell: (params) => <InputEditCell {...params} />,
      editable: true,
    },

    {
      field: 'comments',
      headerName: 'Crew Note',
      width: 160,
      renderCell: (params) => <TooltipCell {...params} />,
      renderEditCell: (params) => <TooltipCell {...params} editMode />,
      editable: true,
    },
  ];

  return workDayColumns;
};

export const timecardColumns = makeWorkDayColumns(timecardContextWrapper);

const defaultDaysExcludeList = ['lineNumber', 'startsAt', 'comments'];

const makeDefaultDayColumns = () => {
  let columns = makeWorkDayColumns(storeWrapper);
  columns = columns.filter(
    (col) => defaultDaysExcludeList.includes(col.field) === false,
  );
  const generalCrewCallCol = columns.find(
    (col) => col.field === 'generalCrewCall',
  );
  generalCrewCallCol.isHidden = undefined;

  const endsAtCol = columns.find((col) => col.field === 'endsAt');
  endsAtCol.showClearBtn = true;

  //ActionsCell in `DefaultDayTable`

  return columns;
};

export const defaultDaysColumns = makeDefaultDayColumns();

export const ADDITIONAL_FIELDS = [
  {
    label: 'Meal 2',
    tableFields: [
      TIMECARD_TABLE_FIELDS.meal2Out,
      TIMECARD_TABLE_FIELDS.meal2In,
    ],
    selected: false,
    defaultVal: null,
  },
  {
    label: 'Walking Meal',
    tableFields: [TIMECARD_TABLE_FIELDS.walkingMeal],
    selected: false,
    defaultVal: false,
  },
  {
    label: 'Grace',
    tableFields: [
      TIMECARD_TABLE_FIELDS.meal1Grace,
      TIMECARD_TABLE_FIELDS.meal2Grace,
      TIMECARD_TABLE_FIELDS.wrapGrace,
    ],
    selected: false,
    defaultVal: false,
  },
  {
    label: 'Half Day',
    tableFields: [TIMECARD_TABLE_FIELDS.halfDay],
    selected: false,
    defaultVal: false,
  },
  {
    label: 'Drive Time',
    tableFields: [TIMECARD_TABLE_FIELDS.driveTime],
    selected: false,
    defaultVal: null,
  },
  {
    label: 'Unpaid Travel Time',
    tableFields: [
      TIMECARD_TABLE_FIELDS.hotelToSetTime,
      TIMECARD_TABLE_FIELDS.setToHotelTime,
    ],
    selected: false,
    defaultVal: null,
  },
];

export const clearGeneralCrewCall = ({ api, day, field, value, rowId }) => {
  if (field === 'hasNdb') {
    day.generalCrewCall = null;
    api.setEditCellValue({
      id: rowId,
      field: 'generalCrewCall',
      value: null,
    });
    const otherField = 'generalCrewCall';
    api.updateRows([
      {
        id: rowId,
        [otherField]: undefined,
      },
    ]);
  }
};

export const workStatusCascadingChanges = ({
  api,
  value,
  rowId,
  day,
  field,
  workZones,
  member,
}) => {
  if (field !== 'workStatus') return;

  if (value?.key === 'IDLE' && !day?.workZone) {
    const workZone = workZones.find((zone) => zone.key === 'Distant') || {
      caps_pay_id: 6,
      id: 2,
      key: 'Distant',
      name: 'Distant',
      description: 'Distant',
    };
    day.workZone = workZone;
    day.workZoneId = workZone.id;
    // todo: move all cell's setEditCellValue and updateRows to update function
    api.setEditCellValue({
      id: rowId,
      field: 'workZone',
      value: day.workZone,
    });
  }
  if (value?.key === 'SHOOT' && member.shootLineNumber) {
    day.lineNumber = member.shootLineNumber;
    api.setEditCellValue({
      id: rowId,
      field: 'lineNumber',
      value: day.lineNumber,
    });
  }
  if (value?.key !== 'SHOOT' && member.lineNumber) {
    day.lineNumber = member.lineNumber;
    api.setEditCellValue({
      id: rowId,
      field: 'lineNumber',
      value: day.lineNumber,
    });
  }
};

export const handleGridRowUpdate = ({ api, rowId, field, cellMode, value }) => {
  if (cellMode === 'view') {
    // need to swap this row in view mode otherwise the checkbox in view mode won't re-render properly
    api.updateRows([
      {
        id: rowId,
        [field]: value,
      },
    ]);
  }
};

export const handleActiveDayToggle = ({
  api,
  value,
  rowId,
  field,
  day,
  applyDayDefault,
  removeReRate,
}) => {
  if (field === 'isActive' && value === false) {
    removeReRate({
      parentRowId: rowId,
      api: api,
      day: day,
    });
  }
  if (field === 'isActive' && value === true) {
    applyDayDefault({
      parentRowId: rowId,
      api: api,
      day: day,
    });
  }
};

export const clearAdditionalFieldsForIdleStatus = ({
  api,
  day,
  field,
  rowId,
}) => {
  if (field === 'workStatus' && day && hideTimeDetails(day) === true) {
    const updateList = [
      { field: TIMECARD_TABLE_FIELDS.meal2Grace, value: false },
      { field: TIMECARD_TABLE_FIELDS.meal1Grace, value: false },
      { field: TIMECARD_TABLE_FIELDS.walkingMeal, value: false },
      { field: TIMECARD_TABLE_FIELDS.wrapGrace, value: false },
      { field: TIMECARD_TABLE_FIELDS.meal2In, value: null, inArray: true },
      { field: TIMECARD_TABLE_FIELDS.meal2Out, value: null, inArray: true },
      { field: TIMECARD_TABLE_FIELDS.driveTime, value: null },
      { field: TIMECARD_TABLE_FIELDS.hotelToSetTime, value: null },
      { field: TIMECARD_TABLE_FIELDS.setToHotelTime, value: null },
    ];

    updateList.forEach((item) => {
      if (!item.inArray) {
        day[item.field] = item.value;
      }
      api.setEditCellValue({
        id: rowId,
        field: item.field,
        value: item.value,
      });
    });

    if (day?.meals?.[1]) {
      day.meals.pop();
    }
  }
};

export const calculateReRateRows = ({ timecard, reRateRows }) => {
  const newReRateRows = timecard?.timecardDays
    .filter((day) => day.hasRerate)
    .map((day, index) => {
      const row = {
        rate: day.rate,
        rateType: timecard.rateType || day.rateType,
        occupation: day.occupation,
        guaranteedHours: day.guaranteedHours,
        id: crypto.randomUUID(),
        parentRowId: day.id,
      };
      return row;
    });
  return newReRateRows;
};
const META_TIME_PROPERTIES = ['date', 'createdAt', 'updatedAt'];

export const adjustYearMonthDay = (day) => {
  matchTimeFieldsToDate(day, day);

  const meals = day['meals'];
  meals.forEach((meal) => {
    matchTimeFieldsToDate(day, meal);
  });
};

const matchTimeFieldsToDate = (day, target) => {
  const date = day.date;
  for (let property in target) {
    const value = target[property];
    if (value instanceof DateTime) {
      const isActualTimeField =
        META_TIME_PROPERTIES.includes(property) === false;
      if (isActualTimeField) {
        target[property] = value.set({
          year: date.year,
          month: date.month,
          day: date.day,
        });
      }
    }
  }
};
