import { createMemoryRouter, RouterProvider, Outlet } from 'react-router-dom';
import UserDashboardView from '@/reactComponents/views/users/UserDashboardView';

describe('UserDashboardView Component', () => {
  const roles = [
    { id: 1, name: 'Admin', description: 'Admin' },
    { id: 2, name: 'User', description: 'User' },
    { id: 3, name: 'PayrollManager', description: 'PayrollManager' },
  ];

  let mountComponent;

  beforeEach(() => {
    mountComponent = (user) => {
      const mockContext = {
        user,
        roles,
      };

      const router = createMemoryRouter([
        {
          path: '/',
          element: <Outlet context={mockContext} />,
          children: [
            {
              path: '/',
              element: <UserDashboardView />,
            },
          ],
        },
      ]);

      cy.mount(<RouterProvider router={router} />);
    };
  });

  it('should render the UserDashboardView component correctly for Admin role', () => {
    const user = { email: '<EMAIL>', phone: '+573145675443', roleId: 1 };
    mountComponent(user);
    cy.contains('Information').should('be.visible');
    cy.contains('<EMAIL>').should('be.visible');
    cy.contains('+573145675443').should('be.visible');
    cy.contains('Admin').should('be.visible');
  });

  it('should render the UserDashboardView component correctly for User role', () => {
    const user = { email: '<EMAIL>', phone: '+573145675444', roleId: 2 };
    mountComponent(user);
    cy.contains('Information').should('be.visible');
    cy.contains('<EMAIL>').should('be.visible');
    cy.contains('+573145675444').should('be.visible');
    cy.contains('User').should('be.visible');
  });

  it('should render the UserDashboardView component correctly for PayrollManager role', () => {
    const user = {
      email: '<EMAIL>',
      phone: '+573145675445',
      roleId: 3,
    };
    mountComponent(user);
    cy.contains('Information').should('be.visible');
    cy.contains('<EMAIL>').should('be.visible');
    cy.contains('+573145675445').should('be.visible');
    cy.contains('PayrollManager').should('be.visible');
  });
});
