export function formatDecimal(value, range) {
  if (value === undefined || value === null) {
    return '';
  }

  let [min = 2, max] = range; // Default max to 4 if not provided

  if (max === undefined) {
    max = min;
  }

  let num = Number(value);
  if (isNaN(num)) {
    throw new Error('Invalid number input');
  }

  let output = num.toFixed(max);
  for (let i = max; i >= min; i--) {
    output = num.toFixed(i);
    if (output[output.length - 1] !== '0') {
      return output;
    }
  }
  return output;
}

// not doing any rounding, just fixing the decimal places
export function fixDecimalTo(input, decimals = 2) {
  const str = input.toString();
  const hasDecimal = str.includes('.');
  if (!hasDecimal) {
    return input;
  }
  const [whole, decimal] = str.split('.');
  const decimalLength = decimal.length;
  if (decimalLength <= decimals) {
    return input;
  }

  const decimalPart = decimal.slice(0, decimals);
  const fixed = `${whole}.${decimalPart}`;
  return fixed;
}
