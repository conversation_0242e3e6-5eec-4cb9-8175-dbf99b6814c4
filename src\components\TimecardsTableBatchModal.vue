<template>
  <Modal
    :model-value="modelValue"
    @update:modelValue="emit('update:modelValue', $event)"
  >
    <div v-if="!newBatchView">
      <div class="flex justify-between items-center">
        <div class="text-lg font-medium text-gray-900 dark:text-gray-200">
          Add to Batch
        </div>
        <XMarkIcon
          class="w-5 h-5 text-gray-400 hover:text-gray-200 cursor-pointer"
          @click="close"
        />
      </div>
      <div
        v-if="!loading"
        class="flex flex-wrap justify-start items-end gap-2 space-y-1 py-3"
      >
        <Badge
          v-for="(batch, batchIndex) in batches"
          :key="`batch-${batchIndex}`"
          class="cursor-pointer"
          type="success"
          :text="`${batch.capsPayId} - ${batch.name}`"
          @click="addToBatch(batch.id)"
        />
        <Badge
          class="cursor-pointer"
          @click="() => (newBatchView = true)"
          text="+ Add to New Batch"
        />
      </div>
      <div class="flex justify-center items-center space-x-2">
        <Spinner v-if="loading" class="w-5 h-5" />
      </div>
    </div>
    <div v-else>
      <div class="flex justify-between items-center">
        <div class="text-lg font-medium text-gray-900 dark:text-gray-200">
          Create Batch
        </div>
        <XMarkIcon
          class="w-5 h-5 text-gray-400 hover:text-gray-200 cursor-pointer"
          @click="close"
        />
      </div>
      <TextInput class="mt-2" v-model="newBatchName" />
      <div class="flex justify-center space-x-2">
        <Button @click="createAndAddBatch()" color="primary" :loading="loading">
          Create and Add
        </Button>
      </div>
    </div>
  </Modal>
</template>

<script setup lang="ts">
import Badge from '@/components/library/Badge.vue';
import Button from '@/components/library/Button.vue';
import Modal from '@/components/library/Modal.vue';
import Spinner from '@/components/library/Spinner.vue';
import TextInput from '@/components/library/TextInput.vue';
import { createBatch } from '@/services/project';
import { updateBatchOnTimecard } from '@/services/timecards';
// import { useSnackbarStore } from '@/stores/snackbar';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import type Batch from '@/types/Batch';
import { XMarkIcon } from '@heroicons/vue/24/outline';
import axios from 'axios';
import { ref, type Ref } from 'vue';

const props = defineProps<{
  modelValue: boolean;
  batches: Batch[];
  currentTimecardId: number | null;
  projectId: number;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
  (e: 'refresh:timecards'): void;
  (e: 'refresh:batches'): void;
}>();

const newBatchView = ref(false);
const newBatchName: Ref<string> = ref('');
const loading: Ref<boolean> = ref(false);

const close = () => {
  newBatchView.value = false;
  newBatchName.value = '';
  emit('update:modelValue', false);
};

const addToBatch = async (batchId: number) => {
  if (loading.value) return;
  loading.value = true;
  try {
    await updateBatchOnTimecard(props.currentTimecardId as number, batchId);
    emit('refresh:timecards');
    SnackbarStore.triggerSnackbar(
      'Successfully added to batch.',
      2500,
      'success',
    );
    close();
  } catch (err) {
    if (axios.isAxiosError(err)) {
      const msg = err.response?.data?.['errors']?.[0]?.message;
      SnackbarStore.triggerSnackbar(msg, 2500, 'error');
    } else {
      SnackbarStore.triggerSnackbar('Failed adding to batch.', 2500, 'error');
    }
  }
  loading.value = false;
};

const createAndAddBatch = async () => {
  loading.value = true;
  try {
    const { data } = await createBatch(props.projectId, newBatchName.value);
    await updateBatchOnTimecard(props.currentTimecardId as number, data.id);
    emit('refresh:timecards');
    emit('refresh:batches');
    close();
    SnackbarStore.triggerSnackbar(
      'Successfully created batch and added timecard.',
      2500,
      'success',
    );
  } catch (err) {
    if (axios.isAxiosError(err)) {
      const msg = err.response?.data?.message
        ? err.response.data.message
        : err.response?.data?.['errors']?.[0]?.message;
      SnackbarStore.triggerSnackbar(msg, 2500, 'error');
    } else {
      SnackbarStore.triggerSnackbar('Failed adding to batch.', 2500, 'error');
    }
  }
  loading.value = false;
};
</script>
