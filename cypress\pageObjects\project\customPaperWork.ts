/// <reference types="cypress" />

export default class CustomPaperWork {
  createPaperWork(): void {
    cy.get('[data-testid="project-paperwork-add-btn"]')
      .should('be.visible')
      .click();
  }

  uploadPaperWork(): void {
    cy.get('input[name="file_upload"]', { timeout: 2000 })
      .should('exist')
      .should('have.attr', 'type', 'file')
      .attachFile('customPaperwork.pdf', { force: true });

    cy.contains('File successfully uploaded.').should('exist');
  }

  fillPaperworkInformation(
    name?: string,
    option?: string,
    unionSetting?: string,
    loanOutSetting?: string,
  ): void {
    if (name) {
      cy.get('[data-testid="custom-paperwork-name-input"] input').type(name);
    }
    if (option) {
      cy.get('[data-testid="work-location-setting-dropdown"] button').click();
      cy.get('[role="menuitem"]').contains(option).click();
    }
    if (unionSetting) {
      cy.get('[data-testid="union-setting-dropdown"] button').click();
      cy.get('[role="menuitem"]').contains(unionSetting).click();
    }
    if (loanOutSetting) {
      cy.get('[data-testid="loan-out-setting-dropdown"] button').click();
      cy.get('[role="menuitem"]').contains(loanOutSetting).click();
    }
  }

  editPaperworkInformation(
    name?: string,
    option?: string,
    unionSetting?: string,
    loanOutSetting?: string,
  ): void {
    cy.wait(5000);
    if (name) {
      cy.get('[data-testid="custom-paperwork-name-input"] input').clear();
      cy.get('[data-testid="custom-paperwork-name-input"] input').type(name);
    }
    if (option) {
      cy.get('[data-testid="work-location-setting-dropdown"] button').click();
      cy.get('[role="menuitem"]').contains(option).click();
    }
    if (unionSetting) {
      cy.get('[data-testid="union-setting-dropdown"] button').click();
      cy.get('[role="menuitem"]').contains(unionSetting).click();
    }
    if (loanOutSetting) {
      cy.get('[data-testid="loan-out-setting-dropdown"] button').click();
      cy.get('[role="menuitem"]').contains(loanOutSetting).click();
    }
  }

  fillPaperworkSchema(): void {
    cy.get('[data-testid="custom-paperwork-countersign-schema-input"]').type(
      '{"schemas":[{}]}',
      { parseSpecialCharSequences: false },
    );
  }
  savePaperworkSchema(): void {
    cy.contains('button', 'Save').click();
  }

  editPaperWork(documentName: string): void {
    cy.get(`[data-testid="custom-doc-edit-${documentName}"]`)
      .scrollIntoView()
      .should('be.visible')
      .click();
  }
  deletePaperWork(documentName: string): void {
    cy.get(`[data-testid="custom-doc-delete-${documentName}"]`)
      .scrollIntoView()
      .should('be.visible')
      .click();
  }

  documentTemplatePage(documentName: string) {
    cy.contains(
      'Custom documents Activate or inactivate custom documents, while also editing assignment rules.',
    ).should('be.visible');
    cy.get(`[data-testid="custom-doc-${documentName}"]`).should('exist');
  }
}
