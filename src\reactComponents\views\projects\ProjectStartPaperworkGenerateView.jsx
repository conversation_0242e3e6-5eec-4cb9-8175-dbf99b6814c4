import { applyPureVueInReact } from 'veaury';
import ProjectStartPaperworkGenerateViewVue from '../../../views/projects/ProjectStartPaperworkGenerateView.vue';
import { useAuth } from '../../AppHooks';
import { useOutletContext, useNavigate } from 'react-router';
const ReactProjectStartPaperworkGenerateView = applyPureVueInReact(
  ProjectStartPaperworkGenerateViewVue,
);

const ProjectStartPaperworkGenerateView = () => {
  useAuth();
  const navigate = useNavigate();
  const context = useOutletContext();
  return (
    <ReactProjectStartPaperworkGenerateView
      navigate={navigate}
      project={context.project}
    />
  );
};

export default ProjectStartPaperworkGenerateView;
