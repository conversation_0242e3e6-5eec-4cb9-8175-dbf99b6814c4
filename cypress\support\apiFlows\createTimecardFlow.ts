import { createTimecard } from '../apiCreateHelpers';
import {
  fetchMembersByProject,
  fetchPayPeriodsByProject,
  fetchProjectIdentifiersByName,
} from '../apiHelpers';
import { addTimecardLine } from '../apiPatchHelpers';

/**
 * Creates a timecard flow using the given user's first and last name.
 * It fetches the project, pay period, user, and then creates a timecard and line.
 */
export function createTimecardFlow(firstName: string, lastName: string) {
  // Step 1: Fetch project identifiers using the project name
  cy.then(() => {
    return fetchProjectIdentifiersByName(Cypress.env('projectName'));
  })
    .then((project) => {
      if (!project || !project.id) {
        throw new Error('Project not found or invalid Project ID');
      }
      Cypress.env('projectId', project.id);
      cy.log(`Project ID set: ${project.id}`);
      Cypress.env('projectHashId', project.hashId);

      // Step 2: Fetch pay periods associated with the project
      return fetchPayPeriodsByProject();
    })
    .then((payPeriodResponse) => {
      const payPeriod = payPeriodResponse.body[0];
      Cypress.env('payPeriodId', payPeriod.id);
      cy.log(`PayPeriod ID set: ${payPeriod.id}`);

      // Step 3: Fetch project members to find user by first and last name
      return fetchMembersByProject();
    })
    .then((userResponse) => {
      const userGroup = userResponse.body.data.find(
        (item: { user: { firstName: string; lastName: string } }) =>
          item.user.firstName === firstName && item.user.lastName === lastName,
      );
      if (userGroup) {
        const projectMemberId = userGroup.id;
        Cypress.env('projectMemberId', projectMemberId);
        cy.log(`projectMemberId ID set: ${projectMemberId}`);
      }
      // Step 4: Create the timecard
      return createTimecard();
    })
    .then((timecardResponse) => {
      const timecardId = timecardResponse.body.id;
      const timecardDayId: string = timecardResponse.body.timecardDays?.[1]?.id;
      Cypress.env('timecardId', timecardId);
      cy.log(`timecardId ID set: ${timecardId}`);
      Cypress.env('timecardDayId', timecardDayId);
      cy.log(`timecardDayId ID set: ${timecardDayId}`);

      // Step 5: Add a timecard line
      return addTimecardLine();
    })
    .then((timecardLineResponse) => {
      expect(timecardLineResponse.status).to.eq(200);
      cy.log('Timecard line creation successful');
    });
}
