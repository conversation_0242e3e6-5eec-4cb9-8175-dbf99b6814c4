<template>
  <div class="min-h-full pt-14">
    <CompanyHeader :company="company" />
    <RouterView class="pt-40" v-if="doneLoading" :company="company" />
  </div>
</template>

<script lang="ts" setup>
import { getProductionCompanyById } from '@/services/production-company';
import type Company from '@/types/Company';

import CompanyHeader from '@/components/CompanyHeader.vue';
import { onMounted, ref, type Ref } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();

const company: Ref<Company> = ref({} as Company);

const doneLoading: Ref<boolean> = ref(false);

onMounted(async () => {
  const { data: companyData } = await getProductionCompanyById(
    route.params.id as string,
  );
  company.value = companyData;
  doneLoading.value = true;
});
</script>
