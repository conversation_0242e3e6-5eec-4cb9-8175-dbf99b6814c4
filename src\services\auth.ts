import type Onboarding from '@/types/Onboarding';
import axios, { type AxiosResponse } from 'axios';
import { coreBaseUrl } from '../utils/constants';

export const passwordlessStart = async (
  phone: string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/auth/passwordless/start`;
  const response = await axios.post(url, { phone }, { withCredentials: true });
  return response;
};

export const passwordlessCode = async (
  phone: string,
  code: string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/auth/passwordless/code`;
  const response = await axios.post(
    url,
    { phone, code },
    { withCredentials: true },
  );
  return response;
};

export const register = async (
  onboarding: Partial<Onboarding>,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/users/register`;
  const response = await axios.post(url, onboarding, { withCredentials: true });
  return response;
};

export const isLoggedIn = async (): Promise<AxiosResponse<boolean>> => {
  const url = `${coreBaseUrl()}/auth/logged-in`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const logout = async (): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/auth/logout`;
  const response = await axios.post(url, {}, { withCredentials: true });
  return response;
};
