import {
  Table,
  TableHead,
  TableRow,
  TableCell,
  styled,
  Box,
} from '@/reactComponents/library';

export const RootBox = styled(Box, { label: 'RootBox' })((props) => {
  const { theme } = props;
  const { palette, spacing } = theme;

  return {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    width: '100%',
    backgroundColor: palette.background.default,
    color: palette.text.primary,
    flexGrow: 1,
    padding: spacing(2),
  };
});

export const PageBox = styled(Box, { label: 'PageBox' })((props) => {
  const { theme } = props;
  const { spacing, palette, shape } = theme;
  return {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    borderRadius: shape.borderRadius,
    backgroundColor: palette.background.paper,
    gap: spacing(2),
    width: '100%',
    border: '1px solid',
    borderColor: palette.background.border,
    padding: spacing(2),
    flexGrow: 1,
    [theme.breakpoints.down('md')]: {
      paddingLeft: 0,
      paddingRight: 0,
    },
    maxWidth: '80rem',
  };
});

//Same as above but without width limit
export const FullWidthPageBox = styled(Box, { label: 'FullWidthPageBox' })(
  (props) => {
    const { theme } = props;
    const { spacing, palette, shape } = theme;

    return {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      borderRadius: shape.borderRadius,
      backgroundColor: palette.background.paper,
      gap: spacing(2),
      width: '100%',
      border: '1px solid',
      borderColor: palette.background.border,
      padding: spacing(2),
      flexGrow: 1,
      [theme.breakpoints.down('md')]: {
        paddingLeft: 0,
        paddingRight: 0,
      },
    };
  },
);
export const SubPageBox = styled(Box, { label: 'SubPageBox' })((props) => {
  const { theme } = props;
  const { spacing, palette, shape } = theme;
  return {
    width: '100%',
    padding: spacing(3),
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    backgroundColor: palette.background.default,
    borderRadius: shape.borderRadius,

    gap: spacing(2),
  };
});

export const GridTable = styled(Table, { label: 'GridTable' })((props) => {
  return {
    // width: 'inherit', //TODO - either uncomment this or remove the GridTable, ask UX/UI
  };
});

export const GridTableHead = styled(TableHead, { label: 'GridTableHead' })(
  (props) => {
    return {
      '& .MuiTableCell-root': {
        padding: '12px 24px',
        fontWeight: 'bold',
      },
    };
  },
);

export const GridTableRow = styled(TableRow, { label: 'GridTableRow' })(
  (props) => {
    const { theme } = props;
    const { palette, shape } = theme;
    // Reminder:
    //
    // Duplicate this logic in TimecardDetails since that is using a different styled component
    //
    // TODO - consolidate this and the TimecardDetails styled component

    return {
      '& .MuiInput-root': {
        borderRadius: shape.borderRadius,
        border: '1px solid',
        borderColor: 'transparent',
        padding: '0px 8px',
      },
      '& .MuiInput-root.Mui-error': {
        border: `2px solid ${palette.error.main}`,
      },
      '& .MuiAutocomplete-popupIndicator, & .MuiAutocomplete-clearIndicator': {
        display: 'none',
      },
      '&:hover': {
        '& .MuiInput-root, & .gridInput': {
          textDecoration: 'underline',
        },
      },
      '&:focus-within': {
        '& .MuiInput-root, & .gridInput': {
          borderColor: palette.background.border,
          textDecoration: 'none',
        },
        '& .MuiAutocomplete-popupIndicator, & .MuiAutocomplete-clearIndicator':
          {
            display: 'inline-flex',
          },
      },
    };
  },
);

export const GridTableCell = styled(TableCell, { label: 'GridTableCell' })(
  (props) => {
    return {
      '& .MuiTableCell-root': {
        padding: '12px 24px',
      },
    };
  },
);
