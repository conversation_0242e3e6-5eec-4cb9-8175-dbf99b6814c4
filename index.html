<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://use.fontawesome.com/releases/v6.7.2/css/all.css"
    />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- keep in sync with title in src\reactComponents\utils\tabTitle.ts -->
    <title>Cast & Crew</title>
  </head>
  <body>
    <div id="app"></div>
    <!-- <script type="module" src="/src/main.ts"></script> -->
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
