<template>
  <div class="w-full">
    <label
      v-if="label"
      class="block text-xs font-medium text-gray-700 dark:text-gray-400 mb-1"
    >
      {{ label }}
    </label>
    <div>
      <textarea
        ref="codeArea"
        :rows="rows"
        name="code"
        id="code"
        :value="modelValue"
        :disabled="disabled"
        @input="update"
        @keydown="handleTab"
        class="bg-white text-gray-800 dark:text-gray-200 code-area border-1 border-solid border-gray-300 dark:border-gray-500 shadow-sm dark:bg-gray-700 sm:text-sm w-full rounded-md"
        :class="{ 'bg-gray-100 dark:bg-gray-600 cursor-not-allowed': disabled }"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

export default defineComponent({
  emits: ['update:modelValue'],
  props: ['modelValue', 'label', 'type', 'errors', 'rows', 'disabled'],
  name: 'CodeTextArea',
  setup() {
    const codeArea = ref(null);
    return { codeArea };
  },
  methods: {
    update(event: any) {
      const value: string = event.target!.value!;
      this.$emit('update:modelValue', value);
    },
    handleTab(event: KeyboardEvent) {
      if (event.key === 'Tab') {
        event.preventDefault(); // Prevent the default action
        const target = event.target as HTMLTextAreaElement;
        const start = target.selectionStart;
        const end = target.selectionEnd;

        // Insert two spaces
        target.value =
          target.value.substring(0, start) + '  ' + target.value.substring(end);

        // Update cursor position
        target.selectionStart = target.selectionEnd = start + 2;

        // Emit the updated value
        this.$emit('update:modelValue', target.value);
      }
    },
  },
});
</script>

<style scoped>
.code-area {
  font-family: 'Courier New', Courier, monospace;
  padding: 10px;
  white-space: pre;
  overflow: auto;
  tab-size: 2;
}
</style>
