import { DateTime } from 'luxon';

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  adaptIsoStrings(input: any) {
    const iso8601 =
      /^(\d{4})-(\d{2})-(\d{2})(?:T(\d{2}):(\d{2}):(\d{2})(?:\.(\d{3}))?(Z|([+-])(\d{2}):(\d{2})))?$/;
    const adaptRecursive = (obj: any) => {
      Object.keys(obj).forEach((key) => {
        const dateTime: DateTime = DateTime.fromISO(obj[key]);
        if (obj[key] !== null && typeof obj[key] === 'object') {
          adaptRecursive(obj[key]);
        } else if (dateTime.isValid && iso8601.test(obj[key])) {
          obj[key] = DateTime.fromISO(obj[key]);
        }
      });
    };
    adaptRecursive(input);
  },
};

// regex to match ISO 8601 date strings
