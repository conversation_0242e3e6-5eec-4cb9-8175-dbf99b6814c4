import { useRef, useImperativeHandle, forwardRef } from 'react';
import { applyPureVueInReact } from 'veaury';
import ProjectTimecardCompleteViewVue from '../../../views/projects/ProjectTimecardCompleteView.vue';
import { useAuth, useReactRouter } from '../../AppHooks';
import { useOutletContext } from 'react-router';

const ReactProjectTimecardCompleteView = applyPureVueInReact(
  ProjectTimecardCompleteViewVue,
);

const ProjectTimecardCompleteView = forwardRef(() => {
  useAuth();
  const { navigate } = useReactRouter();
  const context = useOutletContext();
  const vueRef = useRef();

  useImperativeHandle(context.ref, () => ({
    vueRef: vueRef.current.__veauryVueRef__,
  }));

  return (
    <ReactProjectTimecardCompleteView
      ref={vueRef}
      project={context.project}
      timecard={context.timecard}
      navigate={navigate}
    />
  );
});

export default ProjectTimecardCompleteView;
