import { createMemoryRouter, RouterProvider } from 'react-router-dom';
import LandingReactView from '@/reactComponents/views/landing/LandingView';

describe('LandingView component testing', () => {
  beforeEach(() => {
    cy.intercept('GET', '/v2/api/core/users/me', {
      statusCode: 200,
      body: { success: true },
    }).as('getUser');

    const router = createMemoryRouter([
      {
        path: '/',
        element: <LandingReactView />,
      },
    ]);
    cy.mount(<RouterProvider router={router} />);
  });

  it('should present User Manual information option', () => {
    cy.contains('User Manual').should('be.visible').click();
    cy.contains(
      'Confused where to start? Click here to take a look at our user manual.',
    ).should('be.visible');
  });

  it('should present Profile information option', () => {
    cy.contains('Profile').should('be.visible');
    cy.contains('Manage your personal info.').should('be.visible');
    cy.contains('Profile').click();
  });

  it('should present Projects information option', () => {
    cy.contains('Projects').should('be.visible');
    cy.contains('Manage your personal info.').should('be.visible');
    cy.contains('Projects').click();
  });

  it('should present FAQ information option', () => {
    cy.contains('FAQ').should('be.visible');
    cy.contains('View frequently asked questions.').should('be.visible');
    cy.contains('FAQ').click();
  });
});
