import type { Filter } from '@/types/Filter';
import { SortDirection } from './enum';

export const convertSortsToQuery = (sorts: Filter[]): string => {
  if (!sorts || !sorts.length) {
    return '';
  }

  return sorts
    .filter(({ sortDirection }) => !!sortDirection)
    .map(({ field, sortDirection }) => `${field}=${sortDirection}`)
    .join(',');
};

export const convertSortsToURLQuery = (sorts: Filter[]): string => {
  if (!sorts || !sorts.length) {
    return '';
  }

  return sorts
    .filter(({ sortable }) => sortable)
    .filter(({ sortDirection }) => !!sortDirection)
    .map((filter) => convertSortToURLQueryItem(filter))
    .filter((queryItem) => queryItem)
    .join(',');
};

const convertSortToURLQueryItem = (filter: Filter): string => {
  const { id, sortDirection } = filter;

  // Handle the case where value is null or undefined
  if (!sortDirection) {
    return '';
  }

  // Serialize the value (which is now a string or number)
  return `${encodeURIComponent(id)}=${encodeURIComponent(sortDirection)}`;
};

export const applyQueryStringToSorts = (
  queryString: string,
  sorts: Filter[],
): Filter[] => {
  if (!sorts || !sorts.length) {
    return [];
  }

  if (!queryString) {
    return sorts;
  }

  const sortsCopy: Filter[] = JSON.parse(JSON.stringify(sorts));

  const queryParams = new URLSearchParams(queryString.replace(/;/g, '&'));
  const keys = [...queryParams.keys()];

  sortsCopy.forEach((sort) => {
    const isKeyPresent = keys.includes(sort.id);
    if (!isKeyPresent) {
      return;
    }

    const sortDirection = queryParams.get(sort.id) || '';
    if (
      sortDirection === SortDirection.Ascending ||
      sortDirection === SortDirection.Descending
    ) {
      sort.sortDirection = sortDirection;
    }
  });

  return sortsCopy;
};
