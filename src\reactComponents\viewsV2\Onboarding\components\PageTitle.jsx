import React from 'react';
import PropTypes from 'prop-types';

import { Text, styled, Box } from '@/reactComponents/library';

export const PageTitleBox = styled(Box, { label: 'TitleBox' })(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  padding: theme.spacing(3),
  backgroundColor: theme.palette.background.paper,
  width: '100%',
}));

const PageTitle = (props) => {
  const { topText, bottomText } = props;

  return (
    <PageTitleBox>
      <Text variant="disStrong">{topText}</Text>
      <Text>{bottomText}</Text>
    </PageTitleBox>
  );
};

PageTitle.propTypes = {
  topText: PropTypes.string.isRequired,
  bottomText: PropTypes.string,
};

export default PageTitle;
