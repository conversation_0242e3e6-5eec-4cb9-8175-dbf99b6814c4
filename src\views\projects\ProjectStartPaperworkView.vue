<template>
  <div>
    <div class="m-auto text-center py-8">
      <h1 class="text-xl font-bold mb-2">Starting Paperwork</h1>
      <div class="mb-8">
        Your start paperwork has been successfully submitted!
      </div>
      <div class="flex justify-center mb-12">
        <PDFViewer class="w-4/5" v-if="pdfUrl" :url="pdfUrl" />
      </div>
      <div
        class="flex justify-center w-100 py-3 fixed bottom-0 left-0 right-0 bg-gray-50 dark:bg-gray-700"
      >
        <Button
          v-if="!hasSupervisorSignedPaperwork"
          color="error"
          class="mr-2"
          @click="isConfirmDeleteOpen = true"
        >
          Delete
        </Button>
        <Button class="ml-2" @click="goToProjectHome"> Project Home </Button>

        <Modal v-model="isConfirmDeleteOpen">
          <h2 class="font-semibold mb-2">
            Are you sure you want to delete your start paperwork?
          </h2>
          <div class="flex justify-center space-x-2 mt-6">
            <Button color="gray" @click="isConfirmDeleteOpen = false">
              Cancel
            </Button>
            <Button class="" @click="deleteStartPaperwork"> Delete </Button>
          </div>
        </Modal>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Button from '@/components/library/Button.vue';
import Modal from '@/components/library/Modal.vue';
import PDFViewer from '@/components/library/PDFViewer.vue';
import {
  getStartingPaperwork,
  getStartingPaperworkPdf,
} from '@/services/project';
import { deleteStartPaperwork } from '@/services/start-paperworks';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import type Project from '@/types/Project';
import axios from 'axios';
import { defineComponent, type PropType } from 'vue';

export default defineComponent({
  props: {
    project: {
      type: Object as PropType<Project>,
      required: true,
    },
    navigate: {
      type: Function,
      required: true,
    },
  },
  components: {
    Button,
    PDFViewer,
    Modal,
  },
  data() {
    return {
      signature: null,
      pdfUrl: null as string | null,
      startPaperworks: [] as any[],
      isConfirmDeleteOpen: false,
    };
  },
  methods: {
    async deleteStartPaperwork(): Promise<void> {
      try {
        await deleteStartPaperwork(this.projectCrewId);
        this.goToProjectHome();
        SnackbarStore.triggerSnackbar(
          'Successfully deleted paperwork',
          2500,
          'success',
        );
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
      }
    },
    goToProjectHome(): void {
      this.navigate({ pathname: `/projects/${this.project.hashId}` });
    },
  },
  computed: {
    projectCrewId(): number {
      return this.startPaperworks[0]?.projectMemberId!;
    },
    hasSupervisorSignedPaperwork(): boolean {
      return this.startPaperworks.some(
        ({ supervisorSignedById }) => !!supervisorSignedById,
      );
    },
  },
  async mounted() {
    try {
      const { data: startPaperwork } = await getStartingPaperwork(
        this.project.id!,
      );
      if (!startPaperwork?.length) {
        return;
      }
      this.startPaperworks = startPaperwork;
      this.pdfUrl = getStartingPaperworkPdf(this.project.id!);
    } catch (err) {
      if (axios.isAxiosError(err)) {
        const msg = err.response?.data?.['errors']?.[0]?.message;
        SnackbarStore.triggerSnackbar(msg, 2500, 'error');
      } else {
        SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
      }
    }
  },
});
</script>
