/// <reference types="cypress" />

import { fetchCompanyByName } from '../../support/apiHelpers';
import 'cypress-file-upload';
import {
  interceptCreateTemplate,
  interceptDeleteTemplate,
} from '../../support/apiTimecardInterceptors';
import DocumentTemplates from '../../pageObjects/company/documentTemplates';
import { faker } from '@faker-js/faker';

const template = 'All';
const templateType = 'Union';
const templateLoan = 'Loan Out and Non Loan Out';

function getCompanyIdByName(name: string) {
  return fetchCompanyByName(name).then((company) => {
    const selectedCompany = company?.data?.[0];
    if (!company || !company.data[0].id) {
      throw new Error('company not found or invalid company ID');
    }
    Cypress.env('companyId', selectedCompany.id);
    cy.log(`company ID set: ${selectedCompany.id}`);
  });
}

function setupTemplateInterceptors() {
  interceptCreateTemplate();
  interceptDeleteTemplate();
}

describe('User Supervisor - Company custom documents delete', () => {
  const documentTemplates = new DocumentTemplates();
  const companyName = Cypress.env('companyName');

  beforeEach((): void => {
    // ----------- Arrange: Login as Supervisor -----------
    cy.log('Arrange: Login as Supervisor');
    cy.loginWithSession(
      Cypress.env('PHONE_NUMBER'),
      Cypress.env('OTC_CODE'),
      Cypress.env('BASE_URL'),
      'supervisor',
    );

    // ----------- Arrange: Get company ID by name -----------
    cy.log('Arrange: Get company ID by name');
    cy.then(() => getCompanyIdByName(companyName));
  });

  it('Verify Supervisor is able to delete Custom documents in Documents templates tab', () => {
    const filenameTemplate = `Template-${faker.word.sample()}`;

    // ----------- Arrange: Visit Document Templates tab -----------
    cy.log('Arrange: Visit Document Templates tab');
    cy.visit(
      `${Cypress.env('BASE_URL')}companies/${Cypress.env(
        'companyId',
      )}/document-templates`,
    );

    // ----------- Arrange: Intercept template creation and deletion API -----------
    cy.log('Arrange: Intercept template creation and deletion API');
    setupTemplateInterceptors();

    // ----------- Action: Create new paperwork template -----------
    cy.log('Action: Create new paperwork template');
    documentTemplates.createPaperWork();

    // ----------- Action: Upload paperwork file -----------
    cy.log('Action: Upload paperwork file');
    documentTemplates.uploadPaperWork();

    // ----------- Action: Fill out template schema -----------
    cy.log('Action: Fill out template schema');
    documentTemplates.fillTemplatesSchema();

    // ----------- Action: Fill out template information -----------
    cy.log('Action: Fill out template information');
    documentTemplates.fillTemplatesInformation(
      filenameTemplate,
      template,
      templateType,
      templateLoan,
    );

    // ----------- Action: Save template schema -----------
    cy.log('Action: Save template schema');
    documentTemplates.saveTemplatesSchema();

    // ----------- Assert: Validate template was created -----------
    cy.log('Assert: Validate template was created');
    cy.wait('@createdTemplate').then((interception): void => {
      expect(interception.response?.statusCode).to.equal(200);

      // ----------- Action: Delete the created template -----------
      cy.log('Action: Delete the created template');
      documentTemplates.deletePaperWork(filenameTemplate);

      // ----------- Assert: Validate template was deleted (API and UI) -----------
      cy.log('Assert: Validate template was deleted (API and UI)');
      cy.wait('@deletedTemplate').then((interception): void => {
        // API Assert
        expect(interception.response?.statusCode).to.equal(200);
        // UI Assert
        cy.get(`[data-testid="custom-doc-${filenameTemplate}"]`).should(
          'not.exist',
        );
      });
    });
  });
});
