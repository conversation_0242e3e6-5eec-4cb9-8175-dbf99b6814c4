<template>
  <section class="mt-5">
    <div class="border-t-2 pb-2"></div>
    <div class="flex justify-start items-center space-x-3">
      <h2 class="text-xl font-bold">Kit Rental</h2>
      <Button size="xs" color="secondary" @click="openUpdateKitRentalModal">
        <div class="flex items-center space-x-1 font-m stroke-gray-400">
          <Icon
            v-if="kitRental?.id"
            name="pencil"
            class="h-4 w-4 stroke-gray-500 dark:stroke-gray-400"
          />
          <Icon v-if="!kitRental?.id" name="plus" class="h-4 w-4" />
        </div>
      </Button>
      <Button
        v-if="timecard.kitRental"
        size="xs"
        color="error"
        @click="openDeleteKitRental"
      >
        <div class="flex items-center space-x-1 stroke-gray-400">
          <Icon name="trash" class="h-4 w-4" />
        </div>
      </Button>
      <Modal v-model="addKitRentalModal">
        <div class="p-4">
          <div class="flex justify-between items-center">
            <h2 class="text-lg font-semibold">Add Kit Rental</h2>
            <XMarkIcon
              class="h-5 w-5 cursor-pointer"
              @click="addKitRentalModal = false"
            />
          </div>
          <TextInput
            v-model="addkitRental.lineNumber"
            class="mt-2"
            label="Line Number"
            :disabled="!trackingHeaderDetails?.allowed"
          />
          <TextInput
            v-model="addkitRental.rentalRate"
            class="mt-2"
            label="Rental Rate"
            type="number"
          />
          <Dropdown
            v-model="rateType"
            :menu-items="rentalRateTypes"
            class="mt-2"
            label="Rental Rate Type"
            display-name="name"
          />
          <div v-if="rateType?.key === 'daily'" class="mb-5">
            <h3 class="font-semibold mb-1">Select Rental Days</h3>
            <div class="pb-3 flex justify-between items-baseline text-center">
              <div
                v-for="(timecardDay, timecardDayIndex) in timecard.timecardDays"
                :key="`time-card-day-${timecardDayIndex}`"
                class="flex flex-col px-1"
              >
                <div
                  v-if="timecardDay.isActive"
                  :class="{
                    'border-blue-500 bg-blue-900 text-white':
                      timecardDay.isRentalDay,
                    'border-gray-500 text-gray-500 dark:border-gray-300 dark:text-gray-300':
                      !timecardDay.isRentalDay,
                  }"
                  class="h-8 w-8 pt-1 text-sm rounded border cursor-pointer"
                  @click="selectRentalDay(timecardDay)"
                >
                  {{ timecardDay.date?.toFormat('ccc') }}
                </div>
                <div v-else class="h-8 w-8" />
              </div>
            </div>
          </div>

          <Dropdown
            v-if="project?.castAndCrewId"
            class="grow"
            v-model="addkitRental.workLocation"
            label="Work Location"
            display-name="shootLocation.locationName"
            :menu-items="project.projectShootLocations"
          >
            <template #label>
              {{
                `${addkitRental.workLocation?.shootLocation.locationName} (${addkitRental.workLocation?.zip})`
              }}
            </template>
            <template #item="{ value: projectShootLocation }">
              {{
                `${projectShootLocation?.shootLocation.locationName} (${projectShootLocation?.zip})`
              }}
            </template>
          </Dropdown>
          <FileUpload
            v-if="addkitRental.isUpload"
            v-model="addkitRental.documentId"
            class="my-1"
          />
          <div class="pt-2 flex justify-center items-center space-x-2">
            <Button size="sm" color="gray" @click="addKitRentalModal = false"
              >Cancel</Button
            >
            <Button size="sm" @click="saveKitRentalHandler" :loading="loading"
              >Save</Button
            >
          </div>
        </div>
      </Modal>
      <Modal v-model="editKitRentalModal">
        <div v-if="kitRentalWithEdits" class="p-4">
          <div class="flex justify-between items-center">
            <h2 class="text-lg font-semibold">Edit Kit Rental</h2>
            <XMarkIcon
              class="h-5 w-5 cursor-pointer"
              @click="editKitRentalModal = false"
            />
          </div>
          <TextInput
            v-model="kitRentalWithEdits.lineNumber"
            class="mt-2"
            label="Line Number"
            :disabled="!trackingHeaderDetails?.allowed"
          />
          <TextInput
            v-model="kitRentalWithEdits.rentalRate"
            class="mt-2"
            label="Rental Rate"
            type="number"
          />
          <Dropdown
            v-model="kitRentalWithEdits.rateType"
            :menu-items="rentalRateTypes"
            class="mt-2"
            label="Rental Rate Type"
            display-name="name"
          />
          <div v-if="kitRentalWithEdits.rateType?.key === 'daily'" class="mb-5">
            <h3 class="font-semibold mb-1">Select Rental Days</h3>
            <div
              v-if="timecardDaysWithEdits"
              class="pb-3 flex justify-between items-baseline text-center"
            >
              <div
                v-for="(timecardDay, timecardDayIndex) in timecardDaysWithEdits"
                :key="`time-card-day-${timecardDayIndex}`"
                class="flex flex-col px-1"
              >
                <div
                  v-if="timecardDay.isActive"
                  :class="{
                    'border-blue-500 bg-blue-900 text-white':
                      timecardDay.isRentalDay,
                    'border-gray-500 text-gray-500 dark:border-gray-300 dark:text-gray-300':
                      !timecardDay.isRentalDay,
                  }"
                  class="h-8 w-9 pt-1 rounded border cursor-pointer"
                  @click="selectRentalDay(timecardDay)"
                >
                  {{ timecardDay.date?.toFormat('ccc') }}
                </div>
                <div v-else class="h-8 w-8" />
              </div>
            </div>
          </div>

          <Dropdown
            v-if="project?.castAndCrewId"
            class="grow"
            v-model="kitRentalWithEdits.workLocation"
            label="Work Location"
            display-name="shootLocation.locationName"
            :menu-items="project.projectShootLocations"
          >
            <template #label>
              {{
                `${kitRentalWithEdits.workLocation?.shootLocation.locationName} (${kitRentalWithEdits.workLocation?.zip})`
              }}
            </template>
            <template #item="{ value: projectShootLocation }">
              {{
                `${projectShootLocation?.shootLocation.locationName} (${projectShootLocation?.zip})`
              }}
            </template>
          </Dropdown>
          <FileUpload
            v-if="kitRentalWithEdits.isUpload"
            v-model="kitRentalWithEdits.documentId"
            class="my-1"
          />
          <div class="pt-2 flex justify-center items-center space-x-2">
            <Button size="sm" color="gray" @click="editKitRentalModal = false"
              >Cancel</Button
            >
            <Button size="sm" @click="updateKitRentalHandler" :loading="loading"
              >Update</Button
            >
          </div>
        </div>
      </Modal>
      <Modal v-model="deleteKitRentalModal">
        <div class="flex justify-between items-center">
          <h2 class="text-xl font-bold pb-2">Delete Kit Rental</h2>
        </div>
        <div>Are you sure you want to delete this kit rental?</div>
        <div class="flex justify-center space-x-2 pt-3">
          <Button @click="deleteKitRentalModal = false" color="gray">
            Cancel
          </Button>
          <Button
            @click="deleteMileageFormHandler"
            :loading="loading"
            color="error"
          >
            Delete
          </Button>
        </div>
      </Modal>
    </div>
    <div v-if="timecard?.kitRental" class="mt-5">
      <div class="flex space-x-2">
        <div>
          <div class="text-xs font-bold">Line Number</div>
          <div class="">{{ timecard.kitRental?.lineNumber }}</div>
        </div>
        <div>
          <div class="text-xs font-bold">Rental Rate</div>
          <div class="">{{ timecard.kitRental?.rentalRate }}</div>
        </div>
        <div>
          <div class="text-xs font-bold">Rate Type</div>
          <div class="">{{ timecard.kitRental?.rateType?.name }}</div>
        </div>
        <div v-if="project.castAndCrewId">
          <div class="text-xs font-bold">Work Location</div>
          <div>
            {{
              `${timecard.kitRental.workLocation?.shootLocation.locationName} (${timecard.kitRental.workLocation?.zip})`
            }}
          </div>
        </div>
      </div>
      <div
        v-if="timecard.kitRental?.rateType?.key === 'daily'"
        class="flex justify-center mx-auto w-full py-2 px-5"
      >
        <div class="max-w-7xl">
          <div class="mb-5">
            <h3 class="font-semibold mb-1">Rental Days</h3>
            <div
              class="pb-3 flex space-x-2 justify-between items-baseline text-center"
            >
              <div
                v-for="(timecardDay, timecardDayIndex) in timecard.timecardDays"
                :key="`time-card-day-${timecardDayIndex}`"
                class="flex flex-col px-2"
              >
                <div
                  v-if="timecardDay?.isActive"
                  :class="{
                    'border-blue-500 bg-blue-900 text-white':
                      timecardDay.isRentalDay,
                  }"
                  class="h-8 w-9 pt-1 rounded border border-zinc-300 dark:border-zinc-600"
                >
                  {{ timecardDay.date.toFormat('ccc') }}
                </div>
                <div v-else class="h-8 w-8" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="timecard.kitRental.isUpload">
        Click
        <span class="underline cursor-pointer" @click="openKitRentalForm()"
          >here</span
        >
        to download the form.
      </div>
      <div v-else>
        <h3 class="font-semibold mb-1">Inventory Items</h3>
        <div
          v-for="(inventoryItem, inventoryItemIndex) in timecard.kitRental
            .inventoryItems"
          :key="`inventory-item-${inventoryItemIndex}`"
          class="flex space-x-3"
        >
          <div>
            <div class="text-xs font-bold">Item</div>
            <div class="">{{ inventoryItem.name }}</div>
          </div>
          <div>
            <div class="text-xs font-bold">Amount</div>
            <div class="text-right">{{ inventoryItem.amount }}</div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="text-l pt-3">No kit rental uploaded.</div>
  </section>
</template>

<script setup lang="ts">
import axios from 'axios';
import { computed, ref, type Ref } from 'vue';

import Button from '@/components/library/Button.vue';
import Dropdown from '@/components/library/Dropdown.vue';
import FileUpload from '@/components/library/FileUpload.vue';
import Modal from '@/components/library/Modal.vue';
import TextInput from '@/components/library/TextInput.vue';
import { deleteKitRental, updateKitRental } from '@/services/kit-rentals';
import { getKitRentalPdf, updateOrCreateKitRental } from '@/services/timecards';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import type { KitRental } from '@/types/KitRental';
import type Project from '@/types/Project';
import type ProjectShootLocation from '@/types/ProjectShootLocation';
import type Timecard from '@/types/Timecard';
import type TimecardDay from '@/types/TimecardDay';
import { XMarkIcon } from '@heroicons/vue/24/outline';

import Icon from '@/components/Icon.vue';

const props = defineProps<{
  timecard: Timecard;
  project: Project;
  trackingHeaderDetails: any;
}>();

const emits = defineEmits(['refresh']);

const snackbarStore = SnackbarStore;

const editKitRentalModal = ref(false);
const kitRentalWithEdits: Ref<KitRental | null> = ref<KitRental | null>(null);
const timecardDaysWithEdits: Ref<TimecardDay[]> = ref<TimecardDay[]>(
  [] as TimecardDay[],
);
const rentalRateTypes = ref([
  { id: 1, key: 'daily', name: 'Daily' },
  { id: 2, key: 'weekly', name: 'Weekly' },
]);

const addKitRentalModal = ref(false);
const deleteKitRentalModal = ref(false);
const addkitRental = ref({
  lineNumber: '',
  rentalRate: 0,
  timecardId: 0,
  documentId: undefined as number | undefined,
  date: null as string | null,
  inventoryItems: [
    {
      name: '',
      amount: 0,
    },
  ],
  workLocation: undefined as ProjectShootLocation | undefined,
  isUpload: true,
});
const rateType = ref({} as any);

const loading = ref(false);

const kitRental = computed(() => props.timecard.kitRental);

const openKitRentalForm = async () => {
  const link = document.createElement('a');
  link.href = getKitRentalPdf(props.timecard.id);
  link.setAttribute('download', 'kit_rental.pdf');
  document.body.appendChild(link);
  link.click();
};

const openUpdateKitRentalModal = () => {
  if (props.timecard.kitRental?.id) {
    kitRentalWithEdits.value = JSON.parse(
      JSON.stringify(props.timecard.kitRental),
    );
    timecardDaysWithEdits.value = [];
    props.timecard.timecardDays.forEach((timecardDay) => {
      timecardDaysWithEdits.value.push({
        ...timecardDay,
      });
    });
    editKitRentalModal.value = true;
  } else {
    addKitRentalModal.value = true;
  }
};

const openDeleteKitRental = () => {
  deleteKitRentalModal.value = true;
};

const selectRentalDay = (timecardDay: any) => {
  timecardDay.isRentalDay = !timecardDay.isRentalDay;
};

const saveKitRentalHandler = async () => {
  loading.value = true;
  if (addkitRental.value.isUpload) {
    addkitRental.value.inventoryItems = [];
  }
  const inventoryItems = JSON.parse(
    JSON.stringify(addkitRental.value.inventoryItems),
  );
  inventoryItems.forEach((inventoryItem: any) => {
    inventoryItem.amount = inventoryItem.amount * 100;
  });
  try {
    const lineNumberSpaceCheck = addkitRental.value?.lineNumber.trim();
    if (!lineNumberSpaceCheck && props.trackingHeaderDetails?.required) {
      throw new Error('Line number is required');
    }
    if (!addkitRental.value.documentId) {
      throw Error('Please upload a document.');
    }

    if (!rateType.value.id) {
      throw Error('Rental rate type is required.');
    }

    if (!addkitRental.value.workLocation) {
      throw Error('Work location is required.');
    }
    await updateOrCreateKitRental(props.timecard.id, {
      lineNumber: addkitRental.value.lineNumber,
      rentalRate: addkitRental.value.rentalRate
        ? Math.floor(addkitRental.value.rentalRate * 100)
        : '',
      timecardId: props.timecard.id,
      documentId: addkitRental.value.documentId,
      inventoryItems: inventoryItems,
      rateTypeId: rateType.value.id,
      isUpload: addkitRental.value.isUpload,
      rentalDays: props.timecard.timecardDays,
      workLocation: addkitRental.value.workLocation,
    });
    snackbarStore.triggerSnackbar('Kit rental saved.', 2500, 'success');
    emits('refresh');
    addKitRentalModal.value = false;
  } catch (err) {
    if (axios.isAxiosError(err)) {
      const msg = err.response?.data?.['errors']?.[0]?.message;
      snackbarStore.triggerSnackbar(msg, 2500, 'error');
    } else {
      snackbarStore.triggerSnackbar(err as string, 3500, 'error');
    }
  }
  loading.value = false;
};
const updateKitRentalHandler = async () => {
  loading.value = true;
  try {
    const lineNumberSpaceCheck = kitRentalWithEdits.value?.lineNumber?.trim();
    if (!lineNumberSpaceCheck && props.trackingHeaderDetails?.required) {
      throw new Error('Line number is required');
    }
    if (!kitRentalWithEdits.value?.documentId) {
      throw new Error('Document is required');
    }
    const payload: KitRental = JSON.parse(
      JSON.stringify(kitRentalWithEdits.value),
    );
    payload.rentalDays = timecardDaysWithEdits.value;
    payload.rentalRate = Math.floor(payload.rentalRate * 100);
    await updateKitRental(kitRentalWithEdits.value?.id!, payload);
    snackbarStore.triggerSnackbar(
      'Kit rental updated successfully',
      2500,
      'success',
    );
    emits('refresh');
    editKitRentalModal.value = false;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      snackbarStore.triggerSnackbar(
        error.response?.data.errors?.[0].message,
        2500,
        'error',
      );
    } else {
      snackbarStore.triggerSnackbar(
        (error as any)?.message || 'An error occurred',
        2500,
        'error',
      );
    }
  }
  loading.value = false;
};

const deleteMileageFormHandler = async () => {
  if (!kitRental.value.id) return;
  loading.value = true;
  try {
    await deleteKitRental(kitRental.value.id);
    snackbarStore.triggerSnackbar(
      'Kit Rental deleted successfully.',
      2500,
      'success',
    );
    emits('refresh');
    deleteKitRentalModal.value = false;
  } catch (err) {
    if (axios.isAxiosError(err)) {
      const msg = err.response?.data?.['errors']?.[0]?.message;
      snackbarStore.triggerSnackbar(msg, 2500, 'error');
    } else {
      snackbarStore.triggerSnackbar(err as string, 2500, 'error');
    }
  }
  loading.value = false;
};
</script>
