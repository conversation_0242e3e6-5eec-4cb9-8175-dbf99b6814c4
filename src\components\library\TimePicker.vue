<template>
  <div>
    <TextInput
      class="cursor-pointer"
      :label="label"
      @click="open"
      :model-value="formattedTime"
      :disabled="props.disabled"
    />
    <Modal v-model="isOpen">
      <div class="flex items-end justify-between mb-3">
        <div class="flex-1 text-5xl select-none">
          <span
            class="cursor-pointer transition-all duration-150 ease-in-out"
            :class="{
              'dark:text-gray-200 text-gray-700': settingHour,
              'dark:text-gray-400 text-gray-500 hover:dark:text-gray-200 hover:text-gray-700':
                !settingHour,
            }"
            @click="settingHour = true"
            >{{ hourDisplay }}</span
          >:<span
            class="cursor-pointer transition-all duration-150 ease-in-out"
            :class="{
              'dark:text-gray-200 text-gray-700': !settingHour,
              'dark:text-gray-400 text-gray-500 hover:dark:text-gray-200 hover:text-gray-700':
                settingHour,
            }"
            @click="settingHour = false"
            >{{ minuteDisplay }}</span
          >
          <span class="text-3xl ml-1 dark:text-gray-400 text-gray-500">{{
            `${isPm ? 'PM' : 'AM'}`
          }}</span>
        </div>
        <div class="flex justify-end select-none text-lg">
          <span
            class="mr-1 cursor-pointer transition-all duration-150 ease-in-out"
            :class="{
              'font-bold': !isPm,
              'dark:text-gray-400 text-gray-500 hover:dark:text-gray-300 hover:text-gray-600':
                isPm,
            }"
            @click="isPm = false"
            >AM</span
          >
          <span
            class="ml-1 cursor-pointer transition-all duration-150 ease-in-out"
            :class="{
              'font-bold': isPm,
              'dark:text-gray-400 text-gray-500 hover:dark:text-gray-300 hover:text-gray-600':
                !isPm,
            }"
            @click="isPm = true"
            >PM</span
          >
        </div>
      </div>
      <div class="flex justify-center py-2 group">
        <div
          class="relative flex items-center justify-end w-72 h-72 overflow-hidden rounded-full"
        >
          <!-- MINUTE HAND -->
          <div
            v-if="!settingHour"
            class="absolute w-1/2 h-1 origin-left"
            :style="`transform: rotate(${getMinuteRotation(minute)}deg)`"
          >
            <div class="w-3/4 h-full rounded-full dark:bg-white bg-gray-800" />
          </div>

          <!-- HOUR HAND -->
          <div
            v-if="settingHour"
            class="absolute w-1/2 h-1 origin-left background-red-400"
            :style="`transform: rotate(${getHourRotation(hour)}deg)`"
          >
            <div class="w-3/5 h-full rounded-full dark:bg-white bg-gray-800" />
          </div>

          <template v-if="settingHour">
            <div
              v-for="(h, hIndex) in hours"
              :key="`hour-${hIndex}`"
              class="absolute w-1/2 h-8 origin-left z-20 flex justify-end align-middle"
              :style="`transform: rotate(${getHourRotation(h)}deg)`"
            >
              <div
                class="w-8 h-8 text-xl text-center cursor-pointer select-none"
                :class="`${hour % 12 === h ? 'font-bold' : 'font-light'}`"
                :style="`transform: rotate(${360 - getHourRotation(h)}deg)`"
                @click="setHour(h)"
              >
                {{ h }}
              </div>
            </div>
          </template>
          <template v-else>
            <div
              v-for="(m, mIndex) in minutes"
              :key="`minute-${mIndex}`"
              class="absolute w-1/2 h-8 origin-left z-20 flex justify-end align-middle"
              :style="`transform: rotate(${getMinuteRotation(m)}deg)`"
            >
              <div
                class="w-8 h-8 text-xl text-center cursor-pointer select-none"
                :class="`${minute === m ? 'font-bold' : 'font-light'}`"
                :style="`transform: rotate(${360 - getMinuteRotation(m)}deg)`"
                @click="setMinute(m)"
              >
                {{ m }}
              </div>
            </div>
          </template>

          <div
            ref="clock"
            class="w-full h-full scale-75 rounded-full z-50"
            @mousedown="onMouseDown"
            @touchstart="onMouseDown"
            @mousemove="onDragMove"
            @touchmove="onDragMove"
            @mouseup="onMouseUp"
            @touchend="onMouseUp"
          />
        </div>
      </div>
      <div class="flex justify-center items-center space-x-2">
        <Button class="mt-3" color="gray" @click="close"> Cancel </Button>
        <Button class="mt-3" @click="set"> Select </Button>
      </div>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import Button from '@/components/library/Button.vue';
import Modal from '@/components/library/Modal.vue';
import { DateTime } from 'luxon';
import { computed, ref } from 'vue';
import TextInput from './TextInput.vue';

const props = withDefaults(
  defineProps<{
    label?: string;
    modelValue: DateTime;
    increment?: number;
    disabled?: boolean;
  }>(),
  {
    increment: 5,
  },
);

const emit = defineEmits<{
  (e: 'update:modelValue', value: DateTime): void;
}>();

const isOpen = ref(false);
const settingHour = ref(true); // else setting minute

const clock = ref(null);

const isDragging = ref(false);

const onMouseDown = (e: MouseEvent | TouchEvent) => {
  isDragging.value = true;
  onDragMove(e);
};

const onDragMove = (e: MouseEvent | TouchEvent) => {
  e.preventDefault();

  if (
    !isDragging.value ||
    (isDragging.value &&
      !['click', 'mousedown', 'mousemove', 'touchmove'].includes(e.type)) ||
    !clock.value
  )
    return;

  const clockEl = clock.value as HTMLElement;
  const { width, top, left } = clockEl.getBoundingClientRect();

  // Get the center coordinates of the clock element
  const centerX = left + width / 2;
  const centerY = top + width / 2; // Assuming height and width are the same

  // Get the mouse or touch coordinates
  const clientX = 'clientX' in e ? e.clientX : e.touches[0].pageX;
  const clientY = 'clientY' in e ? e.clientY : e.touches[0].pageY;

  // Calculate the angle in radians
  const angleInRadians = Math.atan2(clientY - centerY, clientX - centerX);

  // Convert the angle to degrees
  let angleInDegrees = (angleInRadians * 180) / Math.PI + 90;

  if (angleInDegrees < 0) {
    angleInDegrees += 360;
  }

  if (settingHour.value) {
    const nearestHour = Math.round(angleInDegrees / 30);
    hour.value = nearestHour;
  } else if (!settingHour.value) {
    const nearestSingleMinute = Math.round(angleInDegrees / 6);
    const nearestMinute = findClosestMinute(nearestSingleMinute, minutes.value);
    minute.value = nearestMinute;
  }
};

const findClosestMinute = (target: number, minuteArray: number[]): number => {
  let closest = minuteArray[0];
  let closestDiff = Math.abs(target - closest);

  for (const minute of minuteArray) {
    const diff = Math.abs(target - minute);
    if (diff < closestDiff) {
      closest = minute;
      closestDiff = diff;
    }
  }

  return closest;
};

const onMouseUp = () => {
  isDragging.value = false;
  if (settingHour.value) {
    setHour(hour.value);
  } else {
    setMinute(minute.value);
  }
};

const hours = ref([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]);
const minutes = ref([] as number[]);

for (let i = 0; i < 60; i += props.increment) {
  minutes.value.push(i);
}

const formattedTime = computed(() => {
  if (!props.modelValue) {
    return '';
  }
  if (props.modelValue instanceof DateTime) {
    return props.modelValue.toFormat('hh:mm a');
  }
  return DateTime.fromISO(props.modelValue).toFormat('hh:mm a');
});

const hourDisplay = computed((): string => {
  return `${hour.value % 12 === 0 ? 12 : hour.value % 12}`;
});

const minuteDisplay = computed((): string => {
  return minute.value.toString().padStart(2, '0');
});

const hour = ref(props.modelValue.hour || 3);
const isPm = ref(props.modelValue.hour >= 12);
const minute = ref(props.modelValue.minute || 0);

const open = () => {
  if (props.disabled) return;
  isOpen.value = true;
  settingHour.value = true;
  hour.value = props.modelValue.hour || 3;
  isPm.value = props.modelValue.hour >= 12;
  minute.value = props.modelValue.minute || 0;
};

const setHour = (h: any) => {
  hour.value = h;
  settingHour.value = false;
};

const setMinute = (m: any) => {
  minute.value = m;
};

const close = () => {
  isOpen.value = false;
};

const getMinuteRotation = (m: number) => {
  return m * 6 - 90;
};

const getHourRotation = (h: number) => {
  const hourImpact = h * 30;
  const rotation = hourImpact - 90;
  return rotation;
};

const set = () => {
  const normalizedHour = hour.value % 12;
  emit(
    'update:modelValue',
    props.modelValue.set({
      hour: isPm.value ? normalizedHour + 12 : normalizedHour,
      minute: minute.value,
    }),
  );
  close();
};
</script>
