{"version": "0.2.0", "configurations": [{"name": "Run", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "npm", "runtimeVersion": "20.18.1", "runtimeArgs": ["run", "dev"], "env": {"VITE_BASE_URL": ""}}, {"name": "Test (Unit)", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "npm", "runtimeVersion": "20.18.1", "runtimeArgs": ["run", "test:unit"], "env": {"VITE_BASE_URL": ""}}, {"name": "Type Check", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "npm", "runtimeVersion": "20.18.1", "runtimeArgs": ["run", "type-check"], "outputCapture": "std", "env": {"VITE_BASE_URL": "", "VITE_STRIPE_CLIENT_ID": "ca_HoCzj5QlxTLUECsSrW8gKmG66VNOVYiI", "VITE_NOVU_APP_ID": "CHqvCNd1y-TD"}}, {"type": "pwa-chrome", "request": "launch", "name": "Debugger", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/app"}]}