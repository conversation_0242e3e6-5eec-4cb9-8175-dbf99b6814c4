import { applyPureVueInReact } from 'veaury';
import CompanyMembersViewVue from '../../../views/companies/CompanyMembersView.vue';
import { useAuth } from '../../AppHooks';
import { useNavigate, useOutletContext } from 'react-router';
const ReactCompanyMembersView = applyPureVueInReact(CompanyMembersViewVue);

const CompanyMembersView = () => {
  useAuth();
  const navigate = useNavigate();
  const context = useOutletContext();
  return (
    <ReactCompanyMembersView company={context.company} navigate={navigate} />
  );
};

export default CompanyMembersView;
