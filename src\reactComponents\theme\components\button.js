const button = {
  defaultProps: {
    variant: 'primary',
  },
  styleOverrides: {
    // Name of the slot
    root: {
      // Some CSS
      // padding: '4px 8px',
      // fontSize: '0.8rem',
      // color: 'green',
      borderRadius: '8px',
      textTransform: 'none',
      minWidth: '40px',
      variants: [
        {
          props: { variant: 'primary' },
          style: (buttonProps) => {
            const { theme, size } = buttonProps;
            const { palette } = theme;
            const newStyles = {
              backgroundColor: palette.primary.main,
              color: palette.primary.contrastText,
              '&:hover': {
                backgroundColor: palette.primary[500],
              },
            };
            if (size === 'small') {
              newStyles.padding = '4px 8px';
              newStyles.fontSize = '0.8rem';
            }
            return newStyles;
          },
        },
        {
          props: { variant: 'primaryOutlined' },
          style: (buttonProps) => {
            const { theme, size } = buttonProps;
            const { palette } = theme;
            const newStyles = {
              backgroundColor: palette.background.paper,
              color: palette.text.primary,
              border: `1px solid ${palette.primary.main}`,
              '&:hover': {
                backgroundColor: palette.pink[200],
              },
            };
            if (size === 'small') {
              newStyles.padding = '4px 8px';
              newStyles.fontSize = '0.8rem';
            }
            return newStyles;
          },
        },
        {
          props: { variant: 'secondary' },
          style: (buttonProps) => {
            const { theme, size } = buttonProps;
            const { palette } = theme;

            const newStyles = {
              backgroundColor: palette.background.paper,
              color: palette.text.primary,
              border: `1px solid ${palette.gray[300]}`,
              '&:hover': {
                backgroundColor: palette.pink[200],
              },
            };

            if (size === 'small') {
              newStyles.padding = '4px 8px';
              newStyles.fontSize = '0.8rem';
            }

            return newStyles;
          },
        },
        {
          props: { variant: 'secondaryContained' },
          style: (buttonProps) => {
            const { theme, size } = buttonProps;
            const { palette } = theme;
            const newStyles = {
              backgroundColor: palette.gray[300],
              color: 'black',
              border: `1px solid ${palette.gray[300]}`,
              '&:hover': {
                backgroundColor: palette.gray[400],
                borderColor: `${palette.gray[400]}`,
              },
              '&.Mui-disabled': {
                backgroundColor: palette.gray[100],
                color: palette.gray[300],
                border: `1px solid ${palette.gray[100]}`,
              },
            };
            if (size === 'small') {
              newStyles.padding = '4px 8px';
              newStyles.fontSize = '0.8rem';
            }
            return newStyles;
          },
        },
      ],
    },
  },
};

export default button;
