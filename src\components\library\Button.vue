<template>
  <button
    type="submit"
    :disabled="disabled || loading"
    @click="handleClick"
    :class="[typeClasses[color as string] || typeClasses['secondary'], sizeClasses[size as string], disabled ? disabledClasses[color as string] : enabledClasses[color as string], className]"
  >
    <div v-if="!loading" class="flex items-center">
      <slot name="icon" />
      <div :class="{ 'ml-1': $slots.icon }">
        <slot />
      </div>
    </div>
    <div v-else class="flex items-center">
      <div
        class="inline-block h-6 w-6 animate-spin rounded-full border-2 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"
        role="status"
      >
        <span
          class="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]"
          >Loading...</span
        >
      </div>
    </div>
  </button>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  props: {
    className: {
      type: String,
      default: '',
    },
    label: {
      type: String,
      default: 'next',
    },
    color: {
      type: String,
      default: 'primary',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: 'md',
    },
    loading: {
      type: Boolean,
      default: false,
    },
    click: {
      type: Function,
      default: () => {},
    },
  },
  methods: {
    handleClick() {
      this?.click?.();
    },
  },
  data() {
    return {
      sizeClasses: {
        md: 'text-md px-[14px] py-[10px] h-10',
        sm: 'text-sm px-2 py-1 h-8',
        xs: 'text-xs px-1 py-1 h-6',
      } as { [key: string]: String },
      typeClasses: {
        primary:
          'btn text-white text-sm font-semibold bg-pink-600 border border-pink-700 rounded-lg hover:bg-pink-500 focus:ring-2 focus:ring-offset-2 focus:ring-pink-500',
        primaryOutlined:
          'btn text-pink-600 text-sm font-semibold bg-white border border-pink-600 rounded-lg hover:bg-pink-50 focus:ring-2 focus:ring-offset-2 focus:ring-pink-500',
        secondary:
          'btn text-gray-700 text-sm font-semibold border border-gray-300 rounded-lg hover:bg-pink-100 shadow-secondary-light dark:bg-gray-800 dark:border-gray-400 dark:text-white',
        secondaryOutlined:
          'btn text-gray-700 text-sm font-semibold rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-800 dark:text-white',
      } as { [key: string]: String },
      enabledClasses: {
        primary: '',
        error: '',
        gray: 'hover:bg-gray-50 dark:hover:bg-gray-700',
        none: '',
        main: '',
        pink: '',
        secondary: '',
      } as { [key: string]: String },
      disabledClasses: {
        primary: 'opacity-50',
        error: '',
        gray: 'opacity-50',
        none: '',
        main: 'opacity-50',
        pink: 'opacity-50',
        secondary: 'opacity-50',
      } as { [key: string]: String },
    };
  },
});
</script>

<style scoped>
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .btn {
    @apply flex justify-center border border-transparent rounded-md;
  }
}
</style>
