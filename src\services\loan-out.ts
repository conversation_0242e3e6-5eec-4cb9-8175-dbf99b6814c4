import axios, { type AxiosResponse } from 'axios';
import { coreBaseUrl } from '../utils/constants';

export const getLoanOut = async (): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/loan-outs`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const getLoanOutById = async (
  userId: number | string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/loan-outs/${userId}`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const createLoanOut = async (data: any): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/loan-outs`;
  const response = await axios.post(url, data, { withCredentials: true });
  return response;
};

export const updateLoanOut = async (data: any): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/loan-outs/${data.id}`;
  const response = await axios.patch(url, data, { withCredentials: true });
  return response;
};

export const getLoanOutArticleOfIncorporation = (
  loanOutId: string | number,
): string => {
  const url = `${coreBaseUrl()}/loan-outs/${loanOutId}/article-of-incorporation`;
  return url;
};

export const getLoanOutAgentAuthorization = (
  loanOutId: string | number,
): string => {
  const url = `${coreBaseUrl()}/loan-outs/${loanOutId}/agent-authorization`;
  return url;
};
