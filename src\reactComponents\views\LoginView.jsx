import React, { useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { AxiosError } from 'axios';

import { Box, Text, Loader } from '@/reactComponents/library';
import { snackbarErr } from '@/reactComponents/library/Snackbar';

import OktaLogin, {
  Context as OktaAuthContext,
} from '@/reactComponents/library/OktaLogin';

import AuthStore from '@/reactComponents/stores/auth';
import PermissionStore from '@/reactComponents/stores/permission';
import { getMe } from '@/services/users';
import { register } from '@/services/auth';

const styles = {
  root: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    width: 400,
    maxWidth: 400,
    minHeight: '100vh',
    overflow: 'hidden',
    mx: 'auto',
  },
  container: {
    width: '100%',
    margin: 'auto',
    backgroundColor: 'background.paper',
    p: '2.5rem',
    borderRadius: '0.5rem',
    boxShadow: '0 1px 2px 0 rgb(0 0 0 / .05)',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 2,
  },
  title: {
    lineHeight: '2.25rem',
    fontWeight: 800,
    fontSize: '1.875rem',
  },
};

const LoginView = () => {
  const {
    client: authClient,
    contextForm,
    login,
  } = React.useContext(OktaAuthContext);

  const [loggedIn, setLoggedIn] = useState(false);
  const [loading, setLoading] = useState(false);
  const [URLQuery] = useSearchParams();
  const navigate = useNavigate();
  const redirectUrl = URLQuery.get('redirect') || '/';

  const loadUser = async (authState, userData) => {
    const { accessToken } = authState.accessToken || {};
    if (!accessToken) return;

    const { data: me } = (await getMe(accessToken, userData)) || {};
    return me;
  };

  const submitRegistration = async (user, { encryptedPhone }) => {
    try {
      setLoading(true);
      const { data: newUser } = await register({
        ...user,
        encryptedPhone,
      });
      if (!newUser) {
        throw new Error('Registration failed');
      }

      AuthStore.login(newUser);
      navigate(redirectUrl);
    } catch (e) {
      if (e instanceof AxiosError) {
        const error = e.response?.data?.errors?.[0]?.message;
        snackbarErr(error || e.message);
      } else {
        snackbarErr(e.message);
      }
      await reload();
    } finally {
      setLoading(false);
    }
  };

  const reload = async () => {
    await authClient.logout();
    window.location.reload();
  };

  const mergeUserData = (newUserData) => {
    const userData = (key) =>
      newUserData[key] ||
      contextForm[key] ||
      newUserData.user?.[key] ||
      contextForm.user?.[key] ||
      userData[key];

    return {
      phone: userData('phone'),
      email: userData('email'),
      firstName: userData('firstName'),
      lastName: userData('lastName'),
      middleName: userData('middleName'),
      oktaId: userData('uid'),
    };
  };

  const handleSuccess = async (newUserData) => {
    try {
      setLoading(true);

      const mergedUserData = mergeUserData(newUserData);
      login(mergedUserData);

      const verified = await authClient.verifyTokens();
      if (!verified) {
        throw new Error('Tokens invalid. Restarting auth flow...');
      }

      const state = await authClient.getAuthState();
      if (!state?.isAuthenticated) {
        throw new Error('Auth state is invalid. Restarting auth...');
      }

      const me = await loadUser(state, mergedUserData);
      if (me) {
        if (me.newUser) {
          await submitRegistration(mergedUserData, me);
        } else {
          setLoggedIn(true);
          AuthStore.login(me);
          await PermissionStore.fetchPermissions();
          await PermissionStore.fetchAdmin();
          navigate(redirectUrl);
        }
      }
    } catch (err) {
      await authClient.logout();
      window.location.reload();
    } finally {
      setLoading(false);
    }
  };

  const handleError = async (errors) => {
    setLoading(false);
    await authClient.logout();
    errors.forEach(({ message } = {}) => message && snackbarErr(message));
  };

  return (
    <Box sx={styles.root}>
      <Box sx={styles.container}>
        {loggedIn && <Loader message="Logged in. Redirecting..." />}
        {!loggedIn && (
          <>
            <Text sx={styles.title}>Welcome to FPS</Text>
            {loading && <Loader />}
            {!loading && (
              <OktaLogin onSuccess={handleSuccess} onError={handleError} />
            )}
          </>
        )}
      </Box>
    </Box>
  );
};

export default LoginView;
