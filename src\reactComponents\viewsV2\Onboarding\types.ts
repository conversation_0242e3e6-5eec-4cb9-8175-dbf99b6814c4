import type { DateTime } from 'luxon';

export interface I9Data {
  citizenStatus: CitizenStatus | null;
  workAuthorizationType: I9DocWorkAuthType | null;
  workAuthorizationExpirationDate: string;
  workAuthorizationNumber: string;
  sec1Acknowledgement: boolean;
  proofOfIdentity: I9DocumentType | null;
  proofOfIdentityIssuingAuthority: string;
  proofOfIdentityDocumentNumber: string;
  proofOfIdentityExpirationDate: string;
  employmentAuthorizationType: I9DocWorkAuthType | null;
  employmentAuthorizationIssuingAuthority: string;
  employmentAuthorizationDocumentNumber: string;
  employmentAuthorizationExpirationDate: string;
}

export interface CitizenStatus {
  id: number;
  name: string;
  description: string;
  key: string;
  createdAt: DateTime;
  updatedAt: DateTime;
}

// adapted from src\navigation\StepperNav\StepperNavList\types.ts
export interface OnboardingStep {
  id: StepIds;
  label?: string;
  name?: string;
  indicator?: number | string | undefined;
  isCompleted?: boolean;
  isInProgress?: boolean;
  isDisabled?: boolean;
  isSelected?: boolean;
}

export enum StepIds {
  Personal = 'personal',
  Project = 'project',
  LoanOut = 'loan-out',
  I9 = 'i9',
  Review = 'review',
}

export interface I9DocumentType {
  id: number;
  name: string;
  provesEmploymentEligibility: boolean;
  expiryRequirement: I9ExpReq;
  key: string;
}

export interface I9DocWorkAuthType {
  id: number;
  name: string;
  key: string;
  expDate: I9ExpReq;
}

export enum I9ExpReq {
  Required = 'required',
  Optional = 'optional',
  NA = 'n/a',
}
