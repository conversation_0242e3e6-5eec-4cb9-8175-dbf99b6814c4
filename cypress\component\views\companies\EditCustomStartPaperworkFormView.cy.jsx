import { createMemoryRouter, RouterProvider, Outlet } from 'react-router-dom';
import { SnackbarProvider } from 'notistack';
import PropTypes from 'prop-types';

import CompanyCustomStartPaperworkFormView from '@/reactComponents/views/companies/CompanyCustomStartPaperworkFormView';
import 'cypress-file-upload';

const mockCompany = {
  id: 123,
  name: 'Test Company',
};

const LayoutWithOutletContext = ({ context }) => {
  return (
    <SnackbarProvider>
      <Outlet context={context} />
    </SnackbarProvider>
  );
};

LayoutWithOutletContext.propTypes = {
  context: PropTypes.object.isRequired,
};

// TestWrapper to simulate React Router context
const TestWrapper = () => {
  const router = createMemoryRouter(
    [
      {
        path: '/',
        handle: { editMode: true },
        element: (
          <LayoutWithOutletContext
            context={{ editMode: true, company: mockCompany }}
          />
        ),
        children: [
          {
            path: '/',
            element: <CompanyCustomStartPaperworkFormView />,
            handle: { editMode: true },
          },
        ],
      },
      {
        path: '/companies/123/document-templates',
        element: (
          <div data-testid="company-document-templates">
            company-document-templates
          </div>
        ),
      },
    ],
    { initialEntries: ['/'] },
  );

  return <RouterProvider router={router} />;
};

TestWrapper.propTypes = {
  company: PropTypes.object.isRequired,
};

describe('CompanyCustomStartPaperworkFormView component testing', () => {
  const nameInput = '[data-testid="Name-input"]';
  const schemaInput = '[data-testid="custom-paperwork-schema-input"]';
  const countersignSchemaInput =
    '[data-testid="custom-paperwork-countersign-schema-input"]';
  const workLocationDropdown =
    '[data-testid="custom-paperwork-work-location-dropdown"]';
  const unionSettingDropdown =
    '[data-testid="custom-paperwork-union-setting-dropdown"]';
  const loanOutDropdown = '[data-testid="custom-paperwork-loan-out-dropdown"]';
  const sendPayrollToggle =
    '[data-testid="custom-send-payroll-toggle"] [role="switch"]';
  const optionalDocumentToggle =
    '[data-testid="custom-optional-document-toggle"] [role="switch"]';
  const fileInput = 'label input[type="file"]';

  beforeEach(() => {
    cy.intercept(
      'GET',
      '**/v2/api/core/project-document-template-work-location-settings',
      {
        statusCode: 200,
        body: [
          {
            id: 1,
            name: 'All',
            key: 'all',
            description: 'Should be generated for all locations.',
          },
          {
            id: 2,
            name: 'Specific Work Locations',
            key: 'specific_work_locations',
            description:
              'Should be generated for selected locations Including or Not including.',
          },
        ],
      },
    ).as('workLocationSetting');

    cy.intercept('GET', '**/v2/api/core/project-document-template-types/', {
      statusCode: 200,
      body: {
        data: [
          {
            id: 1,
            key: 'non_union',
            name: 'Non Union',
            description: 'Only for non union project members',
          },
          {
            id: 2,
            key: 'union',
            name: 'Union',
            description: 'Only for union project members',
          },
          {
            id: 3,
            key: 'all',
            name: 'Non-Union and Union',
            description: 'For all project members',
          },
          {
            id: 4,
            key: 'specific_unions',
            name: 'Specific Union(s)',
            description:
              'For when there is a specific set of unions that apply to this document template',
          },
        ],
      },
    }).as('documentTemplatesTypes');

    cy.intercept(
      'GET',
      '**/v2/api/core/project-document-template-loan-out-settings',
      {
        statusCode: 200,
        body: [
          {
            id: 1,
            name: 'Loan Out and Non Loan Out',
            key: 'loan_out_and_non_loan_out',
            description:
              'Should be generated regardless of if the user is using a loan out or not.',
          },
          {
            id: 2,
            name: 'Loan Out',
            key: 'loan_out',
            description:
              'Should only be generated if the user is using a loan out.',
          },
          {
            id: 3,
            name: 'Non Loan Out',
            key: 'non_loan_out',
            description:
              'Should only be generated if the user is not using a loan out.',
          },
        ],
      },
    ).as('documentTemplatesLoanOut');

    cy.intercept('GET', '**/v2/api/core/inclusion-statuses', {
      statusCode: 200,
      body: [
        {
          id: 1,
          name: 'Including',
          key: 'including',
          description: 'Including the selections',
        },
        {
          id: 2,
          name: 'Not Including',
          key: 'not_including',
          description: 'Not including the selections',
        },
      ],
    }).as('inclusionStatuses');

    cy.intercept(
      'GET',
      '**/v2/api/core/shoot-locations/?search=&page=1&limit=50**',
      {
        statusCode: 200,
        body: {
          workLocationList: [
            {
              locationId: 29,
              locationName: 'Alaska',
              locationCode: 'AK',
              state: 'AK',
              zipcode: '99801',
              country: 'US',
            },
            {
              locationId: 30,
              locationName: 'Alabama',
              locationCode: 'AL',
              state: 'AL',
              zipcode: '36101',
              country: 'US',
            },
          ],
        },
      },
    ).as('shootLocation');

    cy.intercept('GET', '**/v2/api/core/unions/?search=&page=1&limit=50**', {
      statusCode: 200,
      body: {
        unionList: [
          {
            id: 579,
            name: '1',
            number: '1',
            unionLocalId: 579,
            description: 'LOCAL ONE',
          },
          {
            id: 928,
            name: '10',
            number: '10',
            unionLocalId: 928,
            description: '10',
          },
        ],
      },
    }).as('unions');

    cy.fixture('/componentPayloads/companyDocumentTemplate.json').then(
      (documentTemplate) => {
        cy.intercept(
          'GET',
          '**/v2/api/core/production-companies/**/document-templates/**',
          {
            statusCode: 200,
            body: documentTemplate,
          },
        ).as('documentTemplate');
      },
    );

    cy.intercept('GET', '**/v2/api/core/pdf/**', {
      statusCode: 200,
      headers: { 'Content-Type': 'application/pdf' },
      body: 'mock-pdf-content-or-empty-binary-data',
    }).as('getPdf');

    cy.mount(<TestWrapper />);
  });
  it('should present the Company Custom StartPaperwork with previous created information', () => {
    cy.intercept(
      'PATCH',
      '**/v2/api/core/production-companies/**/document-templates/**',
      { statusCode: 200 },
    ).as('updateDocumentTemplate');
    // ----------- Main Labels and Fields -----------
    cy.contains('Create Custom Start Paperwork').should('be.visible');
    cy.contains('Name').should('be.visible');
    cy.get(nameInput)
      .should('be.visible')
      .and('have.value', 'documentTemplateName');
    // eslint-disable-next-line cypress/unsafe-to-chain-command
    cy.get(nameInput)
      .focus()
      .type('{selectall}{backspace}Updated Paperwork Name')
      .should('have.value', 'Updated Paperwork Name');

    cy.contains('Schema').should('be.visible');
    cy.get(schemaInput).should('be.visible');

    cy.contains('Countersign Schema').should('be.visible');
    cy.get(countersignSchemaInput).should('be.visible');

    cy.contains('Work Location Setting').should('be.visible');
    cy.get(workLocationDropdown)
      .should('be.visible')
      .and('contain.text', 'All');

    // ----------- Union Setting Dropdown -----------
    cy.contains('Union Setting').should('be.visible');
    cy.get(unionSettingDropdown)
      .should('be.visible')
      .and('contain.text', 'Non Union');

    // ----------- Loan Out Setting Dropdown -----------
    cy.contains('Loan Out Setting').should('be.visible');
    cy.get(loanOutDropdown)
      .should('be.visible')
      .and('contain.text', 'Loan Out and Non Loan Out');

    // ----------- Toggles -----------
    cy.contains('Send to Payroll').should('be.visible');
    cy.contains('Optional document').should('be.visible');
    cy.get(sendPayrollToggle).should('have.attr', 'aria-checked', 'true');
    cy.get(optionalDocumentToggle).should('have.attr', 'aria-checked', 'false');

    // ----------- File Upload Section -----------
    cy.get('label[for="file_upload"], label input[type="file"]').should(
      'exist',
    );
    cy.get('label')
      .should('have.class', 'flex')
      .and('contain.text', 'File successfully uploaded');
    cy.get(fileInput).should('exist').and('have.class', 'hidden');

    // ----------- Action Buttons -----------
    cy.contains('button', 'Back')
      .should('exist')
      .and('be.visible')
      .and('not.be.disabled');
    cy.contains('button', 'Save')
      .should('exist')
      .and('be.visible')
      .and('not.be.disabled')
      .click({ force: true });

    // ----------- Verify Update -----------
    cy.wait('@updateDocumentTemplate')
      .its('response.statusCode')
      .should('eq', 200);
  });
});
