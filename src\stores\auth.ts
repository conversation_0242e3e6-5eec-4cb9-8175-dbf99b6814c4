import { logout } from '@/services/auth';
import { getUser } from '@/services/users';
import type User from '@/types/User';
import { defineStore } from 'pinia';

const loadFromStorage = (): User | undefined => {
  const userString = localStorage.getItem('user');
  if (!userString) return;
  return JSON.parse(userString) as User | undefined;
};

export const useAuthStore = defineStore({
  id: 'auth',
  state: () => ({
    user: loadFromStorage() as User | undefined,
  }),
  getters: {
    getUser: (state) => state.user as User,
    isLoggedIn: (state) => !!state.user,
  },
  actions: {
    login(user: User) {
      this.user = user;
      localStorage.setItem('user', JSON.stringify(this.user));
    },
    async reloadUser() {
      const { data: user } = await getUser('me');
      this.login(user);
    },
    logout() {
      this.user = undefined;
      logout();
      localStorage.removeItem('user');
      clearCookies();
    },
  },
});

const clearCookies = () => {
  document.cookie.split(';').forEach((c) => {
    document.cookie =
      c.trim().split('=')[0] +
      '=;expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/';
  });
};
