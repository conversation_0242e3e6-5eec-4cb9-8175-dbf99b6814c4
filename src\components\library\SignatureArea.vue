<template>
  <div
    class="flex justify-end border-2 dark:border-gray-300 border-gray-700 dark:bg-gray-200"
  >
    <canvas id="signature-pad" ref="signaturePad" />
    <div class="undo">
      <XMarkIcon
        class="text-gray-300 dark:text-gray-600 w-6 h-6"
        @click="clear"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { XMarkIcon } from '@heroicons/vue/24/outline';
import SignaturePad from 'signature_pad';

export default {
  data() {
    return {
      signaturePad: undefined as SignaturePad | undefined,
    };
  },
  mounted() {
    const canvas: HTMLCanvasElement = this.$refs
      .signaturePad as HTMLCanvasElement;
    this.signaturePad = new SignaturePad(canvas);
    this.signaturePad.penColor = 'rgb(0, 0, 0)';
    this.signaturePad.toDataURL(); // save image as PNG
    this.signaturePad.toDataURL('image/jpeg'); // save image as JPEG
    this.signaturePad.toDataURL('image/svg+xml'); // save image as SVG
  },
  methods: {
    getSignatureImage() {
      return this.signaturePad!.toDataURL();
    },
    clear() {
      this.signaturePad!.clear(); // save image as JPEG
    },
    isEmpty() {
      return this.signaturePad!.isEmpty();
    },
  },
  components: { XMarkIcon },
};
</script>

<style>
.undo {
  margin-left: -25px;
}
</style>
