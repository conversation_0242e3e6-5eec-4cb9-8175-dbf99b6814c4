FROM node:20.18.1-slim as builder
ARG VITE_MUI_PRO_LICENSE_KEY
ARG VITE_PASSWORDLESS_OKTA_CLIENT_ID
ARG VITE_PASSWORDLESS_OKTA_ISSUER
ARG VITE_PASSWORDLESS_OKTA_MYCNC_CLIENT_ID
ARG VITE_PASSWORDLESS_OKTA_BASE_URL
ARG VITE_API_DASHBOARD_URL
ARG VITE_NPM_TOKEN

ENV VITE_MUI_PRO_LICENSE_KEY=$VITE_MUI_PRO_LICENSE_KEY
ENV VITE_PASSWORDLESS_OKTA_CLIENT_ID=$VITE_PASSWORDLESS_OKTA_CLIENT_ID
ENV VITE_PASSWORDLESS_OKTA_ISSUER=$VITE_PASSWORDLESS_OKTA_ISSUER
ENV VITE_PASSWORDLESS_OKTA_MYCNC_CLIENT_ID=$VITE_PASSWORDLESS_OKTA_MYCNC_CLIENT_ID
ENV VITE_PASSWORDLESS_OKTA_BASE_URL=$VITE_PASSWORDLESS_OKTA_BASE_URL
ENV VITE_API_DASHBOARD_URL=$VITE_API_DASHBOARD_URL

ENV VITE_NPM_TOKEN=$VITE_NPM_TOKEN

# TODO - remove this after we remove need for --legacy-peer-deps
ENV NODE_OPTIONS=--max-old-space-size=4096


WORKDIR /src/app
COPY ./package.json ./
COPY ./package-lock.json ./

RUN echo "//registry.npmjs.org/:_authToken=$VITE_NPM_TOKEN" > ./.npmrc

RUN npm i --legacy-peer-deps && npm cache clean --force
COPY . .
RUN npm run build

FROM nginx:1.16.0-alpine
HEALTHCHECK --timeout=1s --retries=99 \
  CMD wget -q --spider http://127.0.0.1:80/ \
  || exit 1
RUN apk add --update --upgrade --no-cache wget
ADD ./scripts/nginx.conf /etc/nginx/conf.d/default.conf
RUN mkdir /usr/share/nginx/html/v2
COPY --from=builder /src/app/dist /usr/share/nginx/html/v2
