import React from 'react';
import PropTypes from 'prop-types';

import { Box, Button, Loader, Text } from '@/reactComponents/library';

import { <PERSON><PERSON> } from '@/utils/cookie';

import {
  STEP_VERIFY_CODE_SMS,
  getTransactionStep,
  authApiError,
} from '../../utils';
import OktaLoginContext from '../../Context';

const styles = {
  root: {
    display: 'flex',
    flexDirection: 'column',
    gap: 2,
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',
  },
  error: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: 2,
    px: 2,
  },
};

const SendSMSCode = ({ onSuccess }) => {
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState();
  const { client: authClient, setTransaction } =
    React.useContext(OktaLoginContext);

  const setChallengeSending = (val) => Cookie.set('challengeSending', val);
  const isChallengeSending = () => Cookie.get('challengeSending') === 'true';

  React.useEffect(() => {
    if (isChallengeSending()) return;

    const sendChallengeCode = async (email) => {
      try {
        setLoading(true);
        setError(null);

        const response = await authClient.authenticate({ methodType: 'SMS' });
        const nextStep = getTransactionStep(response);
        setTransaction(response);
        if (nextStep !== STEP_VERIFY_CODE_SMS) {
          window.setTimeout(() => setChallengeSending(false), 500);
          const error = authApiError(response);
          throw new Error(error || `Invalid Response: ${nextStep}`);
        }

        onSuccess({ nextStep });
      } catch (e) {
        setError(e.message);
      } finally {
        setChallengeSending(false);
        setLoading(false);
      }
    };

    setChallengeSending(true);
    window.setTimeout(sendChallengeCode, 200);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Box sx={styles.root} data-testid="login-sendSMS-root">
      {loading && <Loader message="Sending SMS code" />}
      {!loading && error && (
        <Box sx={styles.error}>
          <Text color="error">{error}</Text>
          <Button
            onClick={async () => {
              window.location.reload();
            }}
          >
            Back
          </Button>
        </Box>
      )}
    </Box>
  );
};

SendSMSCode.propTypes = {
  onSuccess: PropTypes.func.isRequired,
};

export default SendSMSCode;
