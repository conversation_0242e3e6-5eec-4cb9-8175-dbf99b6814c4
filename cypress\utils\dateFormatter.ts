class DateFormatter {
  static formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  static formatTime(date: Date): string {
    return date.toISOString().split('T')[1].substring(0, 5);
  }

  static formatDateMMDD(date: Date, separator: string = '/'): string {
    const formattedDate = this.formatDate(date);
    const [, month, day] = formattedDate.split('-');
    return `${month}${separator}${day}`;
  }

  static formatDateDayNameMonthDay(dateString: string): string {
    const [, month, day] = dateString.split('-');

    const date = new Date(`${dateString}T12:00:00`);
    const dayName = date.toLocaleString('en-US', {
      weekday: 'short',
      timeZone: 'America/Los_Angeles',
    });

    return `${dayName} ${month}/${day}`;
  }

  static formatDateMonthNameDayYear(dateString: string): string {
    const [year, , day] = dateString.split('-');

    const date = new Date(`${dateString}T12:00:00`);
    const monthDay = date.toLocaleDateString('en-US', {
      month: 'long',
      timeZone: 'America/Los_Angeles',
    });
    return `${monthDay} ${Number(day)}, ${year}`;
  }

  static formatDateMMDDYYYY(date: string, separator = '/'): string {
    const [year, month, day] = date.split('-');
    return `${month}${separator}${day}${separator}${year}`;
  }
}

export { DateFormatter };
