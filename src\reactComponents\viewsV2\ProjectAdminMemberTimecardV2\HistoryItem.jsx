import React from 'react';
import PropTypes from 'prop-types';

import { Box } from '@/reactComponents/library';

import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';
import DescriptionOutlinedIcon from '@mui/icons-material/DescriptionOutlined';
import FileUploadOutlinedIcon from '@mui/icons-material/FileUploadOutlined';
import CheckIcon from '@mui/icons-material/Check';
import NorthWestIcon from '@mui/icons-material/NorthWest';

const styles = {
  historyItem: {
    display: 'flex',
    width: '100%',
    gap: 2,
    '&:not(:last-child)::after ': {
      content: '""',
      position: 'absolute',
      left: '20px',
      top: '45px',
      height: 'calc(100% - 30px)',
      width: '2px',
      backgroundColor: 'secondary.main',
    },
  },
  icon: {
    borderRadius: '25px',
    color: 'secondary.contrastText',
    backgroundColor: 'secondary.main',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '40px',
    height: '40px',
  },
  textContent: {
    display: 'flex',
    flexDirection: 'column',
    pt: '6px',
    maxWidth: '80%',
  },
  text: { display: 'flex', gap: 1 },
  topText: { fontWeight: 'bold' },
  midText: {
    display: '-webkit-box',
    '&:hover': { cursor: 'pointer' },
  },
  hideFull: {
    WebkitBoxOrient: 'vertical',
    WebkitLineClamp: '3',
    overflow: 'hidden',
  },
  bottomText: { color: 'text.secondary' },
};

const HistoryItem = (props) => {
  const { audit } = props;

  const timestamp = audit.createdAt.toFormat('LLL dd,h:mm a');
  const actorName = audit.updatedBy
    ? audit.updatedBy
    : `${audit.user.firstName} ${audit.user.lastName}`;
  const bottomText = `by ${actorName}, ${timestamp}`;
  const midText = audit?.notes || '';
  const action = `${audit.action}`;
  const key = `${audit.newValues.status.key}`;

  const [showFullNotes, setShowFullNotes] = React.useState(false);

  const toggleText = () => {
    setShowFullNotes((v) => !v);
  };

  let Icon = EditOutlinedIcon;
  let topText = '';

  switch (action) {
    case 'edit':
      Icon = EditOutlinedIcon;
      topText = 'Edited';
      break;
    case 'status_update':
      if (key === 'created') {
        Icon = DescriptionOutlinedIcon;
        topText = 'Created';
      } else if (key === 'in_progress') {
        Icon = EditOutlinedIcon;
        topText = 'Edited';
      } else if (key === 'submitted') {
        Icon = FileUploadOutlinedIcon;
        topText = 'Submitted';
      } else if (key === 'approved') {
        Icon = CheckIcon;
        topText = 'Approved';
      } else if (key === 'payroll_manager_requested_changes') {
        Icon = NorthWestIcon;
        topText = 'Requested Changes';
      } else {
        console.warn('Unknown audit status key:', key);
        topText = 'Edited';
      }
      break;
    case 'batch_update':
      Icon = AddIcon;
      topText = `Added to batch ${key}`;
      break;
    case 'batch_remove':
      Icon = RemoveIcon;
      topText = `Removed from batch ${key}`;
      break;
    default:
      console.warn('Unknown audit action:', action);
  }

  const midTextSX = showFullNotes
    ? styles.midText
    : { ...styles.midText, ...styles.hideFull };

  return (
    <Box sx={styles.historyItem}>
      <Box sx={styles.icon}>
        <Icon />
      </Box>

      <Box sx={styles.textContent}>
        <Box sx={styles.text}>
          <Box sx={styles.topText}>{topText}</Box>
        </Box>
        {midText && (
          <Box sx={styles.text}>
            <Box onClick={toggleText} sx={midTextSX}>
              {midText}
            </Box>
          </Box>
        )}
        <Box sx={styles.text}>
          <Box sx={styles.bottomText}>{bottomText}</Box>
        </Box>
      </Box>
    </Box>
  );
};

HistoryItem.propTypes = {
  audit: PropTypes.object.isRequired,
};

export default HistoryItem;
