import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import _cloneDeep from 'lodash/cloneDeep';
import { Checkbox, InputAdornment, Autocomplete } from '@mui/material';
import { TextFieldRaw } from '../../library';
import ColumnsIcon from '../../assets/icons/ColumnsIcon';
import { useEffect } from 'react';
import PropTypes from 'prop-types';

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;

export default function AdditionalFields(props) {
  const {
    additionalFields,
    triggerRecalculateAdditionalFields,
    setAdditionalFields,
    timecard = {},
    updateTimecard,
    readOnlyMode,
  } = props;

  const handleSelect = (option) => {
    option.selected = !option.selected;
    setAdditionalFields((v) =>
      v.map((f) => (f.label === option.label ? option : f)),
    );
    if (option.selected === false) {
      const timecardDays = timecard.timecardDays.map((d) => {
        const day = _cloneDeep(d);

        if (option.label === 'Meal 2') {
          if (day.meals && day.meals.length > 1) {
            day.meals = [{ ...day.meals[0] }];
          }
        } else {
          option.tableFields.forEach((field) => {
            day[field] = option.defaultVal;
          });
        }
        if (day.updated !== undefined) {
          //mark updated for defaultDays
          day.updated = true;
        }
        return day;
      });
      updateTimecard({ timecardDays });
    }
  };

  const isExempt = timecard.isNonUnion && timecard.isExemptIndicator;

  useEffect(() => {
    if (timecard.isNonUnion && !timecard.isExemptIndicator) {
      setAdditionalFields((prevList) => {
        return prevList.filter((field) => field.label === 'Meal 2');
      });
    }
  }, [
    timecard.isNonUnion,
    timecard.isExemptIndicator,
    timecard.id,
    setAdditionalFields,
  ]);

  useEffect(() => {
    const hasMeal2 = timecard?.timecardDays?.some(
      (day) => day.isActive && day.meals && day.meals.length > 1,
    );
    const hasGrace = timecard?.timecardDays?.some(
      (day) =>
        day.isActive &&
        (day.hasMealGraceOne || day.hasMealGraceTwo || day.hasWrapGrace),
    );
    const hasHalfDay = timecard?.timecardDays?.some(
      (day) => day.isActive && day.hasHalfDay,
    );
    const hasDriveTime = timecard?.timecardDays?.some(
      (day) => day.isActive && day.driveTime && day.driveTime > 0,
    );
    const hasUnpaidTravelTime = timecard?.timecardDays?.some(
      (day) =>
        day.isActive &&
        ((day.hotelToSetTime && day.hotelToSetTime > 0) ||
          (day.setToHotelTime && day.setToHotelTime > 0)),
    );
    const hasWalkingMeal = timecard?.timecardDays?.some(
      (day) => day.isActive && day.hasWalkingMeal,
    );
    setAdditionalFields((prev) => {
      return prev.map((field) => {
        if (field.label === 'Meal 2' && hasMeal2) {
          return { ...field, selected: hasMeal2 };
        }
        if (field.label === 'Grace' && hasGrace) {
          return { ...field, selected: hasGrace };
        }
        if (field.label === 'Half Day' && hasHalfDay) {
          return { ...field, selected: hasHalfDay };
        }
        if (field.label === 'Drive Time' && hasDriveTime) {
          return { ...field, selected: hasDriveTime };
        }
        if (field.label === 'Unpaid Travel Time' && hasUnpaidTravelTime) {
          return { ...field, selected: hasUnpaidTravelTime };
        }
        if (field.label === 'Walking Meal' && hasWalkingMeal) {
          return { ...field, selected: hasWalkingMeal };
        }
        return field;
      });
    });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timecard.id, triggerRecalculateAdditionalFields?.current]);

  return (
    <Autocomplete
      sx={{
        marginLeft: 'auto',
        marginBottom: '8px',
        marginRight: '24px',
        marginTop: '8px',
        maxWidth: '200px',
      }}
      value={additionalFields.filter((f) => f.selected)}
      disabled={isExempt}
      multiple
      disableClearable
      options={additionalFields}
      disableCloseOnSelect
      getOptionLabel={(option) => option.label}
      renderTags={() => null}
      renderOption={(allProps, option, { selected }) => {
        const { key, ...optionProps } = allProps;
        return (
          <li
            key={key}
            {...optionProps}
            onClick={(e) => {
              e.stopPropagation();
              if (readOnlyMode) return;
              handleSelect(option);
            }}
          >
            <Checkbox
              disabled={readOnlyMode}
              icon={icon}
              checkedIcon={checkedIcon}
              style={{ marginRight: 8 }}
              checked={selected}
            />
            {option.label}
          </li>
        );
      }}
      renderInput={(params) => {
        return (
          <TextFieldRaw
            {...params}
            variant="standard"
            inputProps={{
              ...params.inputProps,
              readOnly: true,
            }}
            onClick={(e) => {
              e.stopPropagation();
            }}
            InputProps={{
              ...params.InputProps,
              startAdornment: (
                <InputAdornment
                  sx={{ marginRight: '4px', marginLeft: '4px' }}
                  position="start"
                >
                  <ColumnsIcon />
                </InputAdornment>
              ),
              sx: [
                {
                  color: 'black',
                  fontWeight: '600',
                },
                (theme) =>
                  theme.applyStyles('dark', {
                    color: 'white',
                    fontWeight: '600',
                  }),
              ],
            }}
            slotProps={{
              ...params.slotProps,
              popper: {
                disablePortal: true,
              },
            }}
            placeholder="More Fields"
          />
        );
      }}
    />
  );
}

AdditionalFields.propTypes = {
  additionalFields: PropTypes.array.isRequired,
  triggerRecalculateAdditionalFields: PropTypes.object,
  setAdditionalFields: PropTypes.func.isRequired,
  timecard: PropTypes.object.isRequired,
  updateTimecard: PropTypes.func.isRequired,
  readOnlyMode: PropTypes.bool.isRequired,
};
