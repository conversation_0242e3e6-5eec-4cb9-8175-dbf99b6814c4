on:
  release:
    types:
      - created

name: Deploy Webapp to Production

jobs:
  deploy:
    name: Deploy Webapp to Production
    runs-on: ubuntu-latest
    environment: prod

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Configure Prod AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.PROD_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.PROD_AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-1

      - name: Build Image Name
        id: build-image
        env:
          ECR_REPOSITORY: ts-vuejs-webapp
        run: |
          sed -i 's/576349523908/703235726644/g' scripts/task-definition.json
          echo "::set-output name=image::703235726644.dkr.ecr.us-west-1.amazonaws.com/$ECR_REPOSITORY:$GITHUB_SHA"

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: scripts/task-definition.json
          container-name: ts-vuejs-webapp
          image: ${{ steps.build-image.outputs.image }}

      - name: Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: ts-vuejs-webapp
          cluster: production-cluster
          wait-for-service-stability: true
