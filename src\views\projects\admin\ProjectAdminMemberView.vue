<template>
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="py-2 mb-6">
      <ProjectAdminMemberHeader
        v-if="projectMember"
        :user-crew="userCrew"
        :project-member="projectMember"
        :project="project"
        :user="user"
        @refresh="load"
        class="mb-2"
      />
      <SecondaryTab
        v-for="tab in tabs"
        :key="tab.label"
        :tab="tab"
        :currentTab="currentTab"
        @tabClick="goTo"
      />
    </div>
    <RouterView
      v-if="user && projectMember"
      :user="user"
      :user-crew="userCrew"
      :project="project"
      :is-admin="isAdmin"
      :project-member="projectMember"
      @refresh="load"
    />
  </div>
</template>

<script lang="ts">
// converted to react using Outlet already
import Button from '@/components/library/Button.vue';
import SecondaryTab from '@/components/library/SecondaryTab.vue';
import ProjectAdminMemberHeader from '@/components/ProjectAdminMemberHeader.vue';
import { getProjectMemberById } from '@/services/project-members';
import type Project from '@/types/Project';
import type { StartPaperwork } from '@/types/StartPaperwork';
import type UserCrew from '@/types/UserCrew';
import { defineComponent, type PropType } from 'vue';

interface Tab {
  label: string;
  key: string;
  routeName: string;
}

export default defineComponent({
  components: {
    Button,
    ProjectAdminMemberHeader,
    SecondaryTab,
  },
  props: {
    project: { type: Object as PropType<Project>, required: true },
    isAdmin: { type: Boolean, required: true },
  },
  data() {
    return {
      user: null as any,
      userCrew: {} as UserCrew,
      startPaperwork: null as StartPaperwork | null,
      projectMember: undefined as any,
      loanOut: null as any,
      userId: null as any,
    };
  },
  computed: {
    tabs(): Tab[] {
      return [
        {
          label: 'Personal Info',
          key: 'personalInfo',
          routeName: 'project-admin-member-detail-personal-info',
        },
        {
          label: 'Onboarding',
          key: 'onboarding',
          routeName: 'project-admin-member-detail-onboarding',
        },
        {
          label: 'Loan Out',
          key: 'loanOut',
          routeName: 'project-admin-member-detail-loan-out',
        },
        {
          label: 'Start Paperwork',
          key: 'startPaperwork',
          routeName: 'project-admin-member-detail-start-paperwork',
        },
        {
          label: 'Timecards',
          key: 'timecards',
          routeName: 'project-admin-member-detail-timecards',
        },
      ];
    },
    currentTab() {
      if (this.$route.name === 'project-admin-member-detail-timecard-detail') {
        return this.tabs.find(
          (tab) => tab.routeName === 'project-admin-member-detail-timecards',
        );
      }
      return this.tabs.find((tab) => tab.routeName === this.$route.name);
    },
  },
  async created(): Promise<void> {
    await this.load();
  },
  methods: {
    async load(): Promise<void> {
      await this.getProjectMember();
    },
    async getProjectMember(): Promise<void> {
      const { data: projectMember } = await getProjectMemberById(
        this.$route.params.projectMemberId.toString(),
      );
      this.projectMember = projectMember;
      this.user = projectMember.user;
      this.userId = projectMember.userId;
    },
    goTo(routeName: string): void {
      this.$router.push({
        name: routeName,
        params: {
          hashId: this.project.hashId!,
          projectMemberId: this.$route.params.projectMemberId.toString(),
        },
      });
    },
  },
});
</script>
