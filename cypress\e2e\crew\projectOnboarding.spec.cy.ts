import { faker } from '@faker-js/faker';
import ProjectViewPageObject from '../../pageObjects/project/projectView';
import {
  addPersonalInfo,
  fetchOccupationsByProjectAndUnion,
  fetchProjectInformation,
  fetchRateTypes,
  fetchShootLocationsByProject,
  fetchUnionsByProject,
  logout,
} from '../../support/apiHelpers';
import { ProjectOnboardingPageObject } from '../../pageObjects/project/projectOnboarding';
import { interceptProjectOnboarding } from '../../support/apiTimecardInterceptors';
import { createProjectFlow } from '../../support/apiFlows/createProjectFlow';

describe('User Crew - Project Onboarding', () => {
  const projectViewPO = new ProjectViewPageObject();
  const projectOnboardingPO = new ProjectOnboardingPageObject();
  const onboardingInfo = {
    workLocation: '',
    union: '',
    occupation: '',
    rateType: '',
  };

  beforeEach((): void => {
    cy.loginWithSession(
      Cypress.env('PHONE_NUMBER'),
      Cypress.env('OTC_CODE'),
      Cypress.env('BASE_URL'),
      'supervisor',
    );

    //Get Details by Api before test
    cy.then(() => {
      // Step 1: Get the project Id by project Name
      return createProjectFlow().then(() => {
        //Get project info to add user
        return fetchProjectInformation().then((res) => {
          const departmentId = res.departments[0].id;
          return logout().then(() => {
            //login with crew into the new project
            const url = `${Cypress.env('BASE_URL')}/projects/${Cypress.env(
              'projectHashId',
            )}?onboard=true&departmentId=${departmentId}`;

            cy.loginWithoutSession(
              Cypress.env('PHONE_NUMBER_CREW'),
              Cypress.env('OTC_CODE'),
              url,
            );
          });
        });
      });
    })
      .then(() => {
        // Getting rate type
        return fetchRateTypes().then((rateTypes) => {
          onboardingInfo.rateType =
            rateTypes[
              faker.number.int({ min: 0, max: rateTypes.length - 1 })
            ].crew_display_name;
        });
      })
      .then(() => {
        // Getting shoot location
        return fetchShootLocationsByProject()
          .then((shootLocations) => {
            onboardingInfo.workLocation =
              shootLocations[0].shootLocation.locationName;
            return shootLocations[0].payrollProjectLocationId;
          })
          .then((payrollProjectLocationId) => {
            return fetchUnionsByProject(payrollProjectLocationId).then(
              (unionResponse) => {
                const union =
                  unionResponse[
                    faker.number.int({ min: 0, max: unionResponse.length - 1 })
                  ];
                onboardingInfo.union = union.name;
                return fetchOccupationsByProjectAndUnion(
                  payrollProjectLocationId,
                  union.id,
                ).then((occupations) => {
                  onboardingInfo.occupation =
                    occupations[
                      faker.number.int({ min: 0, max: occupations.length - 1 })
                    ].name;
                });
              },
            );
          });
      })
      .then(() => {
        // adding personal info to be able to fill project onboarding
        addPersonalInfo();
      });
  });

  it('Verify Crew member is able to fill project onboarding form', () => {
    interceptProjectOnboarding();
    // Navigate to the project and open the supervisor view
    cy.visit(
      `${Cypress.env('BASE_URL')}projects/${Cypress.env('projectHashId')}`,
    );
    projectViewPO.goToProjectOnboarding();
    projectOnboardingPO.fillOutForm(
      onboardingInfo.workLocation,
      onboardingInfo.union,
      onboardingInfo.occupation,
      '200',
      onboardingInfo.rateType,
    );

    cy.wait('@projectOnboarding').then((interception) => {
      const responseBody = interception.response?.body;
      expect(interception.response?.statusCode).to.equal(200);
      expect(Number(responseBody.projectId)).is.eq(Cypress.env('projectId'));
    });
  });
});
