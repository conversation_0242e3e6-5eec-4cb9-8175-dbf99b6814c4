import type { KitRental } from '@/types/KitRental';
import axios, { type AxiosResponse } from 'axios';
import { coreBaseUrl } from '../utils/constants';

export const updateKitRental = async (
  id: number | string,
  kitRental: KitRental,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/kit-rentals/${id}`;
  const response = await axios.patch(url, kitRental, { withCredentials: true });
  return response;
};

export const deleteKitRental = async (
  id: number | string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/kit-rentals/${id}`;
  const response = await axios.delete(url, { withCredentials: true });
  return response;
};
