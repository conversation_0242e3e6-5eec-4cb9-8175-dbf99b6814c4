<!-- #FPS-1323 Project Timecard Create View - Crew Create Timecard  -->

<template>
  <div class="flex justify-center mx-auto w-full py-2 px-5">
    <div class="max-w-7xl">
      <Dropdown
        v-model="payPeriod"
        :menu-items="payPeriods"
        label="Pay Period"
        display-name="name"
        data-testid="payPeriod-select-dropdown"
      />
      Select the week you worked.
      <div class="flex justify-center">
        <Button
          class="mt-2"
          @click="createTimecard"
          data-testid="payPeriod-continue-btn"
        >
          Continue
        </Button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Button from '@/components/library/Button.vue';
import Dropdown from '@/components/library/Dropdown.vue';
import { getProjectPayPeriods } from '@/services/project';
import {
  createTimecard,
  type CreateTimecardPayload,
} from '@/services/timecards';
import type Project from '@/types/Project';
import type Timecard from '@/types/Timecard';
import type { AxiosResponse } from 'axios';
import { DateTime } from 'luxon';
import { defineComponent, type PropType } from 'vue';

export default defineComponent({
  props: {
    project: {
      type: Object as PropType<Project>,
      required: true,
    },
    navigate: {
      type: Function as PropType<Function>,
      required: true,
    },
  },
  components: {
    Button,
    Dropdown,
  },
  data() {
    return {
      payPeriod: {} as any,
      payPeriods: [] as any[],
    };
  },
  methods: {
    async createTimecard() {
      const createTimecardPayload: CreateTimecardPayload = {
        projectId: this.project.id!,
        payPeriodId: this.payPeriod.id,
        timeZone: DateTime.now().zoneName!,
      };
      const { data }: AxiosResponse<any, any> = await createTimecard(
        createTimecardPayload,
      );
      const timecard: Timecard = data;
      this.$props.navigate({
        pathname: `/projects/${this.project.hashId}/timecards/${timecard.id}`,
      });
    },
  },
  async mounted() {
    const { data } = await getProjectPayPeriods(this.project.id!);
    this.payPeriods = data.map((pp: any) => {
      const startsAt = DateTime.fromISO(pp.startsAt);
      const endsAt = DateTime.fromISO(pp.endsAt);
      return {
        id: pp.id,
        name: `${startsAt.toFormat('ccc MM/dd')} - ${endsAt.toFormat(
          'ccc MM/dd',
        )}`,
        value: pp.id,
        startsAt,
        endsAt,
      };
    });
  },
});
</script>

<style></style>
