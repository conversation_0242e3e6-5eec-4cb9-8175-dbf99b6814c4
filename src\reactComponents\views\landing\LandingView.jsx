import React from 'react';
import Card from './Card';
import { actions } from '../../utils/landingActions';
import useAuth from '@/reactComponents/AppHooks';

const LandingReactView = () => {
  useAuth();

  return (
    <div className="relative flex min-h-screen flex-col justify-center overflow-hidden">
      <div className="overflow-hidden max-w-lg grid grid-cols-1 sm:grid-cols-2 gap-4 sm:divide-y-0 m-auto px-4 sm:px-0">
        {actions.map((action) => (
          <Card key={action.title} action={action} />
        ))}
      </div>
    </div>
  );
};

export default LandingReactView;
