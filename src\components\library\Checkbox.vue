<template>
  <input
    type="checkbox"
    :name="name"
    :id="id"
    :checked="modelValue"
    @change="handleChange"
    :disabled="disabled"
    class="h-4 w-4 rounded border-gray-300 dark:border-gray-400 dark:bg-gray-400 text-indigo-600 focus:ring-indigo-500 mt-1 mr-2"
    :class="{ 'bg-gray-100 dark:bg-gray-600 cursor-not-allowed': disabled }"
  />
  {{ label }}
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'Checkbox',
  emits: ['update:modelValue'],
  props: {
    modelValue: {
      default: false,
      type: Boolean,
    },
    disabled: {
      default: false,
      type: Boolean,
    },
    name: {
      default: '',
      type: String,
    },
    id: {
      default: '',
      type: String,
    },
    label: {
      default: '',
      type: String,
    },
  },
  methods: {
    handleChange(value: any) {
      if (this.disabled) return;
      this.$emit('update:modelValue', value.target.checked);
    },
  },
});
</script>
