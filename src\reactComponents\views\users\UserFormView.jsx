import { applyPureVueInReact } from 'veaury';
import UserFormVue from '@/views/users/UserFormView.vue';
import { useAuth, useReactRouter } from '@/reactComponents/AppHooks';
const ReactUserFormView = applyPureVueInReact(UserFormVue);

const UserFormView = () => {
  useAuth();
  const { navigate, route } = useReactRouter();
  return (
    <ReactUserFormView editMode={true} route={route} navigate={navigate} />
  );
};

export default UserFormView;
