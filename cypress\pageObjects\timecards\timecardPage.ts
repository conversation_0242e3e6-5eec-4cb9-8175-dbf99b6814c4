/// <reference types="cypress" />

export default class TimecardPage {
  readonly projectsCard = '[data-testid="card-Projects"]';
  readonly projectList = '[data-testid="projects-list"]';
  readonly projectNamePattern = '[data-testid^="project-name-"]';
  readonly supervisorViewButton = '[data-testid="supervisor-view-btn"]';
  readonly moveToBatchButton = '[data-testid="move-to-batch-btn"]';
  readonly projectFilterByName = '[data-testid="filter-popover-Name"]';
  readonly projectFilterByNameInput = '[data-testid="filter-input-Name"]';

  navigateToProject(projectName: string): void {
    cy.get(this.projectsCard).click();
    cy.get(this.projectFilterByName)
      .parent()
      .find('svg')
      .should('be.visible')
      .click();
    cy.get(this.projectFilterByName)
      .parent()
      .find('svg')
      .should('be.visible')
      .click();
    cy.get(this.projectFilterByNameInput)
      .parent()
      .find('input')
      .should('be.visible')
      .click();
    cy.get(this.projectFilterByNameInput)
      .parent()
      .find('input')
      .should('be.visible')
      .type(projectName);
    cy.get(this.projectList)
      .find(this.projectNamePattern)
      .contains(projectName)
      .should('be.visible')
      .click();
  }

  openSupervisorView(): void {
    cy.get(this.supervisorViewButton, { timeout: 10000 })
      .should('be.visible')
      .click();
    cy.contains('Time').should('be.visible').click();
  }

  openMoveToBatchModal(): void {
    cy.get(this.moveToBatchButton, { timeout: 10000 })
      .should('be.visible')
      .click();
  }
}
