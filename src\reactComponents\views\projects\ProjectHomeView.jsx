import React from 'react';
import { useReactRouter } from '../../AppHooks';
import { useSearchParams } from 'react-router';
import {
  addMember,
  getStartingPaperwork,
  isProjectOnboarded,
} from '@/services/project';
import { needsCrewOnboarding } from '@/services/user-crew';
import { PermissionKeys } from '@/types/Permission';
import PermissionStore from '@/reactComponents/stores/permission';
import {
  Button,
  Icon,
  PillBadge,
  Loader,
  Dialog,
} from '@/reactComponents/library';
import { ExclamationCircleIcon } from '@heroicons/react/24/solid';
import ProjectStore from '@/reactComponents/stores/project';

const OnboardingModal = React.lazy(() =>
  import('@/reactComponents/viewsV2/Onboarding/RootModal'),
);

const ProjectHomeView = () => {
  const { navigate, route } = useReactRouter();

  const [searchParams, setSearchParams] = useSearchParams();
  const onboardingStep = searchParams.get('onboarding');

  const onSetOnBoarding = () => {
    searchParams.set('onboarding', 'true');
    setSearchParams(searchParams);
  };

  const { project, isAdmin } = ProjectStore;

  const [pages, setPages] = React.useState([]);

  const [startingPaperwork, setStartingPaperwork] = React.useState(null);
  const [needsPersonalOnboarding, setNeedsPersonalOnboarding] =
    React.useState(false);
  const [wasProjectOnboarded, setWasProjectOnboarded] = React.useState(false);

  const [isFetching, setIsFetching] = React.useState(false);

  React.useEffect(() => {
    const fetchData = async () => {
      setIsFetching(true);

      const [needsOnboardingData, wasProjectOnboardedData] = await Promise.all([
        needsCrewOnboarding(),
        isProjectOnboarded(project.id),
      ]);

      setWasProjectOnboarded(wasProjectOnboardedData.data);
      setNeedsPersonalOnboarding(needsOnboardingData.data);

      const shouldOnboard = route.query?.onboard?.toString() === 'true';
      const departmentId = Number(route.query?.departmentId);

      try {
        if (wasProjectOnboardedData.data && shouldOnboard && isAdmin) {
          await addMember(project.id, 'me', departmentId);
        }

        if (needsOnboardingData.data) {
          return;
        }

        const { data } = await getStartingPaperwork(project.id);

        setStartingPaperwork(data);
      } catch (err) {
        console.warn('Error fetching starting paperwork', err);
      } finally {
        setIsFetching(false);
      }
    };

    fetchData();
  }, [
    isAdmin,
    needsPersonalOnboarding,
    project?.id,
    route.query?.departmentId,
    route.query?.onboard,
    wasProjectOnboarded,
  ]);

  const getPages = React.useCallback(() => {
    return {
      personalInfoOnboarding: {
        label: 'Personal Info / Loan Out',
        name: 'personal-onboarding',
        pathname: () => `${route.location.pathname}/personal`,
        completed: !needsPersonalOnboarding,
        description:
          'One time onboarding for your personal information. Stored between projects so you only do it once ever!',
        cta: needsPersonalOnboarding ? 'Start' : 'Edit',
      },
      admin: {
        label: 'Supervisor View',
        name: 'project-admin-timecards',
        pathname: () => `${route.location.pathname}/timecards`,
        description: 'Manage your crew and project settings.',
        ignoreStatus: true,
      },
      projectOnboarding: {
        label: 'Project Onboarding',
        name: 'project-onboarding',
        pathname: () => `${route.location.pathname}/onboarding`,
        completed: wasProjectOnboarded,
        description: 'Information specific to this project.',
        cta: !wasProjectOnboarded ? 'Start' : 'View',
      },
      reviewStartingPaperwork: {
        label: 'Start Paperwork',
        name: 'project-starting-paperwork-review',
        pathname: () => `${route.location.pathname}/start/review`,
        description: 'Review your onboarding information.',
        completed: true,
      },
      signStartPaperwork: {
        label: 'Sign Start Paperwork',
        name: 'project-starting-paperwork-generate',
        pathname: () => `${route.location.pathname}/start/generate`,
        description:
          'Generate your onboarding paperwork and information for the project.',
        completed: false,
        cta: 'Sign',
      },
      timecards: {
        label: 'Timecards',
        name: 'project-timecards',
        pathname: () => `${route.location.pathname}/timecards`,
        description: 'Track your time and pay.',
        ignoreStatus: true,
      },
    };
  }, [needsPersonalOnboarding, route.location.pathname, wasProjectOnboarded]);

  const goToPage = (page) => {
    const departmentId = route.query.departmentId?.toString();
    const pathname = page.pathname();
    const searchParams = new URLSearchParams();
    searchParams.append('departmentId', departmentId || '');

    navigate({
      pathname,
      search: searchParams.toString(),
    });
  };

  const isAdminOrHasPermission = () => {
    return (
      isAdmin ||
      PermissionStore.hasPermission(PermissionKeys.ADMIN_VIEW_ON_ALL_PROJECTS)
    );
  };

  const goToAdminView = () => {
    const currentPath = route.location.pathname;
    navigate({
      pathname: `${currentPath}/admin/members`,
    });
  };

  const getFilteredPages = React.useCallback(() => {
    const allPages = getPages();
    const filteredPages = [];

    filteredPages.push(allPages.personalInfoOnboarding);
    if (needsPersonalOnboarding) {
      return filteredPages;
    }
    filteredPages.push(allPages.projectOnboarding);
    if (!wasProjectOnboarded) {
      return filteredPages;
    }

    if (!startingPaperwork || startingPaperwork.length === 0) {
      filteredPages.push(allPages.signStartPaperwork);

      return filteredPages;
    }

    filteredPages.push(allPages.reviewStartingPaperwork);
    filteredPages.push(allPages.timecards);

    return filteredPages;
  }, [
    getPages,
    needsPersonalOnboarding,
    startingPaperwork,
    wasProjectOnboarded,
  ]);

  React.useEffect(() => {
    setPages(getFilteredPages());
  }, [
    needsPersonalOnboarding,
    wasProjectOnboarded,
    startingPaperwork,
    getFilteredPages,
  ]);

  return (
    <div className="flex justify-center mx-auto w-full py-2 px-5">
      {!!onboardingStep && (
        <React.Suspense
          fallback={
            <Dialog open fullScreen sx={{ opacity: '0.5' }}>
              <Loader />
            </Dialog>
          }
        >
          <OnboardingModal onboardingStep={onboardingStep} />
        </React.Suspense>
      )}
      <div className="max-w-7xl">
        <ul className="space-y-3 pt-3 mb-12">
          {isAdminOrHasPermission() && (
            <li className="overflow-hidden px-4 sm:rounded-md sm:px-6">
              <div className="bg-white dark:bg-gray-900 shadow rounded-lg border-2 dark:border-indigo-800 border-indigo-300">
                <div className="px-4 py-3 space-y-2 sm:p-6 sm:flex justify-between items-center">
                  <div>
                    <h3 className="font-semibold text-lg dark:text-indigo-300 text-indigo-700">
                      Supervisor View
                    </h3>
                    <p className="text-sm max-w-sm text-gray-700 dark:text-gray-300">
                      Manage crew onboarding and timecards. For production
                      supervisors and staff only.
                    </p>
                  </div>
                  <Button
                    onClick={goToAdminView}
                    variant="secondary"
                    data-testid="supervisor-view-btn"
                  >
                    View
                  </Button>
                </div>
              </div>
            </li>
          )}
          <li className="overflow-hidden px-4 sm:rounded-md sm:px-6">
            <div className="bg-white dark:bg-gray-900 shadow rounded-lg border-2 dark:border-indigo-800 border-indigo-300">
              <div className="px-4 py-3 space-y-2 sm:p-6 sm:flex justify-between items-center">
                <div>
                  <h3 className="font-semibold text-lg dark:text-indigo-300 text-indigo-700">
                    New Onboarding!
                  </h3>
                  <p className="text-sm max-w-sm text-gray-700 dark:text-gray-300">
                    **TEMP** Trigger the new onboarding over lay **TEMP**
                  </p>
                </div>
                <Button
                  variant="secondary"
                  data-testid="onboarding-view-btn"
                  onClick={onSetOnBoarding}
                >
                  Start New Onboarding
                </Button>
              </div>
            </div>
          </li>
          {!isFetching &&
            pages.map((page) => (
              <li
                key={`page-${page.name}`}
                className="overflow-hidden px-4 sm:rounded-md sm:px-6"
              >
                <div className="bg-white dark:bg-gray-900 shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <div className="flex">
                      {!page.ignoreStatus && (
                        <>
                          {page.completed ? (
                            <PillBadge variant="successIcon">
                              <Icon name="checked" className="w-4 h-4" />
                            </PillBadge>
                          ) : (
                            <ExclamationCircleIcon className="text-yellow-500 dark:text-yellow-400 w-6 h-6" />
                          )}
                        </>
                      )}
                      <h3 className="text-base font-semibold leading-6 ml-2">
                        {page.label}
                      </h3>
                    </div>
                    <div className="mt-2 max-w-xl text-sm text-gray-500 dark:text-gray-400">
                      <p>{page.description}</p>
                    </div>
                    <div className="mt-5">
                      <Button
                        onClick={() => goToPage(page)}
                        variant={page.completed ? 'secondary' : 'primary'}
                        data-testid={`${page.name}-btn`}
                      >
                        {page.cta || 'View'}
                      </Button>
                    </div>
                  </div>
                </div>
              </li>
            ))}
        </ul>
      </div>
    </div>
  );
};

export default ProjectHomeView;
