/* color palette from <https://github.com/vuejs/theme> */
:root {
  --vt-c-divider-light-1: rgba(var(--tw-gray-700), 0.29);
  --vt-c-divider-light-2: rgba(var(--tw-gray-700), 0.12);
  --vt-c-divider-dark-1: rgba(var(--tw-gray-600), 0.65);
  --vt-c-divider-dark-2: rgba(var(--tw-gray-600), 0.48);

  --vt-c-text-light-1: var(--tw-gray-800);
  --vt-c-text-light-2: rgba(var(--tw-gray-700), 0.66);
  --vt-c-text-dark-1: var(--tw-white);
  --vt-c-text-dark-2: var(--tw-gray-200);

  --vt-font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
}

/* semantic color variables for this project */
:root {
  --color-background: var(--tw-gray-100);
  --color-background-soft: var(--tw-gray-200);
  --color-background-mute: var(--tw-gray-300);

  --color-border: var(--vt-c-divider-light-2);
  --color-border-hover: var(--vt-c-divider-light-1);

  --color-heading: var(--vt-c-text-light-1);
  --color-text: var(--vt-c-text-light-1);

  --section-gap: 160px;
}

:root.dark {
  --color-background: var(--tw-gray-800);
  --color-background-soft: var(--tw-gray-700);
  --color-background-mute: var(--tw-gray-700);

  --color-border: var(--vt-c-divider-dark-2);
  --color-border-hover: var(--vt-c-divider-dark-1);

  --color-heading: var(--vt-c-text-dark-1);
  --color-text: var(--vt-c-text-dark-2);
}

/* *,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  position: relative;
  font-weight: normal;
} */

body {
  min-height: 100vh;
  color: var(--color-text);
  background: var(--color-background);
  transition: color 0.5s, background-color 0.5s;
  line-height: 1.6;
  font-family: var(--vt-font-family);
  font-size: 15px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.button-link {
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  border: 1px solid var(--Gray-300, #d0d5dd);
  background: var(--Base-White, #fff);
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
}

.page-title-bar {
  float: left;
  border-radius: 2px;
}
