import React from 'react';
import PropTypes from 'prop-types';

import { Modal, DateCalendar } from '@/reactComponents/library';
import ProjectStore from '@/reactComponents/stores/project';

import CalendarDay from './CalendarDay';
import { dateFmtStr } from './utils';
import { observer } from 'mobx-react-lite';

const DaysCalendarModal = (props) => {
  const { variant = 'add', open, setOpen, onSubmit, copyFrom } = props;

  const isAdd = variant === 'add';

  const title = isAdd
    ? 'Add Days'
    : copyFrom
    ? `Copy ${copyFrom.toFormat('DD')} to`
    : 'Copy to days';

  const project = ProjectStore.project || {};

  const [dates, setDates] = React.useState([]);
  const handleDateChange = (newValue) => {
    const dateStr = newValue.toFormat(dateFmtStr);

    setDates((prevDays) => {
      if (prevDays.includes(dateStr)) {
        return prevDays.filter((day) => day !== dateStr);
      } else {
        return [...prevDays, dateStr];
      }
    });
  };

  if (!open) return null;

  return (
    <Modal
      title={title}
      open={open}
      setOpen={setOpen}
      onClose={() => setOpen(false)}
      onSubmit={() => {
        setOpen(false);
        onSubmit(dates);
        setDates([]);
      }}
      submitText="Apply"
      onCancel={() => {
        setDates([]);
        setOpen(false);
      }}
    >
      <DateCalendar
        minDate={project.startsAt?.startOf('week').minus({ days: 1 })}
        maxDate={project.endsAt?.endOf('week').minus({ days: 1 })}
        slots={{ day: CalendarDay }}
        onChange={handleDateChange}
        slotProps={{
          day: {
            dates,
          },
        }}
      />
    </Modal>
  );
};

DaysCalendarModal.propTypes = {
  variant: PropTypes.string,
  open: PropTypes.bool.isRequired,
  setOpen: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  copyFrom: PropTypes.object,
};

export default observer(DaysCalendarModal);
