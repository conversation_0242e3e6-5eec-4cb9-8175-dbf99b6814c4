import axios, { type AxiosResponse } from 'axios';
import { coreBaseUrl } from '../utils/constants';

export const enableDocumentTemplate = async (
  documentTemplateId: number,
  productionCompanyId: number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/production-company-document-templates/${documentTemplateId}/enable`;
  const response = await axios.post(
    url,
    { productionCompanyId },
    { withCredentials: true },
  );
  return response;
};

export const disableDocumentTemplate = async (
  documentTemplateId: number,
  productionCompanyId: number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/production-company-document-templates/${documentTemplateId}/disable`;
  const response = await axios.post(
    url,
    { productionCompanyId },
    { withCredentials: true },
  );
  return response;
};
