import React from 'react';
import PropTypes from 'prop-types';

import {
  Box,
  IconButton,
  Modal,
  Text,
  Tooltip,
} from '@/reactComponents/library';

import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import DeleteIcon from '@mui/icons-material/Delete';

import DefaultDaysStore from './store';

import DaysCalendarModal from './DaysCalendarModal';

const ActionsCell = (props) => {
  const { id, row } = props;
  const { date } = row;

  const [copyModalOpen, setCopyModalOpen] = React.useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = React.useState(false);

  const [deleting, setDeleting] = React.useState(false);

  return (
    <Box sx={{ display: 'flex', gap: 1 }}>
      <Tooltip variant="html" placement="top" arrow title="Copy to days">
        <IconButton onClick={() => setCopyModalOpen(true)}>
          <ContentCopyIcon />
        </IconButton>
      </Tooltip>
      <IconButton onClick={() => setDeleteModalOpen(true)}>
        <DeleteIcon />
      </IconButton>
      <DaysCalendarModal
        variant="copy"
        open={copyModalOpen}
        setOpen={setCopyModalOpen}
        onSubmit={(dates) => DefaultDaysStore.copyDays(dates, id)}
        copyFrom={date}
      />
      <Modal
        open={deleteModalOpen}
        setOpen={setDeleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onSubmit={async () => {
          setDeleting(true);
          await DefaultDaysStore.deleteDay(id);
          setDeleteModalOpen(false);
          setDeleting(false);
        }}
        submitText="Delete"
        loading={deleting}
        onCancel={() => {
          setDeleteModalOpen(false);
        }}
      >
        <Text>Delete Day?</Text>
      </Modal>
    </Box>
  );
};

ActionsCell.propTypes = {
  id: PropTypes.number.isRequired,
  row: PropTypes.object.isRequired,
};

export default ActionsCell;
