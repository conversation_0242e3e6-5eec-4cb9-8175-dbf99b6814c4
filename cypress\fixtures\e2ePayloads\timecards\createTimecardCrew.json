{"createdAt": "2025-05-09T10:37:27.971-05:00", "updatedAt": "2025-05-09T10:37:49.423-05:00", "statusId": 2, "requestedChanges": "Production supervisor has made changes to your timecard. Please review and if you agree, approve.", "batchId": null, "gross": null, "grossWithBoxRentalAndMileage": null, "fileId": null, "hourlyExempt": false, "lastDownloadedAt": null, "isRevision": false, "revisionId": null, "isActive": true, "castAndCrewId": null, "submittedById": null, "hireLocationId": null, "rateTypeId": 1, "guarHours": null, "guarRate": null, "isOnCall": false, "isExempt": false, "isHalfDayAllowed": true, "isNdbAllowed": false, "capsPayId": null, "payPremiumOvertime": false, "payrollOpsNote": null, "calculationErrorMessage": null, "calculationWarningMessage": null, "hireLocation": null, "kitRental": null, "mileageForm": null, "reimbursements": [], "status": {"id": 2, "key": "submitted", "name": "Submitted", "description": "The crew member has submitted the timecard for review.", "createdAt": "2025-01-31T09:35:17.758+00:00", "updatedAt": "2025-01-31T09:35:17.758+00:00"}}