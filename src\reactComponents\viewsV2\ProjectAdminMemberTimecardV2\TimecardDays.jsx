import React, { useCallback, useEffect } from 'react';
import PropTypes from 'prop-types';

import {
  Box,
  Accordion,
  AccordionDetails,
  Text,
  Chip,
  IconButton,
} from '@/reactComponents/library';
import { timecardColumns } from './timecardTableUtils';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

import { TimecardContext, set } from './timecardUtils';
import CopyToDaysModal from './CopyToDaysModal';
import {
  TIMECARD_TABLE_FIELDS,
  ADDITIONAL_FIELDS,
  clearGeneralCrewCall,
  handleGridRowUpdate,
  handleActiveDayToggle,
  workStatusCascadingChanges,
  clearAdditionalFieldsForIdleStatus,
  calculateReRateRows,
  adjustYearMonthDay,
} from './timecardTableUtils';
import { applyDayTemplateForRow } from '@/services/project';
import _cloneDeep from 'lodash/cloneDeep';

const NON_UNION_NON_EXEMPT_HIDDEN_FIELDS = ADDITIONAL_FIELDS.filter(
  (f) => f.label !== 'Meal 2',
).reduce((hiddenFields, field) => {
  field.tableFields.forEach((tableField) => {
    hiddenFields[tableField] = true;
  });
  return hiddenFields;
}, {});

const TimecardContextProvider = TimecardContext.Provider;

const TimecardTable = React.lazy(() => import('./TimecardTable'));

const TimecardDays = (props) => {
  const {
    timecard = {},
    dayTemplates,
    updateTimecard,
    workLocations,
    workStatuses,
    occupations,
    workZones,
    timeMinuteIncrement,
    trackingDetails,
    readOnlyMode,
    total,
    member,
    fetchOccupations,
    loadingOccupations,
    occupationsHasNextPage,
    displayErrors,
  } = props;

  const [reRateRows, setReRateRows] = React.useState([]);
  const [expanded, setExpanded] = React.useState(true);
  const [copyToDaysModal, setCopyToDaysModal] = React.useState(false);
  const copyFromDay = React.useRef(null);

  const initialReRateLoad = React.useRef(true);
  useEffect(() => {
    // initial load to populate re-rate rows
    // will need to watch for loading flag in the future if avail
    if (
      timecard?.timecardDays &&
      timecard?.timecardDays.length > 0 &&
      initialReRateLoad.current
    ) {
      initialReRateLoad.current = false;
      const newReRateRows = calculateReRateRows({ timecard });
      setReRateRows(newReRateRows);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timecard.timecardDays]);

  useEffect(() => {
    const newReRateRows = calculateReRateRows({ timecard });
    setReRateRows(newReRateRows);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timecard?.rateType?.key]);

  const tableColumns = React.useMemo(() => {
    const isNonUnion = timecard?.isNonUnion;
    const isExemptIndicator = timecard?.isExemptIndicator;
    if (isNonUnion === false) {
      // is Union
      return timecardColumns;
    } else if (isNonUnion === true) {
      // is Non Union
      if (isExemptIndicator) {
        return timecardColumns.filter(
          (col) => !col.isHidden && col.field !== TIMECARD_TABLE_FIELDS.halfDay,
        );
      } else {
        return timecardColumns.filter((col) => {
          return !NON_UNION_NON_EXEMPT_HIDDEN_FIELDS[col.field];
        });
      }
    }

    return timecardColumns;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timecard?.occupation?.key]);

  const totalLabel = (total ?? 0).toLocaleString('en-US', {
    style: 'currency',
    currency: 'USD',
  });

  const triggerRecalculateAdditionalFields = React.useRef(0);

  const applyDayDefault = React.useCallback(
    (args) => {
      const { parentRowId, api, day } = args;
      if (dayTemplates?.length > 0 && day.id === parentRowId && day.isActive) {
        const newDay = applyDayTemplateForRow(day, dayTemplates);
        for (let key in newDay) {
          day[key] = newDay[key];
        }

        api.updateRows([
          {
            ...day,
          },
        ]);
        triggerRecalculateAdditionalFields.current += 1;
      }
    },
    [dayTemplates],
  );

  const removeReRate = useCallback(
    (args) => {
      const { parentRowId, api, day } = args;

      setReRateRows((prev) =>
        prev.filter((row) => row.parentRowId !== parentRowId),
      );
      if (day && day.id === parentRowId) {
        day.hasRerate = false;
      } else {
        const timecardDays = timecard.timecardDays.map((day) => {
          if (day.id === parentRowId) {
            day.hasRerate = false;
          }
          return day;
        });
        updateTimecard({ timecardDays });
      }
      api.updateRows([
        {
          id: parentRowId,
          hasRerate: false,
        },
      ]);
    },
    [updateTimecard, setReRateRows, timecard.timecardDays],
  );

  const update = React.useCallback(
    (args) => {
      const { field, value, rowId, inArray, isWorkDaysGrid, api, cellMode } =
        args;

      if (isWorkDaysGrid) {
        const timecardDays = timecard.timecardDays.map((day) => {
          if (day.id === rowId) {
            if (inArray) {
              // meals fields
              const row = day;
              const path = field;
              set(row, path, value);
            } else {
              // regular fields
              day[field] = value;
              workStatusCascadingChanges({
                api,
                value,
                rowId,
                day,
                field,
                workZones,
                member,
              });
              clearAdditionalFieldsForIdleStatus({
                api,
                day,
                field,
                rowId,
              });
              clearGeneralCrewCall({
                api,
                day,
                field,
                value,
                rowId,
              });
              handleActiveDayToggle({
                api,
                rowId,
                day,
                value,
                field,
                removeReRate,
                applyDayDefault,
              });
              handleGridRowUpdate({
                api,
                rowId,
                field,
                cellMode,
                value,
              });
            }
          }
          return day;
        });
        updateTimecard({ timecardDays });
      }
    },
    [
      timecard.timecardDays,
      updateTimecard,
      workZones,
      member,
      removeReRate,
      applyDayDefault,
    ],
  );

  const updateReRate = React.useCallback(
    (args) => {
      const { field, value, rowId, parentRowId, api } = args;
      update({
        field,
        value,
        rowId: parentRowId,
        isWorkDaysGrid: true,
      });
      if (field === 'occupation') {
        updateReRate({
          field: 'occupationId',
          value: value.id,
          rowId: parentRowId,
          isWorkDaysGrid: true,
          api: api,
        });
      }
      setReRateRows((prev) => {
        const newRows = [...prev];
        const rowIndex = newRows.findIndex((row) => row.id === rowId);
        if (rowIndex !== -1) {
          newRows[rowIndex][field] = value;
        }
        return newRows;
      });
      api.updateRows([
        {
          id: rowId,
          [field]: value,
        },
      ]);
    },
    [setReRateRows, update],
  );

  const addReRate = useCallback(
    (args) => {
      const {
        parentRowId,
        api,
        // row
      } = args;
      const newReRateRowId = crypto.randomUUID();

      const rateTypeDefault = timecard.rateType;
      const occupationDefault = timecard.occupation;
      const guaranteedHours = timecard.guarHours;
      const rate = timecard.hourlyRate;

      const newReRateRow = {
        id: newReRateRowId,
        parentRowId: parentRowId,
        rateType: rateTypeDefault,
        rate: rate,
        occupation: occupationDefault,
        guaranteedHours: guaranteedHours,
      };
      setReRateRows((prev) => [...prev, newReRateRow]);

      const timecardDays = timecard.timecardDays.map((day) => {
        if (day.id === parentRowId) {
          day.hasRerate = true;
          day.occupation = occupationDefault;
          day.occupationId = occupationDefault?.id;
          day.rateType = rateTypeDefault;
          day.rate = rate;
          day.guaranteedHours = guaranteedHours;
        }
        return day;
      });
      updateTimecard({ timecardDays });
      setTimeout(() => {
        api.updateRows([
          {
            id: parentRowId,
            hasRerate: true,
          },
        ]);
      });
    },
    [setReRateRows, updateTimecard, timecard],
  );

  const copyToDays = (args) => {
    const { selectedDays } = args;
    const copyFromId = copyFromDay.current;
    const from = timecard.timecardDays.find((day) => day.id === copyFromId);

    const timecardDays = timecard.timecardDays.map((day) => {
      if (selectedDays.some((selected) => selected.id === day.id)) {
        const newDay = {
          ..._cloneDeep(from),
          date: day.date,
          id: day.id,
          capsPayId: day.capsPayId,
          comments: day.comments,
          isRentalDay: day.isRentalDay,
        };
        // check holiday
        const copyFromHoliday = from.hasHoliday;
        const copyToDate = day.date?.toISODate();
        const isHoliday = timecard?.holidayDates?.some?.((holiday) => {
          return holiday?.holidayDate?.toISODate() === copyToDate;
        });
        if (copyFromHoliday) {
          newDay.hasHoliday = isHoliday;
        }
        if (!day.isActive && isHoliday) {
          // switch to an active day
          newDay.hasHoliday = true;
        }
        // remove meals id
        newDay.meals.forEach((meal) => {
          delete meal.id;
          delete meal.timecardDayId;
        });
        adjustYearMonthDay(newDay);

        return newDay;
      }
      return day;
    });

    updateTimecard({ timecardDays });
    const newReRateRows = calculateReRateRows({
      timecard: { ...timecard, timecardDays },
    });
    setReRateRows(newReRateRows);
    onCloseCopyToDays();
  };

  const onOpenCopyToDays = (args) => {
    const { rowId } = args;
    setCopyToDaysModal(true);
    copyFromDay.current = rowId;
  };

  const onCloseCopyToDays = () => {
    setCopyToDaysModal(false);
    copyFromDay.current = null;
  };

  const contextValue = {
    timecard,
    update,
    updateReRate,
    addReRate,
    removeReRate,
    applyDayDefault,
    workLocations,
    workStatuses,
    occupations,
    workZones,
    timeMinuteIncrement,
    fetchOccupations,
    loadingOccupations,
    occupationsHasNextPage,
    onOpenCopyToDays,
    readOnlyMode,
  };

  return (
    <TimecardContextProvider value={contextValue}>
      <Accordion expanded={expanded} sx={{ width: '100%' }}>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            p: 2,
            borderBottom: `1px solid`,
            borderColor: expanded ? 'divider' : 'transparent',
          }}
        >
          <IconButton onClick={() => setExpanded((x) => !x)}>
            <ExpandMoreIcon
              sx={{
                color: 'text.primary',
                transform: expanded ? 'rotate(180deg)' : '',
              }}
            />
          </IconButton>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Text variant="lgStrong">Work Days</Text>
            <Chip label={`Total: ${totalLabel}`} variant="outlined" />
          </Box>
        </Box>
        <AccordionDetails>
          {expanded && ( // expanded check id required otherwise it load the component even if it is not expanded
            //              not needed here since this will be expanded on default but FYI
            <React.Suspense
              fallback={<Box sx={{ minHeight: '480px' }}>Loading...</Box>}
            >
              <TimecardTable
                timecard={timecard}
                triggerRecalculateAdditionalFields={
                  triggerRecalculateAdditionalFields
                }
                reRateRows={reRateRows}
                trackingDetails={trackingDetails}
                tableColumns={tableColumns}
                updateTimecard={updateTimecard}
                displayErrors={displayErrors}
                readOnlyMode={readOnlyMode}
              />
            </React.Suspense>
          )}
        </AccordionDetails>
      </Accordion>
      {copyToDaysModal && (
        <CopyToDaysModal
          open={copyToDaysModal}
          timecard={timecard}
          reRateRows={reRateRows}
          copyFromDay={copyFromDay.current}
          copyToDays={copyToDays}
          onClose={onCloseCopyToDays}
        />
      )}
    </TimecardContextProvider>
  );
};

TimecardDays.propTypes = {
  timecard: PropTypes.object.isRequired,
  dayTemplates: PropTypes.array.isRequired,
  trackingDetails: PropTypes.object.isRequired,
  updateTimecard: PropTypes.func.isRequired,
  workLocations: PropTypes.array.isRequired,
  workStatuses: PropTypes.array.isRequired,
  workZones: PropTypes.array.isRequired,
  timeMinuteIncrement: PropTypes.number,
  total: PropTypes.number,
  occupations: PropTypes.array,
  member: PropTypes.object.isRequired,
  fetchOccupations: PropTypes.func.isRequired,
  loadingOccupations: PropTypes.bool,
  occupationsHasNextPage: PropTypes.bool,
  displayErrors: PropTypes.object.isRequired,
  readOnlyMode: PropTypes.bool,
};

export default TimecardDays;
