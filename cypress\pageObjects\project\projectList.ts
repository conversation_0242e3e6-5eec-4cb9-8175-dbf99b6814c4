import { DateFormatter } from '../../utils/DateFormatter';

export default class ProjectListPageObject {
  createProjectButton: string = '[data-testid="create-project-btn"]';
  projectName: string = '[data-testid="project-name-{{id}}"]';
  projectCompany: string = '[data-testid="project-company-{{id}}"]';
  projectType: string = '[data-testid="project-type-{{id}}"]';
  projectEndsOn: string = '[data-testid="project-endsOn-{{id}}"]';
  projectNumber: string = '[data-testid="project-number-{{id}}"]';

  clickCreateProjectButton() {
    cy.get(this.createProjectButton).click();
  }

  validateProjectOnList(
    projectId: string,
    projectName: string,
    projectNumber: string,
    endsOn: Date,
    company: string,
    projectType: string,
  ) {
    cy.get(this.projectName.replace('{{id}}', projectId)).should(
      'have.text',
      projectName,
    );
    cy.get(this.projectCompany.replace('{{id}}', projectId)).should(
      'include.text',
      company,
    );
    cy.get(this.projectType.replace('{{id}}', projectId)).should(
      'include.text',
      projectType,
    );
    cy.get(this.projectEndsOn.replace('{{id}}', projectId)).should(
      'include.text',
      `Ends On ${DateFormatter.formatDateMMDD(endsOn)}`,
    );
    cy.get(this.projectNumber.replace('{{id}}', projectId)).should(
      'have.text',
      projectNumber,
    );
  }

  goToProjectDetails(projectId: string) {
    cy.get(this.projectName.replace('{{id}}', projectId)).click();
  }
}
