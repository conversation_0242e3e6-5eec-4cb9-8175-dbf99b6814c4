<template>
  <div
    class="relative flex min-h-screen flex-col justify-center overflow-hidden"
  >
    <div
      class="m-auto max-w-screen-lg space-y-8 divide-y divide-gray-200 dark:divide-gray-400 mt-5"
    >
      <div>
        <div>
          <h3
            class="text-3xl mb-3 mt-3 font-medium leading-6"
            data-testid="user-name-text"
          >
            {{ user?.firstName }} {{ user?.lastName }}
          </h3>
        </div>
        <div class="mt-6 grid grid-cols-1 gap-x-4 sm:grid-cols-6">
          <div class="sm:col-span-6">
            <TextInput
              label="First Name"
              v-model="user.firstName"
              type="name"
              auto-complete="off"
              data-testid="user-firstName-input"
            />
          </div>
          <div class="sm:col-span-6">
            <TextInput
              label="Middle Name"
              v-model="user.middleName"
              type="name"
              auto-complete="off"
              data-testid="user-middleName-input"
            />
          </div>
          <div class="sm:col-span-6">
            <TextInput
              label="Last Name"
              v-model="user.lastName"
              type="name"
              auto-complete="off"
              data-testid="user-lastName-input"
            />
          </div>
          <div class="sm:col-span-6">
            <TextInput
              label="Email Address"
              v-model="user.email"
              data-testid="user-email-input"
            />
          </div>
          <div class="sm:col-span-6">
            <TextInput
              label="Mobile Phone"
              :model-value="user.phone"
              inputmode="tel"
              auto-complete="tel"
              type="phone"
              data-testid="user-mobile-input"
              @update:rawValue="user.phone = $event"
            />
          </div>
          <div class="sm:col-span-6">
            <Dropdown
              @update:modelValue="updateRole"
              label="Role"
              :model-value="roles.find((role:any) => user.roleId === role.id)"
              :menu-items="roles"
              display-name="name"
              data-testid="user-roles-dropdown"
            />
          </div>
        </div>

        <div class="pt-5 mb-5 mx-2">
          <div class="flex justify-center">
            <Button
              class="w-48 mr-1"
              @click="cancel"
              color="gray"
              data-testid="user-cancel-btn"
              >Cancel</Button
            >
            <Button
              class="w-48 ml-1"
              @click="updateUser"
              :loading="loading"
              data-testid="user-update-btn"
              >{{ editMode ? 'Update' : 'Create' }}</Button
            >
          </div>
        </div>
        <div class="py-5" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Button from '@/components/library/Button.vue';
import Dropdown from '@/components/library/Dropdown.vue';
import TextInput from '@/components/library/TextInput.vue';
import SnackbarStore from '@/reactComponents/stores/snackbar';

import { getRoles } from '@/services/role';
import { getUser, updateUser } from '@/services/users';
import type Role from '@/types/Role';
import type User from '@/types/User';
import type { SnackType } from '@/types/Snackbar';
import axios from 'axios';
import { defineComponent } from 'vue';

export default defineComponent({
  props: {
    editMode: {
      type: Boolean,
      default: false,
    },
    navigate: {
      type: Function,
      required: true,
    },
    route: {
      type: Object,
      required: true,
    },
  },
  components: {
    TextInput,
    Button,
    Dropdown,
  },
  data() {
    return {
      roles: [] as Role[],
      user: {} as User,
      taxClassifications: [],
      paydays: [],
      paydayFrequencies: [],
      loading: false,
    };
  },
  methods: {
    triggerSnackbar(msg: string, duration = 2500, type: SnackType = 'success') {
      SnackbarStore.triggerSnackbar(msg, duration, type);
    },
    async updateUser() {
      if (this.loading) return;
      this.loading = true;
      try {
        if (this.editMode) {
          await updateUser(parseInt(this.route.params.id as string), this.user);

          this.navigate(`/users/${this.user.id}`);
          this.triggerSnackbar('User updated successfully');
        }
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          this.triggerSnackbar(msg, 2500, 'error');
        } else {
          this.triggerSnackbar(err as string, 2500, 'error');
        }
      }
      this.loading = false;
    },
    updateRole(role: Role) {
      this.user.roleId = role.id;
      this.user.role = role;
    },
    cancel() {
      this.navigate('/users');
    },
  },
  async mounted() {
    if (this.editMode) {
      const { data: roles } = await getRoles();
      this.roles = roles;
      const { data: userDetail } = await getUser(
        this.route.params.id as unknown as number,
      );
      this.user = userDetail;
    }
  },
});
</script>
