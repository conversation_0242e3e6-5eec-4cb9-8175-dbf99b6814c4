<template>
  <Popover class="relative">
    <PopoverButton
      class="relative p-1 rounded-full focus:outline-none focus:ring-0 focus:ring-offset-1 focus:ring-gray-100 text-opacity-100"
    >
      <span class="sr-only">View notifications</span>
      <Icon name="bell-icon" />
      <span
        v-if="hasUnreadNotifications"
        class="absolute top-0 right-n4 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-red-100 bg-red-600 rounded-full z-50"
      >
        {{ unreadNotificationCount }}
      </span>
    </PopoverButton>

    <transition
      enter-active-class="transition ease-out duration-200"
      enter-from-class="opacity-0 translate-y-1"
      enter-to-class="opacity-100 translate-y-0"
      leave-active-class="transition ease-in duration-150"
      leave-from-class="opacity-100 translate-y-0"
      leave-to-class="opacity-0 translate-y-1"
    >
      <PopoverPanel
        class="fixed sm:absolute z-10 mt-5 flex w-screen max-w-max px-4 transform left-1/2 -translate-x-1/2 sm:left-auto sm:right-0 sm:-translate-x-0"
      >
        <div
          class="w-full sm:max-w-md flex-auto overflow-hidden rounded-3xl bg-white dark:bg-gray-700 text-sm leading-6 shadow-lg ring-1 ring-gray-900/5 dark:ring-gray-700/5"
        >
          <div
            class="overflow-y-auto"
            :style="{ maxHeight: 'calc(100vh - 76px)' }"
          >
            <div class="flex align-middle justify-between pt-4 px-8">
              <h3 class="text-lg font-bold mt-1">Notifications</h3>
              <button
                @click="markAllAsRead"
                class="flex align-middle px-4 py-2 rounded-xl cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
              >
                <EnvelopeOpenIcon
                  class="h-4 w-4 mr-2 mt-1 text-blue-500 group-hover:text-blue-600"
                />
                Mark All Read
              </button>
            </div>
            <div class="px-4 pt-4">
              <NotificationCenterItem
                v-for="notification in notifications"
                :key="notification._id"
                :notification="notification"
                @read="markNotificationsAsRead(notification._id)"
                @delete="deleteNotification(notification._id)"
              />
            </div>
            <div class="p-4 flex">
              <button
                @click="prevPage"
                :disabled="currentPage === 0"
                class="flex-1 px-4 pb-2 pt-3 rounded-bl-xl cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
              >
                Previous
              </button>
              <button
                @click="nextPage"
                :disabled="!hasMore"
                class="flex-1 px-4 pb-2 pt-3 rounded-br-xl cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      </PopoverPanel>
    </transition>
  </Popover>
</template>

<script lang="ts">
import Icon from '@/components/Icon.vue';
import NotificationCenterItem from '@/components/NotificationCenterItem.vue';
import auth from '@/reactComponents/stores/auth';
// import { useAuthStore } from '@/stores/auth'
import type { NovuNotification } from '@/types/Novu';
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/vue';
import { BellIcon, EnvelopeOpenIcon } from '@heroicons/vue/24/outline';
import { HeadlessService, type UpdateResult } from '@novu/headless';
import { autorun } from 'mobx';

const PAGE_SIZE = 5;

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  components: {
    Popover,
    PopoverButton,
    PopoverPanel,
    BellIcon,
    EnvelopeOpenIcon,
    NotificationCenterItem,
    Icon,
  },
  data() {
    return {
      headlessService: null as unknown as HeadlessService,
      unreadNotificationCount: 0 as number,
      notifications: [] as NovuNotification[],
      currentPage: 0 as number,
      hasMore: true as boolean,
    };
  },
  computed: {
    // ...mapState(useAuthStore, ['getUser', 'isLoggedIn']),
    getUser() {
      return auth.getUser;
    },
    isLoggedIn() {
      return auth.isLoggedIn;
    },
    hasUnreadNotifications(): boolean {
      return this.unreadNotificationCount > 0;
    },
  },
  mounted() {
    autorun(() => {
      if (this.getUser) {
        this.initializeHeadlessService();
      }
    });
  },
  methods: {
    initializeHeadlessService() {
      const applicationIdentifier = window.location.origin.includes('app')
        ? 'wKP0Q6C2voUv'
        : '-p2zOfvgt66g';
      const subscriberId = this.getUser?.id?.toString();
      const headlessService = new HeadlessService({
        applicationIdentifier,
        subscriberId,
      });

      headlessService.initializeSession({
        listener: (result: any) => {},
        onSuccess: (session: any) => {
          this.headlessService = headlessService;
          this.fetchNotifications();
          this.getUnreadNotifications();
          this.listenUnreadCountChange();
        },
        onError: console.error,
      });
    },
    fetchNotifications() {
      if (!this.headlessService) {
        return;
      }
      let successCount = 0;
      this.headlessService.fetchNotifications({
        listener: ({
          data,
          error,
          isError,
          isFetching,
          isLoading,
          status,
        }) => {},
        onSuccess: (response) => {
          if (successCount > 0) {
            return;
          }
          this.notifications = response.data as unknown as NovuNotification[];
          this.hasMore = response.hasMore;
          successCount++;
        },
        page: this.currentPage,
        query: { limit: PAGE_SIZE },
      });
    },
    markNotificationsAsRead(messageIds: string[] | string): void {
      if (!Array.isArray(messageIds)) {
        messageIds = [messageIds];
      }
      if (!this.headlessService) {
        return;
      }
      this.headlessService.markNotificationsAsRead({
        messageId: messageIds,
        listener: (result: any) => {},
        onSuccess: (message: any) => {},
        onError: (error: any) => {
          console.error('Error marking notifications as read:', error);
        },
      });
    },
    markAllAsRead(): void {
      if (!this.headlessService) {
        return;
      }
      this.headlessService.markAllMessagesAsRead({
        listener: (result: UpdateResult<number, unknown, undefined>) => {},
        onSuccess: (count: number) => {},
        onError: (error: unknown) => {},
      });
    },
    deleteNotification(messageId: string): void {
      if (!this.headlessService) {
        return;
      }
      this.headlessService.removeNotification({
        messageId,
        listener: (result: any) => {},
        onSuccess: (message: any) => {
          this.fetchNotifications();
        },
        onError: (error: any) => {
          console.error(error);
        },
      });
    },
    markAllMessagesAsRead(feedId: string): void {
      if (!this.headlessService) {
        return;
      }
      this.headlessService.markAllMessagesAsRead({
        listener: (result: any) => {},
        onError: (error: any) => {
          console.error('Error marking all messages as read:', error);
        },
        feedId, // Pass the feed ID here, it can be an array or a single ID
      });
    },
    getUnreadNotifications() {
      if (!this.headlessService) {
        return;
      }
      this.headlessService.fetchUnreadCount({
        listener: (fetchUnread: any) => {},
        onSuccess: (data: { count: number }) => {
          this.unreadNotificationCount = data.count!;
        },
        onError: (error: any) => {
          console.error(error);
        },
      });
    },
    listenUnreadCountChange() {
      if (!this.headlessService) {
        return;
      }
      this.headlessService.listenUnreadCountChange({
        listener: (unreadCount: number) => {
          this.unreadNotificationCount = unreadCount;
          this.fetchNotifications();
        },
      });
    },
    nextPage() {
      if (!this.hasMore) {
        return;
      }
      this.currentPage++;
      this.fetchNotifications();
    },

    prevPage() {
      if (this.currentPage === 0) {
        return;
      }
      this.currentPage--;
      this.fetchNotifications();
    },
  },
};
</script>
