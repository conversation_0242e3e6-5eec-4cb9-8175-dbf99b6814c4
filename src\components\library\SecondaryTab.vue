<template>
  <span
    :class="[
      tab.key === currentTab?.key
        ? 'bg-white dark:bg-gray-700 text-pink-700  dark:text-pink-100'
        : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-700',
      'whitespace-nowrap rounded-md py-2 px-3 text-base leading-6 font-semibold cursor-pointer select-none mr-4',
    ]"
    @click="handleClick"
  >
    {{ tab.label }}
  </span>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
export default defineComponent({
  props: {
    tab: {
      type: Object,
      required: true,
    },
    currentTab: {
      type: Object,
      default: () => ({}),
    },
    tabClick: {
      type: Function,
      required: false,
    },
  },
  name: 'SecondaryTab',
  methods: {
    handleClick() {
      if (this.tabClick) {
        this.tabClick(this.tab);
      }
      this.$emit('tabClick', this.tab);
    },
  },
});
</script>

<style></style>
