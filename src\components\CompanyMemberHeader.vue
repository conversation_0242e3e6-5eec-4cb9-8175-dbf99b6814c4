<template>
  <div>
    <div class="flex items-center space-x-5 py-2">
      <div class="flex-shrink-0">
        <div class="relative">
          <Avatar
            :firstName="member?.user?.firstName"
            :lastName="member?.user?.lastName"
            size="lg"
            bgColor="white"
          />
          <span
            class="absolute inset-0 rounded-full shadow-inner"
            aria-hidden="true"
          />
        </div>
      </div>
      <div>
        <h1 class="text-2xl font-bold">
          {{ fullName }}
        </h1>
        <h2 class="text-xl text-zinc-700 dark:text-zinc-400">
          {{ phone }}
        </h2>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Avatar from '@/components/library/Avatar.vue';
import Button from '@/components/library/Button.vue';
import type ProductionCompanyMember from '@/types/ProductionCompanyMember';
import { defineComponent, type PropType } from 'vue';

export default defineComponent({
  components: {
    Button,
    Avatar,
  },
  props: {
    member: {
      type: Object as PropType<ProductionCompanyMember>,
      required: true,
    },
  },
  data() {
    return {};
  },
  computed: {
    fullName(): string {
      const { firstName, lastName } = this.member?.user || {};
      return `${firstName} ${lastName}`;
    },
    phone(): string {
      return this.member?.user?.phone || '';
    },
  },
});
</script>
