<template>
  <div>
    <label
      v-if="label"
      for="email"
      class="block text-xs font-xs text-gray-700 dark:text-gray-400 pb-1"
    >
      {{ label }} <span v-if="required" class="text-red-600">*</span>
    </label>
    <Menu
      v-if="!disabled"
      as="div"
      class="relative inline-block text-left dark:bg-gray-800 border-none"
      style="width: 100%"
      :disabled="disabled || loading"
      v-slot="{ open }"
    >
      <!-- Used to capture the open value in our data -->
      <template v-if="open !== isOpen">
        {{ captureOpenState(open) }}
      </template>

      <MenuButton
        class="inline-flex w-full justify-between rounded-md border border-gray-300 dark:border-gray-500 bg-white dark:bg-gray-700 px-3 py-[0.6rem] md:py-1.5 text-sm font-sm text-gray-800 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 light:focus:ring-indigo-500 focus:ring-offset-2 focus:ring-offset-gray-100 dark:focus:ring-offset-gray-300"
        :disabled="disabled || loading"
      >
        <div v-if="loading" class="animate-pulse">Loading...</div>
        <div v-else>
          <slot v-if="selectedItem" name="label" :value="selectedItem" />
          <div v-if="!$slots.label">
            {{ selectedItem?.[displayName] || placeholder }}
          </div>
          <div v-else-if="!selectedItem">
            {{ placeholder }}
          </div>
        </div>
        <ChevronDownIcon class="h-4 w-4 mt-0.5" aria-hidden="true" />
      </MenuButton>

      <transition
        enter-active-class="transition ease-out duration-100"
        enter-from-class="transform opacity-0 scale-95"
        enter-to-class="transform opacity-100 scale-100"
        leave-active-class="transition ease-in duration-75"
        leave-from-class="transform opacity-100 scale-100"
        leave-to-class="transform opacity-0 scale-95"
      >
        <MenuItems
          ref="menuItems"
          class="absolute left-0 z-10 my-2 w-56 max-h-64 origin-top-right rounded-md bg-white dark:bg-gray-700 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none overflow-auto"
          :class="positionClass"
        >
          <div class="py-1">
            <MenuItem v-if="!menuItems.length" disabled>
              <span
                class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-400"
              >
                No items found
              </span>
            </MenuItem>
            <MenuItem
              v-for="(item, idx) in menuItems"
              :key="idx"
              v-slot="{ active }"
              :disabled="disabled"
            >
              <span
                class="cursor-pointer justify-between w-full flex items-center"
                @click="update(item)"
                :class="[
                  active
                    ? 'bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-gray-200'
                    : 'text-gray-700 dark:text-gray-400',
                  'block px-4 py-2 text-sm',
                  checkTheStatus(item, selectedItem)
                    ? 'bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-gray-200'
                    : 'text-gray-700 dark:text-gray-400',
                ]"
              >
                <slot name="item" :value="item" />
                <span v-if="!$slots.item">{{
                  (item as any)?.[displayName]
                }}</span>
                <Icon
                  v-if="checkTheStatus(item, selectedItem)"
                  name="dropdown-check"
                  class="inline-flex w-6 h-4 stroke-indigo-600 dark:stroke-indigo-50"
                />
              </span>
            </MenuItem>
          </div>
        </MenuItems>
      </transition>
    </Menu>
    <TextInput
      v-else
      :disabled="disabled"
      :model-value="selected"
      :required="required"
    />
    <p v-if="errors" class="mt-2 pb-1 text-xs text-red-600" id="email-error">
      Your password must be less than 4 characters.
    </p>
    <p v-else class="pb-4"></p>
  </div>
</template>

<script lang="ts">
import Icon from '@/components/Icon.vue';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
import { ChevronDownIcon } from '@heroicons/vue/24/outline';
import type { PropType } from 'vue';
import { defineComponent } from 'vue';
import TextInput from './TextInput.vue';

export default defineComponent({
  components: {
    Menu,
    MenuButton,
    MenuItem,
    MenuItems,
    ChevronDownIcon,
    TextInput,
    Icon,
  },
  emits: ['update:modelValue', 'errors', 'change'],
  props: {
    modelValue: {
      type: Object as PropType<any>,
      default: () => {},
    },
    menuItems: {
      type: Array,
      default() {
        return [];
      },
    },
    label: {
      type: String,
    },
    errors: {
      type: [],
      default: () => null,
    },
    placeholder: {
      type: String,
      default: 'Select',
    },
    displayName: {
      type: String,
      default: 'label',
    },
    valueName: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    required: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isOpen: false,
      isOff: {
        top: false,
        right: false,
        bottom: false,
        left: false,
      },
    };
  },
  watch: {
    isOpen: {
      handler() {
        if (!this.isOpen) {
          this.isOff = {
            top: false,
            right: false,
            bottom: false,
            left: false,
          };
        }
        this.$nextTick(() => {
          this.isOff.top = this.checkIsOffTop();
          this.isOff.right = this.checkIsOffRight();
          this.isOff.left = this.checkIsOffLeft();
          this.isOff.bottom = this.checkIsOffBottom();
        });
      },
      immediate: true,
    },
  },
  computed: {
    selectedItem() {
      if (this.valueName) {
        return this.menuItems.find(
          (item: any) => item[this.valueName] === this.modelValue,
        );
      }
      return this.modelValue;
    },
    selected() {
      return this.selectedItem?.[this.displayName] || '';
    },
    menuItemsBoundingBox(): any {
      const element = (this.$refs.menuItems as any)?.$el;

      if (!element) {
        return null;
      }
      return element.getBoundingClientRect();
    },
    positionClass() {
      const { top, right, bottom, left } = this.isOff;
      let positionClasses = [];

      if (bottom) {
        // Align bottom of MenuItems with the top of MenuButton
        positionClasses.push('bottom-full top-auto');
      } else {
        // Default: Align top of MenuItems with the bottom of MenuButton
        positionClasses.push('top-full');
      }

      if (left && right) {
        // If it's off on both sides, you might want to handle this differently
        positionClasses.push('left-0 right-0');
      } else if (left) {
        // Align left edge with the left edge of the button
        positionClasses.push('left-0');
      } else if (right) {
        // Align right edge with the right edge of the button
        positionClasses.push('right-0');
      }

      return positionClasses.join(' ');
    },
  },
  methods: {
    update(item: any) {
      if (this.valueName) {
        item = item[this.valueName];
      }
      this.$emit('update:modelValue', item);
      this.$emit('change');
    },
    captureOpenState(open: boolean) {
      this.isOpen = open;
    },
    checkIsOffTop() {
      const rect = this.menuItemsBoundingBox;
      return rect && rect.top < 0;
    },
    checkIsOffRight() {
      const rect = this.menuItemsBoundingBox;
      return rect && rect.right > window.innerWidth;
    },
    checkIsOffLeft() {
      const rect = this.menuItemsBoundingBox;
      return rect && rect.left < 0;
    },
    checkIsOffBottom() {
      const rect = this.menuItemsBoundingBox;
      return rect && rect.bottom > window.innerHeight;
    },
    checkTheStatus(item: any, selectedItem: any) {
      const newItem = { ...item };
      const newSelectedItem = { ...selectedItem };
      const isNested = this.displayName.includes('.');
      if (isNested) {
        const splitNestedProperty = this.displayName.split('.');

        let newItemValue = newItem;
        for (const eachProp of splitNestedProperty) {
          if (newItemValue === undefined) break;
          newItemValue = newItemValue[eachProp];
        }
        let newSelectedItemValue = newSelectedItem;
        for (const eachProp of splitNestedProperty) {
          if (newSelectedItemValue === undefined) break;
          newSelectedItemValue = newSelectedItemValue[eachProp];
        }

        return newItemValue === newSelectedItemValue;
      }
      return newItem[this.displayName] === newSelectedItem[this.displayName];
    },
  },
});
</script>
