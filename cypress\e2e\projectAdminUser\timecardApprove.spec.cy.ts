/// <reference types="cypress" />

import BatchModals from '../../pageObjects/timecards/batchModals';
import TimecardDetails from '../../pageObjects/timecards/timecardDetails';
import { createTimecardFlowReimbursement } from '../../support/apiFlows/createTimecardFlowReimbursement';
import { deleteTimecard } from '../../support/apiHelpers';
import {
  interceptGetApprovedTimecard,
  interceptProjectTimecards,
} from '../../support/apiTimecardInterceptors';

describe('User Project Admin - Timecard Approve and modify', () => {
  const batchModals = new BatchModals();
  const timecardDetails = new TimecardDetails();

  beforeEach(() => {
    cy.loginWithSession(
      Cypress.env('PHONE_NUMBER_PROJECT_ADMIN'),
      Cypress.env('OTC_CODE'),
      Cypress.env('BASE_URL'),
      'project-admin',
    );

    const [firstName, lastName] = Cypress.env('employeeName').split(' ');
    createTimecardFlowReimbursement(firstName, lastName);
  });

  afterEach(() => {
    // ----------- Cleanup: Delete the created timecard -----------
    cy.log('Cleanup: Delete the created timecard');
    deleteTimecard(Cypress.env('timecardId'));
  });

  it('Verify Project Admin is able to Approve the timecard by modifying Timecard line items', () => {
    // Navigate to project and open timecard section
    cy.visit(
      `${Cypress.env('BASE_URL')}projects/${Cypress.env(
        'projectHashId',
      )}/admin/time`,
    );

    //get unbatched timecards
    batchModals.unbatchedTimecard();
    interceptProjectTimecards(Cypress.env('projectId'));

    cy.wait('@getTimecards').then(() => {
      timecardDetails.navigateToTimecard(Cypress.env('timecardId'));
      cy.log('Timecard Day ID:', Cypress.env('timecardDayIds')?.[2]);
      timecardDetails.fillOutTimecardDetails(
        Cypress.env('timecardDayIds')?.[2],
      );
      timecardDetails.save();
      timecardDetails.approve();

      cy.signatureApproval();

      // Validate the final timecard status is "Approved"
      interceptGetApprovedTimecard();
      cy.wait('@getApprovedTimecard').then((getIntercept): void => {
        const statusName: string = getIntercept.response?.body?.status?.name;
        cy.log(`Final timecard status: ${statusName}`);
        expect(statusName).to.eq('Approved');
        cy.log(
          'Success: Verify Project Admin is able to Approve the timecard by modifying Timecard line items',
        );
      });
    });
  });

  it('Verify Project Admin is able to Approve the timecard by modifying Reimbursements', () => {
    // Navigate to project and open timecard section
    cy.visit(
      `${Cypress.env('BASE_URL')}projects/${Cypress.env(
        'projectHashId',
      )}/admin/time`,
    );

    //get unbatched timecards
    batchModals.unbatchedTimecard();
    interceptProjectTimecards(Cypress.env('projectId'));

    cy.wait('@getTimecards').then(() => {
      timecardDetails.navigateToTimecard(Cypress.env('timecardId'));
      timecardDetails.fillOutReimbursementDetails();
      timecardDetails.save();
      timecardDetails.approve();
      cy.signatureApproval();

      // Validate the final timecard status is "Approved"
      interceptGetApprovedTimecard();
      cy.wait('@getApprovedTimecard').then((getIntercept): void => {
        const statusName: string = getIntercept.response?.body?.status?.name;
        cy.log(`Final timecard status: ${statusName}`);
        expect(statusName).to.eq('Approved');
        cy.log(
          'Success: Project Admin is able to Approve the timecard by modifying Reimbursements',
        );
      });
    });
  });
});
