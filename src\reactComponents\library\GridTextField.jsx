/* eslint-disable react/prop-types */
import React from 'react';

import { TextInput } from '@/reactComponents/library';
import { useTheme } from '@mui/material/styles';

const GridTextField = (props) => {
  let { sx = {}, InputProps = {}, ...rest } = props;

  const { palette } = useTheme();

  if (!InputProps) InputProps = {};
  if (!InputProps.sx) InputProps.sx = {};

  InputProps.disableUnderline = true;
  InputProps.sx.fontSize = '14px';

  const gridSx = {
    ...sx,
    '&:focus-within': {
      '& .MuiInput-root, & .gridInput': {
        border: `2px solid ${palette.pink[500]} !important`,
        textDecoration: 'none',
      },
    },
  };

  return (
    <TextInput
      {...rest}
      sx={gridSx}
      InputProps={InputProps}
      variant="standard"
    />
  );
};

export default GridTextField;
