import { OktaAuth } from '@okta/okta-auth-js';

class Client {
  static DEFAULT_ERROR_CODE = 'E0000000';
  okta = null;

  constructor(config = {}) {
    this.config = {
      issuer: config.issuer,
      clientId: config.clientId,
      redirectUri: window.location.origin,
      scopes: ['openid', 'profile', 'email'],
      tokenManager: {
        storage: 'sessionStorage',
      },
    };

    this.okta = this.loadClient();
  }

  async sessionExists() {
    return await this.okta.session.exists();
  }

  async authenticate(params = {}) {
    return await this.okta.idx.authenticate(params);
  }

  async proceed(params) {
    return await this.okta.idx.proceed(params);
  }

  async cancel() {
    return await this.okta.idx.cancel();
  }

  async logout() {
    await this.okta.revokeAccessToken(); // strongly recommended
    await this.okta.revokeRefreshToken(); // strongly recommended
    await this.okta.closeSession();
    sessionStorage.removeItem('okta-token-storage');
    sessionStorage.removeItem('okta-transaction-storage');
  }

  async getTokens() {
    const sessionActive = await this.sessionExists();
    if (!sessionActive) return null;

    const tokens = await this.okta.tokenManager.getTokens();
    if (tokens?.accessToken && tokens?.idToken) return tokens;

    const res = await this.okta.token.getWithoutPrompt();
    this.okta.tokenManager.setTokens(res.tokens);
    return res.tokens;
  }

  async verifyTokens() {
    if (!(await this.sessionExists())) return false;

    const tokens = await this.getTokens();
    if (!tokens?.accessToken) return false;

    try {
      const verified = await this.okta.token.verify(tokens.idToken, {
        clientId: this.okta.options.clientId,
      });
      if (verified) {
        this.okta.authStateManager.updateAuthState();
      }
    } catch (err) {
      console.warn('verifyTokens:error', err.message);
      return false;
    }

    return true;
  }

  async getAuthState() {
    return await this.okta.authStateManager.getAuthState();
  }

  async getUserInfo() {
    return await this.okta.token.getUserInfo();
  }

  async reload() {
    await this.cancel();
    this.okta = this.loadClient();
  }

  loadClient() {
    return new OktaAuth(this.config);
  }

  canProceed() {
    return this.okta.idx.canProceed();
  }

  responseErrors(response) {
    const errors = response?.messages?.filter((msg) => msg.class === 'ERROR');
    const code = (e) =>
      e?.i18n?.key?.replace(/^errors\./, '') || this.DEFAULT_ERROR_CODE;

    return (errors || []).map((e) => new Error(`[${code(e)}] ${e.message}`));
  }
}

export default Client;
