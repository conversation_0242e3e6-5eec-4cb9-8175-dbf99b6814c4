import { applyPureVueInReact } from 'veaury';
import UserDashboardVue from '@/views/users/UserDashboardView.vue';
import { useAuth } from '@/reactComponents/AppHooks';
import { useOutletContext } from 'react-router';

const ReactUserDashboardView = applyPureVueInReact(UserDashboardVue);

const UserDashboardView = () => {
  useAuth();

  const { user, roles } = useOutletContext();

  return <ReactUserDashboardView user={user} roles={roles} />;
};

export default UserDashboardView;
