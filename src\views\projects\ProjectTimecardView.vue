<template>
  <div class="max-w-lg mx-auto">
    <router-view v-slot="{ Component }">
      <component
        v-if="project?.id"
        :project="project"
        :timecard="timecard"
        :project-member="projectMember"
        @refresh="load"
        @componentLoaded="componentInitialDataLoaded"
        :edit-disabled="editDisabled"
        ref="view"
        :is="Component"
      />
    </router-view>
    <div
      v-if="loaded"
      class="fixed bottom-0 left-0 z-10 flex mx-auto w-full pb-5 pt-2 py-2 px-5 bg-gray-50 dark:bg-gray-700"
    >
      <div class="container mx-auto">
        <div class="flex justify-center space-x-2 pt-3">
          <Button
            v-if="!isCompleteStage && !editDisabled"
            class="max-w-sm"
            color="gray"
            @click="back"
          >
            <span class="font-semibold">Back</span>
          </Button>
          <Button
            class="max-w-sm"
            :color="isCompleteStage ? 'main' : 'secondary'"
            @click="exit"
            :loading="saveAndExitLoading"
            :disabled="submitDisabled"
          >
            <span class="font-semibold">{{ exitText }}</span>
          </Button>
          <Button
            v-if="!isCompleteStage && !editDisabled"
            class="max-w-sm"
            @click="saveAndContinue"
            color="primary"
            :loading="saveAndContinueLoading"
            :disabled="submitDisabled"
          >
            <span class="font-semibold">{{ continueText }}</span>
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// already converted to react using Outlet and achieve two way binding with ref and useImperativeHandle
import Button from '@/components/library/Button.vue';
import { getProjectMemberById } from '@/services/project-members';
import { getTimecard } from '@/services/timecards';
import type Project from '@/types/Project';
import type ProjectMember from '@/types/ProjectMember';
import type Timecard from '@/types/Timecard';
import { TimecardStatusId } from '@/utils/enum';
import { computed, onMounted, ref, type Ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const props = defineProps<{
  project: Project;
}>();

const route = useRoute();
const router = useRouter();

const timecard: Ref<Timecard> = ref({} as Timecard);
const projectMember: Ref<ProjectMember> = ref({} as ProjectMember);
const loaded: Ref<boolean> = ref(false);
const editDisabled: Ref<boolean> = ref(false);
const saveAndExitLoading: Ref<boolean> = ref(false);
const saveAndContinueLoading: Ref<boolean> = ref(false);
const view = ref(null);
const componentDataLoaded: Ref<boolean> = ref(false);

const exitText = computed(() => {
  if (editDisabled.value) {
    return 'Exit';
  }
  return 'Save & Exit';
});

const isCompleteStage = computed(() => {
  return route.name === 'project-timecard-complete';
});

const continueText = computed(() => {
  if (editDisabled.value) {
    if (route.name === 'project-timecard-complete') {
      return 'Return';
    }
    return 'Continue';
  }
  if (route.name === 'project-timecard-review') {
    return 'Submit';
  }
  if (route.name === 'project-timecard-complete') {
    return 'Return';
  }
  return 'Save & Continue';
});

const submitDisabled = computed(() => {
  if (editDisabled.value) {
    return false;
  }
  return !componentDataLoaded.value;
});

const exit = async () => {
  saveAndExitLoading.value = true;
  try {
    if (!isCompleteStage.value) {
      await (view.value as any)?.save();
    }
    router.push({
      name: 'project-timecards',
      params: { hashId: props.project.hashId },
    });
  } catch (err) {
    console.warn('Exit failed', err);
  }
  saveAndExitLoading.value = false;
};

const saveAndContinue = async () => {
  saveAndContinueLoading.value = true;
  try {
    await (view.value as any).saveAndContinue();
    componentDataLoaded.value = false;
  } catch (err) {
    console.warn('Save failed', err);
  }
  saveAndContinueLoading.value = false;
};

const back = async () => {
  router.back();
  componentDataLoaded.value = false;
};

const load = async () => {
  loaded.value = false;
  const { data } = await getTimecard(
    parseInt(route.params.timecardId as string),
  );
  timecard.value = data as Timecard;
  timecard.value.timecardDays.forEach((timecardDay) => {
    timecardDay.date = timecardDay.date.toUTC();
  });
  const { data: projectMemberData } = await getProjectMemberById(
    timecard.value.projectMemberId,
  );
  projectMember.value = projectMemberData;
  loaded.value = true;
  editDisabled.value =
    timecard.value.status.id === TimecardStatusId.Submitted ||
    timecard.value.status.id === TimecardStatusId.Approved;
};

const componentInitialDataLoaded = async () => {
  componentDataLoaded.value = true;
};

onMounted(async () => {
  await load();
});
</script>
