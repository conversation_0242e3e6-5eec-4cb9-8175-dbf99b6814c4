import { defineStore } from 'pinia';

const loadIsDarkModeEnabledFromStorage = (): boolean | undefined => {
  const isDarkModeEnabled = localStorage.getItem('isDarkModeEnabled');
  if (isDarkModeEnabled === undefined || isDarkModeEnabled === null) return;
  return JSON.parse(isDarkModeEnabled) as boolean | undefined;
};

const loadDoesSystemPreferDarkModeFromStorage = (): boolean | undefined => {
  const doesSystemPreferDarkMode = localStorage.getItem(
    'doesSystemPreferDarkMode',
  );
  if (
    doesSystemPreferDarkMode === undefined ||
    doesSystemPreferDarkMode === null
  )
    return;
  return JSON.parse(doesSystemPreferDarkMode) as boolean | undefined;
};

export const useAppStore = defineStore({
  id: 'app',
  state: () => ({
    isDarkModeEnabled: loadIsDarkModeEnabledFromStorage() as
      | boolean
      | undefined,
    doesSystemPreferDarkMode: loadDoesSystemPreferDarkModeFromStorage() as
      | boolean
      | undefined,
  }),
  getters: {
    getIsDarkModeEnabled: (state) => state.isDarkModeEnabled as boolean,
    getDoesSystemPrefersDarkMode: (state) => state.doesSystemPreferDarkMode,
  },
  actions: {
    setIsDarkModeEnabled(isDarkModeEnabled: boolean) {
      this.isDarkModeEnabled = isDarkModeEnabled;
      localStorage.setItem(
        'isDarkModeEnabled',
        JSON.stringify(this.isDarkModeEnabled),
      );
    },
    setDoesSystemPreferDarkMode(doesSystemPreferDarkMode: boolean) {
      this.doesSystemPreferDarkMode = doesSystemPreferDarkMode;
      localStorage.setItem(
        'doesSystemPreferDarkMode',
        JSON.stringify(this.doesSystemPreferDarkMode),
      );
    },
    setColorModeSettings() {
      const doesSystemPreferDarkMode = window.matchMedia(
        '(prefers-color-scheme: dark)',
      ).matches;
      const didSystemPreferenceChange =
        this.getDoesSystemPrefersDarkMode !== doesSystemPreferDarkMode;
      if (
        didSystemPreferenceChange ||
        this.getIsDarkModeEnabled === undefined
      ) {
        this.setIsDarkModeEnabled(doesSystemPreferDarkMode);
      }
      this.setDoesSystemPreferDarkMode(doesSystemPreferDarkMode);
    },
  },
});
