import { createTimecard } from '../apiCreateHelpers';
import {
  fetchMembersByProject,
  fetchPayPeriodsByProject,
  fetchProjectIdentifiersByName,
} from '../apiHelpers';
import { addTimecardLineReimbursements } from '../apiPatchHelpers';

/**
 * Creates a timecard flow using the given user's first and last name.
 * It fetches the project, pay period, user, and then creates a timecard and line.
 */
export function createTimecardFlowReimbursement(
  firstName: string,
  lastName: string,
) {
  let startsAt: string;
  let endsAt: string;
  // Step 1: Fetch project identifiers using the project name
  cy.then(() => {
    return fetchProjectIdentifiersByName(Cypress.env('projectName'));
  })
    .then((project) => {
      if (!project || !project.id) {
        throw new Error('Project not found or invalid Project ID');
      }
      Cypress.env('projectId', project.id);
      cy.log(`Project ID set: ${project.id}`);
      Cypress.env('projectHashId', project.hashId);

      // Step 2: Fetch pay periods associated with the project
      return fetchPayPeriodsByProject();
    })
    .then((payPeriodResponse) => {
      const payPeriod = payPeriodResponse.body[0];
      startsAt = payPeriod.startsAt;
      endsAt = payPeriod.endsAt;
      Cypress.env('payPeriodId', payPeriod.id);
      cy.log(`PayPeriod ID set: ${payPeriod.id}`);

      // Step 3: Fetch project members to find user by first and last name
      return fetchMembersByProject();
    })
    .then((userResponse) => {
      const userGroup = userResponse.body.data.find(
        (item: { user: { firstName: string; lastName: string } }) =>
          item.user.firstName === firstName && item.user.lastName === lastName,
      );
      if (userGroup) {
        const projectMemberId = userGroup.id;
        Cypress.env('projectMemberId', projectMemberId);
        cy.log(`projectMemberId ID set: ${projectMemberId}`);
      }
      // Step 4: Create the timecard
      return createTimecard();
    })
    .then((timecardResponse) => {
      const timecardId = timecardResponse.body.id;
      Cypress.env('timecardId', timecardId);
      cy.log(`timecardId ID set: ${timecardId}`);
      const timecardDays = timecardResponse.body.timecardDays;

      const timecardDayIds = Array.isArray(timecardDays)
        ? timecardDays.map((day) => day.id)
        : [];
      Cypress.env('timecardDayIds', timecardDayIds);
      cy.log(`timecardDayIds set: ${JSON.stringify(timecardDayIds)}`);

      return addTimecardLineReimbursements(startsAt, endsAt);
    })
    .then((timecardLineResponse) => {});
}
