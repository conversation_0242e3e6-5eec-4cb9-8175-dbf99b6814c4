import React from 'react';

import ProjectAdminTabs from './ProjectAdminTabs';
import ProjectHeaderAdminActions from './ProjectHeaderAdminActions';
import ProjectHeaderBreadcrumb from './ProjectHeaderBreadcrumb';
import ProjectHeaderInfoItems from './ProjectHeaderInfoItems';
import ProjectStore from '@/reactComponents/stores/project';

import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/20/solid';
import PropTypes from 'prop-types';
import { observer } from 'mobx-react-lite';

export const ProjectHeaderContext = React.createContext();

const ProjectHeader = observer((props) => {
  const { isAdmin, route, matches, navigate, fullWidthHeader } = props;
  const [isCollapsed, setIsCollapsed] = React.useState(false);
  const project = ProjectStore.project;

  const toggleCollapse = () => {
    setIsCollapsed((v) => !v);
  };

  const isProjectAdminRoute = !!route?.match?.handle?.isProjectAdmin;

  if (!project) return null;

  return (
    <ProjectHeaderContext.Provider
      value={{ project, isAdmin, route, matches, navigate }}
    >
      <header
        className={`bg-gray-50 dark:bg-gray-700 z-10 w-full pt-6 shadow ${
          isProjectAdminRoute ? '' : 'pb-2'
        }`}
      >
        <div
          className={`px-4 sm:px-6 lg:px-8 flex items-center justify-between ${
            !fullWidthHeader && 'mx-auto max-w-7xl'
          }`}
        >
          <div className="min-w-0 flex-1">
            <ProjectHeaderBreadcrumb
              className={`sm:block mb-3 ${isCollapsed ? 'hidden' : ''}`}
            />

            <div className="items-left">
              <div className="page-title-bar bg-gray-200 w-2 h-16 mr-3"></div>
              <h1
                className={`text-2xl font-bold leading-7 text-gray-900 dark:text-gray-200 sm:truncate sm:text-3xl sm:tracking-tight ${
                  isCollapsed ? 'mt-0' : 'mt-2'
                }`}
                data-testid="project-name-title"
              >
                {project?.name}
              </h1>
              <ProjectHeaderInfoItems
                project={project}
                className={`sm:flex ${isCollapsed ? 'hidden' : ''}`}
              />
            </div>
          </div>
          <div className="flex justify-end">
            {isProjectAdminRoute && (
              <ProjectHeaderAdminActions project={project} />
            )}
          </div>
        </div>
        <div
          className={`flex justify-center pb-3 cursor-pointer sm:hidden ${
            isCollapsed ? 'pt-0' : 'pt-3'
          }`}
          onClick={toggleCollapse}
        >
          {!isCollapsed ? (
            <ChevronUpIcon className="w-5 h-5" />
          ) : (
            <ChevronDownIcon className="w-5 h-5" />
          )}
        </div>
        {isProjectAdminRoute && (
          <ProjectAdminTabs fullWidthHeader={fullWidthHeader} />
        )}
        {!isProjectAdminRoute && <div className="y-5" />}
      </header>
    </ProjectHeaderContext.Provider>
  );
});

ProjectHeader.propTypes = {
  project: PropTypes.object,
  isAdmin: PropTypes.bool.isRequired,
  route: PropTypes.object.isRequired,
  matches: PropTypes.array.isRequired,
  navigate: PropTypes.func.isRequired,
  fullWidthHeader: PropTypes.bool,
};
export default ProjectHeader;
