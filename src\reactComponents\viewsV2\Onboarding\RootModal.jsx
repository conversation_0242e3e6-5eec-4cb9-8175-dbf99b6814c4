import React from 'react';
import { useSearchParams } from 'react-router';

import {
  Loader,
  Text,
  Box,
  IconButton,
  Modal,
  InputLabel,
  CircularProgress,
  Dialog,
} from '@/reactComponents/library';
import {
  MainTitleBox,
  MainBox,
  StepperBox,
  FormBox,
  FooterBox,
  ContentBox,
} from './styledComponents';

import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';

import { Button } from '@/reactComponents/spotlight';

import CloseIcon from '@mui/icons-material/Close';

import { useIsMobile } from '@/reactComponents/utils/customHooks';
import Steps from '@/reactComponents/viewsV2/Onboarding/components/Steps';
import StepperComp from '@/reactComponents/viewsV2/Onboarding/components/Stepper';

import { observer } from 'mobx-react-lite';

import OnboardingStore from './store';

import { useDidMount } from '@/reactComponents/utils/customHooks';

const RootModal = () => {
  const [, setSearchParams] = useSearchParams();

  const { initLoading } = OnboardingStore;

  useDidMount(() => {
    // Looks like onboarding step will be driven by BE db not url
    OnboardingStore.init();
    OnboardingStore.setSearchParams = setSearchParams;
  });

  const isMobile = useIsMobile();

  const stepperRef = React.useRef(null);
  let formSx = {};
  if (isMobile) {
    const stepperHeight = stepperRef.current?.clientHeight || '100';
    formSx = {
      height: `calc(100% - ${stepperHeight}px)`,
    };
  }

  const nextStepIdx = OnboardingStore.onboardingStepIdx + 1;
  const prevStepIdx = OnboardingStore.onboardingStepIdx - 1;

  const nextStep = OnboardingStore.onboardingSteps[nextStepIdx];
  const prevStep = OnboardingStore.onboardingSteps[prevStepIdx];

  return (
    <Dialog
      slotProps={{
        paper: {
          sx: { maxWidth: '100vw', minHeight: '100vh', minWidth: '100vw' },
        },
      }}
      fullScreen
      open={true}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100vh',
          minWidth: isMobile ? '' : '100vw',
          overflow: 'hidden',
        }}
      >
        <MainTitleBox>
          <Text variant="lgSemi">Onboarding</Text>
          <IconButton
            onClick={OnboardingStore.reqCloseRootModal}
            sx={{
              position: 'absolute',
              right: 0,
              top: 0,
              margin: 1,
            }}
          >
            <CloseIcon />
          </IconButton>
        </MainTitleBox>
        {initLoading ? (
          <Loader />
        ) : (
          <MainBox>
            <StepperBox ref={stepperRef}>
              <StepperComp />
            </StepperBox>
            <FormBox sx={formSx}>
              <ContentBox>
                <Steps />
              </ContentBox>
              <FooterBox>
                <Box>
                  {prevStep?.name && (
                    <>
                      <InputLabel>Previous</InputLabel>
                      <Button
                        variant="outlined"
                        disabled={!OnboardingStore.prevBtnEnabled}
                        onClick={OnboardingStore.handleClickPrev}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <KeyboardArrowLeftIcon />
                          {prevStep?.name} details
                        </Box>
                      </Button>
                    </>
                  )}
                </Box>
                <Box>
                  {nextStep?.name ? (
                    <>
                      <InputLabel sx={{ textAlign: 'right' }}>Next</InputLabel>
                      <Button
                        disabled={!OnboardingStore.nextBtnEnabled}
                        onClick={OnboardingStore.handleClickNext}
                      >
                        {OnboardingStore.submitting ? (
                          <CircularProgress size={20} />
                        ) : (
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            {nextStep?.name} details
                            <KeyboardArrowRightIcon />
                          </Box>
                        )}
                      </Button>
                    </>
                  ) : (
                    <>
                      <Box sx={{ height: '1.2em' }} />
                      <Button
                        disabled={!OnboardingStore.nextBtnEnabled}
                        onClick={OnboardingStore.handleClickNext}
                      >
                        {OnboardingStore.submitting ? (
                          <CircularProgress size={20} />
                        ) : (
                          'Submit'
                        )}
                      </Button>
                    </>
                  )}
                </Box>
              </FooterBox>
            </FormBox>
          </MainBox>
        )}
        <Modal
          title="Unsaved Changes"
          open={OnboardingStore.showUnsavedModal}
          setOpen={OnboardingStore.setShowUnsavedModal}
          onSubmit={OnboardingStore.confirmLeave}
          onCancel={() => OnboardingStore.setShowUnsavedModal(false)}
          submitText="Continue, discard changes"
          cancelText="Stay"
        >
          <Box
            sx={{
              display: 'flex',
              height: '75px',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            Are you sure you want to leave this page?
          </Box>
        </Modal>
      </Box>
    </Dialog>
  );
};

export default observer(RootModal);
