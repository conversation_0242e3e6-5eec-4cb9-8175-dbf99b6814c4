import React from 'react';
import CalculateIcon from '@mui/icons-material/Calculate';
import { styled, SvgIcon } from '@mui/material';

const BlinkingIcon = styled(SvgIcon)`
  @keyframes blink {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.3;
    }
  }

  animation: blink 2s infinite;
`;

const CalcPulse = () => {
  return <BlinkingIcon component={CalculateIcon} fontSize="large" />;
};

CalcPulse.propTypes = {};

export default CalcPulse;
