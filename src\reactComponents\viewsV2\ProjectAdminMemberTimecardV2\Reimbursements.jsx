import React from 'react';
import PropTypes from 'prop-types';

import {
  Box,
  Chip,
  Text,
  Accordion,
  AccordionDetails,
  ButtonGroup,
  Button,
  IconButton,
} from '@/reactComponents/library';

import { listProjectReimbursementTypes } from '@/services/project';

import {
  rentalRateTypes,
  MILEAGE_RATE,
  calcMileageDate,
} from '@/reactComponents/viewsV2/ProjectAdminMemberTimecardV2/timecardUtils';

import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';

import Group from './Reimburse/Group';
import { ReimbursementTypeKeys } from '@/types/Reimbursements';

const styles = {
  buttons: {
    display: 'flex',
    justifyContent: 'flex-end',
    width: '100%',
    borderBottom: `1px solid`,
    borderColor: 'divider',
    p: 1,
  },
  emptyState: {
    display: 'flex',
    justifyContent: 'center',
    width: '100%',
    py: 6,
  },
};

const Reimbursements = (props) => {
  const {
    reimbursements,
    updateReimbursements,
    timecard,
    updateTimecard,
    workLocations,
    trackingDetails,
    total,
    member,
    displayErrors,
    readOnlyMode = false,
  } = props;
  const [expenseTypes, setExpenseTypes] = React.useState([]);
  const [expanded, setExpanded] = React.useState(true);

  const { mileageForm, kitRental, project, timecardDays } = timecard;

  const totalLabel = (total ?? 0).toLocaleString('en-US', {
    style: 'currency',
    currency: 'USD',
  });

  const fetchExpenseTypes = React.useCallback(async (projectId) => {
    try {
      const { data } = await listProjectReimbursementTypes(projectId);
      const filteredReimbursementTypes = data.filter(
        (reimbursementType) =>
          reimbursementType.castAndCrewId !==
          ReimbursementTypeKeys.KIT_RENTAL_NON_TAXABLE,
      );
      if (Array.isArray(filteredReimbursementTypes))
        setExpenseTypes(filteredReimbursementTypes);
    } catch (error) {
      console.error('Error fetching project data: ', error);
    }
  }, []);

  React.useEffect(() => {
    if (project?.id) {
      fetchExpenseTypes(project.id);
    }
  }, [fetchExpenseTypes, project?.id]);

  const isEmpty = reimbursements.length === 0 && !mileageForm && !kitRental;

  return (
    <Accordion expanded={expanded} sx={{ width: '100%' }}>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          p: 2,
          borderBottom: `1px solid`,
          borderColor: expanded ? 'divider' : 'transparent',
        }}
      >
        <IconButton onClick={() => setExpanded((x) => !x)}>
          <ExpandMoreIcon
            sx={{
              color: 'text.primary',
              transform: expanded ? 'rotate(180deg)' : '',
            }}
          />
        </IconButton>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            width: '100%',
          }}
        >
          <Text variant="lgStrong">Reimbursements</Text>
          <Chip label={`Total: ${totalLabel}`} variant="outlined" />
        </Box>
      </Box>

      <AccordionDetails sx={{ overflow: 'auto' }}>
        <Box sx={styles.buttons}>
          <ButtonGroup>
            <Button
              variant="secondary"
              startIcon={<AddCircleOutlineIcon />}
              disabled={!!mileageForm || readOnlyMode}
              onClick={() =>
                updateTimecard({
                  mileageForm: {
                    rate: MILEAGE_RATE,
                    date: calcMileageDate(timecard),
                  },
                })
              }
              data-testid="add-mileage-btn"
            >
              Add Mileage Log
            </Button>
            <Button
              variant="secondary"
              startIcon={<AddCircleOutlineIcon />}
              disabled={!!kitRental || readOnlyMode}
              onClick={() =>
                updateTimecard({
                  kitRental: {
                    rateType: rentalRateTypes[0],
                  },
                })
              }
              data-testid="add-kit-rental-btn"
            >
              Add Kit Rental
            </Button>
            <Button
              variant="secondary"
              startIcon={<AddCircleOutlineIcon />}
              onClick={() => {
                const newIndex = reimbursements.length;
                updateReimbursements({}, newIndex);
              }}
              disabled={readOnlyMode}
              data-testid="add-reimbursement-btn"
            >
              Add Reimbursement
            </Button>
          </ButtonGroup>
        </Box>
        {isEmpty && (
          <Box sx={styles.emptyState}>
            <Text sx={{ fontWeight: 'bold' }}>There are no reimbursements</Text>
          </Box>
        )}
        {mileageForm && (
          <Group
            data={mileageForm ? [mileageForm] : []}
            isMileageForm={true}
            updateTimecard={updateTimecard}
            workLocations={workLocations}
            trackingDetails={trackingDetails}
            member={member}
            timecard={timecard}
            displayErrors={displayErrors}
            readOnlyMode={readOnlyMode}
          />
        )}
        {kitRental && (
          <Group
            data={kitRental ? [kitRental] : []}
            isKitRental={true}
            updateTimecard={updateTimecard}
            workLocations={workLocations}
            timecardDays={timecardDays}
            trackingDetails={trackingDetails}
            member={member}
            displayErrors={displayErrors}
            readOnlyMode={readOnlyMode}
          />
        )}
        {reimbursements.length > 0 && (
          <Group
            data={reimbursements}
            updateReimbursements={updateReimbursements}
            workLocations={workLocations}
            expenseTypes={expenseTypes}
            trackingDetails={trackingDetails}
            member={member}
            timecard={timecard}
            displayErrors={displayErrors}
            readOnlyMode={readOnlyMode}
          />
        )}
      </AccordionDetails>
    </Accordion>
  );
};

Reimbursements.propTypes = {
  reimbursements: PropTypes.array.isRequired,
  updateReimbursements: PropTypes.func.isRequired,
  timecard: PropTypes.object.isRequired,
  trackingDetails: PropTypes.object.isRequired,
  updateTimecard: PropTypes.func.isRequired,
  workLocations: PropTypes.array.isRequired,
  total: PropTypes.number,
  member: PropTypes.object.isRequired,
  displayErrors: PropTypes.object.isRequired,
  readOnlyMode: PropTypes.bool.isRequired,
};

export default Reimbursements;
