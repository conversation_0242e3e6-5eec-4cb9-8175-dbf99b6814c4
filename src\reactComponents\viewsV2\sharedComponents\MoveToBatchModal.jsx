import React from 'react';
import PropTypes from 'prop-types';
import { getBatches } from '@/services/project';
import { updateBatchOnTimecard } from '@/services/timecards';

import { Modal, Box, Autocomplete } from '@/reactComponents/library';
import {
  snackbarSuccess,
  snackbarAxiosErr,
} from '@/reactComponents/library/Snackbar';
import { useDidMount } from '@/reactComponents/utils/customHooks';
import { BATCH_FILTERS, prepFilters } from '../ProjectAdminTime/utils';
import { BatchStatusEnum } from '@/types/Batch';

import CreateBatchModal from './CreateBatch';

/**
 *
 * @param {func} onCreateComplete - Callback fired when a new batch is created
 * @param {func} onMoveComplete  - async Callback fired when the move is completed successfully
 *
 * @returns
 */
const MoveToBatchModal = (props) => {
  const {
    open,
    setOpen,
    project,
    timecard,
    batch: fromBatch,
    onMoveComplete = () => {},
    onCreateComplete = () => {},
    checkUnapprovedTimecards,
  } = props;

  const [batches, setBatches] = React.useState([]);
  const [destinationBatch, setDestinationBatch] = React.useState(null);
  const [loadingBatches, setLoadingBatches] = React.useState(true);
  const [moving, setMoving] = React.useState(false);

  const submitMoveToBatch = React.useCallback(
    (toBatch, { isNewBatch = false } = false) => {
      setMoving(true);
      updateBatchOnTimecard(timecard.id, toBatch.id)
        .then(() => {
          onCreateComplete();
          onMoveComplete().then(() => {
            if (checkUnapprovedTimecards) {
              checkUnapprovedTimecards(fromBatch?.id);
            }
            setOpen(false);
            snackbarSuccess(`Timecard moved to batch "${toBatch.name}"`);
            setMoving(false);
          });
        })
        .catch((err) => {
          console.error('Error moving timecard to batch: ', err);
          snackbarAxiosErr(err, 'Error moving timecard to batch');
          setMoving(false);
        });
    },
    [
      timecard,
      onCreateComplete,
      setOpen,
      onMoveComplete,
      checkUnapprovedTimecards,
      fromBatch,
    ],
  );

  const onConfirmMove = () => {
    if (destinationBatch) {
      submitMoveToBatch(destinationBatch);
    }
  };

  useDidMount(() => {
    setLoadingBatches(true);
    const batchFilters = BATCH_FILTERS.map((f) => {
      return {
        ...f,
        options: [{ value: BatchStatusEnum.OPEN, label: 'Open', active: true }],
      };
    });
    const openBatchOnlyFilter = prepFilters(batchFilters);
    const pagination = {
      page: 1,
      limit: 100, //TODO add infinite scroll
      total: 0,
    };
    getBatches(project.id, pagination, openBatchOnlyFilter)
      .then((res) => {
        const batchesData = res.data;
        const newBatches = batchesData.data.filter(
          (batch) => batch.id !== timecard.batchId,
        );
        newBatches.sort((a, b) => b.createdAt - a.createdAt);
        setBatches(newBatches);
      })
      .finally(() => {
        setLoadingBatches(false);
      });
  });

  if (!open) return null;

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Move to Batch"
      onSubmit={onConfirmMove}
      disableSubmit={!destinationBatch}
      submitText="Move to batch"
      loading={loadingBatches || moving}
      loadingMsg={moving ? 'Moving timecard to batch...' : ''}
    >
      <Box sx={{ width: '100%' }}>
        <Autocomplete
          value={destinationBatch}
          options={batches}
          placeholder="Select Batch"
          getOptionLabel={(option) => option.name}
          onChange={(e, newValue) => {
            setDestinationBatch(newValue);
          }}
        />
        <CreateBatchModal
          onCreateComplete={onCreateComplete}
          variant={'moveBatch'}
          submitMoveToBatch={submitMoveToBatch}
        />
      </Box>
    </Modal>
  );
};

MoveToBatchModal.propTypes = {
  open: PropTypes.bool.isRequired,
  setOpen: PropTypes.func.isRequired,
  project: PropTypes.object.isRequired,
  timecard: PropTypes.object.isRequired,
  onMoveComplete: PropTypes.func,
  onCreateComplete: PropTypes.func,
  batch: PropTypes.object.isRequired,
  checkUnapprovedTimecards: PropTypes.func,
};

export default MoveToBatchModal;
