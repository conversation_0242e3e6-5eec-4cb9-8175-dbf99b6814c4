{"timecard": {"id": 4180, "projectId": 1079, "userCrewId": 67, "payPeriodId": 18, "createdAt": "2025-05-30T17:06:00.139-05:00", "updatedAt": "2025-05-30T17:06:00.795-05:00", "statusId": 2, "requestedChanges": "", "batchId": null, "gross": null, "grossWithBoxRentalAndMileage": null, "fileId": "02c6f7b1-a22f-4ceb-b245-f3596260148a", "hourlyExempt": false, "lastDownloadedAt": null, "isRevision": false, "revisionId": null, "isActive": true, "projectMemberId": 1059, "castAndCrewId": null, "createdById": 38, "submittedById": 38, "occupationId": 5010, "hireLocationId": null, "rate": "100.0000", "rateTypeId": 2, "guarHours": "1.00", "guarRate": null, "isOnCall": false, "isExempt": false, "isHalfDayAllowed": true, "isNdbAllowed": false, "hourlyRate": "10.0000", "unionId": 5067, "capsPayId": null, "payPremiumOvertime": false, "payrollOpsNote": null, "calculationErrorMessage": null, "calculationWarningMessage": null, "projectLocationId": 1775, "workLocation": {"id": 1775, "projectId": 1079, "shootLocationId": 316, "createdAt": "2025-05-27T16:25:59.977-05:00", "updatedAt": "2025-05-27T16:25:59.977-05:00", "payrollProjectLocationId": 1692929, "zip": "15213"}, "union": {"id": 5067, "name": "4517", "number": "4517", "description": "4517", "createdAt": "2025-04-22T19:38:37.195-05:00", "updatedAt": "2025-05-30T16:55:55.568-05:00", "castAndCrewId": 1204}, "occupation": {"id": 5010, "name": "ACCOUNT ANALYST", "key": "ACCOUNT ANALYST", "createdAt": "2025-02-07T08:23:07.050-05:00", "updatedAt": "2025-05-30T17:05:15.713-05:00", "castAndCrewId": null, "payrollOccupationCode": "ACCAN"}, "hireLocation": null, "rateType": {"id": 2, "name": "Daily", "key": "daily", "description": "Daily rate", "created_at": "2025-01-31T04:35:17.125-05:00", "updated_at": "2025-03-11T04:49:45.204-05:00", "crew_display_name": "Per Day"}, "payPeriod": {"id": 18, "startsAt": "2025-05-25T00:00:00.000-05:00", "endsAt": "2025-05-31T00:00:00.000-05:00", "createdAt": "2025-02-07T02:07:35.862-05:00", "updatedAt": "2025-02-07T02:07:35.862-05:00"}, "status": {"id": 2, "key": "submitted", "name": "Submitted", "description": "The crew member has submitted the timecard for review.", "createdAt": "2025-01-31T04:35:17.758-05:00", "updatedAt": "2025-01-31T04:35:17.758-05:00"}, "mileageForm": null, "reimbursements": [{"lineNumber": "001", "name": "expense", "type": {"castAndCrewId": "30", "key": "BIKE"}, "totalAmount": 1000, "rate": 1000, "quantity": "1", "date": "2025-05-27T00:00:00.000Z", "workLocation": {"id": 1775, "projectId": 1079, "shootLocationId": 316, "createdAt": "2025-05-27T16:25:59.977-05:00", "updatedAt": "2025-05-27T16:25:59.977-05:00", "payrollProjectLocationId": 1692929, "zip": "15213", "shootLocation": {"id": 316, "createdAt": "2025-05-27T16:25:59.973-05:00", "updatedAt": "2025-05-27T16:25:59.973-05:00", "locationName": "PA - PETERSEN EVENTS CENTER", "locationCode": "PA", "city": "Pittsburgh", "state": "PA", "zipcode": "15213", "country": "US", "locationId": 107}}}], "kitRental": null, "project": {"id": 1079, "name": "TEST-Mayer and Sons 3349", "number": "9887", "code": "8ZPT", "productionCompanyId": 2, "startsAt": "2025-05-27T16:43:32.791-05:00", "endsAt": "2025-08-31T00:00:00.000-05:00", "createdAt": "2025-05-27T16:25:59.955-05:00", "updatedAt": "2025-05-30T09:08:12.512-05:00", "productionCompanyAddressId": 6, "hashId": "9018c897-805b-4fa4-a7e8-9331dc0b5d33", "typeId": 1, "minuteIncrementId": 1, "isApplyingForTaxIncentives": true, "description": "The Julia Fish is the latest in a series of elliptical products from Welch, Stoltenberg and Ryan", "castAndCrewId": 3036, "mileageAicpNumber": "9791", "authToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._rOe8VExHhFQi0bH3A-3C2sb52sRfIp6wV8LyYIlDP0", "authTokenLastUpdated": "2025-05-30T09:08:12.512-05:00", "capsPayId": "178696", "webHookId": "5b6296c2-290a-4d24-abf0-da5f5ed12dfa", "webHookSecret": "$argon2id$v=19$t=3,m=4096,p=1$XYzmxq0rgKgnqh6USnc10w$n6CSM+0dsJRINEwqgjvrkG7Jcp6Z0cedYYQZ6JBLcNY", "unclaimedBatchId": 1773943, "productionCompany": {"id": 2, "name": "TEST COMMERCIALS - TEST01", "capsPayId": "8289"}}, "timecardDays": [{"id": 29122, "timecardId": 4180, "date": "2025-05-25T08:00:00.000Z", "startsAt": "2025-05-25T08:00:00.000Z", "endsAt": "2025-05-25T17:00:00.000Z", "zipCode": null, "rate": null, "comments": null, "createdAt": "2025-05-30T17:06:00.149-05:00", "updatedAt": "2025-05-30T17:06:00.149-05:00", "isActive": false, "isRentalDay": false, "lineNumber": null, "workStatusId": null, "mealPenalties": 0, "workZoneId": null, "hasNdb": false, "hoursWorked": "0.000", "projectLocationId": null, "occupationId": null, "guaranteedHours": null, "hasRerate": false, "hasWalkingMeal": false, "hasMealGraceOne": false, "hasMealGraceTwo": false, "hasWrapGrace": false, "hasHalfDay": false, "driveTime": null, "hotelToSetTime": null, "setToHotelTime": null, "hasHoliday": false, "capsPayId": null, "occupation": null, "projectShootLocation": null, "workStatus": null, "workZone": null, "meals": [{"id": 31118, "timecardDayId": 29122, "startsAt": "2025-05-25T13:00:00.000Z", "endsAt": "2025-05-25T14:00:00.000Z", "createdAt": "2025-05-30T17:06:00.173-05:00", "updatedAt": "2025-05-30T17:06:00.173-05:00"}]}, {"id": 29123, "timecardId": 4180, "date": "2025-05-26T08:00:00.000Z", "startsAt": "2025-05-26T08:00:00.000Z", "endsAt": "2025-05-26T17:00:00.000Z", "zipCode": null, "rate": null, "comments": null, "createdAt": "2025-05-30T17:06:00.152-05:00", "updatedAt": "2025-05-30T17:06:00.152-05:00", "isActive": true, "isRentalDay": false, "lineNumber": "001", "workStatusId": null, "mealPenalties": 0, "workZoneId": null, "hasNdb": false, "hoursWorked": "0.000", "projectLocationId": null, "occupationId": null, "guaranteedHours": null, "hasRerate": false, "hasWalkingMeal": false, "hasMealGraceOne": false, "hasMealGraceTwo": false, "hasWrapGrace": false, "hasHalfDay": false, "driveTime": null, "hotelToSetTime": null, "setToHotelTime": null, "hasHoliday": false, "capsPayId": null, "occupation": null, "projectShootLocation": {"id": 1775, "projectId": 1079, "shootLocationId": 316, "createdAt": "2025-05-27T16:25:59.977-05:00", "updatedAt": "2025-05-27T16:25:59.977-05:00", "payrollProjectLocationId": 1692929, "zip": "15213", "shootLocation": {"id": 316, "createdAt": "2025-05-27T16:25:59.973-05:00", "updatedAt": "2025-05-27T16:25:59.973-05:00", "locationName": "PA - PETERSEN EVENTS CENTER", "locationCode": "PA", "city": "Pittsburgh", "state": "PA", "zipcode": "15213", "country": "US", "locationId": 107}}, "workStatus": {"id": 2, "name": "WORK", "key": "WORK", "description": "WORK", "created_at": "2025-04-30T11:48:40.308-05:00", "updated_at": "2025-05-30T13:53:03.730-05:00", "caps_pay_id": "18"}, "workZone": {"id": 1, "key": "STUDIO", "name": "Studio", "description": "Studio", "created_at": "2025-01-31T04:35:17.893-05:00", "updated_at": "2025-05-30T13:53:03.736-05:00", "caps_pay_id": "5"}, "meals": [{"id": 31119, "timecardDayId": 29123, "startsAt": "2025-05-26T13:00:00.000Z", "endsAt": "2025-05-26T14:00:00.000Z", "createdAt": "2025-05-30T17:06:00.176-05:00", "updatedAt": "2025-05-30T17:06:00.176-05:00"}]}, {"id": 29124, "timecardId": 4180, "date": "2025-05-27T08:00:00.000Z", "startsAt": "2025-05-27T08:00:00.000Z", "endsAt": "2025-05-27T17:00:00.000Z", "zipCode": null, "rate": null, "comments": null, "createdAt": "2025-05-30T17:06:00.154-05:00", "updatedAt": "2025-05-30T17:06:00.154-05:00", "isActive": false, "isRentalDay": false, "lineNumber": null, "workStatusId": null, "mealPenalties": 0, "workZoneId": null, "hasNdb": false, "hoursWorked": "0.000", "projectLocationId": null, "occupationId": null, "guaranteedHours": null, "hasRerate": false, "hasWalkingMeal": false, "hasMealGraceOne": false, "hasMealGraceTwo": false, "hasWrapGrace": false, "hasHalfDay": false, "driveTime": null, "hotelToSetTime": null, "setToHotelTime": null, "hasHoliday": false, "capsPayId": null, "occupation": null, "projectShootLocation": null, "workStatus": null, "workZone": null, "meals": [{"id": 31120, "timecardDayId": 29124, "startsAt": "2025-05-27T13:00:00.000Z", "endsAt": "2025-05-27T14:00:00.000Z", "createdAt": "2025-05-30T17:06:00.178-05:00", "updatedAt": "2025-05-30T17:06:00.178-05:00"}]}, {"id": 29125, "timecardId": 4180, "date": "2025-05-28T08:00:00.000Z", "startsAt": "2025-05-28T08:00:00.000Z", "endsAt": "2025-05-28T17:00:00.000Z", "zipCode": null, "rate": null, "comments": null, "createdAt": "2025-05-30T17:06:00.156-05:00", "updatedAt": "2025-05-30T17:06:00.156-05:00", "isActive": false, "isRentalDay": false, "lineNumber": null, "workStatusId": null, "mealPenalties": 0, "workZoneId": null, "hasNdb": false, "hoursWorked": "0.000", "projectLocationId": null, "occupationId": null, "guaranteedHours": null, "hasRerate": false, "hasWalkingMeal": false, "hasMealGraceOne": false, "hasMealGraceTwo": false, "hasWrapGrace": false, "hasHalfDay": false, "driveTime": null, "hotelToSetTime": null, "setToHotelTime": null, "hasHoliday": false, "capsPayId": null, "occupation": null, "projectShootLocation": null, "workStatus": null, "workZone": null, "meals": [{"id": 31121, "timecardDayId": 29125, "startsAt": "2025-05-28T13:00:00.000Z", "endsAt": "2025-05-28T14:00:00.000Z"}]}, {"id": 29126, "timecardId": 4180, "date": "2025-05-29T08:00:00.000Z", "startsAt": "2025-05-29T08:00:00.000Z", "endsAt": "2025-05-29T17:00:00.000Z", "zipCode": null, "rate": null, "comments": null, "isActive": false, "isRentalDay": false, "lineNumber": null, "workStatusId": null, "mealPenalties": 0, "workZoneId": null, "hasNdb": false, "hoursWorked": "0.000", "projectLocationId": null, "occupationId": null, "guaranteedHours": null, "hasRerate": false, "hasWalkingMeal": false, "hasMealGraceOne": false, "hasMealGraceTwo": false, "hasWrapGrace": false, "hasHalfDay": false, "driveTime": null, "hotelToSetTime": null, "setToHotelTime": null, "hasHoliday": false, "capsPayId": null, "occupation": null, "projectShootLocation": null, "workStatus": null, "workZone": null, "meals": [{"id": 31122, "timecardDayId": 29126, "startsAt": "2025-05-29T13:00:00.000Z", "endsAt": "2025-05-29T14:00:00.000Z"}]}, {"id": 29127, "timecardId": 4180, "date": "2025-05-30T08:00:00.000Z", "startsAt": "2025-05-30T08:00:00.000Z", "endsAt": "2025-05-30T17:00:00.000Z", "zipCode": null, "rate": null, "comments": null, "isActive": false, "isRentalDay": false, "lineNumber": null, "workStatusId": null, "mealPenalties": 0, "workZoneId": null, "hasNdb": false, "hoursWorked": "0.000", "projectLocationId": null, "occupationId": null, "guaranteedHours": null, "hasRerate": false, "hasWalkingMeal": false, "hasMealGraceOne": false, "hasMealGraceTwo": false, "hasWrapGrace": false, "hasHalfDay": false, "driveTime": null, "hotelToSetTime": null, "setToHotelTime": null, "hasHoliday": false, "capsPayId": null, "occupation": null, "projectShootLocation": null, "workStatus": null, "workZone": null, "meals": [{"id": 31123, "timecardDayId": 29127, "startsAt": "2025-05-30T13:00:00.000Z", "endsAt": "2025-05-30T14:00:00.000Z"}]}, {"id": 29128, "timecardId": 4180, "date": "2025-05-31T08:00:00.000Z", "startsAt": "2025-05-31T08:00:00.000Z", "endsAt": "2025-05-31T17:00:00.000Z", "zipCode": null, "rate": null, "comments": null, "isActive": false, "isRentalDay": false, "lineNumber": null, "workStatusId": null, "mealPenalties": 0, "workZoneId": null, "hasNdb": false, "hoursWorked": "0.000", "projectLocationId": null, "occupationId": null, "guaranteedHours": null, "hasRerate": false, "hasWalkingMeal": false, "hasMealGraceOne": false, "hasMealGraceTwo": false, "hasWrapGrace": false, "hasHalfDay": false, "driveTime": null, "hotelToSetTime": null, "setToHotelTime": null, "hasHoliday": false, "capsPayId": null, "occupation": null, "projectShootLocation": null, "workStatus": null, "workZone": null, "meals": [{"id": 31124, "timecardDayId": 29128, "startsAt": "2025-05-31T13:00:00.000Z", "endsAt": "2025-05-31T14:00:00.000Z"}]}], "contractId": 4525, "dailyGuaranteeHours": 0, "holidayDates": [], "isAllowWalkingMeal": false, "isDisAllowPayDaily": false, "isDisAllowPayHourly": false, "isDisAllowPayHourlyGuarantee": false, "isDisAllowPayWeekly": false, "isExemptIndicator": false, "isForcePayPremiumOt": false, "isHideDriveTime": false, "isNonUnion": false, "isOnCallIndicator": false, "minimumCallHours": null, "scheduleId": 32352, "scheduleOccupationId": 523903, "unionContractId": 15544, "disablePayPremiumOt": false, "isHireLocationRequired": false}, "auditNotes": ""}