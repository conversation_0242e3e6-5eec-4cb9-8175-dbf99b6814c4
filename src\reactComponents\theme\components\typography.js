/**
 *  font size legend
 * root font size: 16px
 * 10px = 0.625rem
 * 12px = 0.75rem
 * 14px = 0.875rem
 * 16px = 1rem
 * 18px = 1.125rem
 * 20px = 1.25rem
 * 24px = 1.5rem
 */

const typography = {
  defaultProps: {
    component: 'span',
  },
  styleOverrides: {
    // Name of the slot
    root: {
      variants: [
        {
          props: { variant: 'disReg' },
          style: {
            fontSize: '1.5rem',
            fontWeight: 400,
          },
        },
        {
          props: { variant: 'disMed' },
          style: {
            fontSize: '1.5rem',
            fontWeight: 500,
          },
        },
        {
          props: { variant: 'disSemi' },
          style: {
            fontSize: '1.5rem',
            fontWeight: 600,
          },
        },
        {
          props: { variant: 'disStrong' },
          style: {
            fontSize: '1.5rem',
            fontWeight: 700,
          },
        },
        {
          props: { variant: 'lgReg' },
          style: {
            fontSize: '1.125rem',
            fontWeight: 400,
          },
        },
        {
          props: { variant: 'lgMed' },
          style: {
            fontSize: '1.125rem',
            fontWeight: 500,
          },
        },
        {
          props: { variant: 'lgSemi' },
          style: {
            fontSize: '1.125rem',
            fontWeight: 600,
          },
        },
        {
          props: { variant: 'lgStrong' },
          style: {
            fontSize: '1.125rem',
            fontWeight: 700,
          },
        },
        {
          props: { variant: 'baseReg' },
          style: {
            fontSize: '1rem',
            fontWeight: 400,
          },
        },
        {
          props: { variant: 'baseMed' },
          style: {
            fontSize: '1rem',
            fontWeight: 500,
          },
        },
        {
          props: { variant: 'baseSemi' },
          style: {
            fontSize: '1rem',
            fontWeight: 600,
          },
        },
        {
          props: { variant: 'baseStrong' },
          style: {
            fontSize: '1rem',
            fontWeight: 700,
          },
        },
        {
          props: { variant: 'smReg' },
          style: {
            fontSize: '0.875rem',
            fontWeight: 400,
          },
        },
        {
          props: { variant: 'smMed' },
          style: {
            fontSize: '0.875rem',
            fontWeight: 500,
          },
        },
        {
          props: { variant: 'smSemi' },
          style: {
            fontSize: '0.875rem',
            fontWeight: 600,
          },
        },
        {
          props: { variant: 'smStrong' },
          style: {
            fontSize: '0.875rem',
            fontWeight: 700,
          },
        },
        {
          props: { variant: 'xsReg' },
          style: {
            fontSize: '0.75rem',
            fontWeight: 400,
          },
        },
        {
          props: { variant: 'xsMed' },
          style: {
            fontSize: '0.75rem',
            fontWeight: 500,
          },
        },
        {
          props: { variant: 'xsSemi' },
          style: {
            fontSize: '0.75rem',
            fontWeight: 600,
          },
        },
        {
          props: { variant: 'xsStrong' },
          style: {
            fontSize: '0.75rem',
            fontWeight: 700,
          },
        },
        {
          props: { variant: 'xxsReg' },
          style: {
            fontSize: '0.625rem',
            fontWeight: 400,
          },
        },
        {
          props: { variant: 'xxsMed' },
          style: {
            fontSize: '0.625rem',
            fontWeight: 500,
          },
        },
        {
          props: { variant: 'xxsSemi' },
          style: {
            fontSize: '0.625rem',
            fontWeight: 600,
          },
        },
        {
          props: { variant: 'xxsStrong' },
          style: {
            fontSize: '0.625rem',
            fontWeight: 700,
          },
        },
      ],
    },
  },
};

export default typography;
