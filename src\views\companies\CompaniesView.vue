<template>
  <div>
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pt-20">
      <div class="flex justify-between items-center space-x-1 my-4">
        <h3 class="text-lg font-semibold leading-6">Companies</h3>
        <Button
          class="max-w-xs"
          color="primary"
          size="sm"
          @click="navigate('/companies/new')"
          data-testid="add-company-btn"
        >
          Add Company
        </Button>
      </div>
      <TableFilters :filters="filters" @update:filters="filters = $event" />
      <div class="bg-white dark:bg-gray-900 shadow sm:rounded-md">
        <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-500">
          <li
            v-for="(company, companyIndex) in companies"
            :key="`${company.id}-${companyIndex}`"
            data-testid="company-list"
          >
            <a
              class="block hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
              :class="{
                'rounded-t-md': companyIndex === 0,
                'rounded-b-md': companyIndex === companies.length - 1,
              }"
              @click="goToCompany(company.id!)"
            >
              <div class="px-4 py-4 sm:px-6">
                <div class="flex items-center justify-between">
                  <p
                    class="truncate text-sm font-medium text-indigo-600 dark:text-indigo-300"
                    :data-testid="`company-name-${company.name}`"
                  >
                    {{ company.name }}
                  </p>
                  <div class="flex justify-end space-x-1">
                    <p
                      v-for="(address, addressIndex) in company.addresses"
                      :key="`address-${addressIndex}`"
                      class="rounded-full bg-green-100 px-2 text-xs font-semibold leading-5 text-green-800"
                    >
                      {{ address.city }}
                    </p>
                  </div>
                </div>
                <div class="mt-2 sm:flex sm:justify-between">
                  <div class="sm:flex sm:space-x-2 items-center">
                    <p
                      class="flex items-center text-sm text-gray-500 dark:text-gray-400"
                    >
                      <Icon
                        name="phone"
                        class="mr-1.5 h-5 w-5 flex-shrink-0 text-gray-400 dark:text-gray-500"
                        aria-hidden="true"
                      />
                      {{ formatPhoneNumber(company.phone) }}
                    </p>
                    <p
                      class="flex items-center text-sm text-gray-500 dark:text-gray-400"
                    >
                      <DocumentIcon
                        class="mr-1.5 h-5 w-5 flex-shrink-0 text-gray-400 dark:text-gray-500"
                        aria-hidden="true"
                      />
                      {{ company.payrollProvider?.name }}
                    </p>
                  </div>
                  <div
                    class="flex items-center text-sm text-gray-500 dark:text-gray-400 sm:mt-0"
                  >
                    <CalendarIcon
                      class="mr-1.5 h-5 w-5 flex-shrink-0 text-gray-400 dark:text-gray-500"
                      aria-hidden="true"
                    />
                    <p>
                      Created on
                      {{ ' ' }}
                      <time :datetime="(company.createdAt.toISO() as string)">{{
                        company.createdAt.toFormat('MM/dd')
                      }}</time>
                    </p>
                  </div>
                </div>
              </div>
            </a>
          </li>
        </ul>
      </div>
      <Pagination
        v-if="pagination.total > pagination.limit"
        v-model="pagination"
      />
    </div>
  </div>
</template>

<script lang="ts">
import Icon from '@/components/Icon.vue';
import Button from '@/components/library/Button.vue';
import Pagination from '@/components/library/Pagination.vue';
import TableFilters from '@/components/TableFilters.vue';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';
import { listProductionCompanies } from '@/services/production-company';
import { FilterOperator, FilterUIType, type Filter } from '@/types/Filter';
import type { Pagination as PaginationType } from '@/types/Pagination';
import type ProductionCompany from '@/types/ProductionCompany';
import { convertFiltersToQuery } from '@/utils/filter';
import { formatPhoneNumber } from '@/utils/phone';
import { convertSortsToQuery } from '@/utils/sort';
import {
  copyMetaToPagination,
  updateReactRouteWithPagination,
} from '@/utils/tableview';
import {
  CalendarIcon,
  DocumentIcon,
  PhoneIcon,
} from '@heroicons/vue/24/outline';
import { defineComponent, type PropType, provide, ref, watchEffect } from 'vue';

export default defineComponent({
  props: {
    navigate: {
      type: Function,
      required: true,
    },
    route: {
      type: Object as PropType<ParsedRoute>,
      required: true,
    },
  },
  setup(props, ctx) {
    const route = ref(props.route);
    watchEffect(() => {
      route.value = props.route;
    });
    provide('navigate', props.navigate);
    provide('route', route);
  },
  components: {
    Button,
    PhoneIcon,
    DocumentIcon,
    CalendarIcon,
    Pagination,
    TableFilters,
    Icon,
  },
  data() {
    return {
      companies: [] as ProductionCompany[],
      skipFetch: true,
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
      } as PaginationType,
      filters: [
        {
          id: 'company_name',
          field: 'name',
          label: 'Company Name',
          value: '',
          type: FilterUIType.Text,
          operator: FilterOperator.ILike,
          active: false,
          sortable: true,
          sortDirection: '',
        },
        {
          id: 'phone',
          field: 'phone',
          label: 'Phone Number',
          value: '',
          type: FilterUIType.Text,
          operator: FilterOperator.ILike,
          active: false,
          sortable: true,
          sortDirection: '',
        },
      ] as Filter[],
    };
  },
  computed: {
    activeFilters(): Filter[] {
      return this.filters.filter(({ active }) => active);
    },
  },
  watch: {
    'route.query': {
      deep: true,
      handler(newVal, oldVal) {
        if (newVal.page === oldVal.page && newVal.limit === oldVal.limit) {
          return;
        }
        this.copyQueryParamsToPagination();
        this.skipFetch = true;
        this.getCompanies();
      },
    },
    activeFilters: {
      deep: true,
      handler(newVal, oldVal) {
        if (newVal === oldVal) {
          return;
        }
        this.pagination.page = 1;
        this.skipFetch = true;
        this.getCompanies();
      },
    },
    pagination: {
      deep: true,
      handler(newVal, oldVal) {
        if (newVal.page === oldVal.page && newVal.limit === oldVal.limit) {
          return;
        }
        this.updateRoute(newVal);
        if (!this.skipFetch) {
          this.getCompanies();
        }
        this.skipFetch = false;
      },
    },
  },
  async mounted() {
    this.establishQueryParams();
    await this.getCompanies();
  },
  methods: {
    formatPhoneNumber,
    goToCompany(id: number) {
      this.navigate({
        pathname: `/companies/${id}`,
      });
    },
    async getCompanies(): Promise<void> {
      const filtersString = convertFiltersToQuery(this.activeFilters);
      const sortsString = convertSortsToQuery(this.activeFilters);
      const {
        data: { meta, data },
      } = await listProductionCompanies(
        this.pagination,
        filtersString,
        sortsString,
      );

      const { current_page, last_page } = meta;
      if (current_page > last_page) {
        this.pagination.page = 1;
        this.updateRoute(this.pagination);
        await this.getCompanies();
        return;
      }

      this.companies = data;
      this.pagination = copyMetaToPagination(meta);
    },
    establishQueryParams(): void {
      this.copyQueryParamsToPagination();
      this.updateRoute(this.pagination);
    },
    copyQueryParamsToPagination(): void {
      const { query } = this.route;
      this.pagination.page = Number(query.page) || 1;
      this.pagination.limit = Number(query.limit) || 10;
    },
    updateRoute(pagination: PaginationType): void {
      updateReactRouteWithPagination(
        this.navigate,
        pagination,
        this.route.query,
      );
    },
  },
});
</script>
