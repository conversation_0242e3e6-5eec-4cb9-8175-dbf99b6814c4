{"address": {"street": "<PERSON><PERSON><PERSON>", "street2": "", "city": "New york", "state": {"id": 32, "name": "New York", "key": "NY"}, "zip": "10003"}, "socialSecurityNumber": "230030304", "confirmSocialSecurityNumber": "230030304", "dateOfBirth": "1998-07-17T00:00:00.000-07:00", "maritalStatus": {"id": 1, "name": "Single or Married Filed Separately", "key": "single_or_married_filed_separately", "description": "Single or Married Filed Separately", "created_at": "2025-01-31T01:35:16.410-08:00", "updated_at": "2025-01-31T01:35:16.410-08:00"}, "citizenshipStatus": {"id": 1, "name": "Citizen", "key": "citizen", "description": "Someone who is a citizen.", "createdAt": "2025-01-31T01:35:16.173-08:00", "updatedAt": "2025-01-31T01:35:16.173-08:00"}, "proofOfIdentity": {"type": {"id": 2, "key": "identity", "name": "Identity", "description": "Proof of Identity, like a drivers license.", "createdAt": "2025-01-31T01:35:17.110-08:00", "updatedAt": "2025-01-31T01:35:17.110-08:00"}, "documentTitle": "New york", "documentNumber": "New york", "expiresAt": "2029-04-30T00:00:00.000-07:00", "issuingAuthority": "New york"}, "ethnicity": {"id": 3, "name": "Hispanic", "key": "hispanic", "description": "Someone who is hispanic", "createdAt": "2025-01-31T01:35:16.281-08:00", "updatedAt": "2025-01-31T01:35:16.281-08:00"}, "gender": {"id": 1, "name": "Male", "key": "male"}, "unions": []}