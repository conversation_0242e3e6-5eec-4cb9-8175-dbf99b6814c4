import { useNavigate, useLocation } from 'react-router';
import { applyPureVueInReact } from 'veaury';
import ProfileEditVue from '@/views/UserCrewFormView.vue';
import { useAuth } from '@/reactComponents/AppHooks';
const ProfileEditViewReact = applyPureVueInReact(ProfileEditVue);

const ProfileEditView = () => {
  useAuth();

  //TODO use this to get useAuth from handler??
  // const matches = useMatches()

  const navigate = useNavigate();
  const { pathname } = useLocation();
  let editMode = false;
  if (pathname === '/profile/edit') {
    editMode = true;
  }
  return <ProfileEditViewReact editMode={editMode} navigate={navigate} />;
};

export default ProfileEditView;
