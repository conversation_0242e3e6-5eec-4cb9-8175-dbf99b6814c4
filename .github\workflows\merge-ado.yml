on:
  push:
    branches:
      - devel

name: Deploy typescript-adonis-api to FPS-EKS-NONPROD cluster (dev)

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    environment: dev

    steps:

      - name: Call FPS Microservices DEV ADO Pipeline
        run: |
          curl -i --request POST \
           -u ":${{ secrets.ADO_PAT_TOKEN }}" \
           --header "Content-Type: application/json" \
           --data '{
             "resources": {
                 "repositories": {
                     "self": {
                         "refName": "refs/heads/main"
                     }
                 }
             },
             "templateParameters": {
                 "clustername": "'fps-eks-nonprod'",
                 "sourcebranch": "'devel'",
                 "repository": "'typescript-vuejs-fps'",
                 "environment": "'dev'"
             }
          }' \
          https://dev.azure.com/castandcrew/FPS-Infrastructure/_apis/pipelines/1223/runs?api-version=7.2-preview.1