import React from 'react';
import PropTypes from 'prop-types';

import WarningIcon from '@mui/icons-material/Warning';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import HistoryIcon from '@mui/icons-material/History';
import CalendarIcon from '@mui/icons-material/DateRange';
import DownloadIcon from '@mui/icons-material/Download';
import DeleteIcon from '@mui/icons-material/Delete';

import {
  Box,
  IconButton,
  Button,
  Loader,
  Menu,
  MenuItem,
  Modal,
  StatusBadge,
  Text,
  Tooltip,
} from '@/reactComponents/library';

import { useLocation, useNavigate } from 'react-router';

import {
  downloadTimecard as getTimecardPdf,
  deleteTimecard,
} from '@/services/timecards';

import {
  snackbarSuccess,
  snackbarAxiosErr,
} from '@/reactComponents/library/Snackbar';
import { BatchStatusCapsPayEnum } from '@/types/Batch';

import HistoryModal from './HistoryModal';
import ChangeWeekModal from './ChangeWeekModal';
import MoveToBatchModal from '../sharedComponents/MoveToBatchModal';

const styles = {
  root: {
    display: 'flex',
    gap: 3,
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  week: {
    flexGrow: 1,
    display: 'flex',
    alignItems: 'center',
    gap: 2,
  },
  batch: {
    display: 'flex',
    gap: 1,
    alignItems: 'center',
  },
  changeWeekDisabled: {
    color: 'warning.main',
    cursor: 'default',

    '& .MuiSvgIcon-root': {
      mr: 1,
    },
    '& .MuiListItemText-primary': {
      flexGrow: 1,

      '& span': {
        display: 'block',
      },
    },
  },
};

const CHANGE_WEEK_DISABLED_MESSAGE = {
  inactive: 'You cannot change the pay period for deleted timecard.',
  submitted:
    'You cannot change the pay period for this timecard as it has been Submitted to Payroll.',
};

const HeaderActions = (props) => {
  const {
    timecard,
    member,
    batchDetails,
    refreshTimecard,
    updateTimecard,
    readOnlyMode = false,
  } = props;
  const { payPeriod, isActive } = timecard;
  const navigate = useNavigate();
  const location = useLocation();
  const navFrom = location?.state?.from;

  const [menuOpen, setMenuOpen] = React.useState(false);
  const menuRef = React.useRef(null);

  const [historyOpen, setHistoryOpen] = React.useState(false);
  const [deleting, setDeleting] = React.useState(false);
  const [deleteTimecardModalOpen, setDeleteTimecardModalOpen] =
    React.useState(false);

  const [changeWeekOpen, setChangeWeekOpen] = React.useState(false);
  const [moveModalOpen, setMoveModalOpen] = React.useState(false);

  const handleClose = () => {
    setMenuOpen(false);
  };

  const downloadTimecard = () => {
    if (!timecard.fileId) return;
    const link = document.createElement('a');
    link.href = getTimecardPdf(timecard.id);

    link.setAttribute(
      'download',
      `${member.user.lastName}_${member.user.firstName}_${
        timecard.project.name
      }_${payPeriod?.startsAt?.toFormat('MM_dd')}_timecard.pdf`,
    );

    document.body.appendChild(link);
    link.click();
  };

  const handleDeleteTimecard = () => {
    setDeleting(true);
    deleteTimecard(timecard.id)
      .then(async () => {
        snackbarSuccess('Timecard deleted successfully.');
        const to =
          navFrom === 'project-admin-timecards'
            ? `/projects/${timecard.project.hashId}/admin/members/${member.id}/member/timecards`
            : `/projects/${timecard.project.hashId}/admin/time`;
        navigate(to, { blockable: false });
      })
      .catch((err) => snackbarAxiosErr(err, 'Error deleting timecard.'))
      .finally(() => {
        setDeleting(false);
      });
  };

  let canChangeWeek = true;
  let changeWeekMessage = '';
  if (!isActive) {
    // inactive timecard (deleted)
    canChangeWeek = false;
    changeWeekMessage = CHANGE_WEEK_DISABLED_MESSAGE.inactive;
  } else if (
    // submitted timecard (to Payroll)
    batchDetails &&
    Object.keys(batchDetails).length > 0 &&
    batchDetails?.batchStatus !== BatchStatusCapsPayEnum.Open
  ) {
    canChangeWeek = false;
    changeWeekMessage = CHANGE_WEEK_DISABLED_MESSAGE.submitted;
  }

  const handleChangeWeek = (newData) => updateTimecard(newData);

  return (
    <Box sx={styles.root}>
      <Box sx={styles.week}>
        <Text variant="disReg">
          {`${payPeriod.startsAt.toFormat(
            'ccc. MM/dd',
          )} - ${payPeriod.endsAt.toFormat('ccc. MM/dd')}`}
        </Text>
        <StatusBadge tcStatusId={timecard.status.id}>
          {timecard.status.name}
        </StatusBadge>
      </Box>
      <Box sx={styles.batch}>
        <Text variant="baseStrong">
          Batch:{' '}
          {batchDetails.payrollReferenceId
            ? `${batchDetails.payrollReferenceId} - ${batchDetails.batchDescription}`
            : 'No batch'}
        </Text>
        <Button
          variant="secondary"
          onClick={() => setMoveModalOpen(true)}
          data-testid="move-to-batch-btn"
          disabled={readOnlyMode}
        >
          {batchDetails.payrollReferenceId ? 'Change batch' : 'Move to batch'}
        </Button>
        {moveModalOpen && (
          <MoveToBatchModal
            open={moveModalOpen}
            setOpen={setMoveModalOpen}
            batch={batchDetails}
            project={timecard.project}
            timecard={timecard}
            onMoveComplete={refreshTimecard}
          />
        )}
      </Box>

      <IconButton ref={menuRef} onClick={() => setMenuOpen(true)}>
        <MoreVertIcon />
      </IconButton>
      <Menu anchorEl={menuRef.current} open={menuOpen} onClose={handleClose}>
        <MenuItem
          onClick={() => {
            setHistoryOpen(true);
            handleClose();
          }}
          label="View history"
          icon={<HistoryIcon />}
        />
        <MenuItem
          onClick={() => {
            canChangeWeek && setChangeWeekOpen(true);
            canChangeWeek && setMenuOpen(false);
          }}
          sx={canChangeWeek ? {} : styles.changeWeekDisabled}
          label={
            canChangeWeek ? (
              'Change Week'
            ) : (
              <Tooltip title={changeWeekMessage}>Change week</Tooltip>
            )
          }
          icon={
            canChangeWeek ? <CalendarIcon /> : <WarningIcon color="warning" />
          }
        />
        <MenuItem
          onClick={downloadTimecard}
          label="Download employee timecard"
          icon={<DownloadIcon />}
        />
        <MenuItem
          onClick={() => {
            setDeleteTimecardModalOpen(true);
          }}
          label="Delete timecard"
          icon={<DeleteIcon />}
          disabled={readOnlyMode}
        />
      </Menu>
      <ChangeWeekModal
        open={changeWeekOpen}
        onClose={() => setChangeWeekOpen(false)}
        onSubmit={handleChangeWeek}
        member={member}
        timecard={timecard}
      />

      <HistoryModal
        timecardId={timecard.id}
        open={historyOpen}
        setOpen={setHistoryOpen}
      />

      <Modal
        open={deleteTimecardModalOpen}
        setOpen={setDeleteTimecardModalOpen}
        onCancel={() => setDeleteTimecardModalOpen(false)}
        onSubmit={handleDeleteTimecard}
        loading={deleting}
        submitText="Delete"
        title="Delete Timecard"
      >
        {deleting ? (
          <Loader />
        ) : (
          <div>Are you sure you would like to delete this timecard?</div>
        )}
      </Modal>
    </Box>
  );
};

HeaderActions.propTypes = {
  timecard: PropTypes.object.isRequired,
  member: PropTypes.object.isRequired,
  batchDetails: PropTypes.object.isRequired,
  updateTimecard: PropTypes.func.isRequired,
  refreshTimecard: PropTypes.func.isRequired,
  readOnlyMode: PropTypes.bool.isRequired,
};

export default HeaderActions;
