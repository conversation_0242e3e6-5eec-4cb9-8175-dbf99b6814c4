import React from 'react';
import PropTypes from 'prop-types';

//TODO - see if we need these delete or update functions
// import { deleteMileageForm, updateMileageForm } from '@/services/mileage-forms';
import {
  getMileageFormPdf,
  // updateOrCreateMileageForm,
  getKitRentalPdf,
  // updateOrCreateKitRental,
} from '@/services/timecards';
import {
  getReimbursementReceipt,
  // updateTimecardReimbursements,
} from '@/services/reimbursements';

// import {
//   // deleteKitRental,
//   // updateKitRental
// } from '@/services/kit-rentals';
import { useParams } from 'react-router';

import {
  Box,
  FileUpload,
  IconButton,
  Modal,
  Tooltip,
} from '@/reactComponents/library';

import DownloadIcon from '@mui/icons-material/Download';
import UploadedIcon from '@mui/icons-material/CheckCircle';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
const FileUploadCell = (props) => {
  const {
    updateCell,
    reimbursement,
    column,
    reimbursementType,
    fileName,
    disabled,
  } = props;
  const { columnId } = column;

  const documentId = reimbursement[columnId];
  const reimbursementId = reimbursement?.id;

  const { timecardId } = useParams();
  const [modalOpen, setModalOpen] = React.useState(false);

  const handleDownload = async () => {
    const handlers = {
      mileageForm: () => getMileageFormPdf(timecardId),
      kitRental: () => getKitRentalPdf(timecardId),
      otherExpense: () => getReimbursementReceipt(reimbursement.id),
    };
    const handler = handlers[reimbursementType];
    if (!handler) {
      return console.error(`Invalid reimbursement type "${reimbursementType}"`);
    }
    const link = document.createElement('a');
    link.href = handler();
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
  };

  return (
    <Box>
      {documentId && (
        <Box>
          {reimbursementId ? (
            <Tooltip title={fileName}>
              <IconButton onClick={handleDownload}>
                <DownloadIcon color="primary" />
              </IconButton>
            </Tooltip>
          ) : (
            <Tooltip title="Document successfully uploaded">
              <UploadedIcon color="success" />
            </Tooltip>
          )}
          <IconButton
            onClick={() => {
              setModalOpen(true);
            }}
            disabled={disabled}
          >
            <ClearOutlinedIcon />
          </IconButton>
        </Box>
      )}
      {!documentId && (
        <FileUpload
          documentId={documentId}
          setDocumentId={updateCell}
          disabled={disabled}
        />
      )}
      <Modal
        title="Delete Attachment"
        open={modalOpen}
        setOpen={setModalOpen}
        onSubmit={() => {
          setModalOpen(false);
          updateCell(null);
          //TODO - do we need to actually delete from server?
        }}
        submitText="Delete"
        onCancel={() => setModalOpen(false)}
      >
        Are you sure you want to delete the attachment?
      </Modal>
    </Box>
  );
};

FileUploadCell.propTypes = {
  updateCell: PropTypes.func.isRequired,
  value: PropTypes.any,
  column: PropTypes.object.isRequired,
  reimbursement: PropTypes.object.isRequired,
  reimbursementType: PropTypes.string.isRequired,
  fileName: PropTypes.string.isRequired,
  disabled: PropTypes.bool,
};

export default FileUploadCell;
