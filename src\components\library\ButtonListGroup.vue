<template>
  <div :class="listGroupClasses">
    <slot />
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  props: {
    variant: {
      type: String,
      default: 'default',
    },
  },
  computed: {
    listGroupClasses() {
      return {
        default:
          'w-48 text-sm font-medium bg-white border border-gray-200 rounded-lg dark:bg-gray-700 dark:border-gray-500',
        // Add more variants here if needed
      }[this.variant];
    },
  },
});
</script>
