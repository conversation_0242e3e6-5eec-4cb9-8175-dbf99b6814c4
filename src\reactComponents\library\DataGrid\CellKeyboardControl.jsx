import { Box } from '@mui/material';
import { forwardRef } from 'react';
import PropTypes from 'prop-types';

export const findPreviousTabCell = (api, rowId, field) => {
  // const rowIds = api.getAllRowIds();
  const visibleColumns = api.getVisibleColumns();
  const currentColumnIndex = visibleColumns.findIndex(
    (col) => col.field === field,
  );

  let prevColumnIndex = currentColumnIndex - 1;
  let prevColumnDef = visibleColumns[prevColumnIndex];
  let hasHiddenSetup = prevColumnDef?.isHidden;
  let hidden = true;

  while (hasHiddenSetup && hidden) {
    const row = api.getRow(rowId);
    hidden = hasHiddenSetup(row);
    if (hidden) {
      prevColumnIndex -= 1;
      prevColumnDef = visibleColumns[prevColumnIndex];
      hasHiddenSetup = prevColumnDef?.isHidden;
    }
  }

  prevColumnDef && api.setCellFocus(rowId, prevColumnDef.field);
};

export const findNextTabCell = (api, rowId, field) => {
  // const rowIds = api.getAllRowIds();
  const visibleColumns = api.getVisibleColumns();
  const currentColumnIndex = visibleColumns.findIndex(
    (col) => col.field === field,
  );

  let nextColumnIndex = currentColumnIndex + 1;
  let nextColumnDef = visibleColumns[nextColumnIndex];
  let hasHiddenSetup = nextColumnDef?.isHidden;
  let hidden = true;
  while (hasHiddenSetup && hidden) {
    const row = api.getRow(rowId);
    hidden = hasHiddenSetup(row);
    if (hidden) {
      nextColumnIndex += 1;
      nextColumnDef = visibleColumns[nextColumnIndex];
      hasHiddenSetup = nextColumnDef?.isHidden;
    }
  }

  nextColumnDef && api.setCellFocus(rowId, nextColumnDef.field);
};

// for future use, a Grid Cell level keyboard control
// this is a wrapper around edit cells that gives more precise control over keyboard events

const CellKeyboardFocusBox = forwardRef((props, ref) => {
  const { api, rowId, colDef, children, sx = {}, onKeyDown, ...rest } = props;
  const { field, useDefaultKeyboardControl } = colDef;

  const handleKeyDown = (e) => {
    const key = e.key;
    const shiftKey = e.shiftKey;
    if (!useDefaultKeyboardControl) {
      switch (key) {
        case 'Tab':
          if (shiftKey) {
            findPreviousTabCell(api, rowId, field);
          } else {
            findNextTabCell(api, rowId, field);
          }
          // in order to take back keyboard control from the Grid system, it's necessary to stop propagation and prevent default
          e.stopPropagation();
          e.preventDefault();
          break;
        case 'Enter':
          e.stopPropagation();
          findNextTabCell(api, rowId, field);
          break;
        default:
          break;
      }
    }

    if (onKeyDown) onKeyDown(e);
  };
  const otherProps = {
    ...rest,
  };
  return (
    <Box
      onKeyDown={handleKeyDown}
      ref={ref}
      sx={
        typeof sx === 'function'
          ? (theme) => {
              return { ...sx(theme) };
            }
          : { ...sx }
      }
      {...otherProps}
    >
      {children}
    </Box>
  );
});

CellKeyboardFocusBox.propTypes = {
  api: PropTypes.object.isRequired,
  rowId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  colDef: PropTypes.object.isRequired,
  children: PropTypes.node,
  sx: PropTypes.oneOfType([PropTypes.object, PropTypes.func]),
  onKeyDown: PropTypes.func,
};

export default CellKeyboardFocusBox;
