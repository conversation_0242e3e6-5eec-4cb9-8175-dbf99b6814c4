<template>
  <div class="w-full relative" ref="dropdownContainer">
    <label
      v-if="label"
      for="email"
      class="block text-xs font-xs text-gray-700 dark:text-gray-400 pb-1"
    >
      {{ label }} <span v-if="required" class="text-red-600">*</span>
    </label>
    <div
      class="pl-7 flex relative items-center flex-wrap border border-gray-300 dark:border-gray-500 rounded p-1 gap-1 bg-white dark:bg-gray-700 h-8.5"
      @click="focusInput"
    >
      <div
        v-for="item in selectedItems"
        :key="item[valueName]"
        class="flex items-center bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-500 text-gray-800 dark:text-gray-200 rounded-full text-xs px-1 py-0.5"
      >
        {{ item[displayName] }}
        <span
          class="ml-2 cursor-pointer text-gray-800 dark:text-gray-200"
          @click.stop="removeItem(item)"
          >×</span
        >
      </div>
      <Icon name="search" class="absolute top-2 left-2 w-4 h-4" />
      <div>
        <input
          type="text"
          v-model="searchQuery"
          :placeholder="selectedItems.length ? '' : placeholder"
          @focus="isDropdownVisible = true"
          @input="handleSearch"
          ref="input"
          class="border-none flex-1 p-2 outline-none h-6 focus:ring-white dark:bg-gray-700 focus:outline-none dark:focus:ring-gray-700 focus:border-blue-500 placeholder:text-sm placeholder:text-gray-500"
        />
      </div>
    </div>
    <ul
      ref="dropdownList"
      @scroll="handleScroll"
      v-if="isDropdownVisible"
      class="absolute w-full bg-white border border-gray-300 dark:border-gray-500 max-h-[150px] overflow-y-auto z-10 list-none p-0 mt-1 rounded-mdsi dark:bg-gray-700 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none overflow-auto"
    >
      <span
        v-if="!filteredMenuItems.length && !loading"
        disabled
        class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-400"
      >
        No items found
      </span>

      <li v-if="loading" disabled>
        <span
          class="block px-4 py-2 text-sm animate-pulse text-gray-700 dark:text-gray-400"
        >
          loading..
        </span>
      </li>
      <li
        v-for="item in filteredMenuItems"
        :key="item[valueName]"
        class="p-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center"
        @click="toggleSelection(item)"
      >
        <input
          type="checkbox"
          :value="item"
          :checked="
            selectedItems.some(
              (selected: any) => selected[valueName] === item[valueName],
            )
          "
          class="appearance-none w-5 h-5 border-1 mr-2 border-pink-500 rounded-md bg-pink-100 relative cursor-pointer flex items-center justify-center checked:bg-pink-200 checked:border-pink-500 focus:outline-none focus:ring-0"
        />
        {{ item[displayName] }}
      </li>
      <li v-if="loading" class="flex justify-center">
        <Spinner class="w-4 h-4" />
      </li>
    </ul>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import Icon from '@/components/Icon.vue';
import { debounce, type DebounceSettings } from 'lodash';

export default defineComponent({
  components: {
    Icon,
  },
  props: {
    menuItems: {
      type: Array as () => any[],
      required: true,
    },
    displayName: {
      type: String,
      default: 'label',
    },
    valueName: {
      type: String,
      default: '',
    },
    label: {
      type: String,
      default: '',
    },
    required: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: 'Search',
    },
    modelValue: {
      type: Array as () => any[],
      default: () => [],
    },
    infiniteScroll: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      searchQuery: '',
      selectedItems: [...this.modelValue],
      isDropdownVisible: false,
    };
  },
  watch: {
    modelValue: {
      handler(newValue) {
        this.selectedItems = [...newValue];
      },
      immediate: true,
      deep: true,
    },
  },
  computed: {
    filteredMenuItems() {
      if (!this.searchQuery || this.searchQuery == '') {
        return this.menuItems;
      }
      return this.menuItems.filter((item: any) =>
        item[this.displayName]
          .toLowerCase()
          .includes(this.searchQuery.toLowerCase()),
      );
    },
  },
  methods: {
    handleSearch() {
      this.isDropdownVisible = true;
      this.$emit('update:searchQuery', this.searchQuery);
      if (this.searchQuery) {
        this.filteredMenuItems = [];
      }
    },
    removeItem(item: any) {
      this.selectedItems = this.selectedItems.filter(
        (selected: any) => selected[this.valueName] !== item[this.valueName],
      );
      this.$emit('update:modelValue', [...this.selectedItems]);
    },
    focusInput() {
      (this.$refs.input as HTMLInputElement)?.focus();
    },
    handleClickOutside(event: Event) {
      const dropdown = this.$refs.dropdownContainer as HTMLElement;
      if (dropdown && !dropdown.contains(event.target as Node)) {
        this.isDropdownVisible = false;
        this.searchQuery = '';
        this.$emit('update:searchQuery', this.searchQuery);
      }
    },
    toggleSelection(item: any) {
      const isSelected = this.selectedItems.some(
        (selected: any) => selected[this.valueName] === item[this.valueName],
      );
      if (isSelected) {
        this.removeItem(item);
      } else {
        this.selectedItems.push(item);
        this.$emit('update:modelValue', [...this.selectedItems]);
      }
    },
    handleScroll() {
      if (!this.infiniteScroll) return;

      debounce(
        () => {
          const dropdown = this.$refs.dropdownList as HTMLElement;
          if (
            dropdown.scrollTop + dropdown.clientHeight >=
            dropdown.scrollHeight - 5
          ) {
            this.$emit('load-more');
          }
        },
        1500,
        { leading: true, maxWait: 0 } as DebounceSettings,
      )();
    },
  },

  mounted() {
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeUnmount() {
    document.removeEventListener('click', this.handleClickOutside);
  },
});
</script>

<style scoped>
input[type='checkbox']::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 14px;
  height: 14px;
  background-image: url('@/assets/check.png');
  background-size: cover;
  background-repeat: no-repeat;
  display: none;
}

input[type='checkbox']:checked::after {
  display: block;
}

[type='checkbox']:checked:hover {
  border-color: rgb(236 72 153 / var(--tw-border-opacity));
  background-color: rgb(252 231 243 / var(--tw-bg-opacity));
}
</style>
