import { defineConfig } from 'cypress';

import viteConfig from './viteConfig';

export default defineConfig({
  reporter: 'cypress-mochawesome-reporter',
  reporterOptions: {
    charts: true,
    videoOnFailOnly: false,
    embeddedScreenshots: true,
    inlineAssets: true,
  },
  video: true,
  e2e: {
    setupNodeEvents(on, config) {
      require('cypress-mochawesome-reporter/plugin')(on);
      return config;
    },
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    env: {
      projectId: '',
      projectName: 'TEST-Mayer and Sons 3349',
      projectMemberId: '',
      payPeriodId: '',
      timecardId: '',
      timecardDayIds: [],
      employeeName: 'PA-test PA-test',
      companyName: 'TEST COMMERCIALS - TEST01',
      companyId: '',
      projectHashId: '',
      batchId: '',
      documentId: '',
    },
    retries: {
      runMode: 1,
    },
  },

  component: {
    devServer: {
      framework: 'react',
      bundler: 'vite',
      viteConfig: viteConfig('test'),
    },
    supportFile: 'cypress/support/component.ts',
    retries: {
      runMode: 1,
    },
  },
});
