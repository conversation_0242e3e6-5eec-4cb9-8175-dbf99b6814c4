import React from 'react';
import PropTypes from 'prop-types';
import { Link, useNavigate } from 'react-router-dom';

import { Link as MuiLink, Text } from '@/reactComponents/library';
import OktaLoginContext from '../../Context';

const BackToLogin = ({ label = null, ...props }) => {
  const { setTransaction, client } = React.useContext(OktaLoginContext);
  const navigate = useNavigate();

  const goToLogin = async () => {
    setTransaction(null);
    await client.logout();
    navigate('/');
  };

  return (
    <MuiLink
      component={Link}
      onClick={goToLogin}
      {...props}
      data-testid="login-backToLogin-link"
    >
      <Text variant="smReg">{label || '« back'}</Text>
    </MuiLink>
  );
};
BackToLogin.propTypes = {
  label: PropTypes.string,
};

export default BackToLogin;
