import { createMemoryRouter, RouterProvider } from 'react-router-dom';
import LoginViewHOC from '@/reactComponents/views/LoginView';
import OktaAuthProvider from '@/reactComponents/library/OktaLogin/Provider';

describe('LoginViewHOC component testing', () => {
  beforeEach(() => {
    const router = createMemoryRouter([
      {
        path: '/',
        element: <LoginViewHOC />,
      },
    ]);
    cy.intercept('GET', '**/v2/api/v1/sessions/me', {
      statusCode: 200,
    }).as('me');

    cy.mount(
      <OktaAuthProvider>
        <RouterProvider router={router} />
      </OktaAuthProvider>,
    );
  });
  it('should correctly render the LoginViewHOC component, including the welcome message, mobile phone input, and the enter option', () => {
    cy.contains('Welcome').should('be.visible');
    cy.contains('Phone Number').should('be.visible');
    cy.contains('Next').should('be.visible');
  });

  it('should allow the user to enter a mobile phone number', () => {
    cy.get('[data-testid="login-phone-input"]')
      .should('be.visible')
      .type('+************');
  });

  it('should submit the form with enter option', () => {
    cy.intercept('POST', '**/v2/api/core/auth/user', {
      statusCode: 200,
      body: {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        uid: 'dabkininoasd8021',
      },
    }).as('loginRequest');
    cy.get('[data-testid="login-submit-btn"]').click();
  });
});
