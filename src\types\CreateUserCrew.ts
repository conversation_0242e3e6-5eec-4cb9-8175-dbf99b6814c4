import type { DateTime } from 'luxon';
import type Address from './Address';

export default interface CreateUserCrewDTO {
  dateOfBirth: DateTime;
  ethnicity: any;
  citizenshipStatus: any;
  address: Address;
  socialSecurityNumber: string;
  confirmSocialSecurityNumber: string;
  maritalStatus: any;
  proofOfIdentity: any;
  gender: any;
  unions: any[];

  workAuthorizationNumber: string;
  workAuthorizationType: any;
  foreignPassportIssuingCountry: string;
  workAuthorizationExpirationDate?: DateTime;

  withholdingsBasedOnDependents?: number | null;
  withholdingsFromOtherIncome: number | null;
  withholdingsFromDeductions: number | null;
  extraWithholdings: number | null;
  multipleJobsOrSpouseWorks: boolean;
  exemptFromWithholdingsBasedOnDependents: boolean;
}
