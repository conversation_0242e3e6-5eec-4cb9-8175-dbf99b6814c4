import maskIt from '@/utils/maskIt';
import allCountries from './countries.json';

export const STEP_UNKNOWN = 'UNKNOWN';
export const STEP_SUCCESS = 'SUCCESS';
export const STEP_ERROR = 'ERROR';
export const STEP_IDENTIFY = 'IDENTIFY';
export const STEP_PASSWORD = 'PASSWORD';
export const STEP_PHONE_CODE = 'PHONE';
export const STEP_EMAIL_CODE = 'EMAIL';
export const STEP_VERIFY_CODE_SMS = 'VERIFY_SMS';
export const STEP_VERIFY_CODE_EMAIL = 'VERIFY_EMAIL';
export const STEP_REGISTER = 'REGISTER';

export const COUNTRIES = allCountries;

export const getTransactionStep = (transaction) => {
  if (!transaction) return STEP_UNKNOWN;
  if (transaction.step) return transaction.step;

  const status = transaction?.status;
  if (status?.match(/^success$/i)) return STEP_SUCCESS;
  if (status?.match(/^failure$|^terminal$/i)) return STEP_ERROR;

  const name = transaction?.nextStep?.name;
  const type = transaction?.nextStep?.type;
  const inputs = transaction?.nextStep?.inputs?.reduce(
    (acc, input) => ({ ...acc, [input.name]: input }),
    {},
  );
  if (name?.match(/^identify$/i)) return STEP_IDENTIFY;
  if (name?.match(/^select-authenticator-authenticate$/i)) {
    const inputOptions =
      inputs.authenticator?.options?.map((opt) => opt.value) || [];
    if (inputOptions.includes('okta_email')) {
      return STEP_EMAIL_CODE;
    }
    return STEP_PASSWORD;
  }
  if (name?.match(/^challenge-authenticator$/i)) {
    if (type?.match(/^password$/i)) return STEP_PASSWORD;
    if (type?.match(/^phone$|^email$/i)) {
      if (inputs['verificationCode']) {
        return type?.match(/^phone/)
          ? STEP_VERIFY_CODE_SMS
          : STEP_VERIFY_CODE_EMAIL;
      }
    }
  }

  if (name?.match(/^authenticator-verification-data$/i)) {
    const inputOptions =
      inputs.methodType?.options?.map((opt) => opt.value) || [];
    if (inputOptions.includes('email')) {
      return STEP_EMAIL_CODE;
    }
    return STEP_PHONE_CODE;
  }

  return STEP_UNKNOWN;
};

export const getTransactionId = (transaction, type) => {
  const auth = (transaction.neededToProceed || []).find(
    (n) => n.name === 'select-authenticator-authenticate',
  );
  if (!auth) return;

  const val = auth.value.find((v) => v.name === 'authenticator');
  if (!val) return;

  const opt = val.options.find((o) => o.relatesTo.type === type);
  if (!opt) return;

  return opt.value.form.value.find((v) => v.name === 'id')?.value;
};

export const isInternationalPhone = (val) => Boolean(val.match(/^\+/));

export const formatPhone = (val) => {
  val = val.trim();
  const international = isInternationalPhone(val);
  val = val.replace(/\D/g, '');

  if (international) {
    return `+${val.substring(0, 12)}`;
  } else {
    return maskIt(val, '###-###-####');
  }
};

export const findCountryByCode = (value) => {
  const matches = [
    value.substring(0, 1),
    value.substring(0, 2),
    value.substring(0, 3),
    value.substring(0, 4),
  ];
  const countryScore = (country) => {
    const { code } = country;
    const score = code.startsWith(matches[3])
      ? 4
      : code.startsWith(matches[2])
      ? 3
      : code.startsWith(matches[1])
      ? 2
      : 1;
    return { country, score };
  };
  const countries = COUNTRIES.filter(
    (c) =>
      c.code === matches[0] ||
      c.code === matches[1] ||
      c.code === matches[2] ||
      c.code === matches[3],
  )
    .map(countryScore)
    .sort((a, b) => b.score - a.score);
  return countries[0]?.country;
};

export const authApiError = (response) => {
  const messages = response?.messages || [];
  if (!messages.length) return;

  return messages[0].message;
};

export const decodeAccessToken = (token) => {
  const payload = token.toString().split('.')[1];
  if (!payload) return null;

  try {
    const decoded = atob(payload);
    return JSON.parse(decoded);
  } catch (e) {
    return null;
  }
};
