/// <reference types="cypress" />

export function createTimecard(): Cypress.Chainable<any> {
  const projectData = {
    projectId: Cypress.env('projectId'),
    payPeriodId: Cypress.env('payPeriodId'),
    timeZone: 'America/Bogota',
  };
  const url = `${Cypress.env('BASE_URL')}api/core/project-members/${Cypress.env(
    'projectMemberId',
  )}/timecards`;
  return cy
    .request({
      method: 'POST',
      url,
      body: projectData,
      headers: {
        'Content-Type': 'application/json',
      },
    })
    .then((response) => {
      return response;
    });
}

export function createPaperWork(): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/projects/${Cypress.env(
    'projectId',
  )}/starting-paperwork/sign?useLoanOut=false`;
  return cy.fixture('e2ePayloads/project/paperWork').then((data) => {
    return cy
      .request({
        method: 'POST',
        url,
        body: data,
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then((response) => {
        return response;
      });
  });
}

export function createProject(projectDTO: object): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/projects`;

  cy.log(JSON.stringify(projectDTO));
  return cy
    .request({
      method: 'POST',
      url,
      body: JSON.stringify(projectDTO),
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 150000,
    })
    .then((response) => {
      return response;
    });
}

export function crewCreateTimecard(): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/timecards/`;
  const body = {
    projectId: Cypress.env('projectId'),
    payPeriodId: Cypress.env('payPeriodId'),
    timeZone: 'America/Bogota',
  };
  return cy
    .request({
      method: 'POST',
      url,
      body: body,
      headers: {
        'Content-Type': 'application/json',
      },
    })
    .then((response) => {
      return response;
    });
}
