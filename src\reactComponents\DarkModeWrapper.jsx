import React from 'react';
import PropTypes from 'prop-types';

import { useColorScheme } from '@mui/material/styles';

const DarkModeWrapper = (props) => {
  const { isDarkModeEnabled, children } = props;

  const { setMode } = useColorScheme();
  React.useEffect(() => {
    const mode = isDarkModeEnabled ? 'dark' : 'light';
    setMode(mode);
  }, [isDarkModeEnabled, setMode]);

  return <React.Fragment>{children}</React.Fragment>;
};

DarkModeWrapper.propTypes = {
  isDarkModeEnabled: PropTypes.bool,
  children: PropTypes.node.isRequired,
};

export default DarkModeWrapper;
