import type { Pagination } from '@/types/Pagination';
import axios, { type AxiosResponse } from 'axios';
import { coreBaseUrl } from '../utils/constants';

export const getUnions = async (
  search?: string,
  pagination?: Pagination,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/unions/`;
  const response = await axios.get(url, {
    params: { search, ...pagination },
    withCredentials: true,
  });
  return response;
};
