import React from 'react';
// import Project from '@/types/Project';
// import ProjectMember from '@/types/ProjectMember';
// import User from '@/types/User';
import { ChevronRightIcon } from '@heroicons/react/20/solid';
import { Icon } from '@/reactComponents/library';
import { ProjectHeaderContext } from './ProjectHeader.jsx';
import ProjectStore from '@/reactComponents/stores/project';
import PropTypes from 'prop-types';

const mapDynamicBreadcrumb = (breadcrumb, context = {}) => {
  const regex = /{([^{}]+)}/g;

  // Replace all placeholders within the string
  const replacedBreadcrumb = breadcrumb.replace(
    regex,
    (match, variableName) => {
      const variableParts = variableName.split('.');
      let variableValue = context;

      for (const part of variableParts) {
        variableValue = variableValue?.[part];

        if (variableValue === undefined) {
          // If any part of the variable path doesn't exist, return an empty string
          return '';
        }
      }

      return variableValue;
    },
  );
  return replacedBreadcrumb;
};

const ProjectHeaderBreadcrumb = (props) => {
  const { className } = props;
  const ProjectContext = React.useContext(ProjectHeaderContext);
  const { route, navigate, matches, project } = ProjectContext;

  const [user, setUser] = React.useState(null);

  const projectMemberId = route.params.projectMemberId;
  const projectId = project?.id;
  const hashId = route.params.hashId;
  const userCrewProjectIdKey = `${projectMemberId}-${projectId}`;

  React.useEffect(() => {
    const fetchProjectMember = async () => {
      if (!projectMemberId || !projectId) {
        return;
      }
      const projectMember = await ProjectStore.fetchProjectMember(
        projectMemberId,
      );
      setUser(projectMember?.user || null);
    };
    fetchProjectMember();
  }, [userCrewProjectIdKey, projectMemberId, projectId]);

  const routePath = React.useMemo(() => {
    const paths = matches;
    const routes = paths.filter((path) => path?.handle?.breadcrumb);
    return routes;
  }, [matches]);

  const rawBreadcrumbs = React.useMemo(() => {
    const breadcrumbs = routePath.map((route) => ({
      text: route?.handle?.breadcrumb,
      to: {
        pathname: route.pathname,
        params: {
          hashId,
          projectMemberId: projectMemberId,
        },
      },
    }));
    return breadcrumbs;
  }, [routePath, hashId, projectMemberId]);

  const breadcrumbs = React.useMemo(() => {
    return rawBreadcrumbs.map(({ text, to }) => ({
      text: mapDynamicBreadcrumb(text, { user, project }),
      to: to,
    }));
  }, [rawBreadcrumbs, user, project]);

  const handleNavigate = (route, params, e) => {
    if (navigate) {
      navigate({
        pathname: route.to.pathname,
      });
    }
  };

  return (
    <div className={'flex' + (className ? ` ${className}` : '')}>
      <div className="flex items-center space-x-3" aria-label="Breadcrumb">
        {breadcrumbs.map((breadcrumb, breadcrumbIndex) => (
          <div key={`${breadcrumb.text}`} className="flex items-center">
            {breadcrumbIndex !== 0 && (
              <ChevronRightIcon
                className="h-5 w-5 flex-shrink-0 text-gray-400 mr-3"
                aria-hidden="true"
              />
            )}
            <button
              className="text-sm cursor-pointer font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              onClick={(e) => handleNavigate(breadcrumb, undefined, e)}
            >
              {breadcrumbIndex === 0 ? (
                <Icon name="home" className="w-5 h-5 ml-1" />
              ) : (
                <span>{breadcrumb.text}</span>
              )}
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

ProjectHeaderBreadcrumb.propTypes = {
  className: PropTypes.string,
};

export default ProjectHeaderBreadcrumb;
