import React from 'react';
import PropTypes from 'prop-types';
import { UserNotFound, getUserInfo } from '@/services/okta';

import {
  Box,
  Button,
  CircularProgress,
  InputAdornment,
  Text,
  TextInput,
} from '@/reactComponents/library';
import {
  COUNTRIES,
  STEP_EMAIL_CODE,
  STEP_PHONE_CODE,
  STEP_REGISTER,
  authApiError,
  formatPhone,
  getTransactionStep,
  isInternationalPhone,
  findCountryByCode,
} from '../../utils';
import OktaLoginContext from '../../Context';
import CountrySelect from './CountrySelect';

const styles = {
  root: {
    display: 'flex',
    flexDirection: 'column',
    gap: 2,
    width: '100%',
    alignItems: 'center',

    '& >.MuiBox-root': {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      gap: 1,
    },

    '& .MuiFormLabel-root': {
      display: 'flex',
      justifyContent: 'center',
    },
  },
  inputs: {
    display: 'flex',
    flexDirection: 'column',
    gap: 1,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',

    '& .MuiButtonGroup-root': {
      '& fieldset': {
        borderRadius: 0,
      },

      '& >div:first-of-type': {
        '& fieldset': {
          borderTopLeftRadius: 8,
          borderBottomLeftRadius: 8,
          borderRightColor: 'transparent',
        },

        '&.Mui-focused fieldset': {
          borderRightColor: 'primary.main',
        },
      },

      '& >div:last-of-type fieldset': {
        borderTopRightRadius: 8,
        borderBottomRightRadius: 8,
      },
    },
  },
  phoneInput: {
    minWidth: 200,
    maxWidth: 240,

    '& .MuiFormHelperText-root': {
      textAlign: 'center',
    },
    '& input:focus': {
      boxShadow: 'none',
    },
  },
};

const Identify = ({ onSuccess }) => {
  const { client: authClient, setTransaction } =
    React.useContext(OktaLoginContext);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState();
  const [phone, setPhone] = React.useState('');
  const [formattedPhone, setFormattedPhone] = React.useState('');
  const [country, setCountry] = React.useState(COUNTRIES[0]);
  const [isInternational, setIsInternational] = React.useState(false);
  const phoneInputRef = React.useRef();

  const changePhone = (value, newCountry = null) => {
    setError(null);
    const isInternational = isInternationalPhone(value);
    setIsInternational(isInternational);
    const val = value.replace(/\D/g, '');
    setFormattedPhone(formatPhone(value));

    if (isInternational) {
      const country = findCountryByCode(val);
      if (country) setCountry(country);

      setPhone(`+${val.substring(0, 12)}`);
    } else {
      setPhone(`+${(newCountry || country).code}${val.replace(/^0/, '')}`);
    }
  };

  const changeCountry = (newCountry) => {
    setCountry(newCountry);
    changePhone(formattedPhone, newCountry);
  };

  const identifyUser = async (phone) => {
    const { data: userInfo } = await getUserInfo(phone);

    const response = await authClient.proceed({ username: userInfo.email });
    const nextStep = getTransactionStep(response);
    setTransaction(response);

    if (![STEP_PHONE_CODE, STEP_EMAIL_CODE].includes(nextStep)) {
      const err = authApiError(response) || `Invalid Response: ${nextStep}`;
      throw new Error(err);
    }
    return { userInfo, nextStep };
  };

  const submit = (e) => {
    e.preventDefault();

    const goNext = async () => {
      try {
        setError(null);
        setLoading(true);

        const { nextStep, userInfo } = await identifyUser(phone);
        setLoading(false);
        onSuccess({ nextStep, form: { phone, user: userInfo } });
      } catch (e) {
        if (e instanceof UserNotFound) {
          setLoading(false);
          return onSuccess({
            nextStep: STEP_REGISTER,
            form: {
              phone,
              encryptedPhone: e.data?.encryptedPhone,
              user: e.data?.user,
            },
          });
        }
        await authClient.cancel();

        const message = authApiError(e) || e.message;
        await authClient.authenticate();
        setLoading(false);
        setError(message);
      }
    };
    if (phone) goNext();
  };

  return (
    <Box sx={styles.root} component="form" onSubmit={submit}>
      <TextInput
        label="Phone Number"
        value={formattedPhone}
        onChange={(e) => changePhone(e.target.value)}
        type="tel"
        inputMode="tel"
        autoFocus
        disabled={loading}
        error={Boolean(error)}
        helperText={
          error && (
            <Text variant="xsReg" color="error">
              {error}
            </Text>
          )
        }
        sx={styles.phoneInput}
        slotProps={{
          input: {
            sx: { pl: 0 },
            startAdornment: (
              <InputAdornment position="start">
                <CountrySelect
                  value={country}
                  onChange={(val) => {
                    changeCountry(val);
                    phoneInputRef.current?.focus();
                  }}
                  disabled={isInternational}
                />
              </InputAdornment>
            ),
          },
          htmlInput: {
            'data-testid': 'login-phone-input',
            ref: phoneInputRef,
          },
        }}
      />
      <Button
        type="submit"
        disabled={loading}
        endIcon={loading ? <CircularProgress size={20} color="white" /> : null}
        data-testid="login-submit-btn"
      >
        Next
      </Button>
    </Box>
  );
};
Identify.propTypes = {
  onSuccess: PropTypes.func.isRequired,
};

export default Identify;
