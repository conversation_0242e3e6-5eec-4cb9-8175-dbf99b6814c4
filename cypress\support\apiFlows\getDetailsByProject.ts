import { fetchProjectIdentifiersByName } from '../apiHelpers';

//Generic Before hook to fetch project details by project name
export function getDetailsByProject(projectName: string) {
  cy.log(`projectName: ${projectName}`);
  cy.then(() => {
    return fetchProjectIdentifiersByName(projectName).then((project) => {
      if (!project || !project.id) {
        throw new Error('Project not found or invalid Project ID');
      }
      Cypress.env('projectId', project.id);
      Cypress.env('projectHashId', project.hashId);
    });
  });
}
