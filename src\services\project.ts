import type Batch from '@/types/Batch';
import type { Pagination } from '@/types/Pagination';
import type Project from '@/types/Project';
import type ProjectMember from '@/types/ProjectMember';
import type { Tableview } from '@/types/Tableview';
import axios, { type AxiosResponse } from 'axios';
import { coreBaseUrl } from '../utils/constants';

export const getByCode = async (
  code: string,
): Promise<AxiosResponse<Project | null>> => {
  const url = `${coreBaseUrl()}/projects/code/${code}`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const getProjectByHashId = async (
  hashId: number | string,
): Promise<AxiosResponse<Project | null>> => {
  const url = `${coreBaseUrl()}/projects/${hashId}`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const isProjectAdmin = async (
  id: number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${id}/is-admin`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const updateProject = async (
  project: Project,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${project.id}`;
  const response = await axios.patch(url, project, { withCredentials: true });
  return response;
};

export const createProject = async (
  project: Project,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects`;
  const response = await axios.post(url, project, { withCredentials: true });
  return response;
};

export const getProjectPayPeriods = async (
  id: number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${id}/pay-periods/remaining`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const getPayPeriods = async (
  id: number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${id}/pay-periods`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const listProjects = async (
  signal: AbortSignal,
  pagination?: Pagination,
  filters?: string,
): Promise<AxiosResponse<Tableview<Project>>> => {
  const url = `${coreBaseUrl()}/projects/`;
  const response = await axios.get(url, {
    withCredentials: true,
    params: {
      ...pagination,
      filters,
    },
    signal,
  });

  return response;
};

export const listProjectTimecards = async (
  projectId: string | number,
  filters?: any,
  sort?: string,
  pagination?: any,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/timecards/`;
  const response = await axios.get(url, {
    withCredentials: true,
    params: {
      filters,
      sort,
      ...pagination,
    },
  });
  return response;
};

export const listProjectMembers = async (
  projectId: string | number,
  projectMemberTypeKey?: string,
  pagination?: Partial<Pagination>,
  filters?: string,
  sorts?: string,
  search?: string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/members${
    projectMemberTypeKey ? `?projectMemberTypeKey=${projectMemberTypeKey}` : ''
  }`;
  const response = await axios.get(url, {
    withCredentials: true,
    params: {
      ...pagination,
      filters,
      sorts,
      search,
    },
  });
  return response;
};

export const listProjectMemberUnions = async (
  projectId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/member-unions`;
  const response = await axios.get(url, {
    withCredentials: true,
  });
  return response;
};

export const previewStartingPaperwork = (
  projectId: string | number,
  useLoanOut: boolean,
): string => {
  const url = `${coreBaseUrl()}/projects/${projectId}/starting-paperwork/preview?useLoanOut=${useLoanOut}`;
  return url;
};

export const signStartingPaperwork = async (
  projectId: string | number,
  signature: string,
  useLoanOut: boolean,
  selectedOptionalDocuments: any[],
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/starting-paperwork/sign`;
  const response = await axios.post(
    url,
    { signature, selectedOptionalDocuments },
    { withCredentials: true, params: { useLoanOut } },
  );
  return response;
};

export const getStartingPaperwork = async (
  projectId: string | number,
): Promise<AxiosResponse<Document[]>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/starting-paperwork`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const getStartingPaperworkPdf = (projectId: string | number): string => {
  const url = `${coreBaseUrl()}/projects/${projectId}/starting-paperwork/pdf`;
  return url;
};

export const getCrewStartingPaperworkPdf = async (
  projectId: string | number,
  userCrewId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/crew/${userCrewId}/starting-paperwork/pdf`;
  return await axios({
    url,
    method: 'GET',
    responseType: 'json',
  });
};

export const createProjectDocumentTemplate = async (
  projectId: string | number,
  projectDocumentTemplate: any,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/document-templates/create`;
  const response = await axios.post(url, projectDocumentTemplate, {
    withCredentials: true,
  });
  return response;
};

export const listProjectDocumentTemplates = async (
  projectId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/document-templates`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const getProjectDocumentTemplate = async (
  projectId: string | number,
  projectDocumentTemplateId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/document-templates/${projectDocumentTemplateId}`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const updateProjectDocumentTemplate = async (
  projectId: string | number,
  projectDocumentTemplate: any,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/document-templates/${
    projectDocumentTemplate.id
  }`;
  const response = await axios.patch(url, projectDocumentTemplate, {
    withCredentials: true,
  });
  return response;
};

export const getBatches = async (
  projectId: string | number,
  pagination?: Pagination,
  filters?: string,
): Promise<AxiosResponse<Tableview<Batch>>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/batches`;
  const response = await axios.get(url, {
    withCredentials: true,
    params: {
      ...pagination,
      filters,
    },
  });
  return response;
};

export const createBatch = async (
  projectId: string | number,
  name: string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/batches`;
  const response = await axios.post(url, { name }, { withCredentials: true });
  return response;
};

export const addMember = async (
  projectId: string | number,
  userId: string | number,
  departmentId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/members/${userId}`;
  const response = await axios.post(
    url,
    { departmentId },
    { withCredentials: true },
  );
  return response;
};

export const updateMember = async (
  projectId: string | number,
  onboarding: any,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/members`;
  const response = await axios.patch(url, onboarding, {
    withCredentials: true,
  });
  return response;
};

export const getCurrentMember = async (
  projectId: string | number | undefined,
): Promise<AxiosResponse<ProjectMember | null>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/members/current`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const isProjectOnboarded = async (
  projectId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/is-onboarded`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const exportProjectStartingPaperwork = async (
  projectId: string | number,
): Promise<AxiosResponse> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/starting-paperwork/export`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const listProjectShootLocations = async (
  projectId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/shoot-locations`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const listProjectUnions = async (
  projectId: string | number,
  payrollProjectLocationId: string | number | null,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/unions`;
  const response = await axios.get(url, {
    params: { payrollProjectLocationId },
    withCredentials: true,
  });
  return response;
};

export const listProjectOccupations = async (
  projectId: string | number,
  payrollProjectLocationId: string | number | null,
  unions: any[] | null,
  search?: string,
  pagination?: Pagination,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/occupations`;
  const unionString = unions
    ? unions.map((u) => u.castAndCrewId || u.id).join(',')
    : '';
  const response = await axios.get(url, {
    params: {
      payrollProjectLocationId,
      unions: unionString,
      search,
      ...pagination,
    },
    withCredentials: true,
  });
  return response;
};

export const listProjectReimbursementTypes = async (
  projectId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/reimbursement-types`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const listProjectHireLocations = async (
  projectId: string | number,
  payrollProjectLocationId: string | number,
  unionLocalId: string | number,
  payrollOccupationCode: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/hire-locations`;
  const response = await axios.get(url, {
    params: {
      payrollProjectLocationId,
      unionLocalId,
      payrollOccupationCode,
    },
    withCredentials: true,
  });
  return response;
};

export const getContractSettingsAndScheduleInfo = async (
  projectId: string | number,
  capsPayProjectLocationId: string | number,
  occupationCode: string,
  startDate: string,
  unionNumber: string,
  capsPayUnionId: string | number,
  capsPayHireLocationId?: string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/work-location/${capsPayProjectLocationId}/occupations/${occupationCode}/start-date/${startDate}/unions/${unionNumber}/${capsPayUnionId}/contract-settings-and-schedule-info`;
  const response = await axios.get(url, {
    withCredentials: true,
    params: {
      capsPayHireLocationId,
    },
  });
  return response;
};

export const getProjectSettingsInfo = async (
  projectId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/get-project-settings-info`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const getTrackingDetails = async (
  projectId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/tracking-details`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const listDayTemplates = async (
  projectId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/day-templates`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const applyDayTemplates = (
  timecardDays: any,
  dayTemplates: any,
): any => {
  return timecardDays.map((day: any) => {
    const template = dayTemplates.find((template: any) => {
      const dayDate = day.date.toFormat('yyyy-MM-dd');
      const templateDate = template.date
        ? template.date.toFormat('yyyy-MM-dd')
        : null;
      return dayDate === templateDate;
    });
    if (template) {
      day = makeDayFromTemplate(day, template);
      return {
        ...day,
      };
    }
    return day;
  });
};

export const applyDayTemplateForRow = (
  timecardDay: any,
  dayTemplates: any,
): any => {
  const template = dayTemplates.find((template: any) => {
    const dayDate = timecardDay.date.toFormat('yyyy-MM-dd');
    const templateDate = template.date
      ? template.date.toUTC().toFormat('yyyy-MM-dd')
      : null;
    return dayDate === templateDate;
  });
  if (template) {
    timecardDay = makeDayFromTemplate(timecardDay, template);
    return {
      ...timecardDay,
    };
  }
  return timecardDay;
};

export const makeDayFromTemplate = (day: any, template: any): any => {
  const newDay = {
    id: day.id,
    timecardId: day.timecardId,
    date: day.date,
    zipCode: day.zipCode,
    rate: day.rate,
    comments: day.comments,
    createdAt: day.createdAt,
    updatedAt: day.updatedAt,
    isActive: day.isActive,
    isRentalDay: day.isRentalDay,
    lineNumber: day.lineNumber,
    mealPenalties: day.mealPenalties,
    hoursWorked: day.hoursWorked,
    occupationId: day.occupationId,
    guaranteedHours: day.guaranteedHours,
    hasRerate: day.hasRerate,
    hasHoliday: day.hasHoliday,
    capsPayId: day.capsPayId,
    occupation: day.occupation,
    startsAt: template.generalCrewCall
      ? template.generalCrewCall?.toUTC()
      : day.startsAt?.toUTC(),
    endsAt: template.endsAt ? template.endsAt?.toUTC() : day.endsAt?.toUTC(),
    generalCrewCall: template.hasNdb ? template.generalCrewCall?.toUTC() : null,
    hasNdb: template.hasNdb,
    hasWalkingMeal: template.hasWalkingMeal,
    hasMealGraceOne: template.hasMealGraceOne,
    hasMealGraceTwo: template.hasMealGraceTwo,
    hasHalfDay: template.hasHalfDay,
    hasWrapGrace: template.hasWrapGrace,
    driveTime: template.driveTime,
    hotelToSetTime: template.hotelToSetTime,
    setToHotelTime: template.setToHotelTime,
    workStatusId: template.workStatusId
      ? template.workStatusId
      : day.workStatusId,
    workZoneId: template.workZoneId ? template.workZoneId : day.workZoneId,
    projectLocationId: template.projectLocationId
      ? template.projectLocationId
      : day.projectLocationId,
    workStatus: template.workStatus ? template.workStatus : day.workStatus,
    workZone: template.workZone ? template.workZone : day.workZone,
    projectShootLocation: template.projectShootLocation
      ? template.projectShootLocation
      : day.projectShootLocation,
    meals: template.meals.map((meal: any) => {
      return {
        ...day.meal,
        startsAt: meal.startsAt?.toUTC(),
        endsAt: meal.endsAt?.toUTC(),
      };
    }),
  };
  return newDay;
};

export const updateDayTemplates = async (
  projectId: string | number,
  templates: any, //TODO
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/day-templates`;
  const payload = {
    dayTemplates: templates,
  };
  const response = await axios.patch(url, payload, { withCredentials: true });
  return response;
};

export const deleteDayTemplateDay = async (
  dayTemplateId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/day-templates/${dayTemplateId}`;
  const response = await axios.delete(url, { withCredentials: true });
  return response;
};

export const getUnclaimedBatchDetails = async (
  projectId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/${projectId}/unclaimed-batch-details`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};
