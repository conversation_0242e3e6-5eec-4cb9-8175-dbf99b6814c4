import React from 'react';
import PropTypes from 'prop-types';

import { GridTextField, Autocomplete, Box } from '@/reactComponents/library';

const ReimbursementTypeCell = (props) => {
  const { expenseTypes, updateRow, reimbursement, disabled } = props;
  const { type, name = '' } = reimbursement;

  const [localName, setLocalName] = React.useState(name || '');

  const handleNameOnBlur = (event) => {
    const updated = { ...reimbursement, name: localName };
    updateRow(updated);
  };

  const handleChangeType = (event, newVal) => {
    const updated = { ...reimbursement, type: { ...newVal } };
    updateRow(updated);
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
      <GridTextField
        value={localName}
        onChange={(e) => setLocalName(e.target.value)}
        onBlur={handleNameOnBlur}
        disabled={disabled}
      />
      <Autocomplete
        value={type}
        options={expenseTypes}
        disableClearable={true}
        renderOption={(props, option) => (
          <li {...props} key={option.castAndCrewId}>
            {option.key}
          </li>
        )}
        getOptionLabel={(option) => option.key || ''}
        onChange={handleChangeType}
        disabled={disabled}
        renderInput={(params) => <GridTextField {...params} />}
      />
    </Box>
  );
};

ReimbursementTypeCell.propTypes = {
  column: PropTypes.object.isRequired,
  expenseTypes: PropTypes.array.isRequired,
  updateRow: PropTypes.func.isRequired,
  reimbursement: PropTypes.object.isRequired,
  disabled: PropTypes.bool,
};

export default ReimbursementTypeCell;
