import { Autocomplete } from '@mui/material';
import { TextFieldRaw as TextField, Tooltip } from '@/reactComponents/library';
import { useEffect, useRef, useState } from 'react';
import { tailwindInputFix } from '@/reactComponents/theme/components/utils';
import KeyboardControlBox from './CellKeyboardControl';
import lodashDebounce from 'lodash/debounce';
import CheckIcon from '@mui/icons-material/Check';

const GridAutocompleteCell = (params) => {
  const { update, api, colDef, options = [], value, id, row } = params;
  const infiniteScrolling = colDef?.infiniteScrolling;
  const checkHasNextPage = colDef?.checkHasNextPage;
  const hasNextPage = (checkHasNextPage && checkHasNextPage(params)) || false;
  const checkLoading = colDef?.checkLoading;
  const loading = (checkLoading && checkLoading(params)) || false;
  const onSearch = colDef?.onSearch ? colDef.onSearch(params) : null;
  const onSearchOptions = onSearch ? lodashDebounce(onSearch, 500) : null;

  const searchRef = useRef('');
  const [open, setOpen] = useState(false);
  const getOptionLabel =
    colDef.getOptionLabel ||
    function (option) {
      return option.label || option.name;
    };

  const inputRef = useRef(null);

  useEffect(() => {
    if (params.hasFocus) {
      inputRef?.current?.focus();
      setOpen(true);
    }
  }, [params.hasFocus]);

  const handleChange = (event, newValue) => {
    const isWorkDaysGrid = !!api;
    update({
      field: colDef.field,
      value: newValue,
      rowId: row.id,
      parentRowId: row.parentRowId,
      api: api,
      isWorkDaysGrid: isWorkDaysGrid,
    });
    api.setEditCellValue({
      id: id,
      field: colDef.field,
      value: newValue,
    });
  };
  const val = value || value?.shootLocation || null;

  return (
    <KeyboardControlBox
      sx={{ width: '100%', padding: '0 4px' }}
      api={api}
      rowId={id}
      colDef={colDef}
    >
      <Tooltip arrow title={val ? getOptionLabel(val) : ''} placement="top">
        <Autocomplete
          open={open}
          onOpen={() => {
            setOpen(true);
          }}
          onClose={() => {
            setOpen(false);
          }}
          loading={loading}
          slotProps={{
            listbox: {
              onScroll: (e) => {
                if (infiniteScrolling && hasNextPage) {
                  const { scrollTop, scrollHeight, clientHeight } = e.target;
                  if (scrollTop + clientHeight >= scrollHeight - 240) {
                    if (!loading) {
                      onSearchOptions(searchRef.current, infiniteScrolling);
                    }
                  }
                }
              },
            },
          }}
          options={options}
          getOptionLabel={getOptionLabel}
          renderOption={(liProps, option, state, ownerState) => {
            const { selected } = state;
            const { key, ...rest } = liProps;
            const { multiple } = ownerState;
            const showCheck = !multiple && selected;

            return (
              <li
                key={option?.id || key}
                style={{
                  width: '100%',
                  justifyContent: 'space-between',
                }}
                {...rest}
              >
                <span> {getOptionLabel(option)}</span>
                {showCheck && <CheckIcon color="primary" />}
              </li>
            );
          }}
          value={val}
          onChange={handleChange}
          sx={{
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            '& .MuiOutlinedInput-root': {
              padding: '0px',
            },
          }}
          renderInput={(params) => {
            params.onChange = (e) => {
              const v = e.target.value;
              searchRef.current = v;
              onSearchOptions && onSearchOptions(v);
            };
            params.onBlur = (e) => {
              searchRef.current = '';
              onSearchOptions && onSearchOptions('');
            };
            return (
              <TextField
                {...params}
                inputRef={inputRef}
                readOnly
                sx={{
                  '& .MuiOutlinedInput-root': {
                    padding: '0px',
                    fontSize: '14px',
                  },
                }}
                inputProps={{
                  ...params.inputProps,
                  className: tailwindInputFix,
                  style: { padding: '4px 0px 4px 4px' },
                }}
              />
            );
          }}
        />
      </Tooltip>
    </KeyboardControlBox>
  );
};

const AutocompleteCell = (params) => <GridAutocompleteCell {...params} />;

export default AutocompleteCell;
