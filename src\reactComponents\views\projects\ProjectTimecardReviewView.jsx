import { useRef, useImperativeHandle, forwardRef } from 'react';
import { applyPureVueInReact } from 'veaury';
import ProjectTimecardReviewViewVue from '../../../views/projects/ProjectTimecardReviewView.vue';
import { useAuth, useReactRouter } from '../../AppHooks';
import { useOutletContext } from 'react-router';

const ReactProjectTimecardReviewView = applyPureVueInReact(
  ProjectTimecardReviewViewVue,
);

const ProjectTimecardReviewView = forwardRef(() => {
  useAuth();
  const { navigate, route } = useReactRouter();
  const context = useOutletContext();
  const vueRef = useRef();

  useImperativeHandle(context.ref, () => ({
    vueRef: vueRef.current.__veauryVueRef__,
  }));

  return (
    <ReactProjectTimecardReviewView
      ref={vueRef}
      project={context.project}
      editDisabled={context.editDisabled}
      refresh={context.refresh}
      componentLoaded={context.componentLoaded}
      route={route}
      navigate={navigate}
    />
  );
});

export default ProjectTimecardReviewView;
