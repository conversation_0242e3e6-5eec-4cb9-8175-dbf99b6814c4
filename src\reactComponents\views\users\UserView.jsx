import React from 'react';
import { applyPureVueInReact } from 'veaury';
import UserHeaderVue from '@/components/UserHeader.vue';
import { getRoles } from '@/services/role';
import { getUser } from '@/services/users';
import { Outlet } from 'react-router';
import { useAuth, useReactRouter } from '@/reactComponents/AppHooks';
import { useDidMount } from '@/reactComponents/utils/customHooks';

const ReactUserHeaderView = applyPureVueInReact(UserHeaderVue);

const UserView = () => {
  useAuth();
  const { navigate, route } = useReactRouter();

  const { id } = route.params;

  const [user, setUser] = React.useState({});
  const [roles, setRoles] = React.useState([]);
  const [doneLoading, setDoneLoading] = React.useState(false);

  const init = async () => {
    const { data: userData } = await getUser(id);
    setUser(userData);

    const { data: rolesData } = await getRoles();
    setRoles(rolesData);
    setDoneLoading(true);
  };

  useDidMount(() => {
    init();
  }, []);

  return (
    <div className="flex flex-col h-screen pt-16">
      <ReactUserHeaderView user={user} route={route} navigate={navigate} />
      <div className="pt-40">
        {doneLoading && <Outlet context={{ user, roles }} />}
      </div>
    </div>
  );
};

export default UserView;
