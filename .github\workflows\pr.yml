name: build
on:
  pull_request:

defaults:
  run:
    shell: bash

jobs:
  build_core:
    name: Build and Run Tests for Core
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v2
        with:
          repository: ${{ github.event.pull_request.head.repo.full_name }}
          ref: ${{ github.event.pull_request.head.ref }}

      - name: Authenticate with private NPM package
        run: echo "//registry.npmjs.org/:_authToken=${{ secrets.NPM_TOKEN }}" > .npmrc

      - name: Install Dependencies
        run: npm install --legacy-peer-deps;

      # - name: Run Unit Tests
      #   run: npm run test:unit

      - name: Run Typecheck
        run: npm run type-check;

      - name: Run Build
        run: NODE_OPTIONS=--max-old-space-size=4096 npm run build
