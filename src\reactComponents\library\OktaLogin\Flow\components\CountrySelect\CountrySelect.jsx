import React from 'react';
import PropTypes from 'prop-types';
import {
  ArrowDropDown as DropDownIcon,
  ArrowDropUp as DropUpIcon,
  Search as SearchIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import _throttle from 'lodash/throttle';
import { FixedSizeList } from 'react-window';

import {
  Button,
  ClickAwayListener,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  MenuItem,
  Paper,
  Popper,
  Text,
  TextInput,
  Tooltip,
} from '@/reactComponents/library';
import { COUNTRIES } from '../../../utils';
import Flag from './Flag';

const styles = {
  button: {
    width: 60,
    height: 31,
    pr: 1,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
    color: 'black',
    justifyContent: 'flex-start',

    '& .MuiButton-endIcon': {
      ml: 0,
    },

    '&:hover': {
      backgroundColor: 'grey.200',
    },

    '&.Mui-disabled': {
      opacity: 0.75,
      color: 'black',

      '& .MuiButton-endIcon': {
        display: 'none',
      },
    },
  },
  buttonOpen: {
    backgroundColor: 'grey.200',

    '&:hover': {
      backgroundColor: 'grey.200',
    },
  },
  paper: {
    p: 1,
  },
  searchInput: {
    width: '100%',
    mb: 1,

    '& .MuiInputBase-root': {
      pl: 1,
      pr: 0,

      '& .MuiIconButton-root': {
        width: 24,
        height: 24,
      },
    },
    '& input': {
      fontSize: '0.8rem',
      '&:focus': {
        boxShadow: 'none',
      },
    },
    '& .MuiSvgIcon-root': {
      opacity: 0.5,
      width: 16,
    },
  },
  list: {
    maxHeight: 200,
    width: 240,
    overflowY: 'auto',
    overflowX: 'hidden',
  },
  listItem: {
    fontSize: '0.8rem',
    width: '100%',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    gap: 1,
    borderRadius: 1,
  },
  listItemSelected: {
    backgroundColor: 'primary.main',
    color: 'primary.contrastText',

    '& .MuiListItemIcon-root': {
      color: 'primary.contrastText',
    },

    '&:hover': {
      backgroundColor: 'primary.main',
      color: 'primary.contrastText',

      '& .MuiListItemIcon-root': {
        color: 'primary.contrastText',
      },
    },
  },
  countryCode: {
    justifyContent: 'end',
    fontSize: '0.8rem',
    opacity: 0.5,
  },
  countryName: {
    '& .MuiTypography-root': {
      fontSize: '0.8rem',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      whiteSpace: 'nowrap',
      maxWidth: '100%',
    },
  },
  countryFlag: {
    color: '#000',
    minWidth: '20px !important',
  },
  noResults: {
    fontSize: '0.8rem',
    fontStyle: 'italic',
  },
};

const CountrySelect = ({ value, onChange, disabled = false }) => {
  const [anchor, setAnchor] = React.useState();
  const [searchKeyword, setSearchKeyword] = React.useState('');
  const [countryOptions, setCountryOptions] = React.useState(COUNTRIES);
  const { name: selectedName, abbr: selectedAbbr } = value || COUNTRIES[0];

  const isOpen = React.useMemo(() => Boolean(anchor), [anchor]);
  const hasCountries = React.useMemo(
    () => countryOptions.length > 0,
    [countryOptions],
  );

  const listItemHeight = 30;
  const listHeight = React.useMemo(
    () => Math.min(countryOptions.length * listItemHeight, 200),
    [countryOptions],
  );

  const search = React.useCallback((q) => {
    const doSearch = _throttle((q) => {
      const test = (val) =>
        val.toString().toLowerCase().indexOf(q.toString().toLowerCase()) >= 0;
      const res = COUNTRIES.filter(
        ({ code, name, abbr }) => test(code) || test(name) || test(abbr),
      );
      setCountryOptions(res);
    }, 800);
    doSearch(q);
  }, []);

  const close = () => {
    setAnchor(null);
    setCountryOptions(COUNTRIES);
    setSearchKeyword('');
  };

  const CountryListItem = ({ index, style }) => {
    const country = countryOptions[index];
    const { name, abbr, code } = country || {};
    const flagIcon = <Flag code={abbr} />;

    return (
      <MenuItem
        key={`country-option-${name}`}
        onClick={() => {
          setAnchor(null);
          onChange(country);
          setSearchKeyword('');
        }}
        sx={{
          ...style,
          ...styles.listItem,
          ...(name === selectedName ? styles.listItemSelected : {}),
        }}
        data-testid={`login-countrySelect-country-${abbr}`}
      >
        <ListItemIcon sx={styles.countryFlag}>{flagIcon}</ListItemIcon>
        <ListItemText sx={styles.countryName}>
          <Tooltip title={name} enterDelay={1000}>
            <Text>{name}</Text>
          </Tooltip>
        </ListItemText>
        <Text sx={styles.countryCode}>+{code}</Text>
      </MenuItem>
    );
  };
  CountryListItem.propTypes = {
    index: PropTypes.number.isRequired,
    style: PropTypes.object.isRequired,
  };

  React.useEffect(() => {
    search(searchKeyword);
  }, [searchKeyword, search]);

  const flagIcon = <Flag code={selectedAbbr} />;

  return (
    <>
      <Button
        variant="default"
        endIcon={isOpen ? <DropUpIcon /> : <DropDownIcon />}
        onClick={(e) => setAnchor(isOpen ? null : e.currentTarget)}
        sx={{ ...styles.button, ...(isOpen ? styles.buttonOpen : {}) }}
        disableRipple
        disabled={disabled}
        data-testid="login-countrySelect-btn"
      >
        {flagIcon}
      </Button>
      {isOpen && (
        <ClickAwayListener onClickAway={() => close()}>
          <Popper anchorEl={anchor} open placement="bottom-start">
            <Paper sx={styles.paper}>
              <TextInput
                value={searchKeyword}
                placeholder="search..."
                sx={styles.searchInput}
                autoFocus
                onChange={(e) => {
                  setSearchKeyword(e.target.value);
                }}
                slotProps={{
                  input: {
                    startAdornment: <SearchIcon />,
                    endAdornment: searchKeyword?.length > 0 && (
                      <IconButton
                        variant="text"
                        onClick={() => {
                          setSearchKeyword('');
                          setCountryOptions(COUNTRIES);
                        }}
                        size="small"
                      >
                        <CloseIcon fontSize="small" />
                      </IconButton>
                    ),
                  },
                  htmlInput: {
                    'data-testid': 'login-countrySelect-search-input',
                  },
                }}
              />
              {!hasCountries && (
                <List
                  sx={{ ...styles.list, ...styles.noResults }}
                  data-testid="login-countrySelect-list"
                >
                  <ListItem data-testid="login-countrySelect-noItems">
                    no results.
                  </ListItem>
                </List>
              )}
              {hasCountries && (
                <FixedSizeList
                  width={styles.list.width}
                  height={listHeight}
                  itemCount={countryOptions.length}
                  itemSize={listItemHeight}
                >
                  {CountryListItem}
                </FixedSizeList>
              )}
            </Paper>
          </Popper>
        </ClickAwayListener>
      )}
    </>
  );
};
CountrySelect.propTypes = {
  value: PropTypes.object.isRequired,
  onChange: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
};

export default CountrySelect;
