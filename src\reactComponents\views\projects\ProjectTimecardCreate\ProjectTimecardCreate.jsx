import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Autocomplete, Button } from '@/reactComponents/library';

const ProjectTimecardCreate = ({
  payPeriodsDropdownItems,
  loading,
  onClickCreateTimecard,
  createTimecardLoading = false,
}) => {
  const [payPeriod, setPayPeriod] = useState();

  const handleCreateTimecard = () => {
    if (!payPeriod) {
      return;
    }
    onClickCreateTimecard(payPeriod);
  };

  const onSetPayPeriod = (_, newValue) => {
    setPayPeriod(newValue);
  };

  return (
    <div className="flex justify-center mx-auto w-full py-2 px-5">
      <div className="max-w-7xl">
        <Autocomplete
          label="Pay Period"
          value={payPeriod}
          options={payPeriodsDropdownItems}
          onChange={onSetPayPeriod}
          loading={loading}
          placeholder="Select"
          data-testid="payPeriod-select-dropdown"
        />
        <p>Select the week you worked.</p>
        <div className="flex justify-center">
          <Button
            className="mt-2"
            onClick={handleCreateTimecard}
            data-testid="payPeriod-continue-btn"
            loading={createTimecardLoading}
          >
            Continue
          </Button>
        </div>
      </div>
    </div>
  );
};

ProjectTimecardCreate.propTypes = {
  loading: PropTypes.bool.isRequired,
  createTimecardLoading: PropTypes.bool.isRequired,
  onClickCreateTimecard: PropTypes.func.isRequired,
  payPeriodsDropdownItems: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.number.isRequired,
      label: PropTypes.string.isRequired,
    }),
  ).isRequired,
};

export default ProjectTimecardCreate;
