<template>
  <div class="bg-white dark:bg-gray-900 p-4 rounded-xl">
    <div class="flex items-center justify-between">
      <h2 class="text-xl font-medium mb-3" data-testid="timecard-day-header">
        {{ timecardDay.date.toFormat('cccc') }}
        <span class="text-xl font-light">
          {{ timecardDay.date.toFormat('MM/dd') }}
        </span>
      </h2>
      <button
        v-if="index !== 0 && !disabled"
        class="inline-flex items-center gap-x-1.5 -mt-2 rounded-md bg-white dark:bg-gray-800 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-200 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 hover:dark:bg-gray-700"
        @click="$emit('copy-last-day', timecardDay.id)"
        :data-testid="`timecard-copyLast-btn-${timecardDay?.date.toFormat(
          'cccc',
        )}`"
      >
        <DocumentDuplicateIcon class="mr-1.5 h-5 w-5 flex-shrink-0" />
        Copy Last Day
      </button>
    </div>
    <div class="flex">
      <Dropdown
        v-model="newWorkStatus"
        class="grow"
        :menu-items="workStatus"
        :loading="workStatus.length === 0"
        display-name="name"
        label="Work Status"
        :data-testid="`timecard-workStatus-dropdown-${timecardDay?.date.toFormat(
          'cccc',
        )}`"
      />
    </div>
    <div class="flex">
      <Dropdown
        v-if="projectShootLocations.length > 0"
        class="grow"
        v-model="newProjectShootLoc"
        label="Work Location"
        :menu-items="projectShootLocations"
        display-name="shootLocation.locationName"
        :data-testid="`timecard-workLocation-dropdown-${timecardDay?.date.toFormat(
          'cccc',
        )}`"
      >
        <template #label>
          {{
            `${newProjectShootLoc?.shootLocation.locationName} (${newProjectShootLoc?.zip})`
          }}
        </template>
        <template #item="{ value: projectShootLocation }">
          {{
            `${projectShootLocation?.shootLocation.locationName} (${projectShootLocation?.zip})`
          }}
        </template>
      </Dropdown>
      <TextInput
        v-else
        class="grow"
        label="ZIP Code"
        :model-value="timecardDay.zipCode"
        @update:modelValue="updateZIPCode($event)"
        :disabled="disabled"
      />
    </div>

    <div
      v-if="!hourlyExempt && checkShowTimes(timecardDay.workStatus)"
      class="flex space-x-1"
    >
      <TimePicker
        class="basis-1/4"
        label="Starts At"
        :model-value="timecardDay.startsAt"
        @update:modelValue="updateStartsAt($event)"
        :disabled="disabled"
        :increment="timePickerIncrement"
        :data-testid="`timecard-startsAt-${timecardDay?.date.toFormat('cccc')}`"
      />
      <div class="basis-1/2 space-x-1">
        <div
          v-for="(meal, mealIndex) in timecardDay.meals"
          :key="`meal-${mealIndex}`"
          class="basis-1/2 space-x-1 flex items-center"
        >
          <TimePicker
            label="Meal Start"
            :model-value="meal.startsAt"
            @update:modelValue="updateMealStartsAt($event, mealIndex)"
            :disabled="disabled"
            :increment="timePickerIncrement"
            :data-testid="`timecard-mealStart-${timecardDay?.date.toFormat(
              'cccc',
            )}`"
          />
          <TimePicker
            label="Meal End"
            :model-value="meal.endsAt"
            @update:modelValue="updateMealEndsAt($event, mealIndex)"
            :disabled="disabled"
            :increment="timePickerIncrement"
            :data-testid="`timecard-mealEnd-${timecardDay?.date.toFormat(
              'cccc',
            )}`"
          />
        </div>
        <div class="flex items-center justify-center mt-5 space-x-2">
          <Button
            color="gray"
            size="sm"
            class="flex items-center"
            @click="removeMeal"
            :disabled="disabled"
            :data-testid="`timecard-removeMeal-btn-${timecardDay?.date.toFormat(
              'cccc',
            )}`"
          >
            <MinusIcon class="h-4 w-4" />
          </Button>
          <div class="text-center">Meals</div>
          <Button
            color="gray"
            size="sm"
            class="flex items-center"
            @click="addMeal"
            :disabled="disabled"
            :data-testid="`timecard-addMeal-btn-${timecardDay?.date.toFormat(
              'cccc',
            )}`"
          >
            <PlusIcon class="h-4 w-4" />
          </Button>
        </div>
      </div>
      <TimePicker
        class="basis-1/4"
        label="Ends At"
        :model-value="timecardDay.endsAt"
        @update:modelValue="updateEndsAt($event)"
        :disabled="disabled"
        :increment="timePickerIncrement"
        :data-testid="`timecard-endsAt-${timecardDay?.date.toFormat('cccc')}`"
      />
    </div>
    <TextArea
      class="mt-2"
      label="Notes"
      :model-value="timecardDay.comments"
      @update:modelValue="updateComments($event)"
      :disabled="disabled"
      :data-testid="`timecard-notes-${timecardDay?.date.toFormat('cccc')}`"
    />
    <div
      v-if="!hourlyExempt && checkShowTimes(timecardDay.workStatus)"
      class="flex justify-start mt-2"
    >
      <Toggle
        :modelValue="timecardDay.hasNdb"
        @update:modelValue="updateNdbSetting($event)"
        :data-testid="`timecard-ndb-toggle-${timecardDay?.date.toFormat(
          'cccc',
        )}`"
      >
        NDB
      </Toggle>
    </div>
  </div>
</template>

<script lang="ts">
import Button from '@/components/library/Button.vue';
import Dropdown from '@/components/library/Dropdown.vue';
import TextArea from '@/components/library/TextArea.vue';
import TextInput from '@/components/library/TextInput.vue';
import TimePicker from '@/components/library/TimePicker.vue';
import Toggle from '@/components/library/Toggle.vue';
import type Project from '@/types/Project';
import type TimecardDay from '@/types/TimecardDay';
import type { WorkStatus } from '@/types/WorkStatus';
import type ProjectShootLocation from '@/types/ProjectShootLocation';
import { WorkStatusKeys, WorkStatusKeysNoTimeEntry } from '@/types/WorkStatus';
import {
  DocumentDuplicateIcon,
  MinusIcon,
  PlusIcon,
} from '@heroicons/vue/24/outline';
import type { DateTime } from 'luxon';
import type { PropType } from 'vue';
import { defineComponent } from 'vue';
export default defineComponent({
  components: {
    Dropdown,
    TextInput,
    TimePicker,
    TextArea,
    Button,
    MinusIcon,
    PlusIcon,
    DocumentDuplicateIcon,
    Toggle,
  },
  props: {
    index: {
      type: Number,
      required: true,
    },
    timecardDay: {
      type: Object as PropType<TimecardDay>,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    hourlyExempt: {
      type: Boolean,
      required: true,
    },
    project: {
      type: Object as PropType<Project>,
      required: true,
    },
    projectShootLocations: {
      type: Array as PropType<ProjectShootLocation[]>,
      required: true,
    },
    workStatus: {
      type: Array as PropType<WorkStatus[]>,
      required: true,
    },
  },
  data() {
    return {
      newWorkStatus: this.timecardDay.workStatus,
      newProjectShootLoc: this.timecardDay.projectShootLocation,
    };
  },
  emits: ['update', 'add-meal', 'remove-meal', 'copy-last-day'],
  computed: {
    timePickerIncrement(): number {
      return this.project?.minuteIncrement?.key || 15;
    },
    workStatusKeys() {
      return WorkStatusKeys;
    },
  },
  watch: {
    workStatus: {
      handler: function (val: WorkStatus[]) {
        const workStatusOptions = val;
        let timecardDay = this.timecardDay;
        if (workStatusOptions.length > 0 && !timecardDay.workStatus) {
          const selectedWorkStatus = workStatusOptions.find(
            (eachWorkStatus: WorkStatus) =>
              eachWorkStatus?.key?.toUpperCase() === WorkStatusKeys.KEY_WORK,
          );
          timecardDay = { ...timecardDay };
          timecardDay.workStatus = selectedWorkStatus;
          this.newWorkStatus = selectedWorkStatus;
          this.emitUpdate(timecardDay);
        }
      },
      immediate: true,
    },
    timecardDay: {
      handler: function (val: TimecardDay) {
        this.newWorkStatus = val.workStatus;
        this.newProjectShootLoc = val.projectShootLocation;
      },
      immediate: true,
      deep: true,
    },
    newWorkStatus: {
      handler: function (val: WorkStatus) {
        const timecardDay = { ...this.timecardDay };
        timecardDay.workStatus = val;
        const workStatusKey = val?.key?.toUpperCase() || '';
        const hideTimesKeys = Object.values(
          WorkStatusKeysNoTimeEntry,
        ) as string[];
        if (hideTimesKeys.includes(workStatusKey)) {
          timecardDay.hasNdb = false;
        }
        this.emitUpdate(timecardDay);
      },
    },
    newProjectShootLoc: {
      handler: function (val: ProjectShootLocation) {
        const timecardDay = { ...this.timecardDay };
        timecardDay.projectShootLocation = val;
        this.emitUpdate(timecardDay);
      },
    },
  },
  methods: {
    updateRate(rate: number) {
      const timecardDay = { ...this.timecardDay };
      timecardDay.rate = rate;
      this.emitUpdate(timecardDay);
    },
    updateZIPCode(zipCode: number) {
      const timecardDay = { ...this.timecardDay };
      timecardDay.zipCode = zipCode;
      this.emitUpdate(timecardDay);
    },
    updateStartsAt(startsAt: DateTime) {
      const timecardDay = { ...this.timecardDay };
      timecardDay.startsAt = startsAt;
      this.emitUpdate(timecardDay);
    },
    updateEndsAt(endsAt: DateTime) {
      const timecardDay = { ...this.timecardDay };
      timecardDay.endsAt = endsAt;
      this.emitUpdate(timecardDay);
    },
    updateMealStartsAt(startsAt: DateTime, mealIndex: number) {
      const timecardDay = { ...this.timecardDay };
      timecardDay.meals[mealIndex].startsAt = startsAt;
      this.emitUpdate(timecardDay);
    },
    updateMealEndsAt(endsAt: DateTime, mealIndex: number) {
      const timecardDay = { ...this.timecardDay };
      timecardDay.meals[mealIndex].endsAt = endsAt;
      this.emitUpdate(timecardDay);
    },
    addMeal() {
      const timecardDay = { ...this.timecardDay };
      timecardDay.meals.push({
        timecardDayId: timecardDay.id,
        startsAt: timecardDay?.date.toUTC().set({ hour: 13 }),
        endsAt: timecardDay?.date.toUTC().set({ hour: 14 })!,
      });
      this.emitUpdate(timecardDay);
    },
    removeMeal() {
      const timecardDay = { ...this.timecardDay };
      timecardDay.meals.pop();
      this.emitUpdate(timecardDay);
    },
    updateComments(comments: string) {
      const timecardDay = { ...this.timecardDay, comments };
      this.emitUpdate(timecardDay);
    },
    updateNdbSetting(hasNdb: boolean) {
      const timecardDay = { ...this.timecardDay, hasNdb: hasNdb };
      this.emitUpdate(timecardDay);
    },
    emitUpdate(timecardDay: TimecardDay) {
      this.$emit('update', timecardDay);
    },
    checkShowTimes(workStatus: WorkStatus) {
      const key = workStatus?.key?.toUpperCase() || '';
      const hideTimeKeys = Object.values(WorkStatusKeysNoTimeEntry) as string[];
      const hideTimes = hideTimeKeys.includes(key);
      return !hideTimes;
    },
  },
});
</script>
