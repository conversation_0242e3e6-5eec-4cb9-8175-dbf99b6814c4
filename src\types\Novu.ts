export interface NovuNotification {
  cta: NovuCTA;
  _id: string;
  _templateId: string;
  _environmentId: string;
  _messageTemplateId: string;
  _notificationId: string;
  _organizationId: string;
  _subscriberId: string;
  _jobId: string;
  templateIdentifier: string;
  _feedId: null | string;
  channel: string;
  content: string;
  providerId: string;
  deviceTokens: any[];
  seen: boolean;
  read: boolean;
  status: string;
  transactionId: string;
  payload: Record<string, string>;
  expireAt: string;
  deleted: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
  subscriber: NovuSubscriber;
  actorSubscriber: null | any; // Specify the type if known
  id: string;
}

interface NovuCTA {
  action: NovuCTAAction;
  type: string;
  data: NovuCTAData;
}

interface NovuCTAAction {
  buttons: any[];
}

interface NovuCTAData {
  url: string;
}

interface NovuSubscriber {
  _id: string;
  subscriberId: string;
  id: string;
}
