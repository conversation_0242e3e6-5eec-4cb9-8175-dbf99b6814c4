import React from 'react';

import { Box } from '@/reactComponents/library';
import Section from '@/reactComponents/viewsV2/Onboarding/components/Section';
import PageTitle from '@/reactComponents/viewsV2/Onboarding/components/PageTitle';

import { PageBackgroundBox, PageSectionsBox } from '../../styledComponents';

const DirectDeposit = (props) => {
  return (
    <PageBackgroundBox
      component={'form'}
      id="depositOnboardForm"
      // onSubmit={handleSubmit(onSubmit)}
      onKeyDown={(e) => {
        if (e.ctrlKey && e.key === 'Enter') {
          const form = document.getElementById('depositOnboardForm');
          form.requestSubmit();
        }
      }}
    >
      <PageTitle topText={'DirectDeposit'} />
      <PageSectionsBox>
        <Section title={'DirectDeposit Details'}>
          <Box>Details</Box>
        </Section>
        <Section title={'DirectDeposit Settings'}>Settings</Section>
      </PageSectionsBox>
    </PageBackgroundBox>
  );
};

DirectDeposit.propTypes = {};

export default DirectDeposit;
