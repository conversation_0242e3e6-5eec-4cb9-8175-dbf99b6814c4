import React from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Text,
  Button,
  Loader,
  Pagination,
  Menu,
  MenuItem,
  Tooltip,
  Modal,
} from '@/reactComponents/library';
import { styled } from '@mui/material/styles';

import AuthStore from '@/reactComponents/stores/auth';

import TimecardRow from './TimecardRow';
import CreateTimecardModal from './CreateTimecardModal';
import ExportBatchReportModal from './ExportBatchReportModal';
import ZeroState from './ZeroState';

import { UNBATCHED_ID } from './utils';

import TextSnippetOutlinedIcon from '@mui/icons-material/TextSnippetOutlined';
import FilterListIcon from '@mui/icons-material/FilterList';
import InfoIcon from '@mui/icons-material/Info';
import { ProjectMemberTypeId, BatchStatusLabel } from '@/utils/enum';
import LockOpenOutlinedIcon from '@mui/icons-material/LockOpenOutlined';
import WarningIcon from '@mui/icons-material/Warning';

import {
  snackbarSuccess,
  snackbarAxiosErr_Deprecated,
} from '@/reactComponents/library/Snackbar';
import {
  pointZeroReport,
  timecardReport,
  blobDownload,
  submitBatch,
  openBatch,
} from '@/services/batch';
import {
  manualDownloadFileByUrl,
  extractCSVFileNameFromUrl,
} from '@/utils/download';

import { BatchStatusEnum } from '@/types/Batch';
import PermissionStore from '@/reactComponents/stores/permission';

const BatchTimecardsBox = styled(Box, { label: 'BatchTimecards' })((props) => {
  const { theme } = props;
  const { palette, spacing } = theme;

  return {
    width: '100%',
    backgroundColor: palette.background.default,
    padding: spacing(2),
    display: 'flex',
    flexDirection: 'column',
    borderTop: '1px solid',
    borderColor: palette.background.border,
  };
});

const styles = {
  title: {
    borderBottom: '1px solid',
    borderColor: 'background.border',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    pb: 2,
  },
  titleTextBadge: {
    display: 'flex',
    flexDirection: 'row',
    gap: 1,
    alignItems: 'center',
  },
  timecardsTable: {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
  },
  tableHeader: {
    display: 'flex',
    flexDirection: 'row',
    width: '100%',
    px: 1,
  },
  tableHeaderCell: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    px: 1,
    pt: 2,
    pb: 1,
  },
  tableBody: {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    gap: 1,
  },

  pagination: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    mt: 2,
  },
  unapprovedTCNote: {
    display: 'flex',
    justifyContent: 'space-between',
    background: '#EFF4FF',
    border: '1px solid #D1E0FF',
    p: 1,
    borderRadius: '8px',
    alignItems: 'center',
    mb: '24px',
  },
  errorTCNote: {
    display: 'flex',
    justifyContent: 'space-between',
    background: 'warning.25',
    border: '1px solid',
    borderColor: 'warning.200',
    borderRadius: '8px',
    p: 1,
    mb: '24px',
    alignItems: 'center',
  },
  batchUpdateNote: {
    display: 'flex',
    justifyContent: 'space-between',
    background: '#EFF4FF',
    border: '1px solid',
    borderColor: '#D1E0FF',
    borderRadius: '8px',
    p: 1,
    mb: '24px',
    alignItems: 'center',
  },
};

const COLUMNS = [
  { id: 'avatar', label: '', width: 60 },
  { id: 'name', label: 'Employee', width: 200 },
  { id: 'department', label: 'Dept', width: 100 },
  { id: 'payPeriod', label: 'Pay Period', width: 220 },
  { id: 'gross', label: 'Gross($)', width: 150 },
  { id: 'status', label: 'Timecard Status', width: 150 },
  { id: 'actions', label: '', width: 50 },
];

const AdminTimeBatchTimecards = (props) => {
  const {
    batch = {},
    timecards,
    loading,
    project,
    fetchBatches,
    fetchTimecards,
    timecardPagination,
    zeroStateVariant = '',
    clearFilters = () => {},
    filterUnapprovedOrErrorTimecards,
    unapprovedTcCount,
    checkUnapprovedTimecards,
    errorTcCount,
    projectMember,
    unclaimedBatchDetails = {},
  } = props;

  const [createModalOpen, setCreateModalOpen] = React.useState(false);
  const [exportPdfModalOpen, setExportPdfModalOpen] = React.useState(false);
  const [disableBatchSubmit, setDisableBatchSubmit] = React.useState(false);
  const [disableReOpenBatch, setDisableReOpenBatch] = React.useState(false);
  const [errorBatchStatusModalOpen, setErrorBatchStatusModalOpen] =
    React.useState(false);
  const [batchStatusUpdateErrorMessage, setBatchStatusUpdateErrorMessage] =
    React.useState('');

  const isUnbatched = batch.id === UNBATCHED_ID;

  const userFirst = AuthStore?.getUser?.firstName;
  const menuRef = React.useRef(null);
  const [menuOpen, setMenuOpen] = React.useState(false);

  const sortedTimecards = timecards.sort((a, b) => {
    const aName = a.userCrew.user.lastName;
    const bName = b.userCrew.user.lastName;

    return aName.localeCompare(bName);
  });

  const totalPages = Math.ceil(
    timecardPagination.total / timecardPagination.limit,
  );
  const handleClose = () => {
    setMenuOpen(false);
  };

  const downloadTimecardReport = async () => {
    try {
      const { data } = await timecardReport(batch.id);
      if (data.preSignedUrl) {
        const win = window.open(data.preSignedUrl);
        if (!win) {
          manualDownloadFileByUrl(data.preSignedUrl, 'Timecard Report');
        }
        snackbarSuccess('Timecard report generated successfully.');
      }
    } catch (err) {
      snackbarAxiosErr_Deprecated(err, 'Error fetching Timecard report');
    }
  };

  const downloadPointZeroReport = async () => {
    try {
      const { data } = await pointZeroReport(batch.id);
      if (data.preSignedUrl) {
        const newWin = window.open(data.preSignedUrl);
        if (!newWin) {
          const blob = await blobDownload(data.preSignedUrl);
          const downloadUrl = window.URL.createObjectURL(blob);
          const filename = extractCSVFileNameFromUrl(data.preSignedUrl);
          manualDownloadFileByUrl(downloadUrl, filename);
        }
        snackbarSuccess('PointZero report generated successfully.');
      }
    } catch (err) {
      snackbarAxiosErr_Deprecated(err, 'Error fetching PointZero report');
    }
  };

  const batchSubmitToPayroll = async () => {
    setDisableBatchSubmit(true);
    try {
      const { data } = await submitBatch(batch.id);
      if (data?.capsPayId) {
        fetchBatches();
        fetchTimecards();
        snackbarSuccess('Batch submitted to payroll successfully.');
      }
    } catch (err) {
      const errorMessage =
        err?.response?.data?.errors[0]?.message ||
        'An error occurred while submitting the batch to payroll.';
      setErrorBatchStatusModalOpen(true);
      setBatchStatusUpdateErrorMessage(errorMessage);
    }
    setDisableBatchSubmit(false);
  };

  const reOpenBatch = async () => {
    setDisableReOpenBatch(true);
    try {
      const { data } = await openBatch(batch.id);
      if (data?.capsPayId) {
        fetchBatches();
        snackbarSuccess('Batch Re-opened successfully.');
      }
    } catch (err) {
      snackbarAxiosErr_Deprecated(err, 'Error');
    }
    setDisableReOpenBatch(false);
  };

  const downloadDisabled = batch.timecards?.length <= 0;
  const isProjectAdmin =
    projectMember?.projectMemberType?.id === ProjectMemberTypeId.Admin;
  const isSiteAdmin =
    PermissionStore.isCompanyAdmin || PermissionStore.isSiteAdmin;

  const isBatchOpen = batch.status?.id === BatchStatusEnum.OPEN;
  const isBatchPendingForApproval =
    batch.status?.id === BatchStatusEnum.PENDING_ADMIN_APPROVAL;
  const canUpdateBatch =
    (isProjectAdmin && isBatchOpen) ||
    (isSiteAdmin && (isBatchOpen || isBatchPendingForApproval));
  const shouldShowBatchStatus = !isBatchOpen && !isUnbatched;

  return (
    <BatchTimecardsBox>
      {unapprovedTcCount > 0 && (
        <Box
          sx={{
            ...styles.unapprovedTCNote,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <InfoIcon sx={{ color: '#004EEB' }} />
            <Text variant="baseStrong">
              {unapprovedTcCount} timecards not approved for this batch
            </Text>
          </Box>
          <Button
            variant="outlined"
            sx={{ color: '#344054' }}
            startIcon={<FilterListIcon />}
            onClick={() => filterUnapprovedOrErrorTimecards('unapproved')}
            disabled={loading}
          >
            Filter un-approved
          </Button>
        </Box>
      )}
      {errorTcCount > 0 && (
        <Box
          sx={{
            ...styles.errorTCNote,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <WarningIcon
              sx={{
                color: 'warning.700',
              }}
            />
            <Box sx={{ display: 'flex', flexDirection: 'column' }}>
              <Text variant="baseStrong">
                Batch not ready for payroll submission
              </Text>
              <Text variant="smReg">
                {errorTcCount} timecards with calculation errors.
              </Text>
            </Box>
          </Box>
          <Button
            variant="outlined"
            sx={{ color: '#344054' }}
            startIcon={<FilterListIcon />}
            onClick={() => filterUnapprovedOrErrorTimecards('error')}
            disabled={loading}
          >
            Filter errors
          </Button>
        </Box>
      )}
      {shouldShowBatchStatus && (
        <Box
          sx={{
            ...styles.batchUpdateNote,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <InfoIcon sx={{ color: '#004EEB' }} />
            <Text variant="baseStrong">
              {BatchStatusLabel[batch?.status?.id]}
            </Text>
          </Box>
        </Box>
      )}
      <Box
        sx={{
          ...styles.title,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
          <Box sx={styles.titleTextBadge}>
            <Text variant="disSemi">{batch.name}</Text>
          </Box>
          <Box sx={{ height: '20px' }}>
            {!isUnbatched && <Text>ID:{` ${batch.capsPayId}`}</Text>}
          </Box>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Text variant="lgMed" sx={{ mr: 1 }}>
            Total: $
            {isUnbatched
              ? unclaimedBatchDetails?.batchTotalGross === null
                ? '0.00'
                : unclaimedBatchDetails?.batchTotalGross
              : batch.batchTotalGross != null
              ? batch.batchTotalGross
              : '0.00'}
          </Text>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {isBatchOpen || isUnbatched ? (
            <Button
              variant={'secondary'}
              onClick={() => setCreateModalOpen(true)}
              data-testid="create-timecard-btn"
            >
              Create Timecard
            </Button>
          ) : null}
          {!isBatchOpen && isBatchPendingForApproval && isSiteAdmin && (
            <Button
              variant={'secondary'}
              onClick={reOpenBatch}
              data-testid="reOpen-batch-btn"
              disabled={disableReOpenBatch || loading}
              loading={disableReOpenBatch}
            >
              <LockOpenOutlinedIcon
                sx={{ color: '#344054', fontSize: '18px' }}
              />
              Open batch
            </Button>
          )}
          {batch.capsPayId && canUpdateBatch && (
            <Button
              onClick={batchSubmitToPayroll}
              data-testid="submit-batch-btn"
              disabled={disableBatchSubmit || loading}
              loading={disableBatchSubmit}
            >
              {isProjectAdmin ? 'Ready for payroll' : 'Submit to payroll'}
            </Button>
          )}
          {!isUnbatched && (
            <Box>
              <Tooltip
                title={
                  downloadDisabled
                    ? "Can't download as the batch has no timecard"
                    : ''
                }
              >
                <span
                  onClick={(e) => {
                    if (downloadDisabled) {
                      //prevent loading batch when clicking disabled delete btn
                      e.stopPropagation();
                    }
                  }}
                >
                  <Button
                    variant="secondary"
                    sx={{ px: 0 }}
                    ref={menuRef}
                    disabled={downloadDisabled}
                    onClick={() => setMenuOpen(true)}
                  >
                    <TextSnippetOutlinedIcon />
                  </Button>
                </span>
              </Tooltip>
              <Menu
                anchorEl={menuRef.current}
                open={menuOpen}
                onClose={handleClose}
              >
                <MenuItem
                  label="Export PDFs"
                  onClick={(e) => {
                    setExportPdfModalOpen(true);

                    setMenuOpen(false);
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                />
                <MenuItem
                  label="Open timecard report"
                  onClick={downloadTimecardReport}
                />
                <MenuItem
                  label="Download PointZero"
                  onClick={downloadPointZeroReport}
                />
              </Menu>
            </Box>
          )}
        </Box>
      </Box>

      {loading && (
        <Box sx={{ height: '400px' }}>
          <Loader />
        </Box>
      )}
      {!loading && zeroStateVariant && (
        <ZeroState
          name={userFirst}
          variant={zeroStateVariant}
          clearFilters={clearFilters}
        />
      )}
      {!loading && !zeroStateVariant && (
        <>
          <Box sx={styles.timecardsTable}>
            <Box sx={styles.tableHeader}>
              {COLUMNS.map((column) => {
                const cellStyle = {
                  ...styles.tableHeaderCell,
                  width: column.width,
                  maxWidth: column.width,
                };
                return (
                  <Box key={column.id} sx={cellStyle}>
                    <Text variant="xsSemi">{column.label}</Text>
                  </Box>
                );
              })}
            </Box>
            <Box sx={styles.tableBody}>
              {sortedTimecards.map((timecard) => (
                <TimecardRow
                  key={timecard.id}
                  styles={styles}
                  timecard={timecard}
                  columns={COLUMNS}
                  isUnbatched={isUnbatched}
                  batch={batch}
                  project={project}
                  fetchBatches={fetchBatches}
                  fetchTimecards={fetchTimecards}
                  checkUnapprovedTimecards={checkUnapprovedTimecards}
                />
              ))}
              <Box sx={styles.pagination}>
                <Pagination
                  count={totalPages}
                  boundaryCount={1}
                  page={timecardPagination.page}
                  onChange={(e, newPage) => fetchTimecards(newPage)}
                  color="secondary"
                />
              </Box>
            </Box>
          </Box>
        </>
      )}
      {createModalOpen && (
        <CreateTimecardModal
          open={createModalOpen}
          setOpen={setCreateModalOpen}
          project={project}
          batch={batch}
        />
      )}
      {exportPdfModalOpen && (
        <ExportBatchReportModal
          batch={batch}
          open={exportPdfModalOpen}
          setOpen={setExportPdfModalOpen}
        />
      )}
      <Modal
        open={errorBatchStatusModalOpen}
        setOpen={setErrorBatchStatusModalOpen}
        onSubmit={() => setErrorBatchStatusModalOpen(false)}
        submitText="Ok"
        sx={{
          '& .modalContent': {
            width: '400px',
          },
        }}
      >
        <Text variant="baseMed" sx={{ mb: 2 }}>
          {batchStatusUpdateErrorMessage}
        </Text>
      </Modal>
    </BatchTimecardsBox>
  );
};

AdminTimeBatchTimecards.propTypes = {
  batch: PropTypes.object.isRequired,
  fetchBatches: PropTypes.func.isRequired,
  loading: PropTypes.bool.isRequired,
  project: PropTypes.object.isRequired,
  fetchTimecards: PropTypes.func.isRequired,
  timecards: PropTypes.array.isRequired,
  timecardPagination: PropTypes.object.isRequired,
  zeroStateVariant: PropTypes.string.isRequired,
  clearFilters: PropTypes.func.isRequired,
  filterUnapprovedOrErrorTimecards: PropTypes.func.isRequired,
  unapprovedTcCount: PropTypes.number.isRequired,
  checkUnapprovedTimecards: PropTypes.func.isRequired,
  errorTcCount: PropTypes.number.isRequired,
  projectMember: PropTypes.object.isRequired,
  unclaimedBatchDetails: PropTypes.object,
};

export default AdminTimeBatchTimecards;
