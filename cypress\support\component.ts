// ***********************************************************
// This example support/component.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands';
import '../../src/assets/main.css';
import { mount } from 'cypress/react';
import '@mui/material/styles';
import '@mui/material/styles/createTheme';
import '@mui/icons-material/AddCircleOutline';
import '@mui/material/Autocomplete';

// Augment the Cypress namespace to include type definitions for
// your custom command.
// Alternatively, can be defined in cypress/support/component.d.ts
// with a <reference path="./component" /> at the top of your spec.
declare global {
  namespace Cypress {
    interface Chainable {
      mount: typeof mount;
    }
  }
}

Cypress.Commands.add('mount', mount);

Cypress.on('uncaught:exception', (err) => {
  if (
    err.message.includes('InvalidPDFException') ||
    err.message.includes('Invalid PDF structure') ||
    err.message.includes('template.basePdf must be pdf data')
  ) {
    return false;
  }
});
