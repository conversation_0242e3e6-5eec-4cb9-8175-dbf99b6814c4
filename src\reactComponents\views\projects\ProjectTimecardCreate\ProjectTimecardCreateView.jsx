import { useAuth, useReactRouter } from '../../../AppHooks';
import ProjectTimecardCreate from './ProjectTimecardCreate';
import useCreateTimecard from './useCreateTimecard';
import ProjectStore from '@/reactComponents/stores/project';
import usePayPeriods from '@/reactComponents/serviceHooks/usePayPeriods';
import { observer } from 'mobx-react-lite';

const ProjectTimecardCreateView = observer(() => {
  useAuth();
  const { navigate } = useReactRouter();
  const { createTimecardHandler, createTimecardLoading } = useCreateTimecard(
    ProjectStore.project.id,
    ProjectStore.project.hashId,
    navigate,
  );
  const { getPayPeriodsDropdownItems, loading } = usePayPeriods(
    ProjectStore.project.id,
  );

  const onClickCreateTimecard = async (payPeriodDDItem) => {
    try {
      await createTimecardHandler(payPeriodDDItem.value);
    } catch (err) {
      console.error(err.message);
    }
  };

  return (
    <ProjectTimecardCreate
      onClickCreateTimecard={onClickCreateTimecard}
      payPeriodsDropdownItems={getPayPeriodsDropdownItems()}
      loading={loading}
      createTimecardLoading={createTimecardLoading}
      data-testid="project-timecard-create"
    />
  );
});

export default ProjectTimecardCreateView;
