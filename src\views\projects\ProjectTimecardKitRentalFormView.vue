<template>
  <div class="flex justify-center mx-auto w-full py-2 px-5">
    <div class="max-w-7xl">
      <h2 class="text-xl font-bold mb-3">Kit Rental Form</h2>
      <Toggle v-model="hasKitRental" :disabled="editDisabled">
        Has <PERSON> Rental
      </Toggle>
      <div v-if="hasKitRental">
        <TextInput
          v-model="kitRental.rentalRate"
          class="mt-2"
          label="Rental Rate"
          type="number"
          :disabled="editDisabled"
        />
        <Dropdown
          v-model="rateType"
          :menu-items="rateTypes"
          class="mt-2"
          label="Rental Rate Type"
          display-name="name"
          :disabled="editDisabled"
          @change="defaultTimecardDayWorkLocation"
        />
        <div v-if="rateType.key === 'daily'" class="mb-5">
          <h3 class="font-semibold mb-1">Select Rental Days</h3>
          <div class="pb-3 flex justify-between items-baseline text-center">
            <div
              v-for="(timecardDay, timecardDayIndex) in timecard.timecardDays"
              :key="`time-card-day-${timecardDayIndex}`"
              class="flex flex-col px-2"
            >
              <div
                v-if="timecardDay.isActive"
                :class="{
                  'border-blue-500 bg-blue-900 text-white':
                    timecardDay.isRentalDay,
                  'border-gray-500 text-gray-500 dark:border-gray-300 dark:text-gray-300':
                    !timecardDay.isRentalDay,
                }"
                class="h-8 w-9 pt-1 rounded border cursor-pointer"
                @click="selectRentalDay(timecardDay)"
              >
                {{ timecardDay.date.toFormat('ccc') }}
              </div>
              <div v-else class="h-8 w-8" />
            </div>
          </div>
        </div>

        <Dropdown
          v-if="project?.castAndCrewId"
          class="grow"
          v-model="kitRental.workLocation"
          label="Work Location"
          display-name="shootLocation.locationName"
          :menu-items="project.projectShootLocations"
        >
          <template #label>
            {{
              `${kitRental.workLocation?.shootLocation.locationName} (${kitRental.workLocation?.zip})`
            }}
          </template>
          <template #item="{ value: projectShootLocation }">
            {{
              `${projectShootLocation?.shootLocation.locationName} (${projectShootLocation?.zip})`
            }}
          </template>
        </Dropdown>
        <!-- TODO: Make it so we can generate these -->
        <!-- <Toggle
          class="mb-2"
          v-model="kitRental.isUpload"
          :disabled="editDisabled"
        >
          Manual Entry / Upload
        </Toggle> -->
        <FileUpload
          v-if="kitRental.isUpload"
          v-model="kitRental.documentId"
          class="my-1"
        />
        <!-- <div v-else>
          <div>
            <h3 class="text-xl font-bold pt-4">Kit Inventory</h3>
            <div
              v-for="(
                kitInventoryItem, kitInventoryItemIndex
              ) in kitRental.inventoryItems"
              :key="`kit-inventory-item-${kitInventoryItemIndex}`"
              class="mt-6 grid grid-cols-6 gap-x-4 sm:grid-cols-6"
            >
              <div class="col-span-4">
                <TextInput
                  label="Description"
                  v-model="kitInventoryItem.name"
                  :disabled="editDisabled"
                />
              </div>
              <div class="col-span-2">
                <TextInput
                  label="Price"
                  v-model="kitInventoryItem.amount"
                  :disabled="editDisabled"
                />
              </div>
            </div>
            <div class="flex justify-center">
              <PlusIcon
                class="w-8 mr-1 cursor-pointer dark:text-gray-300 hover:dark:text-gray-200"
                @click="add"
              />
              <MinusIcon
                v-if="kitRental.inventoryItems?.length > 0"
                class="w-8 ml-1 cursor-pointer dark:text-gray-300 hover:dark:text-gray-200"
                @click="remove"
              />
            </div>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Button from '@/components/library/Button.vue';
import DatePicker from '@/components/library/DatePicker.vue';
import Dropdown from '@/components/library/Dropdown.vue';
import FileUpload from '@/components/library/FileUpload.vue';
import PinInput from '@/components/library/PinInput.vue';
import TextInput from '@/components/library/TextInput.vue';
import Toggle from '@/components/library/Toggle.vue';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';
import { deleteKitRental } from '@/services/kit-rentals';
import {
  getKitRental,
  getTimecard,
  updateOrCreateKitRental,
} from '@/services/timecards';
import type Project from '@/types/Project';
import type ProjectShootLocation from '@/types/ProjectShootLocation';
import type Timecard from '@/types/Timecard';
import { MinusIcon, PlusIcon } from '@heroicons/vue/24/outline';
import axios from 'axios';
import { defineComponent, type PropType } from 'vue';

export default defineComponent({
  props: {
    project: {
      type: Object as PropType<Project>,
      required: true,
    },
    editDisabled: {
      type: Boolean,
      default: false,
    },
    route: {
      type: Object as PropType<ParsedRoute>,
      required: true,
    },
    navigate: {
      type: Function as PropType<Function>,
      required: true,
    },
    componentLoaded: {
      type: Function as PropType<Function>,
      required: true,
    },
  },
  components: {
    Button,
    TextInput,
    Dropdown,
    PinInput,
    Toggle,
    FileUpload,
    PlusIcon,
    MinusIcon,
    DatePicker,
  },
  data() {
    return {
      rateTypes: [
        {
          id: 1,
          name: 'Daily',
          key: 'daily',
        },
      ],
      rateType: {} as any,
      hasKitRental: false,
      kitRental: {
        rentalRate: 0,
        timecardId: 0,
        documentId: undefined as number | undefined,
        date: null,
        inventoryItems: [
          {
            name: '',
            amount: 0,
          },
        ],
        workLocation: undefined as ProjectShootLocation | undefined,
        isUpload: true,
      } as any,
      timecard: {} as Timecard,
    };
  },
  methods: {
    async save() {
      if (this.editDisabled) return;
      if (this.hasKitRental) {
        if (this.kitRental.isUpload) {
          this.kitRental.inventoryItems = [];
        }
        const inventoryItems = JSON.parse(
          JSON.stringify(this.kitRental.inventoryItems),
        );
        inventoryItems.forEach((inventoryItem: any) => {
          inventoryItem.amount = inventoryItem.amount * 100;
        });
        if (!this.kitRental.documentId) {
          SnackbarStore.triggerSnackbar(
            'Please upload a document.',
            2500,
            'error',
          );
          throw Error('Please upload a document.');
        }
        try {
          await updateOrCreateKitRental(
            parseInt(this.route.params.timecardId as string),
            {
              rentalRate: this.kitRental.rentalRate
                ? Math.floor(this.kitRental.rentalRate * 100)
                : '',
              timecardId: parseInt(this.route.params.timecardId as string),
              documentId: this.kitRental.documentId,
              inventoryItems: inventoryItems,
              rateTypeId: this.rateType.id,
              isUpload: this.kitRental.isUpload,
              rentalDays: this.timecard.timecardDays,
              workLocation: this.kitRental.workLocation,
            },
          );
          SnackbarStore.triggerSnackbar('Kit rental saved.', 2500, 'success');
        } catch (err) {
          if (axios.isAxiosError(err)) {
            const msg = err.response?.data?.['errors']?.[0]?.message;
            SnackbarStore.triggerSnackbar(msg, 2500, 'error');
          } else {
            SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
          }
          throw err;
        }
      } else if (this.kitRental?.id) {
        try {
          await deleteKitRental(this.kitRental.id);
        } catch (err) {
          if (axios.isAxiosError(err)) {
            const msg = err.response?.data?.['errors']?.[0]?.message;
            SnackbarStore.triggerSnackbar(msg, 2500, 'error');
          } else {
            SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
          }
          throw err;
        }
      }
    },
    async saveAndContinue() {
      await this.save();
      const curPath = this.route.location.pathname;
      this.navigate({
        pathname: curPath.replace('kit-rental', 'reimbursements'),
      });
    },
    add() {
      if (this.editDisabled) return;
      this.kitRental.inventoryItems.push({
        name: '',
        amount: 0,
      });
    },
    remove() {
      if (this.editDisabled) return;
      this.kitRental.inventoryItems.pop();
    },
    selectRentalDay(timecardDay: any) {
      if (this.editDisabled) return;
      timecardDay.isRentalDay = !timecardDay.isRentalDay;
      this.defaultTimecardDayWorkLocation();
    },
    async defaultTimecardDayWorkLocation() {
      if (this.kitRental.workLocation) return;
      if (this.editDisabled) return;
      if (this.rateType.key === 'weekly') {
        this.kitRental.workLocation = this.timecard.timecardDays.find(
          (day) => day.isActive,
        )?.projectShootLocation;
      } else {
        this.kitRental.workLocation = this.timecard.timecardDays.find(
          (day) => day.isRentalDay,
        )?.projectShootLocation;
      }
    },
  },
  async mounted() {
    const { data } = await getKitRental(
      parseInt(this.route.params.timecardId as string),
    );
    if (data.id) {
      this.hasKitRental = true;
      this.kitRental = data;
      this.kitRental.rentalRate = data.rentalRate / 100;
      if (!this.kitRental.inventoryItems) {
        this.kitRental.inventoryItems = [];
      }
      this.kitRental.inventoryItems.forEach((inventoryItem: any) => {
        inventoryItem.amount = inventoryItem.amount / 100;
      });
      this.rateType = data.rateType;
    }
    const { data: timecard } = await getTimecard(
      parseInt(this.route.params.timecardId as string),
    );
    this.timecard = timecard as Timecard;
    this.componentLoaded();
  },
});
</script>

<style></style>
