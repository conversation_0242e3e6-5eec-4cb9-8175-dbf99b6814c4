import { enqueueSnackbar } from 'notistack';
import axios from 'axios';

export const triggerSnackbar = (msg, timeout = 5000, variant) => {
  enqueueSnackbar(msg, { variant, autoHideDuration: timeout });
};

export const snackbarAxiosErr = (err, errMsg, timeout = 5000) => {
  let msg;
  console.error(err);
  if (axios.isAxiosError(err)) {
    msg = err.response?.data?.['errors']?.[0]?.message || errMsg;
  } else {
    msg = errMsg || err;
  }
  triggerSnackbar(msg, timeout, 'error');
};

//only use this if you know the BE is throwing unhandled error, and you need to read the message from that error
//BE will be refactoring its error handling to handle all errors return a consistent error message structure
//until that BE work is done, you can use this.
export const snackbarAxiosErr_Deprecated = (err, errMsg, timeout = 5000) => {
  let msg;
  if (axios.isAxiosError(err)) {
    msg =
      err.response?.data?.['errors']?.[0]?.message ||
      err.response?.data?.message ||
      errMsg;
  } else {
    msg = errMsg || err;
  }
  triggerSnackbar(msg, timeout, 'error');
};

export const snackbarSuccess = (msg, timeout = 5000) => {
  triggerSnackbar(msg, timeout, 'success');
};
export const snackbarInfo = (msg, timeout = 5000) => {
  triggerSnackbar(msg, timeout, 'info');
};
export const snackbarErr = (msg, timeout = 5000) => {
  console.error(msg);
  triggerSnackbar(msg, timeout, 'error');
};
export const snackbarWarn = (msg, timeout = 5000) => {
  triggerSnackbar(msg, timeout, 'warning');
};
