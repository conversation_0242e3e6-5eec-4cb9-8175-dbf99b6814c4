import React from 'react';
import PropTypes from 'prop-types';

import { Box, Modal, InputLabel } from '@/reactComponents/library';
import SignaturePad from '@/reactComponents/library/SignaturePad';
import { TimecardStatusId } from '@/utils/enum';
import {
  snackbarSuccess,
  snackbarAxiosErr,
  snackbarErr,
} from '@/reactComponents/library/Snackbar';
import { approveTimecard, submitTimecard } from '@/services/timecards';
import { DateTime } from 'luxon';

const ApproveModal = (props) => {
  const { open, setOpen, timecard, refreshTimecard, fetchCalcInfo } = props;

  const [sigPad, setSigPad] = React.useState(null);
  const [loading, setLoading] = React.useState(false);
  const [loadingMessage, setLoadingMessage] = React.useState('');

  const inProgress = timecard.status?.id === TimecardStatusId.InProgress;

  const [inProgressConfirmed, setInProgressConfirmed] = React.useState(false);

  const acknowledgeInProgress = () => {
    setInProgressConfirmed(true);
  };

  const updateLoading = (state, message) => {
    setLoading(state);
    setLoadingMessage(message || '');
  };

  const approve = async () => {
    const signature = sigPad.toDataURL();
    if (sigPad.isEmpty()) {
      snackbarErr('You must sign to approve.');
      return;
    }
    updateLoading(true, 'Approving timecard');
    try {
      if (timecard.status?.id === TimecardStatusId.InProgress) {
        const EMPTY_SIGNATURE_BASE64 =
          'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAGQCAQAAAC+fJXwAAACzElEQVR42u3SQQ0AAAgDMeZf9DBBwqeVcLm0A+diLIyFsTAWGAtjYSwwFsbCWGAsjIWxwFgYC2OBsTAWxgJjYSyMBcbCWBgLjIWxMBYYC2NhLDAWxsJYYCyMhbHAWBgLY4GxMBbGAmNhLIwFxsJYGAuMhbEwFhgLY2EsMBbGwlhgLIyFscBYGAtjgbEwFsYCY2EsjAXGwlgYC4yFsTAWGAtjYSwwFsbCWGAsjIWxwFgYC2NhLBEwFsbCWGAsjIWxwFgYC2OBsTAWxgJjYSyMBcbCWBgLjIWxMBYYC2NhLDAWxsJYYCyMhbHAWBgLY4GxMBbGAmNhLIwFxsJYGAuMhbEwFhgLY2EsMBbGwlhgLIyFscBYGAtjgbEwFsYCY2EsjAXGwlgYC4yFsTAWGAtjYSwwFsbCWGAsjIWxwFgYC2OBsTAWxsJYxsJYGAtjgbEwFsYCY2EsjAXGwlgYC4yFsTAWGAtjYSwwFsbCWGAsjIWxwFgYC2OBsTAWxgJjYSyMBcbCWBgLjIWxMBYYC2NhLDAWxsJYYCyMhbHAWBgLY4GxMBbGAmNhLIwFxsJYGAuMhbEwFhgLY2EsMBbGwlhgLIyFscBYGAtjgbEwFsYCY2EsjAXGwlgYC2MZC2NhLIwFxsJYGAuMhbEwFhgLY2EsMBbGwlhgLIyFscBYGAtjgbEwFsYCY2EsjAXGwlgYC4yFsTAWGAtjYSwwFsbCWGAsjIWxwFgYC2OBsTAWxgJjYSyMBcbCWBgLjIWxMBYYC2NhLDAWxsJYYCyMhbHAWBgLY4GxMBbGAmNhLIwFxsJYGAuMhbEwFhgLY2EsMBbGwlgYSwSMhbEwFhgLY2EsMBbGwlhgLIyFscBYGAtjgbEwFsYCY2EsjAXGwlgYC4yFsTAWGAtjYSwwFsbCWGAsjIWxwFgYC2OBsTAWxgJjYSyMBcbCWBgLjIWxMBYYiw8Ln98enhovrYAAAAAASUVORK5CYII=';
        const timeZone = DateTime.now().zoneName;
        await submitTimecard(timecard.id, EMPTY_SIGNATURE_BASE64, timeZone);
        refreshTimecard();
      }
      const { data: newTimecard } = await approveTimecard(
        timecard.id,
        signature,
      );
      snackbarSuccess('Timecard approved.');
      fetchCalcInfo(newTimecard);

      updateLoading(true, 'Refreshing timecard');
      await refreshTimecard();

      updateLoading(false);
      setOpen(false);
    } catch (err) {
      updateLoading(false);
      snackbarAxiosErr(err, err);
    }
  };

  const needToConfirmInProgress = !inProgressConfirmed && inProgress;

  if (!open) return null;

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      onClose={() => setOpen(false)}
      title="Approve Timecard"
      onSubmit={needToConfirmInProgress ? acknowledgeInProgress : approve}
      submitText={needToConfirmInProgress ? 'Continue' : 'Approve'}
      disableSubmit={loading}
      loading={loading}
      loadingMsg={loadingMessage}
      sx={{
        '& .modalContent': {
          justifyContent: 'center',
          gap: 0,
        },
      }}
    >
      <Box>
        {needToConfirmInProgress ? (
          <Box>
            This timecard is in progress. Approving it will submit the timecard
            on behalf of the employee. Do you want to continue?
          </Box>
        ) : (
          <Box>
            <InputLabel>Sign here</InputLabel>
            <SignaturePad sigPad={sigPad} setSigPad={setSigPad} />
          </Box>
        )}
      </Box>
    </Modal>
  );
};

ApproveModal.propTypes = {
  open: PropTypes.bool,
  setOpen: PropTypes.func,
  timecard: PropTypes.object.isRequired,
  refreshTimecard: PropTypes.func.isRequired,
  fetchCalcInfo: PropTypes.func.isRequired,
};

export default ApproveModal;
