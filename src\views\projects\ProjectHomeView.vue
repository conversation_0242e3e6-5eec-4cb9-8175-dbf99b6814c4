<!-- //TODO - deprecated FPS-1346 -->

<template>
  <div class="flex justify-center mx-auto w-full py-2 px-5">
    <div class="max-w-7xl">
      <ul role="list" class="space-y-3 pt-3 mb-12">
        <li
          v-if="isAdminOrHasPermission()"
          class="overflow-hidden px-4 sm:rounded-md sm:px-6"
        >
          <div
            class="bg-white dark:bg-gray-900 shadow rounded-lg border-2 dark:border-indigo-800 border-indigo-300"
          >
            <div
              class="px-4 py-3 space-y-2 sm:p-6 sm:flex justify-between items-center"
            >
              <div>
                <h3
                  class="font-semibold text-lg dark:text-indigo-300 text-indigo-700"
                  data-testid="supervisor-view-title"
                >
                  Supervisor View
                </h3>
                <p class="text-sm max-w-sm text-gray-700 dark:text-gray-300">
                  Manage crew onboarding and timecards. For production
                  supervisors and staff only.
                </p>
              </div>
              <Button
                @click="goToAdminView()"
                color="secondary"
                data-testid="supervisor-view-btn"
              >
                View
              </Button>
            </div>
          </div>
        </li>
        <li
          v-for="(page, pageIndex) in pages"
          :key="`page-${pageIndex}`"
          class="overflow-hidden px-4 sm:rounded-md sm:px-6"
        >
          <div class="bg-white dark:bg-gray-900 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex">
                <template v-if="!page.ignoreStatus">
                  <Badge
                    v-if="page.completed"
                    type="success"
                    text=""
                    size="icon"
                    class="w-6 h-6"
                  >
                    <template #icon>
                      <Icon name="checked" class="w-4 h-4" />
                    </template>
                  </Badge>
                  <ExclamationCircleIcon
                    v-else
                    class="text-yellow-500 dark:text-yellow-400 w-6 h-6"
                  />
                </template>
                <h3
                  class="text-base font-semibold leading-6 ml-2"
                  :data-testid="`${page.label}-name-title`"
                >
                  {{ page.label }}
                </h3>
              </div>
              <div
                class="mt-2 max-w-xl text-sm text-gray-500 dark:text-gray-400"
              >
                <p>{{ page.description }}</p>
              </div>
              <div class="mt-5" :data-testid="`${page.label}-action-btn`">
                <Button
                  @click="goToPage(page)"
                  :color="page.completed ? 'secondary' : 'main'"
                >
                  {{ page.cta || 'View' }}
                </Button>
              </div>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script lang="ts">
import Icon from '@/components/Icon.vue';
import Badge from '@/components/library/Badge.vue';
import Button from '@/components/library/Button.vue';
import {
  addMember,
  getStartingPaperwork,
  isProjectOnboarded,
} from '@/services/project';
import { needsCrewOnboarding } from '@/services/user-crew';
import { PermissionKeys } from '@/types/Permission';
import type Project from '@/types/Project';
import { ExclamationCircleIcon } from '@heroicons/vue/24/solid';
import PermissionStore from '@/reactComponents/stores/permission';
import { defineComponent, type PropType } from 'vue';

interface Page {
  label: string;
  name: string;
  description: string;
  completed?: boolean;
  cta?: string;
  ignoreStatus?: boolean;
  pathname?(): string;
}

type Pages = Record<string, Page>;

export default defineComponent({
  components: {
    Button,
    ExclamationCircleIcon,
    Badge,
    Icon,
  },
  props: {
    navigate: {
      type: Function,
      required: true,
    },
    project: {
      type: Object as PropType<Project>,
      required: true,
    },
    isAdmin: {
      type: Boolean as PropType<boolean>,
      required: true,
    },
    route: {
      type: Object as PropType<any>,
      required: false,
    },
  },
  data() {
    return {
      pages: [] as Page[],
      startingPaperwork: null as any,
      needsPersonalOnboarding: false as boolean,
      wasProjectOnboarded: false as boolean,
    };
  },
  async mounted() {
    await this.fetchData();
    this.pages = this.getFilteredPages();
  },
  methods: {
    hasPermission: PermissionStore.hasPermission,
    async fetchData() {
      const [needsOnboardingData, wasProjectOnboardedData] = await Promise.all([
        needsCrewOnboarding(),
        isProjectOnboarded(this.project.id!),
      ]);

      this.needsPersonalOnboarding = needsOnboardingData.data;
      this.wasProjectOnboarded = wasProjectOnboardedData.data;

      const shouldOnboard: boolean =
        this.$props.route.query.onboard?.toString() === 'true';
      const departmentId: number = Number(this.$props.route.query.departmentId);
      if (!this.wasProjectOnboarded && shouldOnboard && !this.isAdmin) {
        await addMember(this.project.id!, 'me', departmentId);
      }

      if (this.needsPersonalOnboarding) {
        return;
      }
      try {
        const { data } = await getStartingPaperwork(this.project.id!);
        this.startingPaperwork = data;
      } catch (err) {
        console.warn('Error fetching starting paperwork', err);
      }
    },
    getPages(): Pages {
      return {
        personalInfoOnboarding: {
          label: 'Personal Info / Loan Out',
          name: 'personal-onboarding',
          pathname: () => {
            const currentPath = this.$props.route.location.pathname;
            return `${currentPath}/personal`;
          },
          completed: !this.needsPersonalOnboarding,
          description:
            'One time onboarding for your personal information. Stored between projects so you only do it once ever!',
          cta: this.needsPersonalOnboarding ? 'Start' : 'Edit',
        },
        admin: {
          label: 'Supervisor View',
          name: 'project-admin-timecards',
          pathname: () => {
            const currentPath = this.$props.route.location.pathname;
            return `${currentPath}/timecards`;
          },
          description: 'Manage your crew and project settings.',
          ignoreStatus: true,
        },
        projectOnboarding: {
          label: 'Project Onboarding',
          name: 'project-onboarding',
          pathname: () => {
            const currentPath = this.$props.route.location.pathname;
            return `${currentPath}/onboarding`;
          },
          completed: this.wasProjectOnboarded,
          description: 'Information specific to this project.',
          cta: !this.wasProjectOnboarded ? 'Start' : 'View',
        },
        reviewStartingPaperwork: {
          label: 'Start Paperwork',
          name: 'project-starting-paperwork-review',
          pathname: () => {
            const currentPath = this.$props.route.location.pathname;
            return `${currentPath}/start/review`;
          },
          description: 'Review your onboarding information.',
          completed: true,
        },
        signStartPaperwork: {
          label: 'Sign Start Paperwork',
          name: 'project-starting-paperwork-generate',
          pathname: () => {
            const currentPath = this.$props.route.location.pathname;
            return `${currentPath}/start/generate`;
          },
          description:
            'Generate your onboarding paperwork and information for the project.',
          completed: false,
          cta: 'Sign',
        },
        timecards: {
          label: 'Timecards',
          name: 'project-timecards',
          pathname: () => {
            const currentPath = this.$props.route.location.pathname;
            return `${currentPath}/timecards`;
          },
          description: 'Track your time and pay.',
          ignoreStatus: true,
        },
      };
    },
    goToPage(page: any) {
      const departmentId: string =
        this.$props.route.query.departmentId?.toString();
      const pathname = page.pathname();
      const searchParams = new URLSearchParams();
      searchParams.append('departmentId', departmentId || '');

      this.navigate({
        pathname,
        search: searchParams.toString(),
      });
    },
    isAdminOrHasPermission() {
      // this method use a mobx store but not sure if Observer is needed since isAdmin is a local state that always trigger re-render
      return (
        this.isAdmin ||
        PermissionStore.hasPermission(PermissionKeys.ADMIN_VIEW_ON_ALL_PROJECTS)
      );
    },
    goToAdminView() {
      const currentPath = this.$props.route.location.pathname;
      this.navigate({
        pathname: `${currentPath}/admin/members`,
      });
    },
    getFilteredPages(): Page[] {
      const pages = this.getPages();
      const filteredPages = [];

      filteredPages.push(pages.personalInfoOnboarding);
      if (this.needsPersonalOnboarding) {
        return filteredPages;
      }
      filteredPages.push(pages.projectOnboarding);
      if (!this.wasProjectOnboarded) {
        return filteredPages;
      }

      if (!this.startingPaperwork || this.startingPaperwork.length === 0) {
        filteredPages.push(pages.signStartPaperwork);
        return filteredPages;
      }

      filteredPages.push(pages.reviewStartingPaperwork);
      filteredPages.push(pages.timecards);
      return filteredPages;
    },
  },
});
</script>
