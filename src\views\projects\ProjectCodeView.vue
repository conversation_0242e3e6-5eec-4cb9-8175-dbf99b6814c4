<template>
  <div class="relative flex min-h-screen flex-col overflow-hidden pt-16">
    <div class="mx-auto mb-5">
      <h2 class="text-center text-3xl font-extrabold leading-9 pt-4">
        Submit Project Code
      </h2>
      <p class="text-center mt-3">
        Your production manager should send you a code or a QR code to join a
        project. Enter it below.
        <br />
        <br />
        If you don't have one, reach out to your production manager.
      </p>
      <div>
        <PinInput :digit-count="4" v-model="code" @check-pin="getProject" />
        <div class="flex justify-center">
          <Button class="mt-4" @click="getProject"> Submit </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import PinInput from '@/components/library/PinInput.vue';
import { getByCode } from '@/services/project';
import type Project from '@/types/Project';
import { defineComponent } from 'vue';
import Button from '../../components/library/Button.vue';

export default defineComponent({
  props: {
    navigate: {
      type: Function,
      required: true,
    },
  },
  components: {
    Button,
    PinInput,
  },
  data() {
    return {
      code: '',
    };
  },
  methods: {
    async getProject(code: string) {
      const { data } = await getByCode(code);
      const project: Project = data as Project;
      this.navigate(`/projects/${project.hashId}`);
    },
  },
  async mounted() {},
});
</script>

<style></style>
