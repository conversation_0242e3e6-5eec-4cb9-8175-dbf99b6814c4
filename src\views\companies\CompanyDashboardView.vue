<template>
  <div class="min-h-full pt-40">
    <main>
      <div
        class="mx-auto mt-8 grid max-w-3xl grid-cols-1 gap-6 sm:px-6 lg:max-w-7xl lg:grid-flow-col-dense lg:grid-cols-3"
      >
        <div class="space-y-6 lg:col-span-2 lg:col-start-1">
          <section aria-labelledby="applicant-information-title">
            <div class="bg-white dark:bg-gray-900 shadow sm:rounded-lg">
              <div class="flex justify-between px-4 py-5 sm:px-6">
                <h2
                  id="applicant-information-title"
                  class="text-lg font-medium leading-6"
                >
                  Information
                </h2>
              </div>
              <div
                class="border-t border-gray-200 dark:border-gray-500 px-4 py-5 sm:px-6"
              >
                <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
                  <div
                    class="sm:col-span-1"
                    v-for="detail in companyDetails"
                    :key="detail.label"
                  >
                    <dt
                      class="text-sm font-medium text-gray-500 dark:text-gray-400"
                    >
                      {{ detail.label }}
                    </dt>
                    <dd class="mt-1 text-sm">
                      <div v-if="detail.label !== 'Addresses'">
                        {{ detail.value }}
                      </div>
                      <div v-else>
                        <div v-for="address in detail.value" :key="address">
                          {{ address }}
                        </div>
                      </div>
                    </dd>
                  </div>
                  <div class="sm:col-span-1">
                    <div
                      class="text-sm font-medium text-gray-500 dark:text-gray-400"
                    >
                      Payroll Provider
                    </div>
                    <div class="mt-1 text-sm flex justify-start space-x-1">
                      <div>{{ company.payrollProvider?.name }}</div>
                      <Tooltip
                        :text="
                          company.castAndCrewId ? 'Connected' : 'Not connected'
                        "
                      >
                        <Badge
                          v-if="company.castAndCrewId"
                          type="success"
                          text=""
                          size="icon"
                          class="w-4 h-4"
                        >
                          <template #icon>
                            <Icon name="checked" class="w-3 h-3" />
                          </template>
                        </Badge>
                        <ExclamationCircleIcon
                          v-else
                          class="h-5 w-5 text-gray-500"
                          aria-hidden="true"
                        />
                      </Tooltip>
                    </div>
                  </div>
                </dl>
              </div>
            </div>
          </section>
        </div>
      </div>
    </main>
  </div>
</template>

<script lang="ts">
import Icon from '@/components/Icon.vue';
import Badge from '@/components/library/Badge.vue';
import Tooltip from '@/components/library/Tooltip.vue';
import {
  CheckCircleIcon,
  ExclamationCircleIcon,
} from '@heroicons/vue/20/solid';
import { defineComponent } from 'vue';

export default defineComponent({
  props: ['company'],
  data() {
    return {};
  },
  computed: {
    companyDetails() {
      return [
        { label: 'Phone', value: this.company.phone },
        {
          label: 'Tax Classification',
          value: this.company.taxClassification?.name,
        },
        {
          label: 'Addresses',
          value: this.company.addresses.map(this.formatAddress),
        },
      ];
    },
  },
  methods: {
    formatAddress(address: any) {
      return `${address.street}${address.street2 ? ` ${address.street2}` : ''},
              ${address.city},
              ${address.state.name}
              ${address.zip}`;
    },
  },
  components: { CheckCircleIcon, Tooltip, ExclamationCircleIcon, Badge, Icon },
});
</script>
