import type { SchemaItem } from '@/types/Schema';
import type { Schema } from '@pdfme/common';

export const convertSchemasStringToSchemasArray = (
  schemasString: string,
): SchemaItem[] => {
  if (!schemasString) return [];
  const parsedSchemas = JSON.parse(schemasString);
  return parsedSchemas?.schemas;
};

export const convertSchemasArrayToSchemasString = (schemas: Schema): string => {
  const processedSchemas = {
    schemas: schemas,
  };
  return JSON.stringify(processedSchemas, null, 2);
};
