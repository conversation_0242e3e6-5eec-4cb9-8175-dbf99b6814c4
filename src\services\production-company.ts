import type { Pagination } from '@/types/Pagination';
import type ProductionCompany from '@/types/ProductionCompany';
import type ProductionCompanyMember from '@/types/ProductionCompanyMember';
import axios, { type AxiosResponse } from 'axios';
import { coreBaseUrl } from '../utils/constants';

export const createProductionCompany = async (
  createProductionCompany: ProductionCompany,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/production-companies/`;
  const response = await axios.post(url, createProductionCompany, {
    withCredentials: true,
  });
  return response;
};

export const getProductionCompanyById = async (
  id: string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/production-companies/${id}`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const updateProductionCompany = async (
  updateProductionCompany: ProductionCompany,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/production-companies/${
    updateProductionCompany.id
  }`;
  const response = await axios.patch(url, updateProductionCompany, {
    withCredentials: true,
  });
  return response;
};

export const listProductionCompanies = async (
  pagination?: Pagination,
  filters?: string,
  sorts?: string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/production-companies`;
  const response = await axios.get(url, {
    withCredentials: true,
    params: {
      ...pagination,
      filters,
      sorts,
    },
  });
  return response;
};

export const addProductionCompanyMember = async (
  id: string,
  email: string,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/production-companies/${id}/members`;
  const response = await axios.post(url, { email }, { withCredentials: true });
  return response;
};

export const listProductionCompanyMembers = async (
  id: string,
  pagination?: any,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/production-companies/${id}/members`;
  const response = await axios.get(url, {
    withCredentials: true,
    params: { ...pagination },
  });
  return response;
};

export const getProductionCompanyMember = async (
  id: string,
  memberId: string,
): Promise<AxiosResponse<ProductionCompanyMember>> => {
  const url = `${coreBaseUrl()}/production-companies/${id}/members/${memberId}`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const createProductionCompanyDocumentTemplate = async (
  productionCompanyId: string | number,
  projectDocumentTemplate: any,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/production-companies/${productionCompanyId}/document-templates`;
  const response = await axios.post(url, projectDocumentTemplate, {
    withCredentials: true,
  });
  return response;
};

export const listProductionCompanyDocumentTemplates = async (
  productionCompanyId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/production-companies/${productionCompanyId}/document-templates`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const getProductionCompanyDocumentTemplate = async (
  productionCompanyId: string | number,
  projectDocumentTemplateId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/production-companies/${productionCompanyId}/document-templates/${projectDocumentTemplateId}`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const updateProductionCompanyDocumentTemplate = async (
  productionCompanyId: string | number,
  productionCompanyDocumentTemplate: any,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/production-companies/${productionCompanyId}/document-templates/${
    productionCompanyDocumentTemplate.id
  }`;
  const response = await axios.patch(url, productionCompanyDocumentTemplate, {
    withCredentials: true,
  });
  return response;
};
export const deleteProductionCompanyDocumentTemplate = async (
  productionCompanyId: string | number,
  productionCompanyDocumentTemplateId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/production-companies/${productionCompanyId}/document-templates/${productionCompanyDocumentTemplateId}`;
  const response = await axios.delete(url, { withCredentials: true });
  return response;
};

export const deleteProductionCompanyMember = async (
  productionCompanyId: string | number,
  productionCompanyMemberId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/production-companies/${productionCompanyId}/members/${productionCompanyMemberId}`;
  const response = await axios.delete(url, { withCredentials: true });
  return response;
};

export const getProductionCompanyWorkLocations = async (
  productionCompanyId: string | number,
  payrollProjectTypeId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/production-companies/${productionCompanyId}/shoot-locations/${payrollProjectTypeId}`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const listProjectTypes = async (
  productionCompanyId: string | number,
  projectId?: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/production-companies/${productionCompanyId}/project-types`;
  const response = await axios.get(url, {
    withCredentials: true,
    params: {
      projectId,
    },
  });
  return response;
};
