<template>
  <div class="flex justify-center mx-auto w-full py-2 sm:px-5">
    <div>
      <h2 class="text-center text-3xl font-light leading-9">Timecards</h2>
      <div v-if="timecards.length == 0">
        <p class="text-center text-lg pt-4 font-light leading-9">
          Click below to create your first timecard.
        </p>
        <div class="flex justify-center items-center pt-4 space-x-2">
          <Button
            color="gray"
            size="sm"
            @click="goToProjectHome"
            data-testid="timecard-project-btn"
          >
            Project Home
          </Button>
          <Button
            @click="goToTimecardCreate"
            size="sm"
            color="primary"
            data-testid="timecard-add-btn"
          >
            Start Timecard
          </Button>
        </div>
      </div>
      <div v-else>
        <div class="flex flex-wrap mb-4 pt-3">
          <div class="sm:rounded-md w-full">
            <ul role="list" class="divide-y dark:divide-gray-500">
              <li v-for="timecard in timecards" :key="timecard.id">
                <a
                  class="block bg-white hover:bg-gray-50 rounded-lg dark:bg-gray-900 dark:hover:bg-gray-700 cursor-pointer"
                  @click="goToTimecard(timecard)"
                >
                  <div class="py-4 sm:px-6 px-2">
                    <div class="flex items-center justify-between">
                      <p
                        class="truncate text-sm font-medium text-indigo-600 dark:text-indigo-400"
                        :data-testid="`timecard-date-${timecard?.id}`"
                      >
                        {{ formatMonthDay(timecard.payPeriod!.startsAt) }} -
                        {{ formatMonthDay(timecard.payPeriod!.endsAt) }}
                      </p>
                      <div v-if="!timecard.revisionId && !timecard.isRevision">
                        <Button
                          @click.stop="revisionDialog = true"
                          color="gray"
                          size="xs"
                          class="flex items-center"
                          :data-testid="`timecard-addMoreTime-btn-${timecard?.id}`"
                        >
                          <div class="text-sm">Add'l Timecard</div>
                          <template #icon>
                            <DocumentDuplicateIcon
                              class="h-4 w-4 text-gray-400 dark:text-gray-500"
                              aria-hidden="true"
                            />
                          </template>
                        </Button>
                        <Modal v-model="revisionDialog">
                          <h2 class="font-semibold mb-2">Are you sure?</h2>
                          <p class="font-xs dark:text-gray-400">
                            Additional timecards are used if you need to create
                            a duplicate of a timecard for the same pay period.
                          </p>
                          <div class="flex justify-center space-x-2 mt-6">
                            <Button
                              color="gray"
                              @click="revisionDialog = false"
                              data-testid="addMoreTime-cancel-btn"
                            >
                              Cancel
                            </Button>
                            <Button
                              class=""
                              @click="createRevision(timecard)"
                              data-testid="addMoreTime-create-btn"
                            >
                              Create
                            </Button>
                          </div>
                        </Modal>
                      </div>
                      <div v-if="timecard.isRevision">
                        <p
                          class="rounded-full bg-green-100 px-2 text-xs font-semibold leading-5 text-green-800"
                        >
                          Duplicate
                        </p>
                      </div>
                    </div>
                    <div class="mt-2 flex justify-between space-x-5">
                      <div class="flex sm:space-x-2 items-center">
                        <p
                          class="flex items-center text-sm text-gray-500 dark:text-gray-400"
                          :data-testid="`timecard-status-${timecard?.id}`"
                        >
                          <TimecardStatusIcon :timecard="timecard" />
                        </p>
                      </div>
                      <div
                        class="flex items-center text-sm text-gray-500 dark:text-gray-400 sm:mt-0"
                        :data-testid="`timecard-startedOn-${timecard?.id}`"
                      >
                        <CalendarIcon
                          class="mr-1.5 h-5 w-5 flex-shrink-0 text-gray-400 dark:text-gray-500"
                          aria-hidden="true"
                        />
                        <p>
                          Started on
                          {{ ' ' }}
                          <time
                            :datetime="(timecard.createdAt.toISO() as string)"
                            >{{ timecard.createdAt.toFormat('MM/dd') }}</time
                          >
                        </p>
                      </div>
                    </div>
                  </div>
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div class="flex justify-center space-x-2">
          <Button
            color="gray"
            size="sm"
            @click="goToProjectHome"
            data-testid="timecard-projectHome-btn"
          >
            Project Home
          </Button>
          <Button
            v-if="remainingPayPeriods.length > 0"
            size="sm"
            @click="goToTimecardCreate"
            data-testid="timecard-add-btn"
          >
            Start Another Timecard
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Button from '@/components/library/Button.vue';
import Modal from '@/components/library/Modal.vue';
import TimecardStatusIcon from '@/components/TimecardStatusIcon.vue';
import {
  getCurrentMember,
  getProjectPayPeriods as getRemainingProjectPayPeriods,
} from '@/services/project';
import { listProjectMemberTimecards } from '@/services/project-members';
import { createTimecardRevision } from '@/services/timecards';
// import { useSnackbarStore } from '@/stores/snackbar';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import type PayPeriod from '@/types/PayPeriod';
import type Project from '@/types/Project';
import type ProjectMember from '@/types/ProjectMember';
import type Timecard from '@/types/Timecard';
import { DocumentDuplicateIcon } from '@heroicons/vue/20/solid';
import { CalendarIcon } from '@heroicons/vue/24/outline';
import axios from 'axios';
import { DateTime } from 'luxon';
import { defineComponent, type PropType } from 'vue';

export default defineComponent({
  props: {
    project: {
      type: Object as PropType<Project>,
      required: true,
    },
    navigate: {
      type: Function as PropType<Function>,
      required: true,
    },
  },
  components: {
    Button,
    CalendarIcon,
    TimecardStatusIcon,
    DocumentDuplicateIcon,
    Modal,
  },
  data() {
    return {
      timecards: [] as Timecard[],
      remainingPayPeriods: [] as PayPeriod[],
      revisionDialog: false,
      projectMember: null as ProjectMember | null,
    };
  },
  methods: {
    goToTimecard(timecard: Timecard) {
      this.$props.navigate({
        pathname: `/projects/${this.project.hashId}/timecards/${timecard.id}`,
      });
    },
    goToTimecardCreate() {
      this.$props.navigate({
        pathname: `/projects/${this.project.hashId}/timecards/create`,
      });
    },
    goToProjectHome() {
      this.$props.navigate({
        pathname: `/projects/${this.project.hashId}`,
      });
    },
    formatMonthDay(datetime: any) {
      return DateTime.fromISO(datetime).toFormat('MM/dd');
    },
    async createRevision(timecard: Timecard) {
      const timeZone: string = DateTime.now().zoneName!;
      try {
        const { data: newTimecard } = await createTimecardRevision(
          timecard.id!,
          timeZone,
        );
        this.goToTimecard(newTimecard);
        SnackbarStore.triggerSnackbar(
          'Timecard revision created.',
          2500,
          'success',
        );
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
      }
    },
  },
  async mounted() {
    const { data: payPeriods } = await getRemainingProjectPayPeriods(
      this.project.id!,
    );
    this.remainingPayPeriods = payPeriods;
    const { data: projectMemberData } = await getCurrentMember(
      this.project.id!,
    );
    this.projectMember = projectMemberData;
    const {
      data: { meta, data: timecards },
    } = await listProjectMemberTimecards(this.projectMember?.id!);
    this.timecards = timecards;
  },
});
</script>

<style></style>
