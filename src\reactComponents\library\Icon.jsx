import React from 'react';
import PropTypes from 'prop-types';

export function useDynamicSvgImport(iconName) {
  const importedIconRef = React.useRef();
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState();

  React.useEffect(() => {
    setLoading(true);
    // dynamically import the mentioned svg icon name in props
    const importSvgIcon = async () => {
      // please make sure all your svg icons are placed in the same directory
      // if we want that part to be configurable then instead of iconName we will send iconPath as prop
      //https://github.com/pd4d10/vite-plugin-svgr/issues/90#issuecomment-2351138240
      try {
        importedIconRef.current = (
          await import(`../../assets/icons/${iconName}.svg?react`)
        ).default; // svgr provides ReactComponent for given svg path
      } catch (err) {
        setError(err);
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    importSvgIcon();
  }, [iconName]);

  return { error, loading, SvgIcon: importedIconRef.current };
}

export const Icon = ({ name, ...svgProps }) => {
  const { loading, SvgIcon } = useDynamicSvgImport(name);

  if (loading) {
    return null;
  }

  if (!SvgIcon) return null;

  return <SvgIcon width="1em" height="1em" {...svgProps} />;
};

Icon.propTypes = {
  name: PropTypes.string.isRequired,
};
export default Icon;
