import type { DateTime } from 'luxon';
import type { KitRentalInventoryItem } from './KitRentalInventoryItem';
import type { KitRentalRateType } from './KitRentalRateType';
import type ProjectShootLocation from './ProjectShootLocation';

export interface KitRental {
  id?: number;
  timecardId: number;
  isUpload: boolean;
  rentalRate: number;
  lineNumber: string;
  documentTemplateId?: string;
  documentId: string;
  rateType: KitRentalRateType;
  inventoryItems: KitRentalInventoryItem[];
  rentalDays: any[];
  workLocation: ProjectShootLocation;
  rate?: number;
  date?: DateTime | null | undefined;
  totalAmount?: number;
}
