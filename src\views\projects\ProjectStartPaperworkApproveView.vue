<template>
  <div>
    <div class="m-auto text-center">
      <h1 class="text-xl font-bold mb-2">Sign Start Paperwork</h1>
      <div class="flex justify-center mb-12">
        <PDFViewer class="w-4/5" v-if="pdfUrl" :url="pdfUrl" />
      </div>
      <div
        class="flex justify-center w-100 py-3 fixed mt-10 bottom-0 left-0 right-0 bg-gray-50 dark:bg-gray-700"
      >
        <Modal v-model="signatureModal">
          <h1 class="text-xl font-bold mb-3">Your signature</h1>
          <h2 class="font-semibold mt-2 mb-3">Required documents:</h2>
          <ul role="list" class="mt-1 mb-5">
            <li
              v-for="memberDocument in requiredDocuments"
              :key="memberDocument.id"
              class="mb-2"
            >
              <p class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                >
                  <rect width="20" height="20" rx="10" fill="#F2F4F7" />
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M14.247 6.15837L8.2803 11.9167L6.69696 10.225C6.4053 9.95004 5.94696 9.93337 5.61363 10.1667C5.28863 10.4084 5.19696 10.8334 5.39696 11.175L7.27196 14.225C7.4553 14.5084 7.77196 14.6834 8.1303 14.6834C8.47196 14.6834 8.79696 14.5084 8.9803 14.225C9.2803 13.8334 15.0053 7.00837 15.0053 7.00837C15.7553 6.2417 14.847 5.5667 14.247 6.15004V6.15837Z"
                    fill="#667085"
                  />
                </svg>
                <span class="text-sm text-black-500 dark:text-black-400 ml-2">{{
                  memberDocument.name
                }}</span>
              </p>
            </li>
          </ul>
          <h2
            class="font-semibold mt-1 mb-2"
            v-if="optionalDocuments.length > 0"
          >
            Optional documents:
          </h2>
          <ul role="list" class="mb-5" v-if="optionalDocuments.length > 0">
            <li
              v-for="memberDocument in optionalDocuments"
              :key="memberDocument.id"
              class="mb-1"
            >
              <p class="flex items-center">
                <Checkbox v-model="memberDocument.isSelected"></Checkbox>
                <span
                  class="text-sm text-black-500 dark:text-black-400 ml-1 pt-1"
                  >{{ memberDocument.name }}</span
                >
              </p>
            </li>
          </ul>
          <h2 class="font-semibold mb-2">Sign here:</h2>
          <SignatureArea ref="signaturePad" v-model="signature" />
          <div class="flex justify-center space-x-2">
            <Button class="mt-3" color="gray" @click="signatureModal = false">
              Cancel
            </Button>
            <Button class="mt-3" :loading="loadingSignature" @click="sign()">
              Submit
            </Button>
          </div>
        </Modal>
        <Button @click="signatureModal = true" color="primary"> Sign </Button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Button from '@/components/library/Button.vue';
import Checkbox from '@/components/library/Checkbox.vue';
import Modal from '@/components/library/Modal.vue';
import PDFViewer from '@/components/library/PDFViewer.vue';
import SignatureArea from '@/components/library/SignatureArea.vue';
import {
  previewStartingPaperwork,
  signStartingPaperwork,
} from '@/services/project';
import { listProjectMemberDocuments } from '@/services/project-members';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import type Project from '@/types/Project';
import axios from 'axios';
import { defineComponent, type PropType } from 'vue';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';

export default defineComponent({
  props: {
    project: {
      type: Object as PropType<Project>,
      required: true,
    },
    route: {
      type: Object as PropType<ParsedRoute>,
      required: true,
    },
    navigate: {
      type: Function as PropType<Function>,
      required: true,
    },
  },
  components: {
    Button,
    Modal,
    Checkbox,
    SignatureArea,
    PDFViewer,
  },
  data() {
    return {
      pdfSrc: null,
      signatureModal: false,
      signature: null,
      useLoanOut: false as boolean,
      loadingPdf: false as boolean,
      projectMemberId: null as string | null,
      loadingSignature: false as boolean,
      pdfUrl: null as string | null,
      documentTemplates: [] as any[],
      requiredDocuments: [] as any[],
      optionalDocuments: [] as any[],
    };
  },
  methods: {
    async loadDocumentTemplates() {
      try {
        const { data } = await listProjectMemberDocuments(
          this.projectMemberId!,
          this.useLoanOut,
        );
        this.documentTemplates = data;
        this.requiredDocuments = this.documentTemplates.filter(
          (doc: any) => !doc.isOptional,
        );
        this.optionalDocuments = this.documentTemplates.filter(
          (doc: any) => doc.isOptional,
        );
        this.optionalDocuments.forEach((doc: any) => {
          doc.isSelected = true;
        });
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
      }
    },
    async sign() {
      if (this.loadingSignature) return;
      const selectedOptionalDocuments = this.optionalDocuments
        .filter((doc: any) => doc.isSelected)
        .map((doc: any) => doc.id);
      const signatureRef = this.$refs.signaturePad as any;
      if (signatureRef.isEmpty()) {
        SnackbarStore.triggerSnackbar(
          'Please sign before submitting',
          2500,
          'error',
        );
        return;
      }
      this.loadingSignature = true;
      const signature = signatureRef.getSignatureImage();
      try {
        await signStartingPaperwork(
          this.project.id!,
          signature,
          this.useLoanOut,
          selectedOptionalDocuments,
        );
        this.$props.navigate({
          pathname: `/projects/${this.project.hashId}/start/review`,
        });
        SnackbarStore.triggerSnackbar(
          'Successfully signed paperwork',
          2500,
          'success',
        );
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
      }
      this.loadingSignature = false;
    },
  },
  async mounted() {
    this.useLoanOut = this.$props.route.query.useLoanOut === 'true';
    this.projectMemberId = this.$props.route.query.projectMemberId as string;
    try {
      this.pdfUrl = previewStartingPaperwork(this.project.id!, this.useLoanOut);
    } catch (err) {
      if (axios.isAxiosError(err)) {
        const msg = err.response?.data?.['errors']?.[0]?.message;
        SnackbarStore.triggerSnackbar(msg, 2500, 'error');
      } else {
        SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
      }
      throw err;
    }
    await this.loadDocumentTemplates();
  },
});
</script>
