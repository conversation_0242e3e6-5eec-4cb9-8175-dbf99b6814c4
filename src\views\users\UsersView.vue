<template>
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pt-20">
    <div class="flex justify-between items-center space-x-1 my-4">
      <h3 class="text-lg font-semibold leading-6">Users</h3>
    </div>
    <UsersTable />
  </div>
</template>

<script lang="ts">
import UsersTable from '@/components/UsersTable.vue';
import { defineComponent, provide, watchEffect, ref } from 'vue';

export default defineComponent({
  components: {
    UsersTable,
  },
  props: {
    navigate: {
      type: Function,
      required: true,
    },
    route: {
      type: Object,
      required: true,
    },
  },
  setup(props: any) {
    provide('navigate', props.navigate);

    const route = ref(props.route);

    watchEffect(() => {
      route.value = props.route;
    });

    provide('route', route);
  },
});
</script>
