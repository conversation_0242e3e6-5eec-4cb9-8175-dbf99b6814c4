import type { Filter } from '@/types/Filter';

export const convertFiltersToQuery = (filters: Filter[]): string => {
  if (!filters || !filters.length) {
    return '';
  }

  const filtersList = filters
    .filter(({ active }) => active)
    .map(({ field, value, operator, options, subject }) => {
      const operatorString = operator ? `:op:${operator}` : '';
      let valueString = value;
      if (options) {
        valueString = options
          .filter(({ active }) => active)
          .map(({ value: optionValue }) => optionValue)
          .join(',');
      }
      const filterString = `${field}=${valueString}${operatorString}`;
      return filterString;
    });

  return filtersList.join(';');
};

export const convertFiltersToURLQuery = (filters: Filter[]): string => {
  if (!filters || !filters.length) {
    return '';
  }

  return filters
    .map((filter) => convertFilterToURLQueryItem(filter))
    .filter((queryItem) => queryItem)
    .join(';'); // Join the query items with a semi colo
};

const convertFilterToURLQueryItem = (filter: Filter): string => {
  const { id, value, options, active } = filter;

  // Handle the case where value is null or undefined
  if (!active || value === null || value === undefined) {
    return '';
  }

  // Convert Date to a string representation
  const stringValue =
    value instanceof Date ? value.toISOString() : String(value);

  // For filters with options, serialize the ids of the active options with a prefix
  if (options && options.length > 0) {
    const activeOptionIds = options
      .filter((option) => option.active)
      .map((option) => `optionId:${option.id}`) // Prefix to indicate it's an option ID
      .join(',');

    return `${encodeURIComponent(id)}=${encodeURIComponent(activeOptionIds)}`;
  }

  // Serialize the value (which is now a string or number)
  return `${encodeURIComponent(id)}=${encodeURIComponent(stringValue)}`;
};

export const applyQueryStringToFilters = (
  queryString: string,
  filters: Filter[],
): Filter[] => {
  if (!filters || !filters.length) {
    return [];
  }

  if (!queryString) {
    return filters;
  }

  const filtersCopy: Filter[] = JSON.parse(JSON.stringify(filters));

  const queryParams = new URLSearchParams(queryString.replace(/;/g, '&'));
  const keys = [...queryParams.keys()];

  filtersCopy.forEach((filter) => {
    const isKeyPresent = keys.includes(filter.id);

    filter.active = isKeyPresent;
    if (!isKeyPresent) {
      return;
    }

    const filterData = queryParams.get(filter.id) || '';
    if (!filterData.startsWith('optionId:')) {
      filter.value = filterData;
      return;
    }

    const optionIds = filterData
      .split(',')
      .map((opt) => opt.replace('optionId:', ''));
    filter.options?.forEach((option) => {
      option.active = optionIds.includes(option.id);
    });
  });

  return filtersCopy;
};
