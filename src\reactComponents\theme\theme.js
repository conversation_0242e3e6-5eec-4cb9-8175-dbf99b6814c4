import { createTheme } from '@mui/material/styles';
import { lightPalette, darkPalette } from './palette';
import button from './components/button';
import tooltip from './components/tooltip';
import autocomplete from './components/autocomplete';
import tabs, { tab } from './components/tabs';
// import outlinedInput from './components/outlinedInput';
import input from './components/input';
import accordion, {
  accordionSummary,
  accordionDetails,
} from './components/accordion';
import typography from './components/typography';
import chip from './components/chip';
import textfield from './components/textfield';

let theme = createTheme({
  colorSchemes: {
    dark: {
      palette: darkPalette,
    },
    light: {
      palette: lightPalette,
    },
  },
  palette: lightPalette,
  typography: {
    fontFamily: ['Inter', 'Roboto', 'Helvetica', 'Arial', 'sans-serif'].join(
      ',',
    ),
  },
  components: {
    // Name of the component
    MuiButton: button,
    MuiAccordion: accordion,
    MuiAutocomplete: autocomplete,
    MuiAccordionSummary: accordionSummary,
    MuiAccordionDetails: accordionDetails,
    MuiTypography: typography,
    MuiChip: chip,
    MuiInputBase: input,
    MuiTooltip: tooltip,
    MuiTab: tab,
    MuiTabs: tabs,
    MuiTextField: textfield,

    // //part of textfield, but might want to make it a global override for all input behavior
    // MuiOutlinedInput: {
    //   styleOverrides: {
    //     root: {
    //       '& .MuiOutlinedInput-notchedOutline': {
    //         border: `5px solid green`,
    //       },
    //       '&.Mui-focused': {
    //         '& .MuiOutlinedInput-notchedOutline': {
    //           border: `2px dotted blue`,
    //         },
    //       },
    //     },
    //   },
    // },
  },
  shape: {
    borderRadius: 8,
  },
});

theme = createTheme(theme, {
  //Add light,dark, contrastText based on the mainColor for custom color

  palette: {
    gray: theme.palette.augmentColor({
      color: {
        main: '#667085',
      },
      name: 'gray',
    }),
  },
});

// // used for printing out for mui_reference_theme.js
// console.log(JSON.stringify(theme));
export default theme;
