import React from 'react';
import PropTypes from 'prop-types';

import { Box, CircularProgress, Text } from '@/reactComponents/library';

const Loader = (props) => {
  const { boxProps, spinnerProps, message = '' } = props;

  const incomingSX = boxProps?.sx || {};

  return (
    <Box
      {...boxProps}
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: 1,
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center',
        width: '100%',
        height: '100%',
        ...incomingSX,
      }}
    >
      <CircularProgress {...spinnerProps} />
      {message?.trim().length > 0 && <Text variant="smReg">{message}</Text>}
    </Box>
  );
};

Loader.propTypes = {
  boxProps: PropTypes.object,
  spinnerProps: PropTypes.object,
  message: PropTypes.string,
};

export default Loader;
