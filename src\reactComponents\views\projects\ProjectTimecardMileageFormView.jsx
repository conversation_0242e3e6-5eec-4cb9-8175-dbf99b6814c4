import { useImperativeHandle, forwardRef, useRef } from 'react';
import { applyPureVueInReact } from 'veaury';
import ProjectTimecardMileageFormViewVue from '../../../views/projects/ProjectTimecardMileageFormView.vue';
import { useAuth, useReactRouter } from '../../AppHooks';
import { useOutletContext } from 'react-router';
const ReactProjectTimecardMileageFormView = applyPureVueInReact(
  ProjectTimecardMileageFormViewVue,
);

const ProjectTimecardMileageFormView = forwardRef(() => {
  useAuth();
  const { navigate, route } = useReactRouter();
  const context = useOutletContext();
  const vueRef = useRef();

  useImperativeHandle(context.ref, () => ({
    vueRef: vueRef.current.__veauryVueRef__,
  }));

  return (
    <ReactProjectTimecardMileageFormView
      ref={vueRef}
      project={context.project}
      editDisabled={context.editDisabled}
      timecard={context.timecard}
      componentLoaded={context.componentLoaded}
      route={route}
      navigate={navigate}
    />
  );
});

export default ProjectTimecardMileageFormView;
