import { createMemoryRouter, RouterProvider } from 'react-router-dom';
import CreateTimecardModal from '@/reactComponents/viewsV2/ProjectAdminTime/CreateTimecardModal';
import { SnackbarProvider } from 'notistack';
import PropTypes from 'prop-types';
import { DateTime } from 'luxon';

const mockProject = {
  id: 123,
  name: 'Test Project',
};

const mockBatch = {
  id: 456,
};

const TestWrapper = ({ open, setOpen }) => {
  const router = createMemoryRouter(
    [
      {
        path: '/',
        element: (
          <SnackbarProvider>
            <CreateTimecardModal
              open={open}
              setOpen={setOpen}
              project={mockProject}
              batch={mockBatch}
            />
          </SnackbarProvider>
        ),
      },
    ],
    { initialEntries: ['/'] },
  );

  return <RouterProvider router={router} />;
};

TestWrapper.propTypes = {
  open: PropTypes.bool.isRequired,
  setOpen: PropTypes.func.isRequired,
  project: PropTypes.object.isRequired,
  batch: PropTypes.object.isRequired,
};

describe('CreateTimecardModal Component', () => {
  let mockSetOpen;

  beforeEach(() => {
    mockSetOpen = cy.stub();
    cy.wrap(mockSetOpen).as('setOpen');
    cy.intercept('GET', '**/v2/api/core/projects/123/pay-periods', (req) => {
      const rawData = [
        {
          id: 16,
          startsAt: '2025-05-11T00:00:00Z',
          endsAt: '2025-05-17T00:00:00Z',
        },
      ];

      const transformed = rawData.map((item) => ({
        ...item,
        startsAt: DateTime.fromISO(item.startsAt),
        endsAt: DateTime.fromISO(item.endsAt),
      }));

      req.reply({
        statusCode: 200,
        body: transformed,
      });
    }).as('getPayPeriods');

    cy.fixture('projectCurrentMembers').then((members) => {
      cy.intercept(
        'GET',
        '**/v2/api/core/projects/**/members?limit=50&page=1&filters=crewSignedStartPaperwork',
        {
          statusCode: 200,
          body: members,
        },
      ).as('getMembers');
    });
    cy.mount(<TestWrapper open={true} setOpen={mockSetOpen} />);
  });

  it('should validate employee value is required', () => {
    cy.get('[data-testid="Create timecard-btn"]').click();
    cy.contains('Please select an employee.').should('be.visible');
  });

  it('should validate payPeriod value is required', () => {
    cy.fixture('projectCurrentMembers').then((members) => {
      cy.intercept(
        'GET',
        '**/v2/api/core/projects/**/members?limit=50&page=1&filters=crewSignedStartPaperwork&search=**',
        {
          statusCode: 200,
          body: members,
        },
      ).as('getMembers');
    });

    cy.get('input[placeholder="Search for a employee"]').click();
    cy.get('ul[role="listbox"]')
      .should('exist')
      .within(() => {
        cy.get('li').first().should('be.visible').click();
      });
    cy.get('[data-testid="Create timecard-btn"]').click();
    cy.contains('Please select a pay period.').should('be.visible');
  });

  it('should validate that the "Close" button closes the modal', () => {
    cy.get('[data-testid="Cancel-btn"]').click();
    cy.get('[data-testid="create-batch-modal"]').should('not.exist');
    cy.get('@setOpen').should('have.been.calledWith', false);
  });

  it('should validate that the "X" button closes the modal', () => {
    cy.get('[data-testid="CloseIcon"]').click();
    cy.get('[data-testid="create-batch-modal"]').should('not.exist');
    cy.get('@setOpen').should('have.been.calledWith', false);
  });
});
