<template>
  <div
    class="relative flex min-h-screen flex-col justify-center overflow-hidden pt-40"
  >
    <div
      class="m-auto max-w-screen-lg space-y-8 divide-y divide-gray-200 dark:divide-gray-400 mt-5"
    >
      <div>
        <div>
          <h3 class="text-3xl mb-3 mt-3 font-medium leading-6">Company</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            This form is used to create a new production company.
          </p>
        </div>

        <div class="mt-6 grid grid-cols-1 gap-x-4 sm:grid-cols-6">
          <div class="sm:col-span-4">
            <TextInput label="Name" v-model="company.name" />
          </div>
          <div class="sm:col-span-4">
            <TextInput label="Phone" v-model="company.phone" />
          </div>
          <div class="sm:col-span-4">
            <TextInput
              label="Cast and Crew Id"
              v-model="company.castAndCrewId"
            />
          </div>
          <div class="sm:col-span-4">
            <Toggle
              v-model="company.generateStartForm"
              data-testid="company-generate-start-toggle"
            >
              Generate Start Form
            </Toggle>
          </div>
          <div class="sm:col-span-4 mt-2">
            <Toggle
              v-model="company.generateWageTheftProtectionAgreement"
              data-testid="company-generate-wtpa-toggle"
            >
              Generate WTPA
            </Toggle>
          </div>
        </div>
      </div>

      <div class="pt-8">
        <div>
          <h3 class="text-lg font-medium leading-6">Addresses</h3>
        </div>
        <div
          v-for="(address, addressIndex) in company.addresses"
          :key="`address-${addressIndex}`"
          class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6"
        >
          <div class="sm:col-span-6">
            <div class="mt-1">
              <TextInput label="Street Address" v-model="address.street" />
            </div>
          </div>
          <div class="sm:col-span-6">
            <div class="mt-1">
              <TextInput
                label="Street Address Line 2"
                v-model="address.street2"
              />
            </div>
          </div>

          <div class="sm:col-span-2">
            <TextInput label="City" v-model="address.city" />
          </div>

          <div class="sm:col-span-2">
            <Dropdown
              label="State"
              v-model="address.state"
              :menu-items="states"
              display-name="name"
              data-testid="company-state-dropdown"
            />
          </div>

          <div class="sm:col-span-2">
            <TextInput
              label="ZIP / Postal code"
              v-model="address.zip"
              type="postal"
            />
          </div>
        </div>
        <div class="flex justify-center">
          <div class="mr-1 cursor-pointer">
            <Icon
              name="plus"
              class="-mt-1px w-7 h-7"
              @click="addAddress"
              data-testid="add-address-icon"
            />
          </div>
          <div v-if="company.addresses.length > 1" class="ml-1 cursor-pointer">
            <Icon
              name="minusIcon"
              class="w-7 h-7"
              @click="removeAddress"
              data-testid="remove-address-icon"
            />
          </div>
        </div>

        <div class="pt-8">
          <div>
            <fieldset>
              <legend class="sr-only">By Email</legend>
              <div class="text-base font-medium" aria-hidden="true">
                Tax Classification / Payments
              </div>
              <div>
                <Dropdown
                  label="Tax Classification"
                  :menu-items="taxClassifications"
                  display-name="name"
                  v-model="company.taxClassification"
                  data-testid="company-taxClassification-dropdown"
                />
                <Dropdown
                  label="Pay Day Frequency"
                  v-model="company.payDayFrequency"
                  :menu-items="paydayFrequencies"
                  display-name="name"
                  data-testid="company-payFrequency-dropdown"
                />
                <Dropdown
                  v-if="company.payDayFrequency?.key !== 'other'"
                  label="Pay Day"
                  v-model="company.payDay"
                  :menu-items="paydays"
                  display-name="name"
                  data-testid="company-payDays-dropdown"
                />
                <TextArea
                  v-else
                  label="Custom Pay Day"
                  v-model="company.customPayDay"
                  data-testid="company-custom-payday-text"
                />
              </div>
            </fieldset>
          </div>
        </div>
      </div>

      <div class="pt-5 mb-5 mx-2">
        <div class="flex justify-center">
          <Button
            class="w-48 mr-1"
            @click="cancel"
            color="secondary"
            data-testid="company-cancel-button"
            >Cancel</Button
          >
          <Button
            class="w-48 ml-1"
            color="primary"
            @click="createOrUpdateCompany"
            :loading="loading"
            data-testid="company-create-update-btn"
            >{{ editMode ? 'Update' : 'Create' }}</Button
          >
        </div>
      </div>
      <div class="py-5" />
    </div>
  </div>
</template>

<script lang="ts">
import Button from '@/components/library/Button.vue';
import Dropdown from '@/components/library/Dropdown.vue';
import TextInput from '@/components/library/TextInput.vue';
import { getPayday, getPaydayFrequencies } from '@/services/payday';
import {
  createProductionCompany,
  getProductionCompanyById,
  updateProductionCompany,
} from '@/services/production-company';
import { getTaxClassifications } from '@/services/tax-classifications';
import { getStates } from '../../services/address';

import Icon from '@/components/Icon.vue';
import TextArea from '@/components/library/TextArea.vue';
import Toggle from '@/components/library/Toggle.vue';
import type ProductionCompany from '@/types/ProductionCompany';
import axios from 'axios';
import { defineComponent, type PropType } from 'vue';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import type { SnackType } from '@/types/Snackbar';

export default defineComponent({
  props: {
    editMode: {
      type: Boolean,
      default: false,
    },
    navigate: {
      type: Function,
      required: true,
    },
    route: {
      type: Object as PropType<ParsedRoute>,
      required: true,
    },
  },
  components: {
    TextInput,
    Button,
    Dropdown,
    TextArea,
    Toggle,
    Icon,
  },
  data() {
    return {
      company: {
        addresses: [
          {
            street: '',
          },
        ],
        payrollProvider: {
          id: 1,
          name: 'Caps Cast and Crew',
          key: 'caps',
        },
        generateStartForm: true,
        generateWageTheftProtectionAgreement: true,
      } as ProductionCompany,
      taxClassifications: [],
      paydays: [],
      paydayFrequencies: [],
      states: [],
      loading: false,
    };
  },
  methods: {
    triggerSnackbar: (msg: string, timeout: number, type: SnackType) => {
      SnackbarStore.triggerSnackbar(msg, timeout, type);
    },

    async createOrUpdateCompany() {
      if (this.loading) return;
      this.loading = true;
      try {
        if (this.company.payDayFrequency?.key === 'other') {
          this.company.payDay = this.company.payDay || this.paydays[0];
        }
        if (this.editMode) {
          await updateProductionCompany(this.company);
          this.navigate({
            pathname: `/companies/${this.company.id}`,
          });
        } else {
          await createProductionCompany(this.company);
          this.navigate({
            pathname: '/companies',
          });
        }
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.message
            ? err.response.data.message
            : err.response?.data?.['errors']?.[0]?.message;
          this.triggerSnackbar(msg, 2500, 'error');
        } else {
          this.triggerSnackbar(err as string, 2500, 'error');
        }
      }
      this.loading = false;
    },
    cancel() {
      this.navigate({
        pathname: '/companies',
      });
    },
    addAddress() {
      this.company.addresses.push({
        street: '',
        city: '',
        state: {
          id: -1,
          name: '',
          key: '',
        },
        zip: '',
      });
    },
    removeAddress() {
      this.company.addresses.pop();
    },
  },
  async mounted() {
    if (this.editMode) {
      const { data: company } = await getProductionCompanyById(
        this.route.params.id as string,
      );
      this.company = company;
      this.company.payrollProvider = {
        id: 1,
        name: 'Caps Cast and Crew',
        key: 'caps',
      };
    }
    const {
      data: { data },
    } = await getTaxClassifications();
    const {
      data: { data: paydays },
    } = await getPayday();
    const {
      data: { data: states },
    } = await getStates();
    this.states = states;
    this.paydays = paydays;
    const {
      data: { data: paydayFrequencies },
    } = await getPaydayFrequencies();
    this.paydayFrequencies = paydayFrequencies;
    this.taxClassifications = data;
  },
});
</script>
