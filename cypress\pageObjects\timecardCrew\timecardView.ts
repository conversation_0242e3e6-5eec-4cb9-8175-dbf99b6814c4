export class TimecardViewPageObject {
  payPeriod: string = '[data-testid="timeCard-Pay Period"]';
  status: string = '[data-testid="timeCard-Status"]';
  createdOn: string = '[data-testid="timeCard-Created"]';
  updatedOn: string = '[data-testid="timeCard-Updated"]';
  timecardTitle: string = '[data-testid="timeCard-title-{{date}}"]';
  start: string = '[data-testid="timeCard-Start-{{date}}"]';
  ends: string = '[data-testid="timeCard-End-{{date}}"]';
  hoursWorked: string = '[data-testid="timeCard-Hours Worked-{{date}}"]';
  meals: string = '[data-testid="timeCard-Meals-{{date}}"]';

  validateTimecardDetails(
    payPeriod: string,
    status: string,
    createdOn: string,
    updatedOn: string,
  ) {
    cy.get(this.payPeriod).should('have.text', `Pay Period: ${payPeriod}`);
    cy.get(this.status).should('have.text', `Status: ${status}`);
    cy.get(this.createdOn).should('have.text', `Created: ${createdOn}`);
    cy.get(this.updatedOn).should('have.text', `Updated: ${updatedOn}`);
  }

  validateTimecardTime(
    date: string,
    start: string,
    end: string,
    hoursWorked: string,
    meals: string,
  ) {
    cy.get(this.start.replace('{{date}}', date)).should(
      'have.text',
      `Start: ${start}`,
    );
    cy.get(this.ends.replace('{{date}}', date)).should(
      'have.text',
      `End: ${end}`,
    );
    cy.get(this.hoursWorked.replace('{{date}}', date)).should(
      'have.text',
      `Hours Worked: ${hoursWorked}`,
    );
    cy.get(this.meals.replace('{{date}}', date)).should(
      'have.text',
      `Meals: ${meals}`,
    );
  }
}
