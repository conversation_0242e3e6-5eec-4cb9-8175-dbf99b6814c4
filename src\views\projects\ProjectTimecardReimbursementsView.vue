<template>
  <div class="flex justify-center mx-auto w-full py-2 px-5">
    <div class="max-w-7xl mb-32">
      <h2 class="text-xl font-bold mb-3">Reimbursements</h2>
      <div
        class="flex justify-center"
        v-for="(reimbursement, reimbursementIndex) in reimbursements"
        :key="`reimbursementIndex-${reimbursementIndex}`"
      >
        <div class="mt-6 grid grid-cols-6 gap-x-4 sm:grid-cols-6">
          <div class="col-span-3">
            <TextInput
              label="Expense"
              v-model="reimbursement.name"
              :disabled="editDisabled"
            />
          </div>
          <!-- TODO - Add back in when we rework how reimbursements work -->
          <div class="col-span-3">
            <Autocomplete
              label="Type"
              v-model="reimbursement.type"
              :menu-items="reimbursementTypes"
              :disabled="editDisabled || reimbursementTypesLoading"
              :placeholder="reimbursementTypesLoading ? 'Loading' : undefined"
              display-name="key"
              :initial-value="reimbursement.type"
            />
          </div>
          <div class="col-span-3">
            <TextInput
              label="Rate"
              v-model="reimbursement.rate"
              :disabled="editDisabled"
            />
          </div>
          <div class="col-span-3">
            <DatePicker
              v-model="reimbursement.date"
              class="mt-2"
              label="Date"
              :disabled="editDisabled"
              @change="getTimecardDayWorkLocation(reimbursement)"
            />
          </div>
          <div class="col-span-3">
            <Dropdown
              class="grow"
              v-model="reimbursement.workLocation"
              label="Work Location"
              display-name="shootLocation.locationName"
              :menu-items="project.projectShootLocations"
            >
              <template #label>
                {{
                  `${reimbursement.workLocation?.shootLocation.locationName} (${reimbursement.workLocation?.zip})`
                }}
              </template>
              <template #item="{ value: projectShootLocation }">
                {{
                  `${projectShootLocation?.shootLocation.locationName} (${projectShootLocation?.zip})`
                }}
              </template>
            </Dropdown>
          </div>
          <div class="col-span-3">
            <TextInput
              label="Quantity"
              type="number"
              v-model="reimbursement.quantity"
              :disabled="editDisabled"
            />
          </div>
          <div class="col-span-3 mt-3">
            <FileUpload
              v-model="reimbursement.documentId"
              class="my-1"
              :disabled="editDisabled"
            />
          </div>
          <div class="col-span-1 mt-3">
            <div class="font-bold">Total</div>
            ${{ reimbursement.rate * reimbursement.quantity }}
          </div>
        </div>
      </div>
      <div class="flex justify-center">
        <PlusIcon
          class="w-8 mr-1 cursor-pointer dark:text-gray-300 hover:dark:text-gray-200"
          @click="add"
        />
        <MinusIcon
          v-if="reimbursements?.length > 0"
          class="w-8 ml-1 cursor-pointer dark:text-gray-300 hover:dark:text-gray-200"
          @click="remove"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Autocomplete from '@/components/library/Autocomplete.vue';
import DatePicker from '@/components/library/DatePicker.vue';
import Dropdown from '@/components/library/Dropdown.vue';
import FileUpload from '@/components/library/FileUpload.vue';
import TextInput from '@/components/library/TextInput.vue';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';
import { listProjectReimbursementTypes } from '@/services/project';
import {
  listTimecardReimbursements,
  updateTimecardReimbursements,
} from '@/services/reimbursements';
import { getTimecard } from '@/services/timecards';
import type Project from '@/types/Project';
import type Reimbursement from '@/types/Reimbursements';
import type Timecard from '@/types/Timecard';
import type TimecardDay from '@/types/TimecardDay';
import { MinusIcon, PlusIcon } from '@heroicons/vue/24/outline';
import axios from 'axios';
import { DateTime } from 'luxon';
import { defineComponent, type PropType } from 'vue';
import { ReimbursementTypeKeys } from '@/types/Reimbursements';

export default defineComponent({
  props: {
    project: {
      type: Object as PropType<Project>,
      required: true,
    },
    editDisabled: {
      type: Boolean,
      default: false,
    },
    route: {
      type: Object as PropType<ParsedRoute>,
      required: true,
    },
    navigate: {
      type: Function as PropType<Function>,
      required: true,
    },
    componentLoaded: {
      type: Function as PropType<Function>,
      required: true,
    },
  },
  components: {
    TextInput,
    PlusIcon,
    MinusIcon,
    Dropdown,
    DatePicker,
    FileUpload,
    Autocomplete,
  },
  data() {
    return {
      rentalRateTypes: [
        {
          id: 1,
          name: 'Daily',
          key: 'daily',
        },
        {
          id: 2,
          name: 'Weekly',
          key: 'weekly',
        },
      ],
      rentalRateType: {} as any,
      timecard: {} as Timecard,
      reimbursements: [] as Reimbursement[],
      reimbursementTypes: [] as any[],
      reimbursementTypesLoading: true,
    };
  },
  methods: {
    async save() {
      if (this.editDisabled) return;
      try {
        const payload = JSON.parse(JSON.stringify(this.reimbursements));
        payload.forEach((reimbursement: any) => {
          reimbursement.rate ? (reimbursement.rate *= 100) : '';
          reimbursement.totalAmount =
            reimbursement.rate * reimbursement.quantity;
          reimbursement.date = DateTime.fromISO(reimbursement.date).toISODate();
        });
        await updateTimecardReimbursements(this.timecard.id, payload);
        SnackbarStore.triggerSnackbar('Reimbursements saved.', 2500, 'success');
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          SnackbarStore.triggerSnackbar(msg, 2500, 'error');
        } else {
          SnackbarStore.triggerSnackbar(err as string, 2500, 'error');
        }
        throw err;
      }
    },
    async saveAndContinue() {
      await this.save();
      const curPath = this.route.location.pathname;
      this.navigate({
        pathname: curPath.replace('reimbursements', 'review'),
      });
    },
    add() {
      if (this.editDisabled) return;
      this.reimbursements.push({
        name: '',
        lineNumber: '',
        documentId: undefined,
        rate: 0,
        type: '',
        date: DateTime.now(),
        workLocation: undefined,
        quantity: 1,
      });
    },
    remove() {
      if (this.editDisabled) return;
      this.reimbursements.pop();
    },
    selectRentalDay(timecardDay: any) {
      timecardDay.isRentalDay = !timecardDay.isRentalDay;
    },
    async loadReimbursementTypes() {
      this.reimbursementTypesLoading = true;
      if (this.project.productionCompany?.castAndCrewId) {
        try {
          const { data } = await listProjectReimbursementTypes(
            this.project.id!,
          );
          const filteredReimbursementTypes = data.filter(
            (reimbursementType: any) =>
              reimbursementType.castAndCrewId !==
              ReimbursementTypeKeys.KIT_RENTAL_NON_TAXABLE,
          );
          this.reimbursementTypes = filteredReimbursementTypes;
        } catch (err) {
          console.warn('Error loading reimbursement types', err);
        }
      }
      this.reimbursementTypesLoading = false;
    },
    async load() {
      await this.loadReimbursementTypes();
    },
    getTimecardDayWorkLocation(reimbursement: Reimbursement) {
      const workLocation = this.timecard.timecardDays.find(
        (timecardDay: TimecardDay) => {
          return (
            timecardDay.date.toISODate() === reimbursement.date.toISODate()
          );
        },
      )?.projectShootLocation;
      if (workLocation) {
        reimbursement.workLocation = workLocation;
      }
    },
  },
  async mounted() {
    this.load();
    const { data: timecard } = await getTimecard(
      parseInt(this.route.params.timecardId as string),
    );
    this.timecard = timecard as Timecard;
    const { data: reimbursements } = await listTimecardReimbursements(
      parseInt(this.route.params.timecardId as string),
    );
    this.componentLoaded();
    reimbursements.forEach((reimbursement: any) => {
      reimbursement.rate /= 100;
      reimbursement.date = reimbursement.date || DateTime.now();
    });
    this.reimbursements = reimbursements;
  },
});
</script>

<style></style>
