import { faker } from '@faker-js/faker';
import ProjectViewPageObject from '../../pageObjects/project/projectView';
import {
  addPersonalInfo,
  fetchProjectInformation,
  fetchRateTypes,
  logout,
} from '../../support/apiHelpers';
import { createProjectFlow } from '../../support/apiFlows/createProjectFlow';
import { projectOnboardingFlow } from '../../support/apiFlows/projectOnboardingFlow';
import { StartPaperworkPageObject } from '../../pageObjects/project/startPaperWork';
import { interceptSignPaperwork } from '../../support/apiTimecardInterceptors';

describe('User Crew - Start Paperwork', () => {
  const projectViewPO = new ProjectViewPageObject();
  const startPaperworkPO = new StartPaperworkPageObject();
  const onboardingInfo = {
    workLocation: '',
    union: '',
    occupation: '',
    rateType: '',
  };

  beforeEach((): void => {
    cy.loginWithSession(
      Cypress.env('PHONE_NUMBER'),
      Cypress.env('OTC_CODE'),
      Cypress.env('BASE_URL'),
      'supervisor',
    );

    //Get Details by Api before test
    cy.then(() => {
      // Step 1: Get the project Id by project Name
      return createProjectFlow().then(() => {
        //Get project info to add user
        return fetchProjectInformation().then((res) => {
          const departmentId = res.departments[0].id;
          return logout().then(() => {
            //login with crew into the new project
            const url = `${Cypress.env('BASE_URL')}/projects/${Cypress.env(
              'projectHashId',
            )}?onboard=true&departmentId=${departmentId}`;

            cy.loginWithoutSession(
              Cypress.env('PHONE_NUMBER_CREW'),
              Cypress.env('OTC_CODE'),
              url,
            );
          });
        });
      });
    })
      .then(() => {
        // Getting rate type
        return fetchRateTypes().then((rateTypes) => {
          onboardingInfo.rateType =
            rateTypes[
              faker.number.int({ min: 0, max: rateTypes.length - 1 })
            ].name;
        });
      })
      .then(() => {
        //filling out information needed for add time cards using API
        return addPersonalInfo();
      })
      .then(() => {
        return projectOnboardingFlow();
      });
  });

  it('Verify Crew member is able to sign paperwork', () => {
    interceptSignPaperwork();
    // Navigate to the project and open the supervisor view
    cy.visit(
      `${Cypress.env('BASE_URL')}projects/${Cypress.env('projectHashId')}`,
    );
    projectViewPO.goToStartPaperwork();
    startPaperworkPO.signPaperwork();

    cy.wait('@signPaperwork').then((interception) => {
      const responseBody = interception.response?.body;
      expect(interception.response?.statusCode).to.equal(200);
      expect(responseBody.length).is.greaterThan(0);
    });
  });
});
