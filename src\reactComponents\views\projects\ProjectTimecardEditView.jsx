import { forwardRef, useImperativeHandle, useRef } from 'react';
import { applyPureVueInReact } from 'veaury';
import ProjectTimecardEditViewVue from '../../../views/projects/ProjectTimecardEditView.vue';
import { useAuth, useReactRouter } from '../../AppHooks';
import { useOutletContext } from 'react-router';
const ReactProjectTimecardEditView = applyPureVueInReact(
  ProjectTimecardEditViewVue,
);

const ProjectTimecardEditView = forwardRef(() => {
  // const ProjectTimecardEditView = () => {
  useAuth();
  const { navigate, route } = useReactRouter();
  const context = useOutletContext();
  const vueRef = useRef();

  useImperativeHandle(context.ref, () => ({
    vueRef: vueRef.current.__veauryVueRef__,
  }));

  return (
    <ReactProjectTimecardEditView
      ref={vueRef}
      project={context.project}
      projectMember={context.projectMember}
      editDisabled={context.editDisabled}
      refresh={context.refresh}
      componentLoaded={context.componentLoaded}
      navigate={navigate}
      route={route}
    />
  );
});

export default ProjectTimecardEditView;
