import { faker } from '@faker-js/faker';
import {
  fetchProjectInformation,
  fetchRateTypes,
  fetchShootLocationsByProject,
  fetchUnionsByProject,
  fetchOccupationsByProjectAndUnion,
} from '../apiHelpers';
import { addProjectOnboarding } from '../apiPatchHelpers';

/**
 * Adds a project onboarding flow for the crew user in the new project.
 * It fetches the project information, rate types, shoot location, unions, occupations by union
  and then adds project onboarding.
 */
export function projectOnboardingFlow(): Cypress.Chainable<any> {
  const data = {
    rate: '200',
    rateType: {},
    occupation: {},
    startDate: new Date().toISOString(),
    workLocation: {},
    lineNumber: '',
    shootLineNumber: '',
    department: {},
    requiresHireLocation: false,
    unions: {},
    hireLocation: null,
    hireLocationId: null,
  };
  // Step 1: Fetch project information
  return fetchProjectInformation().then((member) => {
    // Step 2: Fetch rate types
    fetchRateTypes().then((rateResponse) => {
      // Step 3: Fetch shoot location
      fetchShootLocationsByProject().then((locationResponse) => {
        const payrollProjectLocationId =
          locationResponse[0].payrollProjectLocationId;
        // Step 4: Fetch unions
        fetchUnionsByProject(payrollProjectLocationId).then((unionResponse) => {
          const union =
            unionResponse[
              faker.number.int({ min: 0, max: unionResponse.length - 1 })
            ];
          // Step 5: Fetch occupations
          fetchOccupationsByProjectAndUnion(
            payrollProjectLocationId,
            union.id,
          ).then((occupationResponse) => {
            data.unions = [union];
            data.workLocation = locationResponse[0];
            data.occupation = occupationResponse[0];
            data.rateType = rateResponse[0];
            data.department = member.departments[0];
            cy.log(`Project onboarding: ${JSON.stringify(data)}`);
            // Step 6: Add project onboarding
            return addProjectOnboarding(data);
          });
        });
      });
    });
  });
}
