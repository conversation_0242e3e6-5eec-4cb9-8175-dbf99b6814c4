const accordion = {
  styleOverrides: {
    // Name of the slot
    root: (props) => {
      const { theme } = props;
      const { shape } = theme;
      const newStyles = {
        margin: '0px !important',
        borderRadius: shape.borderRadius,
        '&::before': {
          //remove top line
          content: 'none',
        },
      };
      return newStyles;
    },
  },
};

export const accordionSummary = {
  styleOverrides: {
    // Name of the slot
    root: (props) => {
      const { theme } = props;
      const { palette, spacing } = theme;
      const newStyles = {
        gap: spacing(2),
        padding: `0px ${spacing(1)} !important`,

        '&.Mui-expanded': {
          borderBottom: `1px solid ${palette.divider}`,
          backgroundColor: palette.gray[50],
          borderRadius: '8px 8px 0px 0px',
        },
      };
      return newStyles;
    },
  },
};
export const accordionDetails = {
  styleOverrides: {
    // Name of the slot
    root: (props) => {
      const newStyles = {
        padding: '0px 0px !important',
      };
      return newStyles;
    },
  },
};

export default accordion;
