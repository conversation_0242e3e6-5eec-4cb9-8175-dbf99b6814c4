import React from 'react';
import {
  Box,
  Autocomplete,
  StatusBadge,
  Text,
  GridTextField,
  ListItemIcon,
  MenuItem,
} from '@/reactComponents/library';

import CheckIcon from '@mui/icons-material/Check';

import { snackbarAxiosErr } from '@/reactComponents/library/Snackbar';

import PropTypes from 'prop-types';
import { useNavigate, useLocation } from 'react-router';

import { FilterOperator, FilterUIType } from '@/types/Filter';
import { convertFiltersToQuery } from '@/utils/filter';
import { listProjectTimecards } from '@/services/project';

const sortTimecards = (timecards) =>
  timecards.sort((a, b) =>
    a.payPeriod.startsAt > b.payPeriod.startsAt ? 1 : -1,
  );

const TimecardsList = (props) => {
  const { timecard, member, project } = props;
  const [timecards, setTimecards] = React.useState([]);
  const [loadingTimecards, setLoadingTimecards] = React.useState(false);
  const navigate = useNavigate();
  const { state: navState } = useLocation();

  const timecardsPaginationState = React.useRef({
    pagination: {
      page: 1,
      limit: 50,
      total: 0,
    },
    hasNextPage: true,
  });
  const fetchUserTimecards = React.useCallback(
    async (search, infiniteScroll = false) => {
      try {
        const allFilters = [];
        if (member) {
          allFilters.push({
            id: 'project_member',
            field: 'projectMemberId',
            label: 'ProjectMemberId',
            value: member.id,
            type: FilterUIType.Text,
            operator: FilterOperator.Equals,
            active: true,
          });
        }
        const timecardFilters = convertFiltersToQuery(allFilters);

        if (infiniteScroll) {
          const hasNextPage = timecardsPaginationState.current.hasNextPage;
          if (!hasNextPage) return;
          timecardsPaginationState.current.pagination.page++;
        }
        if (!infiniteScroll) {
          timecardsPaginationState.current.pagination.page = 1;
          timecardsPaginationState.current.hasNextPage = true;
        }
        setLoadingTimecards(true);
        const {
          data: { data: userTimeCards, meta },
        } = await listProjectTimecards(project.id, timecardFilters, undefined, {
          page: timecardsPaginationState.current.pagination.page,
          limit: timecardsPaginationState.current.pagination.limit,
        });
        const { current_page, total, next_page_url } = meta;
        timecardsPaginationState.current.pagination.total = total;
        timecardsPaginationState.current.pagination.page = current_page;
        timecardsPaginationState.current.hasNextPage = !!next_page_url;
        if (infiniteScroll) {
          if (
            userTimeCards.length <
            timecardsPaginationState.current.pagination.limit
          ) {
            timecardsPaginationState.current.hasNextPage = false;
          }
          if (userTimeCards) {
            setTimecards((prev) => {
              const sorted = sortTimecards(userTimeCards);
              return prev.concat(sorted);
            });
          }
        } else {
          const sorted = sortTimecards(userTimeCards);
          setTimecards(sorted);
        }
      } catch (error) {
        snackbarAxiosErr(error, 'Error fetching timecards');
      } finally {
        setLoadingTimecards(false);
      }
    },
    [member, project.id],
  );
  React.useEffect(() => {
    if (project?.id) {
      fetchUserTimecards();
    }
  }, [project?.id, fetchUserTimecards]);

  return (
    <Box
      sx={{
        alignItems: 'center',
        marginLeft: 'auto',
      }}
    >
      <Autocomplete
        getOptionLabel={() => ''}
        isOptionEqualToValue={(option, value) => option.id === value.id}
        renderOption={(props, option) => {
          const isSelected = timecard?.id === option.id;

          return (
            <MenuItem
              {...props}
              key={option.id}
              disabled={isSelected}
              sx={{ cursor: isSelected ? 'not-allowed' : 'pointer' }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Text variant="disReg" sx={{ fontSize: '0.9rem' }}>
                  {`${option.payPeriod.startsAt.toFormat(
                    'ccc. MM/dd',
                  )} - ${option.payPeriod.endsAt.toFormat('ccc. MM/dd')}`}
                </Text>
                <StatusBadge
                  sx={{ fontSize: '0.7rem' }}
                  tcStatusId={option.status.id}
                >
                  {option.status.name}
                </StatusBadge>
              </Box>
              {isSelected && (
                <ListItemIcon
                  sx={{
                    minWidth: 24,
                    color: '#e91e63',
                    fontWeight: 'bold',
                  }}
                >
                  <CheckIcon sx={{ fontSize: '1.8rem', fontWeight: 'bold' }} />
                </ListItemIcon>
              )}
            </MenuItem>
          );
        }}
        disableClearable={true}
        value={timecard}
        options={timecards}
        loading={loadingTimecards}
        fetchOptions={fetchUserTimecards}
        infiniteScrolling={true}
        onChange={(event, newValue) =>
          navigate(
            `/projects/${timecard.project.hashId}/admin/members/${member.id}/member/timecards/${newValue.id}`,
            {
              state: navState, //preserve nav state
            },
          )
        }
        hasNextPage={timecardsPaginationState.current.hasNextPage}
        renderInput={(params) => (
          <GridTextField
            {...params}
            InputProps={{
              ...params.InputProps,
              sx: { fontSize: '0.9rem' },
              startAdornment: timecard && (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Text variant="disReg" sx={{ fontSize: '0.9rem' }}>
                    {`${timecard.payPeriod.startsAt.toFormat(
                      'ccc. MM/dd',
                    )} - ${timecard.payPeriod.endsAt.toFormat('ccc. MM/dd')}`}
                  </Text>
                  <StatusBadge
                    sx={{ fontSize: '0.7rem' }}
                    tcStatusId={timecard.status.id}
                  >
                    {timecard.status.name}
                  </StatusBadge>
                </Box>
              ),
            }}
            inputProps={{ ...params.inputProps, readOnly: true }}
          />
        )}
      />
    </Box>
  );
};
TimecardsList.propTypes = {
  timecard: PropTypes.object.isRequired,
  member: PropTypes.object.isRequired,
  project: PropTypes.object.isRequired,
};

export default TimecardsList;
