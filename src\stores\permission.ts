import { getUserPermissions } from '@/services/users';
import type { Permission } from '@/types/Permission';
import { defineStore } from 'pinia';

export const usePermissionStore = defineStore({
  id: 'permission',
  state: () => ({
    permissions: [] as Permission[],
  }),
  getters: {
    getPermissions: (state) => state.permissions,
  },
  actions: {
    async setPermissions() {
      const { data } = await getUserPermissions();
      this.permissions = data;
    },
    hasPermission(key: string | undefined): boolean {
      if (!key) return true;
      const permission: Permission | undefined = this.permissions.find(
        (p) => p.key === key,
      );
      return permission !== undefined;
    },
    clearPermissions() {
      this.permissions = [];
    },
  },
});
