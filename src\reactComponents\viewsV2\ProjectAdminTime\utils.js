import { FilterOperator, FilterUIType } from '@/types/Filter';
import _cloneDeep from 'lodash/cloneDeep';
import {
  convertFiltersToQuery,
  convertFiltersToURLQuery,
  // applyQueryStringToFilters,
} from '@/utils/filter';
import { BatchStatusEnum } from '@/types/Batch';
import { BatchStatusLabel } from '@/utils/enum';
import { Cookie } from '@/utils/cookie';

export const UNBATCHED_ID = -1;

export const INITIAL_TIMECARD_BATCH_FILTER = Object.freeze({
  id: 'batch',
  field: 'batchId',
  label: 'Batch',
  value: '',
  subject: 'timecard',
  // unbatched value required by BE
  options: [{ active: true, id: 'option_-1', label: '', value: null }],
  type: FilterUIType.MultiSelect,
  operator: FilterOperator.In,
  active: true,
});

export const CLEAR_FILTERS = Object.freeze([
  INITIAL_TIMECARD_BATCH_FILTER,
  {
    id: 'employee',
    field: 'userCrewId',
    label: 'Employee',
    value: '',
    options: [],
    subject: 'timecard',
    type: FilterUIType.MultiSelect,
    operator: FilterOperator.In,
    active: true,
  },
  {
    id: 'department',
    field: 'projectMember.departmentId',
    label: 'Department',
    value: '',
    options: [],
    subject: 'timecard',
    type: FilterUIType.MultiSelect,
    operator: FilterOperator.In,
    active: true,
  },
  {
    id: 'union',
    field: 'unionId',
    label: 'Union',
    value: '',
    options: [],
    subject: 'timecard',
    type: FilterUIType.MultiSelect,
    operator: FilterOperator.In,
    active: true,
  },
  {
    id: 'payPeriod',
    field: 'payPeriodId',
    label: 'Pay Period',
    value: '',
    options: [],
    subject: 'timecard',
    type: FilterUIType.MultiSelect,
    operator: FilterOperator.In,
    active: true,
  },
  {
    id: 'status',
    field: 'statusId',
    label: 'Timecard Status',
    value: '',
    options: [],
    subject: 'timecard',
    type: FilterUIType.MultiSelect,
    operator: FilterOperator.In,
    active: true,
  },
  {
    id: 'errors',
    field: 'errorMessages',
    label: 'Errors',
    value: '',
    options: [],
    subject: 'timecard',
    type: FilterUIType.MultiSelect,
    active: true,
  },
]);

export const BATCH_FILTERS = Object.freeze([
  {
    id: 'batchStatus',
    field: 'statusId',
    label: 'Batch Status',
    value: '',
    options: [],
    subject: 'batch',
    type: FilterUIType.MultiSelect,
    operator: FilterOperator.In,
    active: true,
  },
]);

export const makeFiltersId = (hashId) => `time-filters-${hashId}`;

export const setInitDefaultFilters = (
  memberDept,
  filtersId,
  setFilters,
  setBatchFilters,
) => {
  const sessionFilters = getSessionFilters(filtersId);
  const tcFilters = sessionFilters?.filter((f) => f.subject === 'timecard');
  const batchFilters = sessionFilters?.filter((f) => f.subject === 'batch');

  if (sessionFilters) {
    setFilters(tcFilters);
    setBatchFilters(batchFilters);
    return sessionFilters;
  } else {
    const initialFilters = _cloneDeep(CLEAR_FILTERS);

    const depFilter = initialFilters.find((f) => f.id === 'department');
    if (depFilter && memberDept?.id) {
      depFilter.options = [
        {
          id: memberDept?.id,
          value: memberDept?.id,
          name: memberDept?.type.name,
          active: true,
        },
      ];
    }
    setFilters(initialFilters);
    return initialFilters;
  }
};

export const updateURLFilters = (filters, batchFilter, setSearchParams) => {
  const filtersWithValues = filters.filter(
    (f) => f.id === 'batch' || f.options?.length > 0,
  );
  const batchFiltersWithValues = batchFilter.filter(
    (f) => f.options?.length > 0,
  );

  const batchFiltersAsQuery = convertFiltersToURLQuery(batchFiltersWithValues);
  const filtersAsQuery = convertFiltersToURLQuery(filtersWithValues);

  setSearchParams(
    (prev) => {
      prev.set('filters', filtersAsQuery);
      prev.set('batchFilters', batchFiltersAsQuery);
      return prev;
    },
    { replace: true },
  );
};

const getIdsFromQueryString = (filterQuery) => {
  if (!filterQuery) return {};

  const queryParams = new URLSearchParams(filterQuery.replace(/;/g, '&'));
  const idMap = {};
  const keys = [...queryParams.keys()];

  keys.forEach((key) => {
    const values = queryParams.get(key);
    const valuesArr = values.split(',');
    const idsArr = valuesArr.map((v) =>
      key === 'errors'
        ? String(v.replace('optionId:', ''))
        : Number(v.replace('optionId:', '')),
    );
    const validIds = idsArr.filter((id) => id); //filter out 0 and NaN

    if (validIds.length > 0) {
      idMap[key] = validIds;
    }
  });

  return idMap;
};

export const setFiltersFromURL = ({
  setFilters,
  filtersQuery,
  options,
  setBatchFilters,
  batchFiltersQuery,
}) => {
  const idMap = getIdsFromQueryString(filtersQuery);
  const batchIdMap = getIdsFromQueryString(batchFiltersQuery);

  setFilters((prev) => {
    const newFilters = _cloneDeep(prev);

    for (const key in idMap) {
      if (Object.prototype.hasOwnProperty.call(idMap, key)) {
        const ids = idMap[key];

        const filter = newFilters.find((f) => f.id === key);
        if (!filter) {
          console.error('filter not found: ', key);
          continue;
        }

        const newOptions = ids
          .map((id) => {
            const option = options[key].find((o) => o.id === id);

            if (key === 'batch' && id === UNBATCHED_ID) {
              return {
                ...option,
                active: true,
                value: null,
              };
            }

            if (!option) {
              console.error('option not found: ', id);
              return null;
            }
            return {
              ...option,
              active: true,
              value: id,
            };
          })
          .filter((o) => o); //remove null

        filter.options = newOptions;
      }
    }
    return newFilters;
  });

  //TODO - dont call setBatchFilters if batchIdMap is empty
  setBatchFilters((prev) => {
    const newBatchFilters = _cloneDeep(prev);

    for (const filterName in batchIdMap) {
      if (Object.prototype.hasOwnProperty.call(batchIdMap, filterName)) {
        const values = batchIdMap[filterName];

        const filter = newBatchFilters.find((f) => f.id === filterName);
        if (!filter) {
          console.error('filter not found: ', filterName);
          continue;
        }

        const newOptions = values
          .map((id) => {
            const option = BatchStatusFilterOptions.find(
              (o) => `${o.id}` === `${id}`,
            );

            if (!option) {
              console.error('option not found: ', id);
              return null;
            }
            return {
              ...option,
              active: true,
            };
          })
          .filter((o) => o); //remove null

        filter.options = newOptions;
      }
    }
    return newBatchFilters;
  });
};

export const prepFilters = (filters) => {
  const filtersWithValues = filters.filter((f) => {
    const hasOptions = f.options?.length > 0;
    //TODO - verify filter is valid, this is user input
    return hasOptions;
  });

  const filtersAsQuery = convertFiltersToQuery(filtersWithValues);

  return filtersAsQuery;
};

export const setSessionFilters = (
  filtersId,
  filters = [],
  batchFilters = [],
) => {
  const allFilters = filters.concat(batchFilters);
  Cookie.set(filtersId, JSON.stringify(allFilters));
};

const getSessionFilters = (filtersId) => {
  const cookieFilters = Cookie.get(filtersId);
  if (cookieFilters) {
    const filters = JSON.parse(cookieFilters);

    return filters;
  } else {
    return null;
  }
};

export const updateSessionFilterBatch = (filterId, batchId) => {
  const sessionFilters = getSessionFilters(filterId);

  if (sessionFilters) {
    const batchFilter = sessionFilters.find((f) => f.id === 'batch');
    if (batchFilter) {
      batchFilter.options = [
        {
          id: batchId === null ? UNBATCHED_ID : batchId,
          value: batchId,
          active: true,
        },
      ];
      setSessionFilters(filterId, sessionFilters);
    }
  }
};

// tentative options for batch filter
export const BatchStatusFilterOptions = Object.entries(BatchStatusLabel).map(
  ([key, value]) => ({
    id: key,
    value: key,
    key: BatchStatusEnum[Number(key)],
    name: value,
    active: false,
  }),
);

// tentative options for timecard errors filter
// TODO - query fields will be removed once BE is updated
export const ErrorsOption = [
  {
    id: 'errors',
    value: 'errors',
    name: 'Calculation Errors',
    active: false,
  },
  {
    id: 'warnings',
    value: 'warnings',
    name: 'Calculation Warnings',
    active: false,
  },
  {
    id: 'none',
    value: 'none',
    name: 'No Errors',
    active: false,
  },
];
