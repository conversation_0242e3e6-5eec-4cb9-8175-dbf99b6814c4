import { createMemoryRouter, RouterProvider, Outlet } from 'react-router-dom';
import CreateBatchModal from '@/reactComponents/viewsV2/sharedComponents/CreateBatch';
import { SnackbarProvider } from 'notistack';
import PropTypes from 'prop-types';

const mockProject = {
  id: 123,
  name: 'Test Project',
};

const LayoutWithOutletContext = ({ context }) => {
  return (
    <SnackbarProvider>
      <Outlet context={context} />
    </SnackbarProvider>
  );
};

LayoutWithOutletContext.propTypes = {
  context: PropTypes.object.isRequired,
};

// TestWrapper to simulate React Router context
const TestWrapper = () => {
  const router = createMemoryRouter(
    [
      {
        path: '/',
        element: <LayoutWithOutletContext context={{ project: mockProject }} />,
        children: [{ path: '/', element: <CreateBatchModal /> }],
      },
    ],
    { initialEntries: ['/'] },
  );

  return <RouterProvider router={router} />;
};

TestWrapper.propTypes = {
  project: PropTypes.object.isRequired,
};

describe('CreateBatchModal Component', () => {
  beforeEach(() => {
    cy.mount(<TestWrapper />);
  });

  it('should create a batch successfully and show snackbar', () => {
    cy.intercept('POST', '/v2/api/core/projects/**/batches', {
      statusCode: 200,
      body: { success: true },
    }).as('batchCreateSuccess');
    cy.get('[data-testid="create-batch-btn"]').click();
    cy.get('[data-testid="create-batch-input"]').type('My New Batch');
    cy.get('[data-testid="Create-btn"]').click();
    cy.contains('Batch created').should('be.visible');
  });

  it('shows error if name is empty', () => {
    cy.get('[data-testid="create-batch-btn"]').click();
    cy.get('[data-testid="Create-btn"]').click();
    cy.contains('Batch name is required').should('be.visible');
  });

  it('should validate that the "X" button closes the modal', () => {
    cy.get('[data-testid="create-batch-btn"]').click();
    cy.get('[data-testid="CloseIcon"]').click();
    cy.get('[data-testid="create-batch-modal"]').should('not.exist');
  });

  it('should validate that the "Close" button closes the modal', () => {
    cy.get('[data-testid="create-batch-btn"]').click();
    cy.get('[data-testid="Cancel-btn"]').click();
    cy.get('[data-testid="create-batch-modal"]').should('not.exist');
  });
});
