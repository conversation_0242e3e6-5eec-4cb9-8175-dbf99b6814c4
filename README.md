# FPS

FPS web app, now in React with legacy VUE areas.

This app is in progress of moving from VUE to React. Currently its a react app at the base, but using [veaury](https://github.com/gloriasoft/veaury) to mount the legacy VUE components.

You will need to have the server up and running locally if you are working on the backend or full-stack functionality. However, if you are only working on the frontend, you do **not** need to have the backend server running. See [typescript-adonis-api](https://github.com/FreelancePayrollSystems/typescript-adonis-api/pulls) project README.md for more details on setting up the backend, if required.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/)

#### If you are using Windows

Use Git Bash to avoid any issue managing node packages

[Git Bash](https://git-scm.com/downloads)

#### Required Extensions (open links in vscode to view install page)

- [Vue - Official](vscode:extension/Vue.volar) - Vue typescript support
- [Prettier](vscode:extension/esbenp.prettier-vscode) - Formatting
- [EsLint](vscode:extension/dbaeumer.vscode-eslint) - static code linting
- [SpellCheck](vscode:extension/streetsidesoftware.code-spell-checker) - spell check

#### Recommend Extensions (open links in vscode to view install page)

- [Tailwind](vscode:extension/bradlc.vscode-tailwindcss) - Intellisense for tailwind (Legacy Styling)
- [MySQL - Database Inspector](vscode:extension/cweijan.vscode-mysql-client2) - Handy if you need to view the database
<details>
<summary>
  ***DEPRECATED*** Type Support for `.vue` Imports in TS
</summary>

Keeping for reference, but this should be covered by the Official VUE extension as of Jan 2025

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [TypeScript Vue Plugin (Volar)](https://marketplace.visualstudio.com/items?itemName=Vue.vscode-typescript-vue-plugin) to make the TypeScript language service aware of `.vue` types.

If the standalone TypeScript plugin doesn't feel fast enough to you, Volar has also implemented a [Take Over Mode](https://github.com/johnsoncodehk/volar/discussions/471#discussioncomment-1361669) that is more performant. You can enable it by the following steps:

1. Disable the built-in TypeScript Extension
   1. Run `Extensions: Show Built-in Extensions` from VSCode's command palette
   2. Find `TypeScript and JavaScript Language Features`, right click and select `Disable (Workspace)`
2. Reload the VSCode window by running `Developer: Reload Window` from the command palette.
</details>

### ENV selection.

By default this is setup to point at a localhost BE. It can be changed to point at dev/qa environments by changing the server.proxy.target value in `./vite.config.mts`

Local BE requires the `typescript-adonis-api` project as well as docker setup, see README.md in that repo for more details.

But if you're just working on the FE, just pointing the proxy target at the dev ENV url is enough.

## Project Setup

```sh
npm i
```

### Config

You'll need to update the src/config file with okta issuer and clientId values to run the app.

muiLicenseKey is optional to suppress the warning on the table

### Compile and Hot-Reload for Development

```sh
npm run dev
```

Before creating a PR, your code should pass both type check and [ESLint](https://eslint.org/) check

```sh
npm run type-check
npm run lintReact
```

### Compile and run production build locally

```sh
npm run build
npm run preview
```

# Data setup

## User

YOU WILL NEED TO BE ON CAST AND CREW VPN (paloAlto) to access these envs

Dev URL: https://fps-dev.nonprod.aws.castandcrew.com/v2/login?redirect=%2F
QA URL: https://fps-stg.nonprod.aws.castandcrew.com/v2/login?redirect=%2F

Currently in process of moving to an Okta login, but for now using sms codes that are mocked on dev env.

Fire up app - should get a 'enter mobile number' login page, use `123456` as code on dev. QA/UAT will send a twilio code to your phone.

Create a user by entering a phone number, entering code, then filling out the onboarding information. See adonis readme for how to make your user an admin. In dev ask someone who is already a dev admin to update your user in the /users tab

## Company Data

If you need to create a company locally, create one called `Arts and Sciences` with Cast and Crew Id set to `93` - the rest of the data can be whatever.

## Project

Create a project using your production company, no restrictions on data.

Once your project is setup, you'll need to complete `Project Onboarding` + `Sign start paperwork` on the project page to create timecards for your user.

# FE Dev

New React pages is built with lightly wrapped MUI components - see `src\reactComponents\library\index.js` for full list.

We haven't had time to move react over to typescript fully but can get close by writing any utility functions in `ts` files instead of `js` to help get some typechecking.

New pages will use MUI Box/SX styling, and quickly converted can maintain the same VUE tailwind classes for now. We have moving to the unified 'platform UI' coming down the pipe, so will likely have to restyle everything then so minimizing the work on it now.

Using MobX to handle any complex state for features with a FEATURE/store.ts

# UI Test Automation

<details>
<summary>
Naming Strategy for locators data-testid attributes
</summary>
The data-testid attributes should follow a structured format:

```
[component]-[element]-[action|role]
```

#### **Components**

Should have a unique, meaningful identifier:

- table
- button
- card

#### **Element**

Describes the type of element within the component:

- input
- button
- link

#### **Action or Role**

Specifies the intended interaction or role:

- submit
- close
- edit

#### **Examples:**

| **UI Element**          | **data-testid**   |
| ----------------------- | ----------------- |
| Login form input phone  | login-phone-input |
| Login form button enter | login-enter-btn   |

</details>

# Workflow

### **Branch Naming**

When creating new branches, please follow the naming convention:

```
ticket-number_short-meaningful-description
```

##### Example

```
FPS-234_fix-duplicate-button
```

### PR + Approval + Merge

When your code is ready to merge, first run type-check + lint and address any issues.

Move ticket to `code review` If you need a specific person to review, assign to them. Otherwise leave assigned to yourself.

Create PR to current dev release branch, 99% it will be `devel` branch. Name PR similar to branch starting with ticket number `FPS-1308 update readme`

Paste Link to ticket AND PR to the appropriate Teams Chat, as well as requesting any reviewers in github

When reviewing someone else's PR, after any changes have been made you'll need to:

- Merge to dev branch
- Delete ticket branch
- Move ticket to `Deploy to QA` and assign back to dev. for SOC reasons, a separate dev must move ticket from `code review` to `deploy to QA`

When a QA deployment is done, then move tickets to `QA` status and assign to a QA resource.
