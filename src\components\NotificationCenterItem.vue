<template>
  <div
    class="group relative flex gap-x-6 rounded-lg p-4 hover:bg-gray-100 dark:hover:bg-gray-600 items-center"
    :class="{ 'cursor-pointer': !!link }"
    @click="navigate(link)"
  >
    <div
      class="mt-1 flex h-10 w-10 flex-none items-center justify-center rounded-lg"
      :class="{
        'bg-gray-50 group-hover:bg-white': notification.read,
        'bg-blue-500 group-hover:bg-blue-600': !notification.read,
      }"
      :key="`notification-icon-container-${notification._id}-${notification.read}`"
    >
      <EnvelopeIcon
        v-if="!notification.read"
        class="h-5 w-5"
        :class="{
          'text-white': !notification.read,
          'text-gray-600 group-hover:text-blue-600': notification.read,
        }"
        aria-hidden="true"
      />
      <EnvelopeOpenIcon
        v-else
        class="h-5 w-5 text-blue-500 group-hover:text-blue-600"
        aria-hidden="true"
        @click.stop="$emit('markAsRead', notification._id)"
      />
    </div>
    <div>
      <p class="font-semibold leading-4 mt-0">
        <span v-html="formattedContent" />
        <span class="absolute inset-0" />
      </p>
      <p class="mt-1 text-gray-600 dark:text-gray-400">{{ timeAgo }}</p>
    </div>
    <div class="flex flex-grow" />
    <DropdownMenu :itemGroups="actions" @click.stop />
  </div>
</template>

<script lang="ts">
import DropdownMenu from '@/components/library/DropdownMenu.vue';
import type { MenuItemGroup } from '@/types/Menu';
import type { NovuNotification } from '@/types/Novu';
import {
  EnvelopeIcon,
  EnvelopeOpenIcon,
  TrashIcon,
} from '@heroicons/vue/24/outline';
import { DateTime } from 'luxon';
import type { PropType } from 'vue';
import { defineComponent, inject } from 'vue';
//
export default defineComponent({
  props: {
    notification: {
      type: Object as PropType<NovuNotification>,
      required: true,
    },
  },
  setup() {
    const navigate = inject('navigate') as Function;
    return { navigate };
  },
  components: {
    EnvelopeIcon,
    EnvelopeOpenIcon,
    DropdownMenu,
    TrashIcon,
  },
  computed: {
    timeAgo(): string | null {
      const dateTime = DateTime.fromISO(this.notification.createdAt);
      return dateTime.toRelative();
    },
    actions(): MenuItemGroup[] {
      const groups = [
        [
          {
            label: 'Mark Read',
            action: this.markRead,
            hide: this.notification.read,
            icon: EnvelopeOpenIcon,
          },
          // { label: 'Mark Unread', action: this.markUnread, hide: !this.notification.read, icon: EnvelopeIcon }, -- headless service doesn't currently support marking as unread
          {
            label: 'Delete',
            action: this.deleteNotification,
            hide: false,
            icon: TrashIcon,
          },
        ],
      ];

      const filteredGroups = [];
      for (const group of groups) {
        const filteredGroup = group.filter((item) => !item.hide);
        if (filteredGroup.length > 0) {
          filteredGroups.push(filteredGroup);
        }
      }
      return filteredGroups;
    },
    formattedContent(): string {
      let content = this.notification.content;
      for (const key in this.notification.payload) {
        const value = this.notification.payload[key];
        const regex = new RegExp(`\\b${value}\\b`, 'g');
        content = content.replace(regex, `<strong>${value}</strong>`);
      }
      return content;
    },
    link(): string {
      return this.notification?.cta?.data?.url || '';
    },
  },
  methods: {
    markRead(): void {
      this.$emit('read');
    },
    markUnread(): void {
      this.$emit('unread');
    },
    deleteNotification(): void {
      this.$emit('delete');
    },
  },
});
</script>
