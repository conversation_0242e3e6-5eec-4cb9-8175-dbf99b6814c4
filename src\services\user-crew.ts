import type CreateUserCrew from '@/types/CreateUserCrew';
import axios, { type AxiosResponse } from 'axios';
import { coreBaseUrl } from '../utils/constants';

export const needsCrewOnboarding = async (): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/user-crews/needs-onboarding/`;
  const response = await axios.get(url, {
    withCredentials: true,
  });
  return response;
};

export const createUserCrew = async (
  createUserCrew: CreateUserCrew,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/user-crews/`;
  const response = await axios.post(url, createUserCrew, {
    withCredentials: true,
  });
  return response;
};

export const getUserCrewById = async (
  id: number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/user-crews/${id}/`;
  const response = await axios.get(url, {
    withCredentials: true,
  });
  return response;
};

export const getUserCrew = async (): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/user-crews/`;
  const response = await axios.get(url, {
    withCredentials: true,
  });
  return response;
};

export const updateUserCrew = async (
  updateUserCrew: any,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/user-crews/`;
  const response = await axios.patch(url, updateUserCrew, {
    withCredentials: true,
  });
  return response;
};
