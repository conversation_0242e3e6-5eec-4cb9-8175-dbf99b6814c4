import React from 'react';
import PropTypes from 'prop-types';
import { TextField, InputLabel, Box } from '@mui/material';
import { Text } from './index';

import { tailwindInputFix } from '../theme/components/utils';

/**
 * TextInput - essentially a TextField with a label that appears above instead of integrated in with the textfield like the default does.
 */
const TextInput = (props) => {
  // todo: inputProps about to be deprecated, change to slotProps: {input: {}} in the future
  let { inputProps, label, onFocus, ...rest } = props;

  if (!inputProps) {
    inputProps = {};
  }
  if (!inputProps.className) {
    inputProps.className = '';
  }

  if (inputProps.className.includes(tailwindInputFix.trim()) === false) {
    inputProps.className += tailwindInputFix;
  }

  const handleFocus = React.useCallback(
    (e) => {
      e.target.select();
      if (onFocus) {
        onFocus(e);
      }
    },
    [onFocus],
  );

  return (
    <Box>
      <InputLabel>
        <Text variant="smMed" data-testid="payroll-note-modal">
          {label}
        </Text>
      </InputLabel>
      <TextField inputProps={inputProps} onFocus={handleFocus} {...rest} />
    </Box>
  );
};

TextInput.propTypes = {
  label: PropTypes.string,
  inputProps: PropTypes.object,
  onFocus: PropTypes.func,
};

export default TextInput;
