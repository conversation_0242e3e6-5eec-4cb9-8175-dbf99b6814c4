import React from 'react';
import PropTypes from 'prop-types';
import _cloneDeep from 'lodash/cloneDeep';

import { Box, Modal, Autocomplete, Button } from '@/reactComponents/library';
import { useTheme } from '@mui/material/styles';
import { rentalRateTypes, isRentalDay, calcKitDate } from '../timecardUtils';

const isActiveDay = (day) => day.isActive;

const RentalDaysCell = (props) => {
  const {
    timecardDays = [],
    reimbursement,
    updateRow,
    updateTimecard,
    disabled,
  } = props;

  const { rateType } = reimbursement;

  const [isModalOpen, setIsModalOpen] = React.useState(false);

  const { palette } = useTheme();

  const isWeekly = rateType?.key === 'weekly';

  const activeDays = timecardDays.filter(isActiveDay);
  const rentalDays = activeDays.filter(isRentalDay);

  const rentalDaysString = rentalDays
    .map((day) => day.date.toFormat('ccc').slice(0, 2))
    .join(', ');

  const handleDayToggle = (day) => {
    const updatedDays = _cloneDeep(timecardDays);

    const updatedDay = updatedDays.find((d) => d.id === day.id);
    if (updatedDay) {
      updatedDay.isRentalDay = !updatedDay.isRentalDay;
      updateTimecard({ timecardDays: updatedDays });
      const updated = _cloneDeep(reimbursement);
      const rentalDays = updatedDays.filter(isRentalDay);
      const rentalDaysCount = rentalDays.length;

      const date = calcKitDate('daily', updatedDays);

      const totalAmount =
        Math.floor(updated.rentalRate * rentalDaysCount * 100) / 100;
      updated.totalAmount = totalAmount;
      updated.date = date;
      updateRow(updated);
    } else {
      console.error('Unable to toggle rental day');
    }
  };

  return (
    <>
      <Button //TODO replace with styled comp
        className={'gridInput'}
        variant="text"
        sx={{
          color: 'text.primary',
          px: 0,
          py: 1,
          minWidth: '85px',
          '&.Mui-focusVisible': {
            border: `2px solid ${palette.pink[500]} !important`, // Customize the border style
            outline: 'none', // Remove default outline if needed
          },
        }}
        disabled={disabled}
        onClick={() => setIsModalOpen(true)}
      >
        {isWeekly ? (
          <Box>Weekly</Box>
        ) : (
          <Box>
            <Box>
              {rentalDays.length} Day{rentalDays.length !== 1 ? 's' : ''}
            </Box>
            <Box>{rentalDaysString}</Box>
          </Box>
        )}
      </Button>
      <Modal
        title="Rental Rate Type"
        open={isModalOpen}
        setOpen={setIsModalOpen}
        submitText="Close"
        onSubmit={(e) => {
          e.stopPropagation();
          setIsModalOpen(false);
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 2,
            alignItems: 'flex-start',
            width: '100%',
          }}
        >
          <Box sx={{ width: '150px' }}>
            <Autocomplete
              value={rateType}
              options={rentalRateTypes}
              disableClearable={true}
              getOptionLabel={(option) => option.name}
              onChange={(event, newVal) => {
                const updated = _cloneDeep(reimbursement);
                updated.rateType = newVal;
                let totalAmount = 0;
                if (newVal.key === 'weekly') {
                  totalAmount = updated.rentalRate || 0;
                } else {
                  totalAmount =
                    Math.floor(updated.rentalRate * rentalDays.length * 100) /
                    100;
                }

                const date = calcKitDate(newVal.key, timecardDays);

                updated.totalAmount = totalAmount;
                updated.date = date;
                updateRow(updated);
              }}
            />
          </Box>
          {!isWeekly && (
            <Box>
              <Box sx={{ width: '150px' }}>Select Days</Box>
              <Box sx={{ display: 'flex', gap: 1 }}>
                {timecardDays.map((day) => {
                  let variant = 'secondary';
                  if (day.isActive && day.isRentalDay) {
                    variant = 'primary';
                  }
                  if (day.isActive && !day.isRentalDay) {
                    variant = 'primaryOutlined';
                  }
                  return (
                    <Button
                      variant={variant}
                      disabled={!day.isActive}
                      key={day.date.toISO()}
                      onClick={() => handleDayToggle(day)}
                    >
                      <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                        <Box data-testid={`rental-days-${day.id}`}>
                          {day.date.toFormat('ccc').slice(0, 2)}
                        </Box>
                        <Box>{day.date.toFormat('dd')}</Box>
                      </Box>
                    </Button>
                  );
                })}
              </Box>
            </Box>
          )}
        </Box>
      </Modal>
    </>
  );
};

RentalDaysCell.propTypes = {
  timecardDays: PropTypes.array.isRequired,
  reimbursement: PropTypes.object.isRequired,
  updateCell: PropTypes.func.isRequired,
  updateRow: PropTypes.func.isRequired,
  updateTimecard: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
};

export default RentalDaysCell;
