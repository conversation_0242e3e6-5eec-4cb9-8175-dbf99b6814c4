import { applyPureVueInReact } from 'veaury';
import ProjectOnboardingFormViewVue from '../../../views/projects/ProjectOnboardingFormView.vue';
import { useAuth, useReactRouter } from '../../AppHooks';
import { useOutletContext } from 'react-router';
const ReactProjectOnboardingFormView = applyPureVueInReact(
  ProjectOnboardingFormViewVue,
);

const ProjectOnboardingFormView = () => {
  useAuth();
  const { navigate, route } = useReactRouter();
  const handle = route?.match?.handle;
  const supervisorView = handle?.supervisorView;
  const context = useOutletContext();
  return (
    <ReactProjectOnboardingFormView
      route={route}
      navigate={navigate}
      project={context.project}
      refresh={context.refresh}
      supervisorView={supervisorView}
    />
  );
};

export default ProjectOnboardingFormView;
