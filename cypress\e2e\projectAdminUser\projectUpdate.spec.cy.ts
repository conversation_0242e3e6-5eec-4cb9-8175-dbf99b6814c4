/// <reference types="cypress" />

import Project from '../../pageObjects/project/projectForm';
import { faker } from '@faker-js/faker';
import { interceptEditProject } from '../../support/apiTimecardInterceptors';
import { createProjectFlow } from '../../support/apiFlows/createProjectFlow';

describe('User Project Admin - Project Update', () => {
  const projectForm = new Project();
  const description = faker.commerce.productAdjective();
  let projectName = '';

  beforeEach((): void => {
    cy.loginWithSession(
      Cypress.env('PHONE_NUMBER_PROJECT_ADMIN'),
      Cypress.env('OTC_CODE'),
      Cypress.env('BASE_URL'),
      'project-admin',
    );

    //Get Details by Api before test
    cy.then(() => {
      // Create a project and get the project name
      return createProjectFlow().then((project) => {
        projectName = project.body.name;
      });
    });
  });

  it('Verify Project Admin is able to Edit Project', () => {
    // Navigate to project and open timecard section
    cy.visit(
      `${Cypress.env('BASE_URL')}projects/${Cypress.env(
        'projectHashId',
      )}/admin/time`,
    );

    //Edit Project
    interceptEditProject();
    projectForm.goToEditProject();
    projectForm.fillOutProjectEdit(description, '100', 'Alabama');
    cy.wait('@editedProject').then((interception) => {
      const responseBody = interception.response?.body;
      // Asserts -
      expect(interception.response?.statusCode).to.equal(200);
      expect(responseBody).to.have.property('id', Cypress.env('projectId'));
      expect(responseBody).to.have.property('name', projectName);
      cy.log('Success: Verify Project Admin is able to Edit Project');
    });
  });
});
