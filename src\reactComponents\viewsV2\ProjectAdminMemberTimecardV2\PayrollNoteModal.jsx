import { useState } from 'react';
import {
  Modal,
  Typography,
  Box,
  Button,
  TextInput,
} from '@/reactComponents/library';
import { useTheme } from '@mui/material/styles';
import PropTypes from 'prop-types';
import NotesIcon from '../../assets/icons/NotesIcon';
import AddIcon from '../../assets/icons/AddIcon';
import { BatchStatusCapsPayEnum } from '@/types/Batch';

const styles = {
  button: {
    display: 'flex',
    alignItems: 'center',
    padding: '4px',
    color: 'pink.700',
  },
};

const PayrollNoteModal = (props) => {
  const { timecard, batchDetails, updateTimecard, disabled } = props;
  const [open, setOpen] = useState(false);
  const [payrollOpsNote, setPayrollOpsNote] = useState(timecard.payrollOpsNote);

  const { palette } = useTheme();

  const isBatchSubmitted =
    batchDetails?.batchStatus &&
    batchDetails?.batchStatus !== BatchStatusCapsPayEnum.Open;

  const hasNote = !!timecard.payrollOpsNote?.trim();
  const icon = hasNote ? <NotesIcon /> : <AddIcon />;
  const buttonLabel = hasNote ? 'View' : 'Add';
  const submitText = hasNote ? 'Update Note' : 'Add Note';

  const submitPayrollNoteChange = () => {
    if (isBatchSubmitted) {
      setOpen(false);
      return;
    }
    updateTimecard({ payrollOpsNote });
    setOpen(false);
  };

  const textDisabled = isBatchSubmitted || disabled;

  return (
    <Box>
      <Button
        variant="none"
        sx={styles.button}
        onClick={() => setOpen(true)}
        startIcon={icon}
      >
        {buttonLabel}
      </Button>
      {open && (
        <Modal
          title={'Payroll note'}
          open={open}
          setOpen={setOpen}
          onCancel={() => setOpen(false)}
          cancelText={disabled ? 'Close' : 'Cancel'}
          onSubmit={disabled ? null : submitPayrollNoteChange}
          submitText={submitText}
        >
          <Box sx={{ width: '350px' }}>
            <Typography>Details</Typography>
            <Box>
              <TextInput
                sx={{
                  width: '350px',
                  '& .MuiInputBase-input': {
                    '-webkit-text-fill-color': `${palette.text.primary} !important`,
                  },
                }}
                value={payrollOpsNote || ''}
                multiline
                minRows={5}
                disabled={textDisabled}
                onChange={(e) => setPayrollOpsNote(e.target.value)}
                placeholder={textDisabled ? '' : 'Comments here'}
              />
            </Box>
          </Box>
        </Modal>
      )}
    </Box>
  );
};

PayrollNoteModal.propTypes = {
  timecard: PropTypes.object.isRequired,
  batchDetails: PropTypes.object.isRequired,
  updateTimecard: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
};

export default PayrollNoteModal;
