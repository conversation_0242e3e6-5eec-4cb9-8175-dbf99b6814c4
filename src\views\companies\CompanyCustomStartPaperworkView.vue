<template>
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pt-40">
    <div
      class="bg-white rounded-md mb-10 dark:bg-gray-900 shadow sm:rounded-lg"
    >
      <div
        class="flex justify-between items-center border-b-2 p-4 border-gray-200"
      >
        <div class="flex justify-start items-center mx-2 space-x-2">
          <div class="text-lg font-semibold">Document Templates</div>
          <div class="rounded-md border-2 border-gray-400 px-1 text-sm">
            {{ numberOfDocuments }}
          </div>
        </div>
        <Observer>
          <Button
            v-if="
              PermissionStore.hasPermission(
                PermissionKeys.EDIT_DOCUMENT_TEMPLATE,
              )
            "
            class="max-w-xs"
            color="gray"
            size="sm"
            @click="createCustomPaperwork"
            data-testid="company-paperwork-add-btn"
          >
            <div class="flex justify-center items-center space-x-1">
              <Icon
                name="plus-circle"
                class="stroke-gray-500 dark:stroke-white"
              />
              <div>Add Paperwork</div>
            </div>
          </Button>
        </Observer>
      </div>
      <div class="mx-auto max-w-xl py-4">
        <section>
          <div class="flex justify-between items-center space-x-1 mt-4">
            <h3 class="text-lg font-bold leading-6">Custom documents</h3>
          </div>
          <p class="mb-10">
            Activate or inactivate custom documents, while also editing
            assignment rules.
          </p>
          <List v-model="customDocuments">
            <template #item="{ value: productionCompanyDocumentTemplate }">
              <div
                class="flex justify-between items-center my-4"
                :data-testid="`custom-doc-${productionCompanyDocumentTemplate.documentTemplate.name}`"
              >
                <div>
                  <div class="flex justify-start items-center space-x-1">
                    <div class="font-semibold">
                      <Toggle
                        v-model="productionCompanyDocumentTemplate.isEnabled"
                        @update:modelValue="
                          updatedocumentTemplate(
                            $event,
                            productionCompanyDocumentTemplate,
                          )
                        "
                        label="Default"
                      >
                        <span class="font-semibold">{{
                          productionCompanyDocumentTemplate.documentTemplate
                            .name
                        }}</span>
                        <div class="text-gray-600 text-sm font-normal">
                          {{
                            supportDocumentText(
                              productionCompanyDocumentTemplate.documentTemplate
                                .documentId,
                            )
                          }}
                        </div>
                      </Toggle>
                    </div>
                    <Icon
                      name="pencil"
                      color="gray"
                      @click="
                        gotoCustomPaperwork(
                          productionCompanyDocumentTemplate.id!,
                          0,
                        )
                      "
                    />
                  </div>
                </div>
                <div class="flex justify-end items-center space-x-2">
                  <Button
                    color="gray"
                    @click.stop="
                      gotoCustomPaperwork(
                        productionCompanyDocumentTemplate.id,
                        0,
                      )
                    "
                    :data-testid="`custom-doc-edit-${productionCompanyDocumentTemplate.documentTemplate.name}`"
                  >
                    Edit
                  </Button>
                  <Button
                    color="gray"
                    @click.stop="
                      deleteCustomStartPaperwork(
                        productionCompanyDocumentTemplate.id,
                      )
                    "
                    :data-testid="`custom-doc-delete-${productionCompanyDocumentTemplate.documentTemplate.name}`"
                  >
                    Delete
                  </Button>
                </div>
              </div>
            </template>
          </List>
        </section>
        <section>
          <div class="flex justify-between items-center space-x-1 mt-4">
            <h3 class="text-lg font-bold leading-6">Default documents</h3>
          </div>
          <p class="mb-10">
            Activate or inactivate default company documents, while also editing
            assignment rules. Note: I-9 and Federal W4 remain active,
            non-editable.
          </p>
          <List v-model="defaultDocuments">
            <template #item="{ value: productionCompanyDocumentTemplate }">
              <div class="flex justify-between items-center my-4">
                <div>
                  <div class="flex justify-start items-center space-x-1">
                    <div
                      v-if="
                        productionCompanyDocumentTemplate.documentTemplate
                          .documentId !==
                          ProductionCompanyDocumentTemplateKeys.KEY_I9 &&
                        productionCompanyDocumentTemplate.documentTemplate
                          .documentId !==
                          ProductionCompanyDocumentTemplateKeys.KEY_W4
                      "
                    >
                      <Toggle
                        :modelValue="
                          productionCompanyDocumentTemplate.isEnabled
                        "
                        @update:modelValue="
                          updatedocumentTemplate(
                            $event,
                            productionCompanyDocumentTemplate,
                          )
                        "
                        label="Default"
                      >
                        <span class="font-semibold">{{
                          productionCompanyDocumentTemplate.documentTemplate
                            .name
                        }}</span>
                        <div class="text-gray-600 text-sm font-normal">
                          {{
                            supportDocumentText(
                              productionCompanyDocumentTemplate.documentTemplate
                                .documentId,
                            )
                          }}
                        </div>
                      </Toggle>
                    </div>
                    <div v-else class="font-semibold">
                      {{
                        productionCompanyDocumentTemplate.documentTemplate.name
                      }}
                    </div>
                    <!-- <Icon name="pencil" color="gray" @click="gotoCustomPaperwork(productionCompanyDocumentTemplate.documentTemplate.id!)"/> -->
                  </div>
                </div>
                <div
                  class="flex justify-end items-center space-x-2"
                  v-if="
                    productionCompanyDocumentTemplate.documentTemplate
                      .documentId !==
                      ProductionCompanyDocumentTemplateKeys.KEY_I9 &&
                    productionCompanyDocumentTemplate.documentTemplate
                      .documentId !==
                      ProductionCompanyDocumentTemplateKeys.KEY_W4
                  "
                >
                  <!-- <Button
                    color="gray"
                    @click.stop="
                      gotoCustomPaperwork(
                        productionCompanyDocumentTemplate.id,
                        1,
                      )
                    "
                  >
                    Edit
                  </Button> -->
                </div>
              </div>
            </template>
          </List>
        </section>
        <Modal v-model="isDocumentPreviewOpen">
          <div class="flex w-full justify-end mb-2">
            <XMarkIcon
              class="h-6 w-6 cursor-pointer"
              @click="isDocumentPreviewOpen = false"
            />
          </div>
          <PDFViewer
            :url="pdfPreviewUrl"
            class="h-3/4 max-h-[500px] overflow-y-scroll"
          />
        </Modal>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Icon from '@/components/Icon.vue';
import Button from '@/components/library/Button.vue';
import List from '@/components/library/List.vue';
import Modal from '@/components/library/Modal.vue';
import PDFViewer from '@/components/library/PDFViewer.vue';
import Toggle from '@/components/library/Toggle.vue';
import PermissionStore from '@/reactComponents/stores/permission';
import SnackbarStore from '@/reactComponents/stores/snackbar';
import { getPdfUrl } from '@/services/pdf';
import {
  deleteProductionCompanyDocumentTemplate,
  listProductionCompanyDocumentTemplates,
} from '@/services/production-company';
import {
  disableDocumentTemplate,
  enableDocumentTemplate,
} from '@/services/production-company-document-templates';
import { PermissionKeys } from '@/types/Permission';
import { ProductionCompanyDocumentTemplateKeys } from '@/types/ProductionCompanyDocumentTemplates';
import type { SnackType } from '@/types/Snackbar';
import { CalendarIcon, XMarkIcon } from '@heroicons/vue/24/outline';
import axios from 'axios';
import { Observer } from 'mobx-vue-lite';
import { defineComponent } from 'vue';

export default defineComponent({
  props: ['company', 'navigate'],
  components: {
    Button,
    CalendarIcon,
    Modal,
    PDFViewer,
    XMarkIcon,
    List,
    Icon,
    Toggle,
    Observer,
  },
  data() {
    return {
      PermissionKeys,
      documentTemplates: [] as any[],
      isDocumentPreviewOpen: false,
      pdfPreviewUrl: '',
      ProductionCompanyDocumentTemplateKeys,
    };
  },
  setup() {
    return {
      PermissionStore,
    };
  },
  computed: {
    canDeleteDocuments(): boolean {
      return PermissionStore.hasPermission(
        PermissionKeys.EDIT_DOCUMENT_TEMPLATE,
      );
    },
    customDocuments(): any[] {
      return this.documentTemplates.filter(
        (doc: any) => doc.documentTemplate.isCustom,
      );
    },
    defaultDocuments(): any[] {
      return this.documentTemplates.filter(
        (doc: any) => !doc.documentTemplate.isCustom,
      );
    },
    numberOfDocuments() {
      return this.documentTemplates?.length;
    },
  },
  async mounted() {
    await this.load();
  },
  methods: {
    triggerSnackbar: (msg: string, timeout: number, type: SnackType) => {
      SnackbarStore.triggerSnackbar(msg, timeout, type);
    },
    gotoCustomPaperwork(id: number, isDefault: any) {
      if (
        PermissionStore.hasPermission(PermissionKeys.EDIT_DOCUMENT_TEMPLATE)
      ) {
        const productionCompanyDocumentTemplateId = id;
        this.navigate({
          pathname: `/companies/${this.company.id}/document-templates/${productionCompanyDocumentTemplateId}/is-default/${isDefault}`,
        });
      }
    },
    async deleteCustomStartPaperwork(id: number) {
      if (
        PermissionStore.hasPermission(PermissionKeys.EDIT_DOCUMENT_TEMPLATE)
      ) {
        try {
          await deleteProductionCompanyDocumentTemplate(this.company.id, id);
          this.triggerSnackbar('Document template deleted', 1000, 'success');
          await this.load();
        } catch (err) {
          console.warn('Error deleting document template', err);
        }
      } else {
        this.triggerSnackbar(
          'You do not have permission to delete this document template',
          1000,
          'error',
        );
      }
    },
    createCustomPaperwork() {
      this.navigate({
        pathname: `/companies/${this.company.id}/document-templates/create`,
      });
    },
    previewDocument(documentTemplate: any) {
      const { documentId } = documentTemplate;
      if (!documentId) {
        this.triggerSnackbar('No document found', 2500, 'error');
        return;
      }
      if (documentId) {
        this.pdfPreviewUrl = getPdfUrl(documentId);
        this.isDocumentPreviewOpen = true;
        return;
      }
    },
    supportDocumentText(documentTemplateId: string) {
      if (
        documentTemplateId ===
        ProductionCompanyDocumentTemplateKeys.KEY_NY_WAGE_THEFT_PROTECTION
      ) {
        return 'Assignment rules here, separated by commas';
      }
      return 'Specific Union IATSE, Loan out setting';
    },
    async updatedocumentTemplate(
      checked: boolean,
      companyDocumentTemplate: any,
    ) {
      try {
        const { data } = checked
          ? await enableDocumentTemplate(
              companyDocumentTemplate.documentTemplateId,
              companyDocumentTemplate.productionCompanyId,
            )
          : await disableDocumentTemplate(
              companyDocumentTemplate.documentTemplateId,
              companyDocumentTemplate.productionCompanyId,
            );
        companyDocumentTemplate.isEnabled = data.isEnabled;
        this.triggerSnackbar('Document template updated', 1000, 'success');
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const msg = err.response?.data?.['errors']?.[0]?.message;
          this.triggerSnackbar(msg, 2500, 'error');
        } else {
          this.triggerSnackbar(err as string, 2500, 'error');
        }
      }
    },

    async load() {
      const {
        data: { data },
      } = await listProductionCompanyDocumentTemplates(this.company.id!);
      this.documentTemplates = data;
    },
  },
});
</script>
