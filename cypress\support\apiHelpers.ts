/// <reference types="cypress" />

export function fetchProjectIdentifiersByName(
  projectName: string,
): Cypress.Chainable<any> {
  const filter = `name=${projectName}:op:ilike`;
  const encoded = encodeURIComponent(filter);
  const url = `${Cypress.env(
    'BASE_URL',
  )}api/core/projects/?filters=${encoded}&page=1&limit=10`;

  return cy
    .request({
      method: 'GET',
      url,
    })
    .then((response) => {
      const project = response.body.data.find((p) => p.name === projectName);
      return project;
    });
}

export function fetchProjectMemberByName(
  firstName: string,
  lastName: string,
): Cypress.Chainable<any> {
  const filters = `user.firstName=${firstName}:op:ilike;user.lastName=${lastName}:op:ilike`;
  const encodedFilters = encodeURIComponent(filters);

  const url = `${Cypress.env('BASE_URL')}api/core/projects/${Cypress.env(
    'projectId',
  )}/members?page=1&limit=25&filters=${encodedFilters}&search=`;

  return cy.request('GET', url).then((response) => {
    return response.body.data[0];
  });
}

export function fetchPayPeriodsByProject(): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/projects/${Cypress.env(
    'projectId',
  )}/pay-periods`;
  return cy.request('GET', url).then((response) => {
    return response;
  });
}

export function fetchMembersByProject(): Cypress.Chainable<any> {
  const filters = 'crewSignedStartPaperwork';
  const encodedFilters = encodeURIComponent(filters);
  const url = `${Cypress.env('BASE_URL')}api/core/projects/${Cypress.env(
    'projectId',
  )}/members?page=1&limit=25&filters=${encodedFilters}&search=`;
  return cy.request('GET', url).then((response) => {
    return response;
  });
}

export function fetchPayPeriodsRemainingByProject(): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/projects/${Cypress.env(
    'projectId',
  )}/pay-periods/remaining`;
  return cy.request('GET', url).then((response) => {
    if (response.status !== 200) {
      throw new Error('Failed to fetch pay periods');
    }
    return response.body;
  });
}

export function fetchProjectInformation(): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/projects/${Cypress.env(
    'projectHashId',
  )}`;
  return cy.request('GET', url).then((response) => {
    if (response.status !== 200) {
      throw new Error('Failed to fetch project information');
    }
    return response.body;
  });
}

export function fetchMemberCurrentByProject(): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/projects/${Cypress.env(
    'projectId',
  )}/members/current`;
  return cy.request('GET', url).then((response) => {
    if (response.status !== 200) {
      throw new Error('Failed to fetch member current');
    }
    return response.body;
  });
}

export function fetchRateTypes(): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/rate-types`;
  return cy.request('GET', url).then((response) => {
    if (response.status !== 200) {
      throw new Error('Failed to fetch rate types');
    }
    return response.body;
  });
}

export function fetchStates(): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/addresses/states`;
  return cy.request('GET', url).then((response) => {
    if (response.status !== 200) {
      throw new Error('Failed to fetch states');
    }
    return response.body;
  });
}

export function fetchShootLocationsByProject(): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/projects/${Cypress.env(
    'projectId',
  )}/shoot-locations`;
  return cy.request('GET', url).then((response) => {
    if (response.status !== 200) {
      throw new Error('Failed to fetch shoot locations');
    }
    return response.body;
  });
}

export function fetchUnionsByProject(
  payrollProjectLocationId: string,
): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/projects/${Cypress.env(
    'projectId',
  )}/unions?payrollProjectLocationId=${payrollProjectLocationId}`;
  return cy.request('GET', url).then((response) => {
    if (response.status !== 200) {
      throw new Error('Failed to fetch unions');
    }
    return response.body;
  });
}

export function fetchOccupationsByProject(): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/projects/${Cypress.env(
    'projectId',
  )}/occupations?unions=&search=&page=1&limit=50&total=0`;
  return cy.request('GET', url).then((response) => {
    if (response.status !== 200) {
      throw new Error('Failed to fetch occupations');
    }
    return response.body;
  });
}

export function fetchOccupationsByProjectAndUnion(
  payrollProjectLocationId: string,
  unionId: string,
): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/projects/${Cypress.env(
    'projectId',
  )}/occupations?payrollProjectLocationId=${payrollProjectLocationId}&unions=${unionId}&search=&page=1&limit=50&total=0`;
  return cy.request('GET', url).then((response) => {
    if (response.status !== 200) {
      throw new Error('Failed to fetch occupations by project and union');
    }
    const occupationList = response.body.jobTitleList;
    return occupationList;
  });
}

export function fetchMinuteIncrements(): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/minute-increments`;
  return cy.request('GET', url).then((response) => {
    if (response.status !== 200) {
      throw new Error('Failed to fetch minute increments');
    }
    return response.body;
  });
}

export function fetchCompanies(): Cypress.Chainable<any> {
  const url = `${Cypress.env(
    'BASE_URL',
  )}api/core/production-companies?page=1&limit=100&total=0&filters=name%3D:op:ilike`;
  return cy.request('GET', url).then((response) => {
    if (response.status !== 200) {
      throw new Error('Failed to fetch companies');
    }
    return response.body;
  });
}

export function fetchCompanyByName(name: string): Cypress.Chainable<any> {
  const encodedName = encodeURIComponent(`${name}:op:ilike`);
  const url = `${Cypress.env(
    'BASE_URL',
  )}api/core/production-companies?page=1&limit=100&total=0&filters=name%3D${encodedName}`;

  return cy.request('GET', url).then((response) => {
    if (response.status !== 200) {
      throw new Error(`Failed to fetch company with name: ${name}`);
    }
    return response.body;
  });
}

export function fetchCompanyAddress(companyId: string): Cypress.Chainable<any> {
  const url = `${Cypress.env(
    'BASE_URL',
  )}api/core/addresses/${companyId}/company`;
  return cy.request('GET', url).then((response) => {
    if (response.status !== 200) {
      throw new Error('Failed to fetch company address');
    }
    return response.body;
  });
}

export function fetchProjectTypesByCompany(
  companyId: string,
): Cypress.Chainable<any> {
  const url = `${Cypress.env(
    'BASE_URL',
  )}api/core/production-companies/${companyId}/project-types`;
  return cy.request('GET', url).then((response) => {
    if (response.status !== 200) {
      throw new Error('Failed to fetch project types by company');
    }
    return response.body;
  });
}

export function fetchWorkLocationByProjectType(
  companyId: string,
  projectTypeId: string,
): Cypress.Chainable<any> {
  const url = `${Cypress.env(
    'BASE_URL',
  )}api/core/production-companies/${companyId}/shoot-locations/${projectTypeId}`;
  return cy.request('GET', url).then((response) => {
    if (response.status !== 200) {
      throw new Error('Failed to fetch work location by project type');
    }
    return response.body;
  });
}

export function fetchDepartments(): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/project-departments/types`;
  return cy.request('GET', url).then((response) => {
    if (response.status !== 200) {
      throw new Error('Failed to fetch work location by project type');
    }
    return response.body;
  });
}

export function logout(): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/auth/logout`;
  return cy
    .request({
      method: 'POST',
      url,
      body: {},
      headers: {
        'Content-Type': 'application/json',
      },
    })
    .then((response) => {
      if (response.status !== 200) {
        throw new Error('Failed to logout');
      }
    });
}

export function triggerPaperWork(id: string): Cypress.Chainable<any> {
  const url = `${Cypress.env(
    'BASE_URL',
  )}api/core/project-members/${id}/start-paperwork-draft?useLoanOut=false`;
  return cy.request('GET', url).then((response) => {
    if (response.status !== 200) {
      throw new Error('Failed to trigger paperwork');
    }
    return response.body;
  });
}

export function addPersonalInfo(): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/user-crews/`;
  return cy.fixture('e2ePayloads/project/personalInfo').then((data) => {
    cy.request({
      method: 'POST',
      url,
      body: data,
      headers: {
        'Content-Type': 'application/json',
      },
    }).then((response) => {
      if (response.status !== 200) {
        throw new Error('Failed to personal info');
      }
    });
  });
}

export function fetchTimecardById(id: string): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/timecards/${id}/`;
  return cy.request('GET', url).then((response) => {
    if (response.status !== 200) {
      throw new Error('Failed to get timecard');
    }
    return response.body;
  });
}

export function deleteBatch(id: string): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/batches/${id}`;
  return cy.request('DELETE', url).then((response) => {
    if (response.status !== 200) {
      throw new Error('Failed to delete batch');
    }
    return response.body;
  });
}

export function deleteTimecard(id: string): Cypress.Chainable<any> {
  const url = `${Cypress.env('BASE_URL')}api/core/timecards/${id}`;
  return cy.request('DELETE', url).then((response) => {
    if (response.status !== 200) {
      throw new Error('Failed to delete timecard');
    }
    return response.body;
  });
}

export function deleteDocument(id: string): Cypress.Chainable<any> {
  const url = `${Cypress.env(
    'BASE_URL',
  )}api/core/project-document-templates/${id}`;
  return cy.request('DELETE', url).then((response) => {
    if (response.status !== 200) {
      throw new Error('Failed to delete Document');
    }
    return response.body;
  });
}

export function deleteTemplate(
  companyid: string,
  id: string,
): Cypress.Chainable<any> {
  const url = `${Cypress.env(
    'BASE_URL',
  )}api/core/production-companies/${companyid}/document-templates/${id}`;
  return cy.request('DELETE', url).then((response) => {
    if (response.status !== 200) {
      throw new Error('Failed to delete Document');
    }
    return response.body;
  });
}
