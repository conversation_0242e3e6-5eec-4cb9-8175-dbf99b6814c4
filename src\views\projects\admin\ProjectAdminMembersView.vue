<template>
  <div class="min-h-full">
    <main class="pt-6 pb-16">
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div class="flex justify-between items-start space-x-2 mb-3">
          <div class="flex items-start space-x-2">
            <TextInput
              class="w-96"
              v-model="search"
              placeholder="Search"
              @keyup.enter="getProjectMembers"
            />
            <Button size="sm" color="primary" @click="getProjectMembers">
              <ArrowPathIcon class="h-5 w-5" />
            </Button>
          </div>
          <div class="flex justify-end space-x-2">
            <!-- <InviteProjectMemberModal :project="project" /> TODO: get this flow working better -->
            <Button
              :loading="isExporting"
              size="sm"
              color="gray"
              @click="exportStartPaperwork"
            >
              <div class="flex items-center space-x-2">
                <ArrowUpTrayIcon class="w-4" />
                <div>Export Start Paperwork</div>
              </div>
            </Button>
          </div>
        </div>
        <div class="flex min-w-full grow">
          <TableFilters :filters="filters" @update:filters="filters = $event" />
        </div>

        <!-- Stacked list -->
        <ul
          role="list"
          class="mt-5 divide-y border-t border-gray-200 dark:border-gray-800 sm:mt-0 sm:border-t-0"
        >
          <li
            v-for="projectMember in projectMembers"
            :key="projectMember.user.email"
          >
            <div
              class="group block cursor-pointer"
              @click="viewMemberDetails(projectMember.id!)"
            >
              <div class="flex items-center py-5 px-4 sm:py-6 sm:px-0">
                <div class="flex min-w-0 flex-1 items-center">
                  <div class="flex-shrink-0">
                    <Avatar
                      :first-name="projectMember.user.firstName"
                      :last-name="projectMember.user.lastName"
                      bgColor="white"
                      size="md"
                    />
                  </div>
                  <div
                    class="min-w-0 flex-1 px-4 md:grid md:grid-cols-5 md:gap-4"
                  >
                    <div>
                      <p
                        class="truncate text-sm font-medium text-indigo-600 dark:text-indigo-400"
                      >
                        {{
                          `${projectMember.user.firstName} ${projectMember.user.lastName}`
                        }}
                      </p>
                      <p
                        class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400"
                      >
                        <EnvelopeIcon
                          class="mr-1.5 h-5 w-5 flex-shrink-0 text-gray-400 dark:text-gray-500"
                          aria-hidden="true"
                        />
                        <span class="truncate">{{
                          projectMember.user.email
                        }}</span>
                      </p>
                      <p
                        class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400"
                      >
                        <span class="truncate"
                          >Department:
                          {{
                            projectMember?.department?.type?.name || 'None'
                          }}</span
                        >
                      </p>
                    </div>
                    <div class="flex md:block">
                      <div class="text-sm">Loan Out</div>
                      <div
                        v-if="
                          projectMember.loanOutStatusId ===
                            ProjectMemberLoanOutStatusId.Approved ||
                          projectMember.loanOutStatusId ===
                            ProjectMemberLoanOutStatusId.Used
                        "
                      >
                        <div
                          class="mt-2 flex justify-start items-center space-x-1"
                        >
                          <Badge
                            type="success"
                            text=""
                            size="icon"
                            class="w-4 h-4"
                          >
                            <template #icon>
                              <Icon name="checked" class="w-3 h-3" />
                            </template>
                          </Badge>
                          <div class="text-sm text-gray-500 dark:text-gray-400">
                            Approved
                          </div>
                        </div>
                      </div>
                      <div
                        v-else-if="
                          projectMember.loanOutStatusId ===
                          ProjectMemberLoanOutStatusId.Requested
                        "
                      >
                        <div
                          class="mt-2 flex justify-start items-center space-x-1"
                        >
                          <Icon name="bell-alert" class="w-5 h-6" />
                          <div class="text-sm text-gray-500 dark:text-gray-400">
                            Requested
                          </div>
                        </div>
                      </div>
                      <div v-else>
                        <div
                          class="mt-2 flex justify-start items-center space-x-2"
                        >
                          <div
                            v-if="projectMember?.user?.userCrew?.loanOut"
                            class="text-sm text-gray-500 dark:text-gray-400"
                          >
                            Uploaded - Unapproved
                          </div>
                          <div v-else>N/A</div>
                        </div>
                      </div>
                    </div>
                    <div class="flex md:block">
                      <div>
                        <p class="text-sm">Start Paperwork</p>
                        <p
                          class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400"
                        ></p>
                        <Badge
                          class="mb-0.5"
                          text="Crew Signed"
                          :type="
                            projectMember.startPaperwork?.[0]?.crewSignedAt
                              ? 'success'
                              : 'error'
                          "
                          size="xs"
                        >
                          <template #icon>
                            <Icon
                              :name="
                                projectMember.startPaperwork?.[0]?.crewSignedAt
                                  ? 'checked'
                                  : 'error-circle'
                              "
                              class="w-3.5 h-3.5 mr-1"
                            />
                          </template>
                        </Badge>
                        <Badge
                          text="Supervisor Signed"
                          :type="
                            projectMember.startPaperwork?.[0]
                              ?.supervisorSignedAt
                              ? 'success'
                              : 'error'
                          "
                          size="xs"
                        >
                          <template #icon>
                            <Icon
                              :name="
                                projectMember.startPaperwork?.[0]
                                  ?.supervisorSignedAt
                                  ? 'checked'
                                  : 'error-circle'
                              "
                              class="w-3.5 h-3.5 mr-1"
                            />
                          </template>
                        </Badge>
                      </div>
                    </div>
                    <div>
                      <p>Occupation</p>
                      <p
                        class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400"
                      >
                        {{ projectMember?.occupation?.name || 'None' }}
                      </p>
                    </div>
                    <div>
                      <p>Active/Deleted</p>
                      <div class="flex justify-start pt-1">
                        <Badge
                          :type="projectMember.isActive ? 'success' : 'error'"
                          :text="projectMember.isActive ? 'Active' : 'Deleted'"
                        >
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <ChevronRightIcon
                    class="h-5 w-5 text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    aria-hidden="true"
                  />
                </div>
              </div>
            </div>
          </li>
        </ul>

        <Pagination v-model="pagination" />
      </div>
    </main>
  </div>
</template>

<script lang="ts" setup>
import Icon from '@/components/Icon.vue';
import Avatar from '@/components/library/Avatar.vue';
import Badge from '@/components/library/Badge.vue';
import Button from '@/components/library/Button.vue';
import Pagination from '@/components/library/Pagination.vue';
import TextInput from '@/components/library/TextInput.vue';
import TableFilters from '@/components/TableFilters.vue';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';
import {
  exportProjectStartingPaperwork,
  getCurrentMember,
  listProjectMembers,
} from '@/services/project';
import { FilterOperator, FilterUIType, type Filter } from '@/types/Filter';
import type { Pagination as PaginationType } from '@/types/Pagination';
import type Project from '@/types/Project';
import type ProjectMember from '@/types/ProjectMember';
import {
  ProjectMemberLoanOutStatusId,
  ProjectMemberTypeId,
} from '@/utils/enum';
import { convertFiltersToQuery } from '@/utils/filter';
import {
  ArrowPathIcon,
  ArrowUpTrayIcon,
  ChevronRightIcon,
  EnvelopeIcon,
} from '@heroicons/vue/20/solid';
import {
  computed,
  onMounted,
  provide,
  ref,
  watch,
  watchEffect,
  type Ref,
} from 'vue';

const props = defineProps<{
  project: Project;
  isAdmin: boolean;
  navigate: Function;
  route: ParsedRoute;
}>();

const route = ref(props.route);
watchEffect(() => {
  route.value = props.route;
});

provide('route', route);
provide('navigate', props.navigate);

const projectMembers: Ref<ProjectMember[]> = ref([] as ProjectMember[]);

const pagination: Ref<PaginationType> = ref({
  page: 1,
  limit: 25,
  total: 0,
});
watch(pagination, async () => {
  getProjectMembers();
});

const filters: Ref<Filter[]> = ref([
  {
    id: 'project_member_type',
    field: 'projectMemberTypeId',
    label: 'User Type',
    value: '',
    type: FilterUIType.MultiSelect,
    operator: FilterOperator.In,
    active: false,
    options: [
      {
        id: 'project_member_user_type_admin',
        label: 'Admin',
        value: parseInt(ProjectMemberTypeId.Admin.toString()),
        active: false,
      },
    ],
  },
  {
    id: 'user_first_name',
    field: 'user.firstName',
    label: 'First Name',
    value: '',
    type: FilterUIType.Text,
    operator: FilterOperator.ILike,
    active: false,
  },
  {
    id: 'user_last_name',
    field: 'user.lastName',
    value: '',
    label: 'Last Name',
    active: false,
    operator: FilterOperator.ILike,
    type: FilterUIType.Text,
  },
  {
    id: 'user_email',
    name: 'Email',
    field: 'user.email',
    value: '',
    label: 'Email',
    active: false,
    operator: FilterOperator.ILike,
    type: FilterUIType.Text,
  },
  {
    id: 'department_type_name',
    name: 'Department',
    field: 'departmentId',
    label: 'Department',
    value: '',
    type: FilterUIType.MultiSelect,
    operator: FilterOperator.In,
    active: false,
    options: props?.project?.departments?.map((department) => ({
      id: `${department.id}_${department.type.key}`,
      label: department.type.name,
      value: department.id!,
      active: false,
    })),
  },
]);

const search = ref('');

const activeFilters = computed(() => {
  return filters.value.filter(({ active }) => active);
});

watch(activeFilters, async () => {
  getProjectMembers();
});

const viewMemberDetails = (id: number): void => {
  props.navigate({
    pathname: `/projects/${props.project.hashId}/admin/members/${id}/member`,
  });
};

const isExporting = ref(false);

const exportStartPaperwork = async () => {
  isExporting.value = true;

  try {
    // TODO zip funcitonality, maybe
    // const href = exportProjectStartingPaperwork(props.project.id!);

    // const link = document.createElement('a');
    // link.href = href;
    // link.setAttribute('download', 'start_paperwork.pdf');

    // document.body.appendChild(link);

    // link.click();
    // link.remove();

    const { data } = await exportProjectStartingPaperwork(props.project.id!);
    const href = data.filePath;
    const link = document.createElement('a');

    link.href = href;
    link.setAttribute('download', data.fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(href);
  } catch (err) {
    console.warn('Error exporting start paperwork', err);
  }

  isExporting.value = false;
};

const getProjectMembers = async (): Promise<void> => {
  const filtersString = convertFiltersToQuery(activeFilters.value);
  const {
    data: { data: members, meta },
  } = await listProjectMembers(
    props.project.id!,
    undefined,
    { page: pagination.value.page, limit: pagination.value.limit },
    filtersString,
    undefined,
    search.value,
  );
  projectMembers.value = members;
  pagination.value.limit = meta.per_page;
  pagination.value.page = meta.current_page;
  pagination.value.total = meta.total;
};

onMounted(async () => {
  getProjectMembers();
  try {
    if (props.project) {
      const { data: authProjectMember } = await getCurrentMember(
        props.project.id,
      );
      const authProjectMemberDepartment = filters.value.find(
        (filter) => filter.field === 'departmentId',
      );
      const authProjectMemberDepartmentOption =
        authProjectMemberDepartment?.options?.find(
          (option) => option.value === authProjectMember?.departmentId,
        );
      if (authProjectMemberDepartment && authProjectMemberDepartmentOption) {
        authProjectMemberDepartment.active = true;
        authProjectMemberDepartmentOption.active = true;
      }
    }
  } catch (err) {
    console.warn('Error getting current member', err);
  }
});
</script>
