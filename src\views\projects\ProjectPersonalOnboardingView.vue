<template>
  <div class="flex justify-center mx-auto w-full py-2 px-5">
    <div class="max-w-7xl">
      <h2 class="text-center text-3xl font-light leading-9">
        Personal Onboarding
      </h2>
      <ul role="list" class="space-y-3 pt-3 mb-12">
        <li
          v-for="(page, pageIndex) in pages"
          :key="`page-${pageIndex}`"
          class="overflow-hidden px-4 sm:rounded-md sm:px-6"
        >
          <div class="bg-white dark:bg-gray-900 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex">
                <template v-if="!page.ignoreStatus">
                  <Badge
                    v-if="page.completed"
                    type="success"
                    text=""
                    size="icon"
                    class="w-6 h-6 mr-2"
                  >
                    <template #icon>
                      <Icon name="checked" class="w-4 h-4" />
                    </template>
                  </Badge>
                  <ExclamationCircleIcon
                    v-else
                    class="text-yellow-500 dark:text-yellow-400 w-6 h-6 mr-2"
                  />
                </template>
                <h3 class="text-base font-semibold leading-6">
                  {{ page.label }}
                </h3>
              </div>
              <div
                class="mt-2 max-w-xl text-sm text-gray-500 dark:text-gray-400"
              >
                <p>{{ page.description }}</p>
              </div>
              <div class="mt-5">
                <Button
                  v-if="page.cta"
                  @click="handleNavigate(page.pathname)"
                  :color="
                    !page.completed && page.required ? 'main' : 'secondary'
                  "
                  size="sm"
                >
                  {{ page.cta }}
                </Button>
              </div>
            </div>
          </div>
        </li>
      </ul>
      <div class="flex justify-center space-x-2">
        <Button color="gray" size="sm" @click="goHome()"> Project Home </Button>
        <Button color="primary" size="sm" @click="projectOnboard()">
          Project Onboarding
        </Button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
//TODO - UNUSED FPS-1348

import Icon from '@/components/Icon.vue';
import Badge from '@/components/library/Badge.vue';
import Button from '@/components/library/Button.vue';
import PermissionStoreInstance from '@/reactComponents/stores/permission';
import type { ParsedRoute } from '@/reactComponents/views/types/reactTypes';
import { getLoanOut } from '@/services/loan-out';
import { getStartingPaperwork, isProjectOnboarded } from '@/services/project';
import { needsCrewOnboarding } from '@/services/user-crew';
import { PermissionKeys } from '@/types/Permission';
import type Project from '@/types/Project';
import {
  CheckCircleIcon,
  ExclamationCircleIcon,
} from '@heroicons/vue/24/solid';
import { defineComponent, type PropType } from 'vue';

interface Page {
  label: string;
  name?: string;
  pathname: () => string;
  description: string;
  completed?: boolean;
  required: boolean;
  cta?: string;
  ignoreStatus?: boolean;
}

type Pages = Record<string, Page>;

export default defineComponent({
  components: {
    Button,
    CheckCircleIcon,
    ExclamationCircleIcon,
    Badge,
    Icon,
  },
  props: {
    project: {
      type: Object as PropType<Project>,
      required: true,
    },
    isAdmin: {
      type: Boolean as PropType<boolean>,
      required: true,
    },
    navigate: {
      type: Function,
      required: false,
    },
    route: {
      type: Object as PropType<ParsedRoute>,
      required: false,
    },
  },
  data() {
    return {
      pages: [] as Page[],
      startingPaperwork: null as any,
      needsPersonalOnboarding: false as boolean,
      wasProjectOnboarded: false as boolean,
      loanOut: null as any,
    };
  },
  async mounted() {
    await this.fetchData();
    this.pages = this.getFilteredPages();
  },
  methods: {
    async fetchData() {
      const [needsOnboardingData, wasProjectOnboardedData] = await Promise.all([
        needsCrewOnboarding(),
        isProjectOnboarded(this.project.id!),
      ]);

      this.needsPersonalOnboarding = needsOnboardingData.data;
      this.wasProjectOnboarded = wasProjectOnboardedData.data;

      if (this.needsPersonalOnboarding) {
        return;
      }
      try {
        const { data } = await getStartingPaperwork(this.project.id!);
        this.startingPaperwork = data;
        const loanOut = await getLoanOut();
        this.loanOut = loanOut.data;
      } catch (err) {
        console.warn('Error fetching starting paperwork', err);
      }
    },
    handleNavigate(path: () => string) {
      if (this.navigate) {
        const pathname = path();
        this.navigate({ pathname });
      }
    },
    getPages(): Pages {
      return {
        personalInfoOnboarding: {
          label: 'Personal Info (Required)',
          pathname: () => {
            return this.needsPersonalOnboarding
              ? `/profile/create`
              : `/profile/edit`;
          },
          required: true,
          completed: !this.needsPersonalOnboarding,
          description: 'Required personal information for your account.',
          cta: this.needsPersonalOnboarding ? 'Start' : 'Edit',
        },
        projectOnboarding: {
          label: 'Loan Out (Optional)',
          pathname: () => `/loan-outs`,
          required: false,
          completed: this.loanOut?.id,
          description: 'Only create a loan out if you need one.',
          cta: !this.wasProjectOnboarded ? 'Start' : 'View',
        },
      };
    },
    isAdminOrHasPermission() {
      return (
        this.isAdmin ||
        PermissionStoreInstance.hasPermission(
          PermissionKeys.ADMIN_VIEW_ON_ALL_PROJECTS,
        )
      );
    },
    goToAdminView() {
      if (this.$props.navigate) {
        this.$props.navigate({
          pathname: `/projects/${this.project.hashId!.toString()}/admin/members`,
        });
      }
    },
    goHome() {
      const departmentId: string | undefined =
        this.$props.route?.query.departmentId?.toString();
      const searchParams = new URLSearchParams();
      searchParams.set('departmentId', departmentId || '');

      if (this?.$props?.navigate) {
        this?.$props?.navigate({
          pathname: `/projects/${this.project.hashId!.toString()}`,
          search: searchParams.toString(),
        });
      }
    },
    projectOnboard() {
      if (this.$props.navigate) {
        this.$props.navigate({
          pathname: `/projects/${this.project.hashId!.toString()}/onboarding`,
        });
      }
    },
    getFilteredPages(): Page[] {
      const pages = this.getPages();
      const filteredPages = [];

      filteredPages.push(pages.personalInfoOnboarding);
      if (!this.needsPersonalOnboarding) {
        filteredPages.push(pages.projectOnboarding);
      }
      return filteredPages;
    },
  },
});
</script>
