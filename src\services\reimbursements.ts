import axios, { type AxiosResponse } from 'axios';
import { coreBaseUrl } from '../utils/constants';

export const getByCode = async (code: string): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/projects/code/${code}`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const listTimecardReimbursements = async (
  timecardId: string | number,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/reimbursements/${timecardId}/timecard/`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const updateTimecardReimbursements = async (
  timecardId: string | number,
  reimbursements: any,
): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/reimbursements/${timecardId}/timecard/`;
  const response = await axios.patch(
    url,
    { reimbursements },
    {
      withCredentials: true,
    },
  );
  return response;
};

export const getReimbursementReceipt = (
  reimbursementId: string | number,
): string => {
  const url = `${coreBaseUrl()}/reimbursements/${reimbursementId}/receipt`;
  return url;
};
