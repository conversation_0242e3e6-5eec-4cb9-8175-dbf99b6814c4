import { defineStore } from 'pinia';

export interface Notification {
  msg: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration: number;
}

export const useSnackbarStore = defineStore({
  id: 'snackbar',
  state: () => ({
    notification: null as Notification | null,
  }),
  getters: {
    getNotification: (state) => state.notification as Notification,
  },
  actions: {
    triggerSnackbar(
      msg: string,
      duration: number = 2500,
      type: 'success' | 'error' | 'warning' | 'info' = 'success',
    ) {
      this.notification = { msg, duration, type };
    },
  },
});
