import { makeAutoObservable } from 'mobx';
import type Timecard from '@/types/Timecard';
class TimeStoreClass {
  private _draggedTimecard: Timecard | null = null;
  private _moving: Record<number, boolean> = {};

  constructor() {
    makeAutoObservable(this, {}, { autoBind: true });
  }

  //Getters/Setters
  get draggedTimecard() {
    return this._draggedTimecard;
  }
  set draggedTimecard(value: Timecard | null) {
    this._draggedTimecard = value;
  }

  moveTimecardId(id: number) {
    this._moving = { ...this._moving, [id]: true };
  }
  stopMovingTimecardId(id: number) {
    this._moving = { ...this._moving, [id]: false };
  }
  isMovingTimecardId(id: number) {
    return this._moving[id] || false;
  }
}

const TimeStore = new TimeStoreClass();
export default TimeStore;
