import type { DateTime } from 'luxon';
import type { KitRental } from './KitRental';
import type { MileageForm } from './MileageForm';
import type PayPeriod from './PayPeriod';
import type TimecardDay from './TimecardDay';
import type { TimecardStatus } from './TimecardStatus';
import type { Union } from './Union';
import type UserCrew from './UserCrew';
import type Reimbursement from './Reimbursements';

export default interface Timecard {
  projectMemberId: number;
  id: number;
  capsPayId?: number | null;
  revisionId: number | null;
  isRevision: boolean;
  union: Union;
  occupationId: number;
  occupation: any;
  hireLocationId: number | null;
  hireLocation: any;
  requiresHireLocation: boolean;
  rate: number;
  hourlyRate: number;
  rateType: any;
  rateTypeId: number;
  guarHours: number;
  guarRate: number;
  payPeriodId: number;
  payPeriod: PayPeriod;
  timecardDays: TimecardDay[];
  reimbursements: Reimbursement[];
  projectId: number;
  userCrewId: number;
  userCrew: UserCrew;
  createdAt: DateTime;
  updatedAt: DateTime;
  lastDownloadedAt: DateTime;
  comments: string;
  mileageForm: MileageForm;
  kitRental: KitRental;
  hourlyExempt: boolean;
  status: TimecardStatus;
  batch: any;
  requestedChanges: string;
  gross: number;
  grossWithBoxRentalAndMileage: number;
  fileId?: string;
  isActive: boolean;
  isOnCall: boolean;
  isExempt: boolean;
  isHalfDayAllowed: boolean;
  isNdbAllowed: boolean;
}
