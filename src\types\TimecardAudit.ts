import type { DateTime } from 'luxon';
import type User from './User';

export default interface TimecardAudit {
  id: number;
  timecardId: number;
  userId: number;
  user: User;
  ipAddress: string;
  action: string;
  oldValues: StatusUpdate;
  newValues: StatusUpdate;
  notes: string;
  createdAt: DateTime;
  updatedAt: DateTime;
}

type StatusUpdate = {
  status: {
    key: string;
    name: string;
  };
};
