/// <reference types="cypress" />

export default class CompanyPage {
  readonly companyNameInput = '[data-testid="Name-input"]';
  readonly companyPhoneInput = '[data-testid="Phone-input"]';
  readonly companyCastCrewIdInput = '[data-testid="Cast and Crew Id-input"]';
  readonly companyAddressInput = '[data-testid="Street Address-input"]';
  readonly companyCityInput = '[data-testid="City-input"]';
  readonly companyZipCodeInput = '[data-testid="ZIP / Postal code-input"]';
  readonly companyStateDropdown =
    '[data-testid="company-state-dropdown"] button';
  readonly companyTaxClassificationDropdown =
    '[data-testid="company-taxClassification-dropdown"] button';
  readonly companyPayFrequencyDropdown =
    '[data-testid="company-payFrequency-dropdown"] button';
  readonly companyPayDaysDropdown =
    '[data-testid="company-payDays-dropdown"] button';
  readonly submitCreateProjectButton =
    '[data-testid="company-create-update-btn"]';

  fillOutCompanyForm(
    companyName: string,
    companyPhone: string,
    address: string,
    city: string,
    state: string,
    zipCode: string,
    taxClassification: string,
    payFrequency: string,
    payDay: string,
  ) {
    cy.get(this.companyNameInput).should('be.visible').type(companyName);

    cy.get(this.companyPhoneInput).should('be.visible').type(companyPhone);
    cy.get(this.companyCastCrewIdInput)
      .should('be.visible')
      .and('not.be.disabled');
    cy.get(this.companyAddressInput).should('be.visible').type(address);
    cy.get(this.companyCityInput).should('be.visible').type(city);
    cy.get(this.companyStateDropdown).click();
    cy.contains('span', state).should('be.visible').click();
    cy.get(this.companyZipCodeInput).should('be.visible').type(zipCode);
    cy.get(this.companyTaxClassificationDropdown).click();
    cy.contains('span', taxClassification).should('be.visible').click();
    cy.get(this.companyPayFrequencyDropdown).click();
    cy.contains('span', payFrequency).should('be.visible').click();
    cy.get(this.companyPayDaysDropdown).click();
    cy.contains('span', payDay).should('be.visible').click();
  }

  submitCreateCompany() {
    cy.get(this.submitCreateProjectButton)
      .scrollIntoView()
      .should('be.visible')
      .click();
  }
}
