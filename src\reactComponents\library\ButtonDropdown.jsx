import {
  Box,
  Button,
  Checkbox,
  TextFieldRaw as Text<PERSON>ield,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  ClickAwayListener,
  MenuItem,
  InputAdornment,
} from './';
import React from 'react';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import HelpCircleIcon from '../assets/icons/HelpCircle';
import _debounce from 'lodash/debounce';
import PropTypes from 'prop-types';

const ButtonDropdown = (props) => {
  const {
    label,
    field,
    children,
    options = [],
    values = [],
    getOptionLabel = (option) => option?.name,
    isSelectedValue = (selected, option) => {
      return selected?.value === option?.value;
    },
    onChange,
    onSearch,
    useTextSearch = true,
    infiniteScrolling = false,
    pagination = { limit: 50, page: 1 },
    // onOpen,
    // onClose,
  } = props;
  const [anchor, setAnchor] = React.useState(null);

  const [textSearch, setTextSearch] = React.useState('');
  const [optionList, setOptionList] = React.useState(options);
  const [loading, setLoading] = React.useState(false);
  const [selectedOptions, setSelectedOptions] = React.useState(values);

  const paginationRef = React.useRef({
    pagination: { ...pagination },
    hasNextPage: true,
  });

  const isAsyncTextSearch = onSearch && typeof onSearch === 'function';

  const onResetPagination = React.useCallback(() => {
    paginationRef.current = {
      pagination: { ...pagination },
      hasNextPage: true,
    };
  }, [pagination]);

  const onSearchOptions = React.useMemo(
    () =>
      _debounce(async (search, infiniteScrolling) => {
        if (search && !search.trim()) return;
        try {
          setLoading(true);
          if (infiniteScrolling && paginationRef.current.hasNextPage) {
            paginationRef.current.pagination.page += 1;
          }
          const options = await onSearch(
            search,
            paginationRef.current.pagination,
          );
          if (Array.isArray(options)) {
            if (infiniteScrolling) {
              if (options.length < paginationRef.current.pagination.limit) {
                paginationRef.current.hasNextPage = false;
              }

              setOptionList((prev) => {
                return [...prev, ...options];
              });
            } else {
              setOptionList(options);
            }
          } else {
            throw new Error('search did not return an array');
          }
        } catch (error) {
          console.error('search error', field, error);
        } finally {
          setLoading(false);
        }
      }, 250),
    [onSearch, setOptionList, setLoading, paginationRef, field],
  );

  const onSearchChange = (search) => {
    setTextSearch(search);
    if (anchor && isAsyncTextSearch && useTextSearch) {
      onResetPagination();
      onSearchOptions(search);
    }
  };

  React.useEffect(() => {
    const hasDiff =
      selectedOptions?.some?.(
        (componentSelected) =>
          !values.some((propsSelected) =>
            isSelectedValue(componentSelected, propsSelected),
          ),
      ) || selectedOptions?.length !== values?.length;

    if (hasDiff) {
      setSelectedOptions(values);
    }
  }, [values, isSelectedValue, selectedOptions]);

  React.useEffect(() => {
    if (options?.length > 0) {
      setOptionList(options);
    }
  }, [options]);

  const handleOnSelect = (option, value) => {
    let newSelectedOptions;
    if (value) {
      newSelectedOptions = selectedOptions.concat([option]);
      setSelectedOptions(newSelectedOptions);
    } else {
      newSelectedOptions = selectedOptions.filter(
        (o) => isSelectedValue(o, option) === false,
      );
      setSelectedOptions(newSelectedOptions);
    }
    if (!onChange) console.warn('onChange not defined');
    else onChange(newSelectedOptions, field);
  };

  React.useEffect(() => {
    if (!anchor && textSearch) {
      setTextSearch('');
      onResetPagination();
      onSearchOptions('');
    }
  }, [anchor, onResetPagination, onSearchOptions, textSearch]);

  return (
    <Box sx={{ alignSelf: 'center' }}>
      <Button
        variant="outlined"
        onClick={(e) => {
          setAnchor(e.currentTarget);
        }}
        sx={[
          {
            padding: '2px 8px',
            borderRadius: '16px',
            color: 'gray.600',
            fontSize: '12px',
            border: `1.5px solid`,
            borderColor: 'gray.600',
            backgroundColor: 'transparent',
          },
          (theme) =>
            theme.applyStyles('dark', {
              color: 'gray.100',
              borderColor: 'gray.100',
            }),
        ]}
        data-testid={`dropdown-${label}`}
      >
        <Typography variant="xsSemi" sx={{ lineHeight: '1.5' }}>
          {label || children}
        </Typography>
        <ExpandMoreIcon sx={{ fontSize: '1rem', ml: '4px' }} />
      </Button>
      <Popper open={!!anchor} anchorEl={anchor} placement="bottom-start">
        <ClickAwayListener onClickAway={() => setAnchor(null)}>
          <Box
            onScroll={(e) => {
              const { scrollTop, scrollHeight, clientHeight } = e.target;
              if (scrollTop + clientHeight >= scrollHeight - 240) {
                if (
                  !loading &&
                  infiniteScrolling &&
                  paginationRef.current.hasNextPage
                ) {
                  onSearchOptions(textSearch, infiniteScrolling);
                }
              }
            }}
            sx={(theme) => ({
              minWidth: '200px',
              minHeight: '40px',
              padding: `${useTextSearch ? 0 : 8}px 12px 8px 12px`,
              backgroundColor: 'background.default',
              borderRadius: '8px',
              border: `1px solid ${theme.palette.gray[200]}`,
              boxShadow:
                '0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03)',
              marginTop: '4px',
              maxHeight: '300px',
              overflowY: 'auto',
            })}
          >
            <DropdownContent
              optionList={optionList}
              selectedOptions={selectedOptions}
              onSelect={handleOnSelect}
              useTextSearch={useTextSearch}
              textSearch={textSearch}
              onSearchChange={onSearchChange}
              isSelectedValue={isSelectedValue}
              getOptionLabel={getOptionLabel}
              isAsyncTextSearch={isAsyncTextSearch}
            />
          </Box>
        </ClickAwayListener>
      </Popper>
    </Box>
  );
};

ButtonDropdown.propTypes = {
  label: PropTypes.string,
  field: PropTypes.string,
  options: PropTypes.array,
  values: PropTypes.array,
  getOptionLabel: PropTypes.func,
  isSelectedValue: PropTypes.func,
  onChange: PropTypes.func,
  onSearch: PropTypes.func,
  useTextSearch: PropTypes.bool,
  infiniteScrolling: PropTypes.bool,
  pagination: PropTypes.shape({
    limit: PropTypes.number,
    page: PropTypes.number,
  }),
  children: PropTypes.node,
};

const DropdownContent = (props) => {
  const {
    optionList = [],
    selectedOptions = [],
    isSelectedValue,
    onSelect,
    useTextSearch,
    onSearchChange,
    textSearch,
    getOptionLabel,
    isAsyncTextSearch,
  } = props;
  const handleOnSelect = (option, value) => {
    onSelect(option, value);
  };

  const filteredList =
    useTextSearch && !isAsyncTextSearch
      ? optionList.filter((option) => {
          return (getOptionLabel(option) || '')
            .toLowerCase()
            .includes(textSearch.toLowerCase());
        })
      : optionList;

  return (
    <Box>
      {useTextSearch && (
        <Box
          sx={{
            padding: '8px 0px 8px 0px',
            position: 'sticky',
            top: 0,
            zIndex: 1,
            borderBottom: '1px solid',
            borderColor: 'gray.200',
            backgroundColor: 'background.default',
          }}
        >
          <TextField
            value={textSearch}
            onChange={(e) => {
              onSearchChange(e.target.value);
            }}
            sx={{ width: '100%' }}
            autoFocus
            placeholder="Type to search..."
            slotProps={{
              input: {
                autoComplete: 'off',
                endAdornment: (
                  <InputAdornment position="end">
                    <HelpCircleIcon />
                  </InputAdornment>
                ),
                sx: {
                  padding: '8px 12px',
                },
              },
            }}
          />
        </Box>
      )}
      <Box>
        {filteredList.map((option, i) => {
          const selected = selectedOptions?.some?.((selected) =>
            isSelectedValue(selected, option),
          );

          return (
            <MenuItem
              key={`${option.name}-${option.id}`}
              onClick={() => {
                handleOnSelect(option, !selected);
              }}
              sx={{
                display: 'flex',
                alignItems: 'center',
                padding: '0px 4px 0px 0px',
              }}
            >
              <Box>
                <Checkbox checked={selected} sx={{ padding: '4px' }} />
              </Box>
              <Box>
                <Typography>{getOptionLabel(option) || ''}</Typography>
                {option.subText && (
                  <Typography sx={{ fontSize: '0.75rem', color: 'gray.200' }}>
                    {option.subText}
                  </Typography>
                )}
              </Box>
            </MenuItem>
          );
        })}
      </Box>
    </Box>
  );
};

DropdownContent.propTypes = {
  optionList: PropTypes.array,
  selectedOptions: PropTypes.array,
  isSelectedValue: PropTypes.func,
  onSelect: PropTypes.func,
  useTextSearch: PropTypes.bool,
  textSearch: PropTypes.string,
  onSearchChange: PropTypes.func,
  getOptionLabel: PropTypes.func,
  isAsyncTextSearch: PropTypes.bool,
};

export default ButtonDropdown;
