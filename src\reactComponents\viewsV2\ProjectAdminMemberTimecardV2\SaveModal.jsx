import React from 'react';
import PropTypes from 'prop-types';

import {
  Box,
  TextInput,
  Modal,
  InputLabel,
  Loader,
} from '@/reactComponents/library';

const SaveModal = (props) => {
  const { saveModalOpen, setSaveModalOpen, onSaveAll, saving } = props;

  const [auditNotes, setAuditNotes] = React.useState('');

  React.useEffect(() => {
    if (!saveModalOpen) {
      setAuditNotes('');
    }
  }, [saveModalOpen]);

  return (
    <Modal
      open={saveModalOpen}
      title="Save Timecard"
      setOpen={setSaveModalOpen}
      onSubmit={() => onSaveAll(auditNotes)}
      onCancel={() => {
        setSaveModalOpen(false);
      }}
      disableSubmit={saving}
      disableCancel={saving}
      submitText="Save"
    >
      {saving ? (
        <Loader />
      ) : (
        <Box>
          <InputLabel>
            Specify the changes and why you made them (optional).
          </InputLabel>
          <TextInput
            sx={{ width: '100%' }}
            value={auditNotes}
            multiline
            minRows={4}
            onChange={(e) => setAuditNotes(e.target.value)}
            placeholder="Enter Optional Audit Notes"
            data-testid="save-modal-input"
          />
        </Box>
      )}
    </Modal>
  );
};

SaveModal.propTypes = {
  saveModalOpen: PropTypes.bool.isRequired,
  setSaveModalOpen: PropTypes.func.isRequired,
  onSaveAll: PropTypes.func.isRequired,
  saving: PropTypes.bool.isRequired,
};

export default SaveModal;
