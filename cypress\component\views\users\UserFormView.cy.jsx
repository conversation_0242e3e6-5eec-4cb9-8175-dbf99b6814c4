import { createM<PERSON>ory<PERSON>outer, RouterProvider, Outlet } from 'react-router-dom';
import UserFormView from '@/reactComponents/views/users/UserFormView';
import PropTypes from 'prop-types';

const TestWrapper = ({ contextValue }) => {
  const router = createMemoryRouter(
    [
      {
        path: '/users/:id',
        element: <Outlet context={contextValue} />,
        children: [{ path: '/users/:id', element: <UserFormView /> }],
      },
    ],
    { initialEntries: ['/users/56'] },
  );
  return <RouterProvider router={router} />;
};

TestWrapper.propTypes = {
  contextValue: PropTypes.object.isRequired,
};

describe('UserFormView Component', () => {
  beforeEach(function () {
    cy.fixture('roles').then((roles) => {
      cy.intercept('GET', '/v2/api/core/roles/list-roles', {
        statusCode: 200,
        body: roles,
      }).as('getRolesList');
    });

    cy.fixture('userRoleAdmin').as('contextData');
    cy.intercept('GET', '**/v2/api/core/users/56', {
      fixture: 'userRoleAdmin.json',
    }).as('getUser');

    cy.mount(<TestWrapper contextValue={this.contextData} />);
  });

  it('should present admin user information', function () {
    cy.get('[data-testid="user-roles-dropdown"]').should('be.visible').click();
    cy.get('[data-testid="user-roles-dropdown"]').contains('Admin').click();
    cy.get('[data-testid="user-roles-dropdown"]').should('be.visible').click();
    cy.get('[data-testid="user-firstName-input"]').should('be.visible').click();
    cy.get('[data-testid="user-middleName-input"]')
      .should('exist')
      .and('be.visible');
    cy.get('[data-testid="user-lastName-input"]')
      .should('exist')
      .and('be.visible');
    cy.get('[data-testid="user-mobile-input"]')
      .should('exist')
      .and('be.visible');
    cy.get('[data-testid="user-email-input"]')
      .should('exist')
      .and('be.visible');
  });

  it('should allow to update the user', function () {
    cy.get('[data-testid="user-update-btn"]').should('be.visible').click();
  });

  it('should allow to cancel', function () {
    cy.get('[data-testid="user-cancel-btn"]').should('be.visible').click();
  });
});
