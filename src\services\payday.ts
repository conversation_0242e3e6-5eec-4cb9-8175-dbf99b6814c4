import axios, { type AxiosResponse } from 'axios';
import { coreBaseUrl } from '../utils/constants';

export const getPayday = async (): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/payday`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};

export const getPaydayFrequencies = async (): Promise<AxiosResponse<any>> => {
  const url = `${coreBaseUrl()}/payday/frequencies`;
  const response = await axios.get(url, { withCredentials: true });
  return response;
};
