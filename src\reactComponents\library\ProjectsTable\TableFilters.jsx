import React from 'react';
import { ChevronDownIcon, PlusCircleIcon } from '@heroicons/react/24/outline';
import { <PERSON><PERSON>, Modal, Popover } from '@/reactComponents/library';
import { FilterUIType } from '@/types/Filter';
import { useSearchParams } from 'react-router-dom';
import PropTypes from 'prop-types';
import { debounce } from 'lodash';

const TableFilters = ({ filters, onFiltersChange }) => {
  const [searchParams, setSearchParams] = useSearchParams();

  const [isFilterModalOpen, setIsFilterModalOpen] = React.useState(false);

  const visibleFilters = React.useMemo(
    () => filters.filter((f) => f.type !== FilterUIType.Hidden),
    [filters],
  );
  const activeFilters = React.useMemo(
    () => filters.filter((f) => f.active),
    [filters],
  );

  const updateFilter = (filter) => {
    const updatedFilters = filters.map((f) =>
      f.id === filter.id ? filter : f,
    );

    onFiltersChange(updatedFilters);
  };

  const removeFilter = (filter) => {
    const updated = {
      ...filter,
      active: false,
      value: '',
      options: filter.options?.map((o) => ({ ...o, active: false })),
    };

    const filtersString = searchParams.get('filters') || '';

    const filterMap = Object.fromEntries(
      filtersString
        .split(';')
        .filter(Boolean)
        .map((pair) => pair.split('=')),
    );

    filterMap[filter.id] = '';

    const newFilterString = Object.entries(filterMap)
      .map(([key, val]) => `${key}=${encodeURIComponent(val)}`)
      .join(';');

    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('filters', newFilterString);

    setSearchParams(newSearchParams, { replace: true });

    updateFilter(updated);
  };

  return (
    <section aria-labelledby="filter-heading" className="w-full">
      <h2 id="filter-heading" className="sr-only">
        Filters
      </h2>
      <div className="border-b border-gray-200 dark:border-gray-500 pb-4">
        <div className="mx-auto flex max-w-7xl items-center px-4 sm:px-6 lg:px-8">
          <div className="flex grow" />
          {/* MOBILE FILTER BUTTON */}
          <div className="sm:hidden">
            <Button size="small" onClick={() => setIsFilterModalOpen(true)}>
              Filter
            </Button>
          </div>
          <Modal open={isFilterModalOpen} setOpen={setIsFilterModalOpen}>
            <div className="p-4">
              <h2 className="text-xl font-bold pb-2">Filters</h2>
              {visibleFilters.map((filter) => (
                <div key={filter.id} className="flex align-middle py-2 px-4">
                  {filter.active || filter.type === FilterUIType.MultiSelect ? (
                    <SearchInput
                      filters={filter}
                      setFilters={updateFilter}
                      data-testid={`filter-input-${filter?.label}`}
                    />
                  ) : (
                    <>
                      <PlusCircleIcon
                        onClick={() =>
                          updateFilter({ ...filter, active: true })
                        }
                        className="cursor-pointer mr-1 mt-0.5 h-5 w-5 text-gray-700 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300"
                      />
                      {filter.label}
                    </>
                  )}
                </div>
              ))}
              <div className="flex justify-center space-x-2 pt-3">
                <Button onClick={() => setIsFilterModalOpen(false)}>
                  Close
                </Button>
              </div>
            </div>
          </Modal>

          {/* DESKTOP FILTERS */}
          <div className="hidden sm:block">
            <div className="flex gap-x-8 items-center divide-x divide-gray-200 dark:divide-gray-800">
              {visibleFilters.map((filter) => (
                <FilterInputPopover
                  key={filter.id}
                  filter={filter}
                  onFiltersChange={onFiltersChange}
                  updateFilter={updateFilter}
                />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Active Filters */}
      <div className="mx-auto max-w-7xl px-4 py-3 sm:flex sm:items-center sm:px-6 lg:px-8">
        <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
          Filters<span className="sr-only">, active</span>
        </h3>
        <div
          aria-hidden="true"
          className="hidden h-5 w-px bg-gray-300 dark:bg-gray-500 sm:ml-4 sm:block"
        />
        <div className="mt-2 sm:ml-4 sm:mt-0">
          <div className="-m-1 flex flex-wrap items-center">
            {activeFilters.map((activeFilter) => (
              <div
                key={activeFilter.id}
                className="m-1 inline-flex items-center rounded-full border border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-700 py-1 px-2.5 text-xs font-medium text-gray-900 dark:text-gray-400"
              >
                <span>{activeFilter.label}</span>
                <span>: {activeFilter.value}</span>

                <button
                  type="button"
                  className="ml-1 inline-flex h-4 w-4 flex-shrink-0 rounded-full p-1 text-gray-400 hover:bg-gray-200 hover:text-gray-500"
                  onClick={() => removeFilter(activeFilter)}
                >
                  <span className="sr-only">
                    Remove filter for {activeFilter.label}
                  </span>
                  <svg
                    className="h-2 w-2"
                    stroke="currentColor"
                    fill="none"
                    viewBox="0 0 8 8"
                  >
                    <path
                      strokeLinecap="round"
                      strokeWidth="1.5"
                      d="M1 1l6 6m0-6L1 7"
                    />
                  </svg>
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

TableFilters.propTypes = {
  filters: PropTypes.array.isRequired,
  onFiltersChange: PropTypes.func.isRequired,
};

const FilterInputPopover = ({ filter, onFiltersChange, updateFilter }) => {
  const [filterInput, setFilterInput] = React.useState(null);

  const isDropdownDisabled = (filter) =>
    !filter.active && filter.type !== FilterUIType.MultiSelect;

  return (
    <div>
      <div className="flex">
        <button
          onClick={(e) => setFilterInput(e.currentTarget)}
          type="button"
          data-testid={`filter-popover-${filter?.label}`}
          className="group inline-flex justify-center text-sm font-medium text-gray-700 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300"
        >
          <span>{filter.label}</span>
          {!isDropdownDisabled(filter) && (
            <ChevronDownIcon
              className="-mr-1 ml-1 h-5 w-5 text-gray-700 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300"
              aria-hidden="true"
            />
          )}
        </button>
        {isDropdownDisabled(filter) && (
          <PlusCircleIcon
            onClick={() => updateFilter({ ...filter, active: true })}
            className="cursor-pointer ml-1 h-5 w-5 text-gray-700 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300"
          />
        )}
      </div>
      <Popover
        id={filter.id}
        disableScrollLock
        className="relative inline-block px-4 text-left"
        open={Boolean(filterInput)}
        onClose={() => setFilterInput(null)}
        anchorEl={filterInput}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: -5,
          horizontal: 'left',
        }}
      >
        <div className="rounded-md bg-white dark:bg-gray-800 p-4 shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-none">
          <SearchInput
            filters={filter}
            setFilters={onFiltersChange}
            data-testid={`filter-input-${filter?.label}`}
          />
        </div>
      </Popover>
    </div>
  );
};

FilterInputPopover.propTypes = {
  filter: PropTypes.object.isRequired,
  onFiltersChange: PropTypes.func.isRequired,
  updateFilter: PropTypes.func.isRequired,
};

const SearchInput = (props) => {
  const filterId = props?.filters.id;

  const filterInputRef = React.useRef(null);

  React.useEffect(() => {
    if (filterInputRef.current) {
      filterInputRef.current.focus();
      filterInputRef.current.select();
    }
  }, []);

  const [searchParams, setSearchParams] = useSearchParams();
  const filtersString = searchParams.get('filters') || '';
  const valueString = filtersString.split('%3B').join('');
  const currentFilters = valueString.split(';').reduce((acc, filter) => {
    const [id, value] = filter.split('=');
    if (id && value) {
      acc[id] = decodeURIComponent(value);
    }
    return acc;
  }, {});

  const debouncedSetSearchParams = React.useMemo(
    () =>
      debounce((value) => {
        const filters = new URLSearchParams(searchParams);
        const currentRaw = filters.get('filters');
        const current = currentRaw ? decodeURIComponent(currentRaw) : '';

        const filterMap = Object.fromEntries(
          current
            .split(';')
            .filter(Boolean)
            .map((pair) => pair.split('=')),
        );

        filterMap[filterId] = value;

        const newFilterString = Object.entries(filterMap)
          .map(([key, val]) => `${key}=${encodeURIComponent(val)}`)
          .join(';');

        filters.set('filters', newFilterString);

        return setSearchParams(filters, { replace: true });
      }, 500),
    [filterId, searchParams, setSearchParams],
  );

  const handleChange = (e) => {
    const value = e.target.value;
    props.setFilters((prev) => {
      const updatedFilters = prev.map((filter) => {
        if (filter.id === filterId) {
          return { ...filter, value };
        }
        return filter;
      });
      return updatedFilters;
    });

    debouncedSetSearchParams(value);
  };

  React.useEffect(() => {
    return () => {
      debouncedSetSearchParams.cancel();
    };
  }, [debouncedSetSearchParams]);

  return (
    <div className="flex items-center justify-end gap-x-2">
      <input
        data-testid={`filter-input-${props.filters.label}`}
        defaultValue={currentFilters[filterId]}
        ref={filterInputRef}
        onChange={handleChange}
        type="text"
        placeholder={`Search by ${props.filters.label}`}
        className="appearance-none w-56 bg-white border border-gray-300 dark:border-gray-500 rounded-md shadow-sm text-gray-800 dark:text-gray-200 dark:bg-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-xs block px-3 py-2"
      />
    </div>
  );
};

SearchInput.propTypes = {
  filters: PropTypes.object.isRequired,
  setFilters: PropTypes.func.isRequired,
};

export { TableFilters };
