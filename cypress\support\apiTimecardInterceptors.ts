/// <reference types="cypress" />

export function interceptProjectTimecards(projectId: string | number): void {
  cy.intercept(
    'GET',
    new RegExp(`/v2/api/core/projects/${projectId}/timecards/\\?.*`),
  ).as('getTimecards');
}

export function interceptTimecardsBatch(
  projectId: string,
  batchId: string,
): void {
  const encodedBatchId = encodeURIComponent(`batchId=${batchId}`);
  const urlPattern = new RegExp(
    `/v2/api/core/projects/${projectId}/timecards/\\?filters=${encodedBatchId}.*`,
  );
  cy.intercept('GET', urlPattern).as('getTimecardsBatch');
}

export function interceptProjectIdentifiersByName(projectName: string): void {
  const filter = `name=${projectName}:op:ilike`;
  const encodedFilter = encodeURIComponent(filter);
  const url = `${Cypress.env(
    'BASE_URL',
  )}/api/core/projects/?filters=${encodedFilter}&page=1&limit=10`;
  cy.intercept('GET', url).as('getProjectByName');
}

export function interceptCreateTimecard(): void {
  cy.intercept(
    'POST',
    `${Cypress.env('BASE_URL')}api/core/project-members/**/timecards`,
  ).as('createTimecard');
}

export function interceptApprovedTimecard(): void {
  cy.intercept(
    'PATCH',
    `${Cypress.env('BASE_URL')}api/core/timecards/**/approve/`,
  ).as('approvedTimecard');
}

export function interceptEditTimecard(): void {
  cy.intercept('PATCH', `${Cypress.env('BASE_URL')}api/core/timecards/**/`).as(
    'editedTimecard',
  );
}
export function interceptDeleteTimecard(): void {
  cy.intercept('DELETE', `${Cypress.env('BASE_URL')}api/core/timecards/**`).as(
    'deletedTimecard',
  );
}

export function interceptDeleteTimecardFromBatch(): void {
  cy.intercept(
    'DELETE',
    `${Cypress.env('BASE_URL')}api/core/timecards/**/batches/`,
  ).as('deletedTimecardFromBatch');
}

export function interceptGetApprovedTimecard(): void {
  cy.intercept('GET', `${Cypress.env('BASE_URL')}api/core/timecards/**/`).as(
    'getApprovedTimecard',
  );
}

export function interceptAssignTimecardToBatch(): void {
  cy.intercept(
    'PATCH',
    `${Cypress.env('BASE_URL')}api/core/timecards/**/batches/**`,
  ).as('assignedTimecardFromBatch');
}

export function interceptExportPdf(): void {
  const queryParams = new URLSearchParams({
    scope: 'all',
    format: 'pdf',
    includeKitRental: 'true',
    includeMileageForm: 'true',
    includeReimbursementForms: 'true',
    exportOrder: 'startPaperworkFirst',
  });

  const url = `${Cypress.env(
    'BASE_URL',
  )}api/core/batches/**/pdf?${queryParams.toString()}`;
  cy.intercept('GET', url).as('getExportPDF');
}

export function interceptAddTimecard(): void {
  cy.intercept('POST', `${Cypress.env('BASE_URL')}api/core/timecards/`).as(
    'addTimecard',
  );
}

export function interceptSubmitTimecard(timecardId: string): void {
  cy.intercept(
    'PATCH',
    `${Cypress.env('BASE_URL')}api/core/timecards/${timecardId}/submit/`,
  ).as('submitTimecard');
}

export function interceptCreateProject(): void {
  cy.intercept('POST', `${Cypress.env('BASE_URL')}api/core/projects`).as(
    'createProject',
  );
}

export function interceptEditProject(): void {
  cy.intercept('PATCH', `${Cypress.env('BASE_URL')}api/core/projects/**`).as(
    'editedProject',
  );
}

export function interceptProjectOnboarding(): void {
  cy.intercept(
    'PATCH',
    `${Cypress.env('BASE_URL')}api/core/projects/${Cypress.env(
      'projectId',
    )}/members`,
  ).as('projectOnboarding');
}

export function interceptSignPaperwork(): void {
  cy.intercept(
    'POST',
    `${Cypress.env('BASE_URL')}api/core/projects/${Cypress.env(
      'projectId',
    )}/starting-paperwork/sign?useLoanOut=false`,
  ).as('signPaperwork');
}

export function interceptEditBatch(): void {
  cy.intercept('PATCH', `${Cypress.env('BASE_URL')}api/core/batches/**`).as(
    'editedBatch',
  );
}

export function interceptCreateBatch(): void {
  cy.intercept(
    'POST',
    `${Cypress.env('BASE_URL')}api/core/projects/**/batches`,
  ).as('createdBatch');
}

export function interceptDeleteBatch(): void {
  cy.intercept('DELETE', `${Cypress.env('BASE_URL')}api/core/batches/**`).as(
    'deletedBatch',
  );
}

export function interceptCreateTemplate(): void {
  cy.intercept(
    'POST',
    `${Cypress.env(
      'BASE_URL',
    )}api/core/production-companies/**/document-templates`,
  ).as('createdTemplate');
}

export function interceptEditTemplate(): void {
  cy.intercept(
    'PATCH',
    `${Cypress.env(
      'BASE_URL',
    )}api/core/production-companies/**/document-templates/**`,
  ).as('editedTemplate');
}

export function interceptDeleteTemplate(): void {
  cy.intercept(
    'DELETE',
    `${Cypress.env(
      'BASE_URL',
    )}api/core/production-companies/**/document-templates/**`,
  ).as('deletedTemplate');
}

export function interceptCreatePaperWork(): void {
  cy.intercept(
    'POST',
    `${Cypress.env('BASE_URL')}api/core/projects/**/document-templates/create`,
  ).as('createdPaperWork');
}

export function interceptEditPaperWork(): void {
  cy.intercept(
    'PATCH',
    `${Cypress.env('BASE_URL')}api/core/projects/**/document-templates/**`,
  ).as('editedPaperWork');
}

export function interceptDeletePaperWork(): void {
  cy.intercept(
    'DELETE',
    `${Cypress.env('BASE_URL')}api/core/project-document-templates/**`,
  ).as('deletedPaperWork');
}

export function interceptUserInfo(): void {
  cy.intercept('GET', `${Cypress.env('BASE_URL')}api/core/users/me`).as(
    'userInfo',
  );
}

export function interceptTimecardRevision(baseTimecardId: string): void {
  cy.intercept(
    'POST',
    `${Cypress.env('BASE_URL')}api/core/timecards/${baseTimecardId}/revision`,
  ).as('timecardRevision');
}
