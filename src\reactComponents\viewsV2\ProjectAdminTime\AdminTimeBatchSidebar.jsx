import React from 'react';
import PropTypes from 'prop-types';
import _cloneDeep from 'lodash/cloneDeep';
import {
  Box,
  MenuItem,
  Text,
  TextInput,
  Modal,
  Pagination,
  PaginationItem,
  Tooltip,
  CircularProgress,
  StatusBadge,
} from '@/reactComponents/library';
import { styled } from '@mui/material/styles';
import {
  updateBatchOnTimecard,
  removeBatchFromTimecard,
} from '@/services/timecards';

import { BatchStatusEnum } from '@/types/Batch';

import {
  snackbarSuccess,
  snackbarAxiosErr,
  snackbarErr,
} from '@/reactComponents/library/Snackbar';
import BatchItemActions from './AdminTimeBatchItemActions';

import CreateBatch from '../sharedComponents/CreateBatch';
import { deleteBatch, updateBatch } from '@/services/batch';

import TimeStore from './store';
import { observer } from 'mobx-react-lite';

import { UNBATCHED_ID } from './utils';

const BatchBox = styled(Box, { label: 'BatchBox' })((props) => {
  const { theme } = props;
  const { palette } = theme;

  return {
    display: 'flex',
    flexDirection: 'column',
    width: '300px',
    minWidth: '300px',
    backgroundColor: palette.background.paper,
    borderTop: '1px solid',
    borderColor: palette.background.border,
  };
});

const styles = {
  sidebarSection: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    p: 1,
    borderBottom: '1px solid',
    borderColor: 'background.border',
    borderRightWidth: 1,
  },
  scrollList: {
    overflowY: 'auto',
    display: 'flex',
    flexDirection: 'column',
    flexGrow: 1,
  },
  batchItem: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 1,
    paddingLeft: 2,
    height: '76px',

    borderTop: '0 solid',
    borderTopColor: 'background.border',

    borderRight: '1px solid',
    borderRightColor: 'background.border',

    borderBottom: '1px solid',
    borderBottomColor: 'background.border',

    borderLeft: '2px solid',
    borderLeftColor: 'background.default',
  },
  selectedItem: {
    backgroundColor: 'background.default',
    borderBottomColor: 'background.border',
    borderRightColor: 'background.default',
    borderLeftColor: 'background.border',
  },
  batchItemInfo: {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
  },
  nameLine: {
    display: 'flex',
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'flex-start',
    gap: 1,
  },
  itemLine: {
    color: 'text.secondary',
    display: 'flex',
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'flex-start',
    gap: 0.1,
  },
  paginationBox: {
    display: 'flex',
    justifyContent: 'center',
    pt: 1,
    flexWrap: 'nowrap',
    borderRight: '1px solid',
    borderRightColor: 'background.border',
  },
  sidebarBorderExtension: {
    width: '100%',
    minHeight: '1rem',
    borderRight: '1px solid',
    borderRightColor: 'background.border',
    flexGrow: 1,
  },
  hiddenContainer: {
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
  },
  hiddenText: { overflow: 'hidden', textOverflow: 'ellipsis' },
};

const AdminTimeBatchSidebar = observer((props) => {
  const {
    selectedBatchId,
    selectBatch,
    batches,
    fetchBatches,
    batchPagination,
    fetchTimecards,
    reloadingBatches,
    unclaimedBatchDetails,
    checkUnapprovedTimecards,
  } = props;
  const [batchToDelete, setBatchToDelete] = React.useState(null);
  const [deleting, setDeleting] = React.useState(false);
  const [editBatchOpen, setEditBatchOpen] = React.useState(false);
  const [batchName, setBatchName] = React.useState('');
  const [editing, setEditing] = React.useState(false);
  const [batchId, setBatchId] = React.useState('');

  const [dragActiveId, setDragActiveId] = React.useState(null);

  const openBatchToEdit = (batch) => {
    setBatchName(batch.name);
    setBatchId(batch.id);
    setEditBatchOpen(true);
  };
  const onEditBatch = () => {
    const trimmedBathName = batchName.trim();
    if (!trimmedBathName) {
      snackbarErr('Batch name is required');
      return;
    }
    setEditing(true);
    updateBatch(batchId, { name: trimmedBathName })
      .then(() => {
        setEditBatchOpen(false);
        setBatchName('');
        fetchBatches();
        snackbarSuccess('Batch Updated');
      })
      .catch((err) => {
        if (err?.response?.data?.errors) {
          snackbarErr(err.response.data.errors[0].message);
        } else {
          snackbarErr(err, 'Error editing batch');
        }
        console.error('Error editing batch: ', err);
      })
      .finally(() => setEditing(false));
  };

  const confirmDelete = () => {
    setDeleting(true);
    deleteBatch(batchToDelete.id)
      .then(() => {
        setBatchToDelete(null);
        snackbarSuccess('Batch deleted');
        if (selectedBatchId === batchToDelete.id) {
          selectBatch(UNBATCHED_ID);
          fetchBatches();
        } else {
          fetchBatches();
        }
      })
      .catch((err) => {
        snackbarAxiosErr(err, 'Error deleting batch');
        console.error('Error deleting batch: ', err);
      })
      .finally(() => {
        setDeleting(false);
      });
  };

  const handleDrag = (e, batch) => {
    if (
      (batch.statusId === BatchStatusEnum.OPEN ||
        batch.statusId === BatchStatusEnum.REOPENED ||
        batch.id === UNBATCHED_ID) &&
      selectedBatchId !== batch.id &&
      e.dataTransfer.effectAllowed === 'move'
    ) {
      e.preventDefault();
      e.stopPropagation();

      if (e.type === 'dragleave') {
        setDragActiveId((activeId) =>
          activeId === batch.id ? null : activeId,
        );
      }
      if (e.type === 'dragenter') {
        setDragActiveId(batch.id);
      }
      if (e.type === 'dragover') {
        //do nothing
      }
    }
  };

  const handleDrop = (e, batch) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActiveId(null);
    const timecardId = Number(e.dataTransfer.getData('text/plain'));
    if (!timecardId) {
      console.error('No timecard ID found in drag event');
      return;
    }
    TimeStore.moveTimecardId(timecardId);
    const sourceBatchId = TimeStore.draggedTimecard.batchId;

    if (batch.id === UNBATCHED_ID) {
      removeBatchFromTimecard(timecardId)
        .then(() => {
          Promise.all([fetchTimecards(), fetchBatches()]).then(() => {
            checkUnapprovedTimecards(sourceBatchId);
            TimeStore.stopMovingTimecardId(timecardId);
            snackbarSuccess('Timecard removed from batch');
          });
        })
        .catch((err) => {
          console.error('Error removing timecard from batch: ', err);
          TimeStore.stopMovingTimecardId(timecardId);
          snackbarAxiosErr(err, 'Error removing timecard from batch');
        });
    } else {
      updateBatchOnTimecard(timecardId, batch.id)
        .then(() => {
          Promise.all([fetchTimecards(), fetchBatches()]).then(() => {
            checkUnapprovedTimecards(sourceBatchId);
            snackbarSuccess(`Timecard moved to batch "${batch.name}"`);
            TimeStore.stopMovingTimecardId(timecardId);
          });
        })
        .catch((err) => {
          console.error('Error moving timecard to batch: ', err);
          snackbarAxiosErr(err, 'Error moving timecard to batch');
          TimeStore.stopMovingTimecardId(timecardId);
        });
    }
  };

  const withUnbatched = React.useMemo(() => {
    const newArr = _cloneDeep(batches);

    newArr.unshift({
      id: UNBATCHED_ID,
      name: 'Unbatched Timecards',
      status: null,
    });
    return newArr;
  }, [batches]);

  const totalPages = Math.ceil(batchPagination.total / batchPagination.limit);
  const showPagination = totalPages > 1;

  if (reloadingBatches) {
    return (
      <BatchBox>
        <CircularProgress size={36} sx={{ margin: 'auto' }} />
      </BatchBox>
    );
  }

  return (
    <BatchBox>
      <Box sx={styles.sidebarSection}>
        <Text variant="lgSemi">Batches</Text>

        <CreateBatch onCreateComplete={fetchBatches} />
      </Box>
      <Box sx={styles.scrollList}>
        {withUnbatched.map((batch) => {
          const isSelected = selectedBatchId === batch.id;

          let itemStyles = {
            ...styles.batchItem,
          };
          if (isSelected) {
            itemStyles = {
              ...itemStyles,
              ...styles.selectedItem,
            };
          }

          const deleteDisabled = batch.timecards?.length > 0;
          const isLongBatchName = batch.name.length > 24;
          const disableBatchEdit = batch.status?.id !== BatchStatusEnum.OPEN;

          const isDroppable =
            (batch.statusId === BatchStatusEnum.OPEN ||
              batch.statusId === BatchStatusEnum.REOPENED ||
              batch.id === UNBATCHED_ID) &&
            !isSelected;
          const dragStyles = {
            ...itemStyles,
            border: '2px dotted',
            borderColor: 'primary.main',
            borderRadius: 1,
            '*': {
              //keep children from triggering dragLeave
              pointerEvents: 'none',
            },
          };

          if (dragActiveId === batch.id) {
            itemStyles = {
              ...dragStyles,
              border: '4px solid',
            };
          } else if (isDroppable && TimeStore.draggedTimecard) {
            itemStyles = dragStyles;
          }
          return (
            <MenuItem
              sx={itemStyles}
              key={batch.id}
              onClick={() => selectBatch(batch.id)}
              onDragEnter={(e) => handleDrag(e, batch)}
              onDragOver={(e) => handleDrag(e, batch)}
              onDragLeave={(e) => handleDrag(e, batch)}
              onDrop={(e) => handleDrop(e, batch)}
              id={`${batch.id}-batchDragTarget`}
              data-testid={`batch-${batch.id}`}
            >
              <Box sx={styles.hiddenContainer}>
                <Tooltip title={isLongBatchName ? batch.name : ''}>
                  <span>
                    <Box sx={styles.nameLine}>
                      <Text
                        sx={styles.hiddenText}
                        variant="smSemi"
                        data-testid="batch-scroll-list"
                      >
                        {batch.name}
                      </Text>
                    </Box>
                  </span>
                </Tooltip>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  {batch.id > 0 && (
                    <Text variant="xsMed" sx={styles.itemLine}>
                      <Text variant="xsMed">ID:</Text>
                      {batch.capsPayId}
                    </Text>
                  )}
                  <Text variant="xsMed" sx={styles.itemLine}>
                    <Text variant="xsMed">Total:</Text>$
                    {batch.id === UNBATCHED_ID
                      ? unclaimedBatchDetails?.batchTotalGross === null
                        ? '0.00'
                        : unclaimedBatchDetails?.batchTotalGross
                      : batch.batchTotalGross != null
                      ? batch.batchTotalGross
                      : '0.00'}
                  </Text>
                </Box>
                {batch?.status?.id && (
                  <div>
                    <StatusBadge batchStatusId={batch?.status?.id} />
                  </div>
                )}
              </Box>
              {batch.id !== UNBATCHED_ID && (
                <BatchItemActions
                  batch={batch}
                  openBatchToEdit={openBatchToEdit}
                  setBatchToDelete={setBatchToDelete}
                  deleteDisabled={deleteDisabled}
                  disableBatchEdit={disableBatchEdit}
                />
              )}
            </MenuItem>
          );
        })}
        {showPagination && (
          <Box sx={styles.paginationBox}>
            <Pagination
              count={totalPages}
              boundaryCount={1}
              page={batchPagination.page}
              onChange={(e, newPage) => fetchBatches(newPage)}
              color="secondary"
              renderItem={(params) => {
                const { page, type } = params;
                if (
                  type.includes('ellipsis') ||
                  (type === 'page' &&
                    page &&
                    page !== 1 &&
                    page !== totalPages &&
                    Math.abs(page - batchPagination.page) > 1)
                ) {
                  return null;
                }
                return <PaginationItem {...params} />;
              }}
            />
          </Box>
        )}
        <Box sx={styles.sidebarBorderExtension} />
      </Box>
      <Modal
        open={!!batchToDelete}
        setOpen={(o) => {
          if (!o) setBatchToDelete(null);
        }}
        title="Delete Batch"
        onSubmit={confirmDelete}
        onCancel={() => setBatchToDelete(null)}
        loading={deleting}
      >
        Delete batch "{batchToDelete?.name}"?
      </Modal>
      <Modal
        open={editBatchOpen}
        setOpen={setEditBatchOpen}
        title="Edit Batch"
        onSubmit={onEditBatch}
        onCancel={(e) => {
          setEditBatchOpen(false);
        }}
        loading={editing}
      >
        <Box sx={{ width: '100%' }}>
          <TextInput
            sx={{ width: '100%' }}
            label={'Batch name'}
            data-testid="edit-batch-input"
            value={batchName}
            onChange={(e) => setBatchName(e.target.value)}
          />
        </Box>
      </Modal>
    </BatchBox>
  );
});

AdminTimeBatchSidebar.propTypes = {
  selectedBatchId: PropTypes.number.isRequired,
  selectBatch: PropTypes.func.isRequired,
  project: PropTypes.object.isRequired,
  batches: PropTypes.array.isRequired,
  fetchBatches: PropTypes.func.isRequired,
  batchPagination: PropTypes.object.isRequired,
  fetchTimecards: PropTypes.func.isRequired,
  reloadingBatches: PropTypes.bool.isRequired,
};

export default AdminTimeBatchSidebar;
