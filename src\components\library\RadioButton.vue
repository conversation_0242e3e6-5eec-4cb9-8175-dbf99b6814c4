<template>
  <input
    type="radio"
    :name="name"
    :id="id"
    :value="value"
    :checked="modelValue === value"
    @change="handleChange"
    :disabled="disabled"
    class="h-4 w-4 text-indigo-600 border-gray-300 mr-2"
    :class="{ 'bg-gray-100 cursor-not-allowed': disabled }"
  />
  {{ label }}
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'RadioButton',
  emits: ['update:modelValue'],
  props: {
    modelValue: {
      required: true,
      type: String,
    },
    value: {
      required: true,
      type: String,
    },
    disabled: {
      default: false,
      type: Boolean,
    },
    name: {
      default: '',
      type: String,
    },
    id: {
      default: '',
      type: String,
    },
    label: {
      default: '',
      type: String,
    },
  },
  methods: {
    handleChange(evt: any) {
      if (this.disabled) return;
      this.$emit('update:modelValue', evt.target.value);
    },
  },
});
</script>
