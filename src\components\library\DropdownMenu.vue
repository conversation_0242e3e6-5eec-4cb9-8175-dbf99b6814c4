<template>
  <div class="text-right">
    <Menu
      as="div"
      class="relative z-20 inline-block text-left"
      v-slot="{ open }"
    >
      <!-- Used to capture the open value in our data -->
      <template v-if="open !== isOpen">
        {{ captureOpenState(open) }}
      </template>
      <div class="z-40">
        <slot name="buttonContent">
          <MenuButton
            class="rounded-md px-2 py-2 text-sm font-medium text-blue focus:outline-none focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75"
          >
            <EllipsisVerticalIcon
              class="h-5 w-5 text-blue-500 hover:text-blue-700 dark:text-gray-400 dark:hover:text-gray-300"
              aria-hidden="true"
            />
          </MenuButton>
        </slot>
      </div>

      <transition
        enter-active-class="transition duration-100 ease-out"
        enter-from-class="transform scale-95 opacity-0"
        enter-to-class="transform scale-100 opacity-100"
        leave-active-class="transition duration-75 ease-in"
        leave-from-class="transform scale-100 opacity-100"
        leave-to-class="transform scale-95 opacity-0"
      >
        <MenuItems
          class="absolute my-2 w-56 divide-y divide-gray-100 dark:divide-gray-700 rounded-md bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
          ref="menuItems"
          :class="positionClass"
        >
          <div
            v-for="(itemGroup, itemGroupIndex) in itemGroups"
            :key="'itemGroup-' + itemGroupIndex"
          >
            <div class="px-1 py-1">
              <MenuItem
                v-for="(item, itemIndex) in itemGroup"
                :key="'item-' + itemIndex"
              >
                <button
                  @click="handleClick(item.action)"
                  class="group flex w-full items-center rounded-md px-2 py-2 text-sm text-gray-900 dark:text-gray-200 active:bg-gray-100 active:dark:bg-gray-600 hover:bg-gray-100 hover:dark:bg-gray-600"
                >
                  <component
                    v-if="item.icon"
                    :is="item.icon"
                    class="mr-1 h-5 w-5 text-blue-400"
                    aria-hidden="true"
                  />
                  <span
                    :class="['mr-2 text-blue-400']"
                    aria-hidden="true"
                  ></span>
                  {{ item.label }}
                </button>
              </MenuItem>
            </div>
          </div>
        </MenuItems>
      </transition>
    </Menu>
  </div>
</template>

<script lang="ts">
import type { MenuItemGroup } from '@/types/Menu';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
import { EllipsisVerticalIcon } from '@heroicons/vue/20/solid';
import type { PropType } from 'vue';

export default {
  components: {
    Menu,
    MenuButton,
    MenuItems,
    MenuItem,
    EllipsisVerticalIcon,
  },
  props: {
    itemGroups: {
      type: Array as PropType<MenuItemGroup[]>,
      default: () => [],
    },
  },
  watch: {
    isOpen: {
      handler() {
        if (!this.isOpen) {
          this.isOff = {
            top: false,
            right: false,
            bottom: false,
            left: false,
          };
        }
        this.$nextTick(() => {
          this.isOff.top = this.checkIsOffTop();
          this.isOff.right = this.checkIsOffRight();
          this.isOff.left = this.checkIsOffLeft();
          this.isOff.bottom = this.checkIsOffBottom();
        });
      },
      immediate: true,
    },
  },
  data() {
    return {
      isOpen: false,
      isOff: {
        top: false,
        right: false,
        bottom: false,
        left: false,
      },
    };
  },
  computed: {
    menuItemsBoundingBox(): any {
      const element = (this.$refs.menuItems as any)?.$el;

      if (!element) {
        return null;
      }
      return element.getBoundingClientRect();
    },
    positionClass(): string {
      const { top, right, bottom, left } = this.isOff;
      let classes = [];

      if (bottom) {
        classes.push('bottom-full');
        classes.push('top-auto');
      } else {
        classes.push('top-full');
      }

      if (right) {
        classes.push('right-0');
      } else if (left) {
        classes.push('left-0');
        classes.push('right-auto');
      }

      return classes.join(' ');
    },
  },
  methods: {
    handleClick(action: () => void) {
      if (action && typeof action === 'function') {
        action();
      }
    },
    captureOpenState(open: boolean) {
      this.isOpen = open;
    },
    checkIsOffTop() {
      const rect = this.menuItemsBoundingBox;
      return rect && rect.top < 0;
    },
    checkIsOffRight() {
      const rect = this.menuItemsBoundingBox;
      return rect && rect.right > window.innerWidth;
    },
    checkIsOffLeft() {
      const rect = this.menuItemsBoundingBox;
      return rect && rect.left < 0;
    },
    checkIsOffBottom() {
      const rect = this.menuItemsBoundingBox;
      return rect && rect.bottom > window.innerHeight;
    },
  },
};
</script>
