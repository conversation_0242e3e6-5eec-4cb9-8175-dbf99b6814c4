import React from 'react';
import PropTypes from 'prop-types';
import { IconButton, Tooltip, Menu, MenuItem } from '@/reactComponents/library';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import MoreVertIcon from '@mui/icons-material/MoreVert';

const BatchItemActions = (props) => {
  const {
    batch,
    openBatchToEdit,
    setBatchToDelete,
    deleteDisabled,
    disableBatchEdit,
  } = props;

  const [menuOpen, setMenuOpen] = React.useState(false);

  return (
    <React.Fragment>
      <IconButton
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          setMenuOpen(e.currentTarget);
        }}
        disabled={disableBatchEdit}
      >
        <MoreVertIcon />
      </IconButton>
      <Menu
        anchorEl={menuOpen}
        open={Boolean(menuOpen)}
        onClose={(e) => {
          e.stopPropagation();
          e.preventDefault();
          setMenuOpen(false);
        }}
      >
        <MenuItem
          onClick={(e) => {
            openBatchToEdit(batch);
            e.stopPropagation();
            e.preventDefault();
            setMenuOpen(false);
          }}
          sx={{ gap: 1 }}
        >
          <EditIcon />
          Edit Batch
        </MenuItem>
        <Tooltip
          title={deleteDisabled ? "Can't delete batch with timecards" : ''}
        >
          <span
            onClick={(e) => {
              if (deleteDisabled) {
                //prevent loading batch when clicking disabled delete btn
                e.stopPropagation();
                e.preventDefault();
                setMenuOpen(false);
              }
            }}
          >
            <MenuItem
              onClick={(e) => {
                setBatchToDelete(batch);
                e.stopPropagation();
                setMenuOpen(false);
                e.preventDefault();
              }}
              disabled={deleteDisabled}
              sx={{ gap: 1 }}
            >
              <DeleteIcon />
              Delete Batch
            </MenuItem>
          </span>
        </Tooltip>
      </Menu>
    </React.Fragment>
  );
};

BatchItemActions.propTypes = {
  batch: PropTypes.object.isRequired,
  openBatchToEdit: PropTypes.func.isRequired,
  setBatchToDelete: PropTypes.func.isRequired,
  deleteDisabled: PropTypes.bool,
  disableBatchEdit: PropTypes.bool,
};

export default BatchItemActions;
